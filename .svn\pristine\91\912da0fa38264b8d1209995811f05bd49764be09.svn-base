<template>
    <transition name="slide">
        <div class="user_favorites_page second_level_page">
            <mrHeader>
                <template #title>
                    {{ lang.cloud_favorites }}
                </template>
                <template #right>
                    <van-popover
                        class="more"
                        v-model="popupMore"
                        theme="dark"
                        trigger="click"
                        placement="bottom-end"
                        :offset="[-3, -5]"
                        :actions="actions"
                        @open="setActions"
                        @select="onFavoritesSelect"
                    >
                        <template #reference>
                            <van-icon name="add-o" color="#fff" size="1rem" class="menu-icon" />
                        </template>
                    </van-popover>
                </template>
            </mrHeader>
            <div class="user_favorites_container">
                <div v-show="!loadedFavorites" class="full_loading_spinner van_loading_spinner">
                    <van-loading color="#00c59d" />
                </div>
                <div class="favorites_groups">
                    <div
                        v-for="(group, g_index) of favoritesGroups"
                        class="favorites_group_item clearfix"
                        :key="group.favorite_id"
                    >
                        <p v-if="showType == 1" class="group_subject">{{ group.subject }}</p>
                        <div v-if="showType == 2 && group.exam_id != -1" class="exam_detai">
                            <div class="clearfix">
                                <p class="left_item">{{ lang.patient_name }}: {{ group.patientInfo.patient_name }}</p>
                                <p class="right_item">{{ lang.exam_type }}: {{ lang.exam_types[group.exam_type] }}</p>
                            </div>
                            <div class="clearfix">
                                <p class="left_item">{{ lang.patient_age }}: {{ group.patientInfo.patient_age }}</p>
                                <p class="right_item">{{ lang.patient_sex }}: {{ group.patientInfo.patient_sex }}</p>
                            </div>
                            <div class="clearfix">
                                <p>{{ lang.exam_time }}: {{ formatTime(group.patient_series_datetime) }}</p>
                            </div>
                        </div>
                        <div v-if="showType == 2 && group.exam_id == -1" class="exam_detai">
                            {{ lang.not_exam_picture }}
                        </div>
                        <div v-if="showType == 3">
                            <p class="group_subject">
                                {{ formatTime(group.send_ts) }}
                                <i
                                    class="iconfont icon-unlock"
                                    v-show="group.public_status > 0"
                                    @click="changePublicStatus(group)"
                                ></i>
                                <i
                                    class="iconfont icon-lock"
                                    v-show="group.public_status == 0"
                                    @click="changePublicStatus(group)"
                                ></i>
                            </p>
                        </div>
                        <v-touch @press="editImage = true" class="clearfix">
                            <div
                                v-for="(file, f_index) of group.list"
                                @click="clickItem(g_index, f_index)"
                                class="file_item"
                                :key="file.favorite_id"
                            >
                                <div class="file_container">
                                    <common-image :fileItem="file"></common-image>
                                </div>
                                <van-checkbox-group
                                    v-show="editImage"
                                    v-model="checkList"
                                >
                                    <van-checkbox :name="g_index + '_' + f_index" checked-color="#00c59d"></van-checkbox>
                                </van-checkbox-group>
                            </div>
                        </v-touch>
                    </div>
                </div>
            </div>
            <div v-show="editImage" class="favorites_operate">
                <div class="transmit" @click="gotoTransmit">{{ lang.transmit_title }}</div>
                <div class="delete" @click="deleteItem">{{ lang.action_delete_text }}</div>
                <div class="select_all" v-show="!isSelectAll" @click="selectAll">{{ lang.select_all }}</div>
                <div class="select_all" v-show="isSelectAll" @click="selectAll">{{ lang.cancel_select_all }}</div>
                <div class="select_all" @click="edit(false)">{{ lang.cancel_btn }}</div>
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from "../lib/base";
import { Icon, Toast, Loading, Popover, Checkbox, CheckboxGroup } from "vant";
import send_message from "../lib/send_message.js";
import aiAnalyze from "../lib/aiAnalyze";
import service from "../service/service";
import { parseImageListToLocal, transferPatientInfo, cancelFavoriteCommit } from "../lib/common_base";
import Tool from "@/common/tool";
import moment from "moment";
export default {
    mixins: [base, send_message, aiAnalyze],
    name: "user_favorites",
    components: {
        VanIcon: Icon,
        VanLoading: Loading,
        VanPopover: Popover,
        VanCheckbox: Checkbox,
        VanCheckboxGroup: CheckboxGroup
    },
    data() {
        return {
            favorites: [],
            loadedFavorites: false,
            sortedFavorites: [],
            editImage: false,
            checkList: [],
            isSelectAll: false,
            popupMore: false,
            showType: 3, //排序：1,按群  2,按检查 3,按收藏时间
            actions: [],
        };
    },
    beforeDestroy() {},
    beforeRouteEnter(to, from, next) {
        if (!from.name) {
            // //二级页面刷新时返回上级页面
            next(false);
            window.location.replace(`#/index`);
        } else {
            next();
        }
    },
    computed: {
        favoritesGroups() {
            return this.$store.state.userFavorites;
        },
    },
    mounted() {
        this.$nextTick(() => {
            var that = this;
            this.getFavorites();
            this.$root.eventBus.$off("favoritesTransmit").$on("favoritesTransmit", function (data) {
                let queue = [];
                for (let checked of that.checkList) {
                    let g_index = checked.split("_")[0];
                    let f_index = checked.split("_")[1];
                    let index = that.getIndex(g_index, f_index);
                    let img = that.sortedFavorites[index];
                    img.resource_id = img.resource_copied_from
                    queue.push(img);
                }
                that.$root.transmitQueue[data.cid || "f-" + data.id] = queue;
                if (that.conversationList[data.cid]) {
                    //会话已开启则直接转发
                    that.sendTransmitMessage(data.cid);
                } else {
                    //会话未开启则开启会话,开启会话后检查待转发队列
                    if (data.cid) {
                        that.openConversation(data.cid, 7);
                    } else {
                        that.openConversation(data.id, 3);
                    }
                }
                that.edit(false);
            });
            this.$root.eventBus.$off("favoritesGalleryTransmit").$on("favoritesGalleryTransmit", (data)=> {
                let queue=this.$root.transmitTempList;
                queue = queue.map(item=>{
                    item.resource_id = item.resource_copied_from
                    return item
                })
                that.$root.transmitQueue[data.cid || "f-" + data.id] = queue;
                if (that.conversationList[data.cid]) {
                    //会话已开启则直接转发
                    that.sendTransmitMessage(data.cid);
                } else {
                    //会话未开启则开启会话,开启会话后检查待转发队列
                    if (data.cid) {
                        that.openConversation(data.cid, 7);
                    } else {
                        that.openConversation(data.id, 3);
                    }
                }
                that.edit(false);
                Toast(that.lang.transmiting_tip);
            });
        });
    },
    methods: {
        setActions() {
            let actions = [
                {
                    text: this.lang.sort_by_time,
                    className: "menu-item",
                    showType: 3,
                    functionName: "parseFavoritesByCollect",
                    disabled: false,
                },
                {
                    text: this.lang.sort_by_group,
                    className: "menu-item",
                    showType: 1,
                    functionName: "parseFavoritesByGroup",
                    disabled: false,
                },
                {
                    text: this.lang.sort_by_exam,
                    className: "menu-item",
                    showType: 2,
                    functionName: "parseFavoritesByExam",
                    disabled: false,
                },
                {
                    text: this.lang.multi_select,
                    className: "menu-item",
                    functionName: "edit",
                    disabled: this.editImage,
                },
            ];
            actions.forEach((action) => {
                if (action.showType === this.showType) {
                    action.className += " active";
                } else {
                    action.className = "menu-item";
                }
            });
            console.log(actions);
            this.$set(this, "actions", actions);
        },
        onFavoritesSelect(val) {
            if (val.functionName === "edit") {
                this[val.functionName](true);
            } else {
                this[val.functionName]();
            }
        },
        getFavorites() {
            this.$store.commit("userFavorites/setFavoritesGroups", []);
            let controller = window.main_screen.controller;
            var that = this;
            controller.emit("query_user_favorites", null, function (is_succ, data) {
                if (is_succ) {
                    console.log("query_user_favorites",data);
                    that.loadedFavorites = true;
                    that.favorites = data;
                    console.log(data);
                    parseImageListToLocal(that.favorites, "url");
                    that.parseFavorites();
                } else {
                    Toast("get favorites error");
                }
            });
        },
        parseFavorites() {
            if (this.showType == 1) {
                this.parseFavoritesByGroup();
            } else if (this.showType == 2) {
                this.parseFavoritesByExam();
            } else {
                this.parseFavoritesByCollect();
            }
        },
        parseFavoritesByGroup() {
            this.edit(false);
            this.popupMore = false;
            this.showType = 1;
            let groups = {};
            for (let item of this.favorites) {
                let cid = item.conversation_list[0].group_id;
                item.group_id = cid;
                item.resource_id = item.favorite_id
                let subject = item.conversation_list[0].subject;
                if (groups[cid]) {
                    groups[cid].list.push(item);
                } else {
                    groups[cid] = {
                        cid: cid,
                        subject: item.conversation_list[0].subject,
                        list: [item],
                    };
                }
            }
            var tempArr = [];
            this.sortedFavorites = [];
            for (let key in groups) {
                tempArr.push(groups[key]);
                this.sortedFavorites = this.sortedFavorites.concat(groups[key].list);
            }
            this.$store.commit("userFavorites/setFavoritesGroups", tempArr);
        },
        parseFavoritesByExam() {
            this.edit(false);
            this.popupMore = false;
            this.showType = 2;
            let groups = {};
            for (let item of this.favorites) {
                let cid = item.conversation_list[0].group_id;
                let exam_id = item.exam_id || -1;
                item.group_id = cid;
                item.resource_id = item.favorite_id
                let subject = item.conversation_list[0].subject;
                if (exam_id) {
                    if (groups[exam_id]) {
                        groups[exam_id].list.push(item);
                    } else {
                        groups[exam_id] = {
                            cid: cid,
                            exam_id: exam_id,
                            subject: subject,
                            list: [item],
                            patientInfo: transferPatientInfo(item),
                            exam_type: item.exam_type,
                            patient_series_datetime: item.patient_series_datetime,
                        };
                    }
                }
            }
            var tempArr = [];
            this.sortedFavorites = [];
            for (let key in groups) {
                if (key != -1) {
                    tempArr.push(groups[key]);
                    this.sortedFavorites = this.sortedFavorites.concat(groups[key].list);
                }
            }
            if (groups[-1]) {
                tempArr.push(groups[-1]);
                this.sortedFavorites = this.sortedFavorites.concat(groups[-1].list);
            }

            this.$store.commit("userFavorites/setFavoritesGroups", tempArr);
        },
        parseFavoritesByCollect() {
            this.edit(false);
            this.popupMore = false;
            this.showType = 3;
            let groups = {};
            for (let item of this.favorites) {
                let cid = item.conversation_list[0].group_id;
                item.group_id = cid;
                item.resource_id = item.favorite_id
                let public_status = item.public_status || 0;
                let send_ts = item.send_ts;
                item.send_ts = send_ts;
                if (groups[send_ts]) {
                    groups[send_ts].list.push(item);
                } else {
                    groups[send_ts] = {
                        cid: cid,
                        send_ts: send_ts,
                        list: [item],
                        public_status: public_status,
                    };
                }
            }
            var tempArr = [];
            this.sortedFavorites = [];
            for (let key in groups) {
                tempArr.push(groups[key]);
                this.sortedFavorites = this.sortedFavorites.concat(groups[key].list);
            }
            this.$store.commit("userFavorites/setFavoritesGroups", tempArr);
        },
        clickItem(g_index, f_index) {
            if (this.editImage) {
                //编辑图片
                console.log("check", this.checkList);
                if (this.isSelectAll) {
                    this.isSelectAll = false;
                } else {
                    if (this.checkList.length == this.sortedFavorites.length) {
                        this.isSelectAll = true;
                    }
                }
            } else {
                let index = this.getIndex(g_index, f_index);
                //打开画廊
                let list = [];
                this.sortedFavorites.forEach((element) => {
                    // 为了能正确评论 使用源resource_id 进行评论
                    list.push({
                        ...element,
                        // origin_resource_id: element.resource_id,
                        // resource_id: element.resource_copied_from,
                    });
                });
                this.$store.commit("gallery/setGallery", {
                    list: list,
                    index: index,
                });
                this.$nextTick(() => {
                    this.$router.push(`/index/user_favorites/gallery`);
                });
            }
        },
        getIndex(g_index, f_index) {
            let index = 0;
            for (var i = 0; i < g_index; i++) {
                index += this.favoritesGroups[i].list.length;
            }
            index += parseInt(f_index);
            return index;
        },
        edit(mode) {
            if (mode) {
                this.editImage = true;
                this.popupMore = false;
            } else {
                this.editImage = false;
                this.checkList = [];
                this.isSelectAll = false;
            }
        },
        selectAll() {
            if (this.isSelectAll) {
                this.checkList = [];
                this.isSelectAll = false;
            } else {
                let temp = [];
                for (let g_index = 0; g_index < this.favoritesGroups.length; g_index++) {
                    let group = this.favoritesGroups[g_index];
                    for (let f_index = 0; f_index < group.list.length; f_index++) {
                        temp.push(g_index + "_" + f_index);
                    }
                }
                this.checkList = temp;
                this.isSelectAll = true;
            }
        },
        deleteItem() {
            if (this.checkList.length <= 0) {
                Toast(this.lang.transmit_less_tip);
                return;
            }
            let del_list = [];
            let del_index_list = [];
            for (let checked of this.checkList) {
                let g_index = checked.split("_")[0];
                let f_index = checked.split("_")[1];
                let target = this.favoritesGroups[g_index].list[f_index];
                del_list.push({
                    resource_id: target.resource_copied_from,
                    cid: target.group_id,
                });
                let index = 0;
                for (var i = 0; i < g_index; i++) {
                    index += this.favoritesGroups[i].list.length;
                }
                index += parseInt(f_index);
                del_index_list.push(index);
            }
            this.loadedFavorites = true;
            cancelFavoriteCommit(del_list, () => {
                this.removeFavorites(del_index_list);
                this.edit(false);
            });
        },
        removeFavorites(index_list) {
            let removeNum = 0;
            index_list = index_list.sort(function (a, b) {
                return a - b;
            });
            for (let index of index_list) {
                let file = this.sortedFavorites[index - removeNum];
                console.log("delete", file.favorite_id);
                this.sortedFavorites.splice(index - removeNum, 1);

                removeNum++;
            }
            let log = [];
            for (let file of this.sortedFavorites) {
                log.push(file.favorite_id);
            }
            console.log("exist id", log);
            this.favorites = this.sortedFavorites;
            this.parseFavorites();
        },
        gotoTransmit() {
            if (this.checkList.length <= 0) {
                Toast(this.lang.transmit_less_tip);
                return;
            }
            this.$router.push(`/index/user_favorites/transmit`);
        },
        getShowDes(name) {
            if (name.length > 8) {
                name = name.substring(0, 8) + "......";
            }
            return name;
        },
        cancelShare() {
            this.editImage = false;
        },
        changePublicStatus(group) {
            let tip = "";
            let targetStatus = 0;
            if (group.public_status == 0) {
                tip = this.lang.favorites_public_tip;
                targetStatus = 1;
            } else if (group.public_status > 0) {
                tip = this.lang.favorites_private_tip;
                targetStatus = 0;
            }
            let userFavoriteIdList = [];
            for (let item of group.list) {
                userFavoriteIdList.push(item.favorite_id);
            }
            Tool.openMobileDialog({
                message: tip,
                showRejectButton: true,
                confirm: () => {
                    service
                        .updatePublicStatus({
                            userFavoriteIdList: userFavoriteIdList,
                            publicStatus: targetStatus,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                group.public_status = targetStatus;
                                for (let item of group.list) {
                                    item.public_status = targetStatus;
                                }
                            }
                        });
                },
            });
        },
    },
};
</script>
<style lang="scss">
.user_favorites_page {
    .user_favorites_container {
        flex: 1;
        position: relative;
        overflow: auto;
        .van_loading_spinner {
            display: flex;
            justify-content: center;
            align-items: center;

            .van-loading {
                margin-top: 0.5rem;
            }

            .van-loading__spinner {
                width: 2.8rem;
                height: 2.8rem;

                .van-loading__circular circle {
                    stroke-width: 0.2rem;
                }
            }
        }

        .favorites_groups {
            margin-bottom: 2.5rem;
            padding: 0.5rem 0.3rem 0;
            .favorites_group_item {
                margin-bottom: 0.5rem;
                background-color: #fff;
                .group_subject {
                    color: #333;
                    line-height: 2;
                    font-size: 0.8rem;
                    padding: 0 0.2rem;
                    .icon-lock {
                        margin-left: 0.3rem;
                        color: #5f92f6;
                    }
                    .icon-unlock {
                        margin-left: 0.3rem;
                        color: #5f92f6;
                    }
                }
                .exam_detai {
                    font-size: 0.8rem;
                    padding: 0 0.4rem;
                    box-sizing: border-box;
                    line-height: 1.6;
                    .left_item,
                    .right_item {
                        float: left;
                        width: 50%;
                    }
                }
                .file_item {
                    float: left;
                    width: 25%;
                    border: 1px solid #fff;
                    box-sizing: border-box;
                    position: relative;
                    .file_container {
                        position: relative;
                        padding-top: 100%;
                        height: 0;
                        background-color: #333;
                        .file_image {
                            max-width: 100%;
                            max-height: 100%;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }
                        .icon-videofill {
                            font-size: 1rem;
                            color: #00c59d;
                            position: absolute;
                            bottom: 0.2rem;
                            left: 0.3rem;
                        }
                        .review_text {
                            color: yellow;
                            position: absolute;
                            top: 5%;
                            left: 50%;
                            transform: translateX(-50%) scale(0.4);
                            font-size: 1.2rem;
                            text-align: center;
                            white-space: nowrap;
                            font-weight: 600;
                        }
                    }
                    .van-checkbox-group {
                        max-height: 200px;
                        overflow: auto;
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        border-radius: 0.75rem;
                        top: 0px;
                        right: 0px;

                        .van-checkbox{
                            width: 100%;
                            height: 100%;
                            padding: 5px;
                            box-sizing: border-box;
                            justify-content: flex-end;
                            align-items: flex-end;
                        }
                    }
                }
            }
        }
    }
    .edit_favorites_btn {
        position: absolute;
        right: 0;
        width: 3rem;
    }
    .favorites_operate {
        position: fixed;
        bottom: 0;
        height: 2.4rem;
        width: 100%;
        background-color: #f7f7f7;
        border-top: 1px solid #ccc;
        color: #00c59d;
        text-align: center;
        display: flex;
        line-height: 2.4rem;
        & > div {
            flex: 1;
            font-size: 0.9rem;
        }
    }
}
</style>
