<template>
    <div class="case_fliter">
        <div class="header flex justify_column align_center">
            <div class="title over_hidden">
                {{ lang.search_text }}
            </div>
            <div class="tips flex justify_column flex_wrap align_center">
                <div class="tip_ele" v-for="item of conditionArray" :key="item.field">
                    <span>
                        {{ lang.case_database_fliter[item.field] }}
                        <i
                            class="icon iconfont iconclose"
                            @click.stop="fieldChange(item.field, item.parent, item.gparent)"
                            v-show="conditionArray.length > 0"
                        >
                        </i>
                    </span>
                </div>
            </div>
            <div class="icon_aear" v-show="conditionArray.length > 0">
                <i class="iconfont iconel-icon-delete2" @click.stop="resetCondition()"></i>
            </div>
        </div>

        <div class="content flex justify_row flex_wrap">
            <div class="row flex justify_column" v-for="(item, title_f) of allCondition" :key="title_f">
                <div class="title_first over_hidden flex justify_column flex_wrap align_center">
                    <el-tooltip
                        class="item breast-search-tooltip"
                        effect="dark"
                        :content="lang.case_database_fliter[title_f]"
                        placement="top"
                        :disabled="isShowTooltip"
                    >
                        <div class="title_content" @mouseenter="visibilityChange($event)">
                            {{ lang.case_database_fliter[title_f] }}
                        </div>
                    </el-tooltip>
                </div>
                <template v-if="isArrary(item)">
                    <div class="row_line flex justify_column flex_wrap">
                        <div class="second_title_div"></div>
                        <div class="fields flex justify_column align_center flex_wrap">
                            <div v-for="(field, j) of item" :key="field + j">
                                <el-tooltip
                                    class="item breast-search-tooltip"
                                    effect="dark"
                                    :content="lang.case_database_fliter[field]"
                                    placement="top"
                                    :disabled="isShowTooltip"
                                >
                                    <div
                                        :class="{ field: true, active: newCondition[title_f].indexOf(field) >= 0 }"
                                        @mouseenter="visibilityChange($event)"
                                        @click="fieldChange(field, title_f)"
                                    >
                                        {{ lang.case_database_fliter[field] }}
                                    </div>
                                </el-tooltip>
                            </div>
                        </div>
                        <div class="switch_icon"></div>
                    </div>
                </template>
                <template v-else>
                    <div class="row_line flex justify_row flex_wrap">
                        <div
                            class="fields fields_two flex justify_column align_center"
                            v-for="(second, title_s, num) of item"
                            :key="title_s"
                            v-show="drownArray[title_f] || num == 0"
                        >
                            <template v-if="drownArray[title_f] || num == 0">
                                <div class="second_title_content flex justify_column">
                                    <el-tooltip
                                        class="item breast-search-tooltip"
                                        effect="dark"
                                        :content="lang.case_database_fliter[title_s]"
                                        placement="top"
                                        :disabled="isShowTooltip"
                                    >
                                        <div class="title_second over_hidden">
                                            <div @mouseenter="visibilityChange($event)" class="over_hidden">
                                                {{ lang.case_database_fliter[title_s] }}
                                            </div>
                                        </div>
                                    </el-tooltip>
                                </div>
                                <div class="flex justify_column align_center flex_wrap">
                                    <div v-for="(field, j) of second" :key="field + j">
                                        <el-tooltip
                                            class="item breast-search-tooltip"
                                            effect="dark"
                                            :content="lang.case_database_fliter[field]"
                                            placement="top"
                                            :disabled="isShowTooltip"
                                        >
                                            <div
                                                class="over_hidden field"
                                                :class="{ active: newCondition[title_f][title_s].indexOf(field) >= 0 }"
                                                @click="fieldChange(field, title_s, title_f)"
                                                @mouseenter="visibilityChange($event)"
                                            >
                                                {{ lang.case_database_fliter[field] }}
                                            </div>
                                        </el-tooltip>
                                    </div>
                                </div>
                                <div class="switch_icon">
                                    <i
                                        class="icon iconfont icondown"
                                        refs="iconup"
                                        v-if="!drownArray[title_f] && num == 0"
                                        @click.stop="switchDisplay(title_f)"
                                    ></i>
                                    <i
                                        class="icon iconfont iconup"
                                        refs="icondown"
                                        v-if="drownArray[title_f] && num == 0"
                                        @click.stop="switchDisplay(title_f)"
                                    ></i>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>
<script>
import base from "../../../../lib/base";
import Tool from "@/common/tool.js";
import { cloneDeep } from "lodash";
export default {
    mixins: [base],
    components: {},
    props: {
        condition: {
            type: Object,
            default: () => {
                return {};
            },
        },
        // isCaseImage: {
        //     type: Boolean,
        //     default: true
        // }
    },
    data() {
        return {
            isShowTooltip: false,
            oldCondition: {},
            newCondition: {},
            drownArray: {
                bi_rads_feature: false,
                //乳腺癌病理分类
                pathological_classification_breast_cancer: false,
            },
            defaultCondition: {},
            //lang.case_database_fliter['case_database_fliter']
            allCondition: {},
        };
    },
    mounted() {},
    created() {
        this.defaultCondition = cloneDeep(this.$store.state.caseDatabase.defaultCondition);
        this.allCondition = cloneDeep(this.$store.state.caseDatabase.allCondition);
        this.oldCondition = cloneDeep(this.$store.state.caseDatabase.defaultCondition);
        this.newCondition = cloneDeep(this.$store.state.caseDatabase.defaultCondition);
    },
    watch: {
        condition: {
            handler(newVal) {
                this.newCondition = cloneDeep(newVal);
                this.oldCondition = cloneDeep(newVal);
            },
            immediate: true,
            deep: true,
        },
    },
    computed: {
        conditionArray() {
            let list = [];
            for (let key in this.newCondition) {
                let item = this.newCondition[key];
                if (this.isArrary(item)) {
                    for (let i in item) {
                        list.push({ parent: key, field: item[i] });
                    }
                } else {
                    for (let key_ in item) {
                        for (let j in item[key_]) {
                            list.push({ parent: key_, gparent: key, field: item[key_][j] });
                        }
                    }
                }
            }
            return list;
        },
    },
    methods: {
        visibilityChange(event) {
            const ev = event.target;
            const evWeight = ev.scrollWidth;
            const contentWeight = ev.clientWidth;
            if (evWeight > contentWeight) {
                this.isShowTooltip = false;
            } else {
                this.isShowTooltip = true;
            }
        },
        // handleback(){
        //     this.oldCondition = cloneDeep(this.newCondition)
        //     this.$emit('handleback',this.newCondition)
        // },
        resetCondition() {
            this.oldCondition = cloneDeep(this.defaultCondition);
            this.newCondition = cloneDeep(this.defaultCondition);
            this.$emit("handleback", this.newCondition);
        },
        switchDisplay(re) {
            this.drownArray[re] = !this.drownArray[re];
        },
        isArrary(a) {
            return a instanceof Array;
        },
        fieldChange(field, parent, gparent) {
            var condition = cloneDeep(this.newCondition);
            if (gparent) {
                condition[gparent] = condition[gparent] || {};
                condition[gparent][parent] = condition[gparent][parent] || [];
                let index = condition[gparent][parent].indexOf(field);
                index >= 0 ? condition[gparent][parent].splice(index, 1) : condition[gparent][parent].push(field);
            } else {
                condition[parent] = condition[parent] || [];
                let index = condition[parent].indexOf(field);
                index >= 0 ? condition[parent].splice(index, 1) : condition[parent].push(field);
            }
            this.newCondition = condition;
            this.$emit("handleback", condition);
        },
    },
};
</script>
<style lang="scss" scoped>
.case_fliter {
    border-top: 1px solid #dbdbdb;
    flex-shrink: 0;
    display: flex;
    justify-content: column;
    flex-wrap: wrap;
    cursor: pointer;
    .header {
        width: 100%;
        min-height: 35px;
        color: #303133;
        .iconel-icon-delete2 {
            color: #666;
            right: 4px;
            font-size: 20px;
            cursor: pointer;
        }
        .title {
            width: 15%;
            font-family: "Arial Negreta", "Arial Normal", "Arial";
            font-weight: 500;
            line-height: 35px;
            display: inline;
            font-size: 17px;
            font-style: normal;
        }
        .tips {
            width: 82%;
            .tip {
                height: 35px;
            }
        }
        .tip_ele {
            height: 25px;
            display: inline-block;
            color: white;
            background: #83a5a1;
            display: flex;
            align-items: center;
            border-radius: 10px;
            padding: 1px 6px 0 6px;
            margin: 4px 4px 4px 0;
            cursor: pointer;
            position: relative;
            word-break: break-all;
            .iconclose {
                font-size: 15px;
                position: absolute;
                right: calc(50% - 10px);
                top: 2px;
                color: #83a5a1;
                cursor: pointer;
                visibility: hidden;
            }
            &:hover {
                background-color: #e2e2e2;
                .iconclose {
                    visibility: visible;
                }
            }
        }
        .icon_aear {
            flex: 1;
            text-align: right;
            padding-right: 5px;
        }
    }
    .content {
        width: 100%;
        .row {
            width: 100%;
            .title_first {
                width: 15%;
                background-color: rgb(242, 242, 242);
                color: black;
                text-align: center;
                border-top: 0.5px solid white;
                border-left: 1px solid white;
                margin-right: 2px;
                .title_content {
                    vertical-align: middle;
                    margin: auto;
                    align-self: center;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }

            .row_line {
                width: 90%;
                .second_title_div {
                    width: 149px;
                    flex-shrink: 0;
                    min-height: 35px;
                    border-left: 0.5px solid #efefef;
                    border-top: 0.5px solid #efefef;
                    border-bottom: 0.5px solid #efefef;
                    margin-left: 1px;
                }
                .fields {
                    width: calc(100% - 150px);
                    border-right: 0.5px solid #efefef;
                    border-top: 0.5px solid #efefef;
                    border-bottom: 0.5px solid #efefef;
                    min-height: 35px;
                    .second_title_content {
                        width: 150px !important;
                        flex-shrink: 0;
                        height: 100%;
                    }
                    .title_second {
                        height: 100%;
                        background-color: rgb(242, 242, 242);
                        color: black;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        margin: 1px;
                        padding: 2px 8px 2px 8px;
                        div {
                            width: 100%;
                        }
                    }
                    .field {
                        margin: auto;
                        width: 150px;
                        color: black;
                        padding: 0 20px;
                        border-radius: 10px;
                        margin: 4px;
                        cursor: pointer;
                        // position: relative;
                        word-break: break-all;
                        div {
                            width: 150px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }

                    .active {
                        color: #777777;
                    }
                    .switch_icon {
                        flex: 1;
                        text-align: right;
                        margin-right: 5px;
                        align-self: center;
                    }
                }
                .fields_two {
                    width: 100%;
                }
            }
        }
    }
    .over_hidden {
        text-overflow: ellipsis !important;
        overflow: hidden !important;
        white-space: nowrap !important;
        list-style-position: inside;
    }
    .flex {
        display: flex;
    }
    .justify_row {
        justify-content: row;
    }
    .justify_column {
        justify-content: column;
    }
    .flex_wrap {
        flex-wrap: wrap;
    }
    .align_center {
        align-items: center;
    }
    .flex_no_wrap {
        flex-wrap: nowrap;
    }
}
.breast-search-tooltip {
    // .el-tooltip__popper.is-dark
    top: -100px;
}
</style>

<style lang="scss" >
.el-tooltip__popper.is-dark {
    top: -100px;
}
</style>
