<template>
	<div class="international_page third_level_page">
        <mrHeader>
            <template #title>
                {{lang.international_title}}
            </template>
        </mrHeader>
        <div class="setting_list_group">
        	<div class="setting_list_item" @click="changeLang('CN')">
                <p class="setting_list_title">简体中文</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'CN'"></i>
            </div>
            <div class="setting_list_item" @click="changeLang('EN')">
                <p class="setting_list_title">English</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'EN'"></i>
            </div>
            <div class="setting_list_item" @click="changeLang('ES')">
                <p class="setting_list_title">Español</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'ES'"></i>
            </div>
            <div class="setting_list_item" @click="changeLang('PTBR')">
                <p class="setting_list_title">Português</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'PTBR'"></i>
            </div>
            <div class="setting_list_item" @click="changeLang('RU')">
                <p class="setting_list_title">Русский язык</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'RU'"></i>
            </div>
            <div class="setting_list_item" @click="changeLang('DE')">
                <p class="setting_list_title">Deutsch</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'DE'"></i>
            </div>
            <div class="setting_list_item" @click="changeLang('FR')">
                <p class="setting_list_title">Français</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'FR'"></i>
            </div>
            <div class="setting_list_item" @click="changeLang('IT')">
                <p class="setting_list_title">Italiano</p>
                <i  class="icon iconfont icon-duihao" v-if="lang.currentLanguage === 'IT'"></i>
            </div>
        </div>

	</div>

</template>
<script>
import languages from '@/common/language'
import base from '../lib/base'
import { Toast } from 'vant';
export default {
    mixins: [base],
    name: 'InternationalPage',
    components: {},
    data(){
        return {
        }
    },
    methods:{
    	changeLang(lang){
            window.iSLanguageChanged = true
    		window.localStorage.setItem('lang',lang)
    		this.$store.commit('language/setLanguage', languages[lang]);
            this.$store.commit('language/setLanguage', { currentLanguage: lang })
    		Toast({
    			message:this.lang.change_language_success,
    			duration:2000
    		})
            window.CWorkstationCommunicationMng.SetLanguage({language:lang})
    		this.$router.back();
    	}
    }
}

</script>
<style scoped lang="scss">
.international_page{
    .setting_list_item{
        position: relative;
        .icon-duihao {
            position: absolute;
            right: 0.5rem;
            top: 0;
            color: green;
            font-size: 1.3rem;
        }
    }

}

</style>
