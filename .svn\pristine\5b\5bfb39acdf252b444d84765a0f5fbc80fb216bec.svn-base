<template>
    <transition name="slide">
    	<div class="add_group_page second_level_page">
            <mrHeader>
                <template #title>
                    {{lang.wechat_create_group}}
                </template>
            </mrHeader>
            <div class="second_level_content container">
                <div>
                    <div class="subject_bar">
                        <input class="subject" type="text" v-model="subject" :placeholder="lang.group_chat_name">
                    </div>
                    <div class="public_bar clearfix">
                        <span class="left">{{lang.group_setting_is_public}}</span>
                        <div class="fr">
                            <group-public-type class="public_mode_seleter"
                                :seletedId="isPublicOptionSelected"
                                :callback="changeIsPublicType">
                            </group-public-type>
                        </div>
                    </div>
                    <button class="primary_bg wechat_group_share" @click="wechatCreateGroup">{{lang.invite_wechat_user_to_group}}</button>
                </div>
            </div>
    	</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import Tool from '@/common/tool.js'
import groupsetTool from '../lib/groupsetTool'
import groupPublicType from '../MRComponents/groupPublicType'
import share_to_wechat from '../lib/share_to_wechat'
export default {
    mixins: [base,groupsetTool,share_to_wechat],
    name: 'wechatAddGroup',
    components: { groupPublicType },
    data(){
        return {
            ConversationConfig:this.$store.state.systemConfig.ConversationConfig,
            subject:'',
            isPublicOptions:[
                {
                    id:0,
                    value:''
                },
                {
                    id:1,
                    value:''
                }
            ],
            isPublicOptionSelected:0,
            isNeedToApprove:0,
            isNeedToApproveOptions:[
                {
                    id:0,
                    value:''
                },
                {
                    id:1,
                    value:''
                }
            ],
            isNeedToApproveOptionSelected:0,
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.isPublicOptions[0].value = this.lang.private_group_text;
            this.isPublicOptions[1].value = this.lang.public_group_text;
            this.isPublicOptionSelected = 0;

            this.isNeedToApproveOptions[0].value = this.lang.cancel_button_text;
            this.isNeedToApproveOptions[1].value = this.lang.confirm_button_text;
            this.isNeedToApproveOptionSelected = 0;
        })
    },
    deactivated(){
        this.subject = ''
    },
    methods:{
        changeIsPublicType(item){
            this.isPublicOptionSelected = parseInt(item.id);
        },
        changeIsNeedToApprove(item){
            this.isNeedToApproveOptionSelected = parseInt(item.id);
        },
        wechatCreateGroup(){
            if (this.subject=='') {
                Toast(this.lang.subject_empty_tip)
                return
            }

            var that = this;
            let is_public = this.systemConfig.groupPublicState.Private;
            if (1 == this.isPublicOptionSelected) {
                if (0 == this.isNeedToApproveOptionSelected) {
                    is_public = this.systemConfig.groupPublicState.Public;
                } else {
                    is_public = this.systemConfig.groupPublicState.SemiPublic;
                }
            } else {
                is_public = this.systemConfig.groupPublicState.Private;
                Toast(this.lang.private_group_not_share)
                return;
            }

            var group_user_list= this.user.uid + ","
            var data={
                subject:this.subject,
                group_user_list:group_user_list,
                is_single_chat:0,
                type:this.ConversationConfig.type.Group,
                is_public : is_public,
                record_mode:that.systemConfig.serverInfo.enable_record_mode_for_default,
                voice_ctrl_mode:0
            }
            if (this.systemConfig.clientType == 5) {
                Toast(this.lang.use_app_tip)
                return
            }
            this.$root.socket.emit("request_create_conversation",data,async function(is_succ,data){
                Toast(that.lang.create_group_text)
                var group_id = data;
                let groupTemp={
                    id:data,
                    subject:that.subject,
                    is_single_chat:0
                }
                let chatTemp={
                    id:data,
                    cid:data,
                    subject:that.subject,
                    is_single_chat:0,
                    message:{}
                }
                that.setDefaultImg([groupTemp,chatTemp]);
                that.loading=true
                let currentGroupset=that.$store.state.chatList.currentGroupset
                if (currentGroupset) {
                    that.autoAddGroupToGroupset(data,()=>{
                        that.loading=false
                        that.openConversation(data,5);
                    })
                }else{
                    that.loading=false
                    that.$store.commit('chatList/addAndTopChat',chatTemp);
                    that.openConversation(data,5);
                }
                console.log(chatTemp)
                that.$store.commit('groupList/addGroup',groupTemp);
                var ids = [{
                    id: "weixin",
                    ex: "WXSceneSession"  /*微信好友*/
                }, {
                    id: "weixin",
                    ex: "WXSceneTimeline" /*微信朋友圈*/
                }
                ];
                const subject = encodeURIComponent(that.subject);
                console.error(subject,'subject')
                await Tool.handleAfterConversationCreated(group_id)
                window.main_screen.conversation_list[group_id].generateInviteCode({
                    autoMakeFriend:false,
                },(res)=>{
                    if(res.error_code === 0){
                        const inviteCode = res.data.inviteCode;
                        let ajaxServer=that.systemConfig.server_type.protocol+that.systemConfig.server_type.host+that.systemConfig.server_type.port;
                        let surl = that.systemConfig.server_type.protocol+that.systemConfig.server_type.host+that.systemConfig.server_type.port + '/';
                        let fullUrl = Tool.transferLocationToCe(surl + `activity/activity.html#/qr_install_app?act=add_group&inviteCode=${inviteCode}&from=weChat&subject=${subject}`);

                        // let fullUrl = `${ajaxServer}/activity/activity.html#/qr_install_app?act=add_group&inviteCode=${inviteCode}&from=weChat&subject=${subject}`// ps:之后再加参数，必须要在from之前加，因为APP那边在尾部会拼接其他参数
                        let thumb=`${ajaxServer}/mobile/static/resource/images/new_logo.png`
                        var shareContent = {
                            img_url: '1',
                            video_url: fullUrl,
                            title: that.user.nickname +' '+ that.lang.invite_you_join_group+that.subject,
                            content: that.lang.mindray_final_mission,
                            thumb: thumb
                        };
                        that.shareLinkToWeChat(shareContent);
                    }
                })

            })
        },
    }
}

</script>
<style lang="scss">
	.add_group_page{
		background-color:#fff;
        .container{
            .subject_bar{
                padding:.4rem .6rem;
                .subject{
                    width:100%;
                    box-sizing:border-box;
                    height: 2rem;
                    border: 1px solid #ccc;
                    padding: 0 .4rem;
                    font-size: .8rem;
                    border-radius:4px;
                    display:block;
                }
            }
            .wechat_group_share{
                display: block;
                width: 100%;
                border: none;
                font-size: 1rem;
                line-height: 2rem;
                margin: 5rem 0 .6rem;
                border-radius: .2rem;
            }
            .public_bar{
                padding:.4rem .6rem;
                font-size: .8rem !important;
                background-color:#fff !important;
                display: flex;
                .left{
                    flex:1;
                }
            }

        }
	}
</style>
