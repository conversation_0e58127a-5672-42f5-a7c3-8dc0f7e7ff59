<template>
    <div v-tapoutside="clickOutside">
        <div class="realtime_review_msg" @click="openGallery">
            <i
                class="iconfont icon-comment1"
                v-show="
                    gallery.commentObj[message.resource_id] &&
                    gallery.commentObj[message.resource_id].comment_list.length
                "
            >
                <span>{{
                    gallery.commentObj[message.resource_id] &&
                    gallery.commentObj[message.resource_id].comment_list.length
                }}</span>
            </i>
            <div class="message_image_content">
                <!-- <img class="file_image" src="static/resource/images/realtime_background.png" /> -->
                <img :src="limitImageSize(message.coverUrl,300)" alt="" srcset="" v-if="message.coverUrl" @error="reloadCover(message)"/>
                <img src="static/resource/images/realtime_background.png" v-else />
                <i class="iconfont svg_icon_play icon-videoplay"></i>
                <!-- <div class="tag">{{ lang.consulation_review_text }}</div> -->
                <div class="diff_time">{{ formatDurationTime(message.duration) }}</div>
            </div>

            <div class="review_time_wrap">
                <div class="review_item_read">
                    <div class="review_item_read_content">
                        {{ getRecordSubject(message) }}
                    </div>
                </div>
                <div class="review_item_read">
                    <div class="review_item_read_theme_text">{{ lang.start_time }}</div>
                    <div class="review_item_read_content">{{ formatTime(message.start_ts) }}</div>
                </div>
                <div class="review_item_read review_item_read_desc">
                    <div class="review_item_read_theme_text">{{ lang.moderator }}</div>
                    <div class="review_item_read_content">{{ message.live_record_data.speaker || message.live_record_data.creator_name}}</div>
                </div>
                <!-- <template v-if="formatTemplateType(message.live_record_data.type) === 'consultation'">
                    <div class="review_item_read review_item_read_column">
                        <div class="review_item_read_content review_item_read_patient">
                            <div
                                class="patient-info-label longwrap"
                                v-if="message.live_record_data.name"
                                style="flex: 1"
                            >
                                {{ lang.patient_name }}：{{ message.live_record_data.name }}
                            </div>
                            <div
                                class="patient-info-label longwrap"
                                v-if="message.live_record_data.age && Number(message.live_record_data.age)"
                                style="flex: 0.7"
                            >
                                {{ lang.patient_age }}：{{ message.live_record_data.age }}
                            </div>
                            <div
                                class="patient-info-label longwrap"
                                v-if="message.live_record_data.hasOwnProperty('gender')"
                                style="flex: 0.7"
                            >
                                {{ lang.patient_sex }}：{{
                                    message.live_record_data.gender | formatSexType(that)
                                }}
                            </div>
                        </div>
                        <div class="review_item_read_content review_item_read_desc">
                            <div class="patient-info-label" v-if="message.live_record_data.part">
                                {{ lang.body_parts }}：{{ message.live_record_data.part }}
                            </div>
                        </div>
                    </div>
                </template> -->
                <div class="review_item_read">
                    <div class="review_item_read_content review_item_read_desc">
                        {{ message.live_record_data.description }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import base from "../../lib/base";
import { formatDurationTime, getRecordSubject } from "../../lib/common_base";
import Tapoutside from "../../directive/tapoutside";
import moment from "moment";
import Tool from "@/common/tool"
export default {
    mixins: [base],
    name: "ReviewItemMsg",
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    directives: {
        Tapoutside,
    },
    filters: {
        formatSexType: (value, that) => {
            const arr = [that.lang.male, that.lang.female, that.lang.unknown];
            return arr[Number(value)];
        },
        formatReviewType: (value, that) => {
            const arr = ["", that.lang.universal_live, that.lang.consultation_live, that.lang.teaching_live];
            return arr[Number(value)];
        },
    },
    data() {
        return {
            formatDurationTime,
            getRecordSubject,
            that: this,
            reloadTimes: 0,
        };
    },

    beforeDestroy() {},
    methods: {
        openGallery() {
            this.$emit("openGallery");
        },
        clickOutside(e, target) {
            // console.log(target.target)
            // let isTooltips = target.target.className.indexOf('tooltips')>-1
            // let isOverlay = target.target.className.indexOf('van-overlay')>-1 || target.target.className.indexOf('van-action-sheet')>-1
        },
        formatTemplateType(value) {
            let arr = ["", "common", "consultation", "teaching"];
            return arr[Number(value)];
        },
        reloadCover(msg){
            const url = msg.coverUrl
            window.vm.$set(msg,'coverUrl','');
            if (this.reloadTimes > 7) {
                return;
            }else{
                this.reloadTimes++;
                setTimeout(()=>{
                    window.vm.$set(msg,'coverUrl',this.limitImageSize(url,300));
                },5000)
            }
        }
    },
};
</script>
<style lang="scss" scoped>
.realtime_review_msg {
    position: relative;
    padding: 0;
    width: 12.65rem;
    transform: translate3d(0, 0, 0);
    .message_image_content {
        position: relative;
        width: 100%;
        height: 8.65rem;
        border-radius: 0.75rem 0.75rem 0 0;
        position: relative;
        // background: url("/static/resource_pc/images/realtime_background.png") !important;
        background-repeat: no-repeat;
        background-size: contain !important;
        background-position: center;
        position: relative;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
            border-radius: 0.75rem 0.75rem 0 0;
            width: 100%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #000;
            height: 100%;
            object-fit: scale-down;
        }
        .svg_icon_play {
            color: #fff;
            position: absolute;
            font-size: 3.5em;
            // width: 2.55rem;
            // height: 2.55rem;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .tag {
            position: absolute;
            font-size: 0.55rem;
            left: 0.75rem;
            bottom: 0.55rem;
            width: 2.95rem;
            height: 1rem;
            line-height: 1rem;
            text-align: center;
            color: #fff;
            border-radius: 0.5rem;
            background-color: rgb(255, 177, 66);
        }
        .template_type {
            position: absolute;
            bottom: 0.55rem;
            color: #fff;
            font-size: rgba(255, 177, 66);
            color: rgba(255, 177, 66);
            right: 0.5rem;
            top: 0.5rem;
            font-size: 0.5rem;
            font-weight: 800;
        }
        .diff_time {
            position: absolute;
            bottom: 0.55rem;
            color: #fff;
            font-size: rgba(255, 177, 66);
            color: rgba(255, 177, 66);
            right: 0.5rem;
            bottom: 0.5rem;
            font-size: 0.5rem;
            font-weight: 800;
        }
    }
    .review_time_wrap {
        color: #000;
        padding: 0.5rem 0.75rem 0.55rem;
        font-size: 0.55rem;
        line-height: 1;
        background: #f0f5f9;
        border-radius: 0 0 0.75rem 0.75rem;
        .review_item_read {
            display: flex;
            margin-bottom: 0.5rem;
            .review_item_read_theme_text {
                color: #444547;
                margin-right: 0.5rem;
                flex-shrink: 0;
            }
            .review_item_read_content {
                color: #000;
                display: flex;
            }
            .review_item_read_desc {
                color: #7e7e7e;
                .patient-info-label {
                    line-height: 0.8rem;
                }
            }
            .review_item_read_patient {
                color: #7e7e7e;
                margin-bottom: 0.3rem;
            }
            &.review_item_read_column {
                flex-direction: column;
            }
        }
    }
    .icon-comment1 {
        position: absolute;
        top: 0.2rem;
        left: 0.3rem;
        z-index: 2;
        color: #f00;
        font-size: 1rem;
        line-height: 1rem;
        color: #56C7FD;
        span {
            color: #fff;
            position: absolute;
            font-size: 0.6rem;
            top: 0.1rem;
            left: 50%;
            transform: translate(-50%, 0);
            line-height: 0.6rem;
            white-space: nowrap;
        }
    }
}
</style>
