<template>
    <van-popup
        v-model="dialogVisible"
        closeable
        position="bottom"
        :style="{ height: '100%' }"
        get-container="body"
    >
    <div class="quick_lauch_live_page third_level_page">
        <mrHeader @click-left="dialogVisible = false">
            <template #title>
               {{lang.quick_lauch_live_title}}
            </template>
        </mrHeader>
        <div class="quick_lauch_live_container">
            <div class="search_container">
                <input type="text" :placeholder="lang.quick_lauch_live_search_placeholder" @input="search" v-model="searchText" />
            </div>
            <div :class="[checkOnlyList?'quick_only_box':'quick_multiple_box']">
                <div v-show="choosenChatList.length>0" class="quick_recent_item">
                    <p class="title">{{lang.recent_chat_text}}</p>
                    <div class="quick_lauch_live_list">
                        <div v-for="(chatItem,index) of choosenChatList" @click="handleSubmit(chatItem,1)" class="quick_lauch_live_item clearfix" :key="index">
                            <mr-avatar :url="getLocalAvatar(chatItem)" :origin_url="chatItem.avatar" :showOnlineState="false" :key="chatItem.avatar"></mr-avatar>
                            <p class="fl quick_lauch_live_subject">{{chatItem.subject}}</p>
                        </div>
                    </div>
                </div>
                <div v-show="choosenFriendList.length>0" class="quick_recent_item">
                        <p class="title">{{lang.contact_text}}</p>
                        <div class="quick_lauch_live_list">
                            <div v-for="(chatItem,index) of choosenFriendList" @click="handleSubmit(chatItem,2)" class="quick_lauch_live_item clearfix" :key="index">
                                <mr-avatar :url="getLocalAvatar(chatItem)" :origin_url="chatItem.avatar" :showOnlineState="false" :key="chatItem.avatar"></mr-avatar>
                                <p class="fl quick_lauch_live_subject">{{chatItem.nickname}}</p>
                            </div>
                        </div>
                </div>
                <div v-show="choosenGroupList.length>0" class="quick_recent_item">
                    <p class="title">{{lang.group}}</p>
                    <div class="quick_lauch_live_list">
                        <div v-for="(chatItem,index) of choosenGroupList" @click="handleSubmit(chatItem,3)" class="quick_lauch_live_item clearfix" :key="index">
                            <mr-avatar :url="getLocalAvatar(chatItem)" :origin_url="chatItem.avatar" :showOnlineState="false" :key="chatItem.avatar"></mr-avatar>
                            <p class="fl quick_lauch_live_subject">{{chatItem.subject}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <p v-if="choosenChatList.length==0&&choosenFriendList.length==0&&choosenGroupList.length==0" class="no_search_data">
                {{lang.no_search_data}}
            </p>
        </div>
    </div>
    <CommonDialog v-model="submitDialog"  :showConfirmButton="false" :title="lang.quick_lauch_live_title">
        <div class="checkbox-container">
            <div class="dialog-container">
                <div class="container-title longwrap">{{currentTarget.subject}}</div>
                <div class="submit-button">
                    <van-button type="primary" class="submit-button" block @click="confirmSubmit">{{lang.start_live_broadcast}}</van-button>
                </div>
            </div>

        </div>
    </CommonDialog>
    </van-popup>
</template>
<script>
import base from '../lib/base'
import {cloneDeep} from 'lodash'
import { Popup,Checkbox,Button } from 'vant';
import {getLocalAvatar} from "../lib/common_base"
import CommonDialog from '../MRComponents/commonDialog.vue'
import DialogManager from "../lib/dialogManager";
export default {
    name: 'quickLaunchLiveComponent',
    mixins: [base],
    props:{
        show: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        VanPopup:Popup,
        VanButton:Button,
        CommonDialog
    },
    data(){
        return {
            getLocalAvatar,
            choosenChatList:[],
            choosenFriendList:[],
            choosenGroupList:[],
            plainChatList: [],
            searchText:'',
            callback:'',
            isInit:false,
            record_mode:true,
            submitDialog:false,
            currentTarget:{},
            // DialogManager 相关的 ID
            mainDialogId: null, // 主弹窗的 DialogManager ID
            submitDialogId: null, // 确认提交弹窗的 DialogManager ID
        }
    },
    computed:{
        chatList(){
            return this.filterEnableList(this.$store.state.chatList.list)
        },
        friendList(){
            return this.filterEnableList(this.$store.state.friendList.list)
        },
        groupList(){
            return this.filterEnableList(this.$store.state.groupList)
        },
        checkOnlyList(){
            let chatNum = this.choosenChatList.length>0?1:0
            let friendNum = this.choosenFriendList.length>0?1:0
            let groupNum = this.choosenGroupList.length>0?1:0
            if(( chatNum+ friendNum + groupNum) === 1){
                return true
            }else{
                return false
            }
        },
        dialogVisible: {
            get() {
                if(!this.show){
                    this.record_mode = true
                }else{
                    this.choosenChatList = []
                    this.plainChatList = []
                    this.choosenChatList=this.filterGroupset(this.chatList);
                }
                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            },
        },
    },
    watch: {
        // 监听主弹窗显示状态
        dialogVisible(val) {
            if (val && !this.mainDialogId) {
                // 打开弹窗时，注册到 DialogManager
                this.mainDialogId = DialogManager.register(this, {
                    open: () => {
                        // 弹窗已经通过 v-model 显示，这里不需要额外操作
                    },
                    close: () => {
                        this.dialogVisible = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.mainDialogId) {
                // 关闭弹窗时，从 DialogManager 注销
                DialogManager.unregister(this.mainDialogId);
                this.mainDialogId = null;
            }
        },
        // 监听确认提交弹窗状态
        submitDialog(val) {
            if (val && !this.submitDialogId) {
                this.submitDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.submitDialog = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.submitDialogId) {
                DialogManager.unregister(this.submitDialogId);
                this.submitDialogId = null;
            }
        }
    },
    beforeDestroy(){
        // 清理所有 DialogManager 注册
        if (this.mainDialogId) {
            DialogManager.unregister(this.mainDialogId);
            this.mainDialogId = null;
        }
        if (this.submitDialogId) {
            DialogManager.unregister(this.submitDialogId);
            this.submitDialogId = null;
        }
    },
    mounted(){
    },
    methods:{
        search(){
            let keyword=this.searchText;
            if (keyword=='') {
                this.plainChatList = []
                this.choosenChatList=this.filterGroupset(this.chatList);
                this.choosenFriendList=[];
                this.choosenGroupList=[];
            }else{
                this.choosenChatList=[];
                this.choosenFriendList=[];
                this.choosenGroupList=[];
                for(let friend of this.friendList){
                    let friendItem=Object.assign({},friend);
                    if (friendItem.alias) {
                        friendItem.nickname=friendItem.alias;
                    }
                    if (friendItem.nickname.indexOf(keyword)>=0) {
                        this.choosenFriendList.push(friendItem)
                    }
                }
                for(let chatItem of this.groupList){
                    if (chatItem.subject.indexOf(keyword)>=0) {
                        this.choosenGroupList.push(chatItem)
                    }
                }
                console.log(this.choosenFriendList,this.choosenGroupList)
            }
        },
        confirmSubmit(){
            let record_mode = this.record_mode?1:0
            if(this.currentTarget.cid){
                this.openConversationToStartLive(this.currentTarget.cid,record_mode,1)
            }else if(this.currentTarget.from === 'friend'){
                this.openConversationToStartLive(this.currentTarget.uid,record_mode,3)
            }
            this.submitDialog = false
            this.dialogVisible = false
        },
        async beforeCloseAddDialog(action,done){
            let record_mode = this.record_mode?1:0
            if(action ==='confirm'){
                try {
                    if(this.currentTarget.cid){
                        this.openConversationToStartLive(this.currentTarget.cid,record_mode,1)
                    }else if(this.currentTarget.from === 'friend'){
                        this.openConversationToStartLive(this.currentTarget.uid,record_mode,3)
                    }
                    this.dialogVisible = false
                    done()
                } catch (error) {
                    done(false)
                }
            }else{
                this.dialogVisible = false
                done()
            }
        },
        handleSubmit(item,type){
            let that=this;
            let target={

            }
            if (type==1) {
                target.subject=item.subject
                target.cid=item.cid
                if(item.is_single_chat){
                    target.from = 'friend'
                    target.uid = item.fid
                }else{
                    target.from = 'group'
                }
            }else if(type==2){
                target.subject=item.nickname
                target.id=item.id
                target.from = 'friend'
                target.uid = item.id
            }else if(type==3){
                target.subject=item.subject
                target.cid=item.id
                target.from = 'group'
            }

            // let message=this.lang.whether_to_launch_live_broadcast+target.subject;
            let message =this.lang.group_setting_whether_live_record
            if (!item.cid&&item.nickname) {
                //从联系人点击的单聊，从chatlist中寻找cid
                for(let chatItem of this.chatList){
                    if (item.id==chatItem.fid) {
                        target.cid=chatItem.cid;
                        break;
                    }
                }
            }else if(!item.cid&&item.is_single_chat==0){
                target.cid=item.id;
            }else{
                target.cid=item.cid;
            }

            this.currentTarget = target
            this.submitDialog = true
        },
        openConversationToStartLive(id,record_mode,start_type){
            this.openConversation(id,start_type,(is_success,data)=>{
                if(!is_success){
                    return
                }
                let cid = 0
                if(typeof data === 'object'){
                    cid = data.id
                }else{
                    cid = data
                }

                setTimeout(async ()=>{
                    // await this.preHandleRecordMode(cid,record_mode)
                    this.$root.eventBus.$emit('chatWindowStartJoinRoom',{main:1,aux:1,isSender:1})
                },1000)

            });
        },
        isGroupMember(group, groupList) {
            // debugger
            if(group.type !== 2) { // 不是群，直接false
                return false
            }
            for(const item of groupList) {
                if(item.id == group.cid) {
                    return true
                }
            }
            return false
        },
        isFriend(person, friendList) {
            // debugger
            if(person.type !== 1) { // 不是单聊，直接false
                return false
            }
            for(const item of friendList) {
                if(item.id == person.fid) {
                    return true
                }
            }
            return false
        },
        filterGroupset(ochatList, filter) {
            let chatList = cloneDeep(ochatList)
            let filterObj = filter || {}
            for(const item of chatList) {
                // 如果是群落，深度遍历
                if(item.type === 3 && item.list.length > 0) {
                    for(const chat of item.list) {
                        if(chat.cid in filterObj){ // 去重
                            continue
                        }
                        if(chat.type === 3 && item.list.length > 0) { // 如果还是群落，则继续深度遍历
                            this.filterGroupset(chat.list, filterObj)
                        }else{
                            const isGroupMember = this.isGroupMember(chat, this.groupList)
                            const isFriend = this.isFriend(chat, this.friendList)
                            if(isGroupMember || isFriend) {
                                this.plainChatList.push(chat)
                                filterObj[chat.cid] = ""
                            }
                        }
                    }
                }else{
                    // 单聊或群聊，判断自己是否属于该群群成员
                    if(item.cid in filterObj){ // 去重
                        continue
                    }
                    const isGroupMember = this.isGroupMember(item, this.groupList)
                    const isFriend = this.isFriend(item, this.friendList)
                    if(isGroupMember || isFriend) {
                        this.plainChatList.push(item)
                        filterObj[item.cid] = ""
                    }

                }

            }
            return this.plainChatList
        },
        preHandleRecordMode(gid,record_mode){
            return new Promise((resolve,reject)=>{
                let data={
                    gid,
                    record_mode
                }
                let timer = setTimeout(()=>{
                    reject('preHandleRecordMode time out')
                },10000)
                this.conversationList[gid].socket.emit('edit_record_mode',data,(is_succ,data)=>{
                    if(is_succ){
                        //修改成功
                        this.$store.commit('conversationList/updateIsLiveRecord',{
                            cid:gid,
                            record_mode
                        });
                        resolve(true)
                    }else{//修改失败
                        reject(false)
                    }
                    clearTimeout(timer)
                    timer = null
                })

            })
        },
        backQuickLaunchPage(){
            if(this.submitDialog){
                this.submitDialog = false
                return
            }
            this.dialogVisible = false
        },
        filterEnableList(list){
            return list.filter(item=>(item.service_type === this.systemConfig.ServiceConfig.type.None)&&(item.user_status!==this.systemConfig.userStatus.Destroy))
        }

    }
}

</script>
<style lang="scss">
.quick_lauch_live_page{
    z-index:910;
    color:#333;
    .quick_lauch_live_container{
        background:#fff;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .title{
            padding:0.2rem;
            color:#000;
            .share_risk_content{
                font-size: 0.6rem;
                color: red;
            }
        }
        .search_container{
            padding:0.3rem;
            input{
                width: 100%;
                height: 100%;
                display: block;
                font-size: 0.9rem;
                padding: 0.3rem 0.8rem;
                box-sizing: border-box;
                border: none;
                background: #eee;
                border-radius: 0.5rem;
            }
        }
        .quick_only_box{
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .quick_recent_item{
                flex: 1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
                .quick_lauch_live_list{
                    overflow: auto;
                }
            }
        }
        .quick_multiple_box{
            flex: 1;
            overflow: auto;
        }
        .quick_lauch_live_list{
            border-top: 2px solid #00c59d;
            flex: 1;

            .quick_lauch_live_item{
                padding: 0.3rem;
                border-bottom: 1px solid #ccc;
                display:flex;
                &>p{
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    width:calc(100% - 3rem);
                    margin-left:0.4rem;
                    margin-top:0.3rem;
                    font-size:0.9rem;
                }
            }
        }
        .no_search_data{
            padding: 0.3rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
            width: 100%;
            text-align: center;
        }
    }

}
.checkbox-container{
    padding: 10px 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .dialog-container{
        width: 100%;
        .container-title{
            text-align: center;
            font-size: 24px;
            padding: 10px 0;
        }
        .container-body{
            padding: 10px 0;
            .van-checkbox__label{
                font-size: 14px;
            }
        }
    }

    .submit-button{
        padding: 10px 0;
    }
}
</style>
