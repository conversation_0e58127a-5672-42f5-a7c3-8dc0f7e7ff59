<template>
    <transition name="slide">
        <div class="public_favorites_page third_level_page">
            <mrHeader>
                <template #title>
                    {{lang.personal_favorite_text}}
                </template>
            </mrHeader>
            <div class="user_favorites_container">
                <div v-show="!loadedFavorites" class="full_loading_spinner van_loading_spinner">
                    <van-loading color="#00c59d" />
                </div>
                <div class="favorites_groups">
                    <van-list
                        ref="loadmore"
                        v-model="favoritesIsLoading"
                        @load="getFavorites"
                        :finished="bottomAllLoaded"
                        offset="0"
                    >
                        <div v-for="(group,g_index) of favoritesGroups" class="favorites_group_item clearfix" :key="group.favorite_id">
                            <div>
                                <p  class="group_subject">
                                    {{formatTime(group.send_ts)}}
                                </p>
                            </div>
                            <div class="clearfix">
                                <div v-for="(file,f_index) of group.list" @click="clickItem(g_index,f_index)" class="file_item" :key="file.favorite_id">
                                    <div  class="file_container">
                                        <common-image :fileItem="file"></common-image>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <template v-slot:loading>
                            <p>{{loadedFavorites?lang.bottom_loading_text:""}}</p>
                        </template>
                    </van-list>
                    <div class="no_data_txt" v-if="loadedFavorites&&bottomAllLoaded&&favoritesGroups.length>0">{{lang.no_more_data}}</div>
                    <div class="no_data_txt" v-if="favoritesGroups.length==0&&loadedFavorites">{{lang.no_data_txt}}</div>
                </div>
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import { Loading, List } from 'vant';
export default {
    mixins: [base],
    name: 'user_favorites',
    components: {
        VanLoading: Loading,
        VanList: List
    },
    data(){
        return {
            userId:this.$route.params.uid,
            userFavorites:[],
            loadedFavorites:false,
            sortedFavorites:[],
            favoritesGroups:[],
            favoritesIsLoading: false,
            bottomAllLoaded: false,
            page:0,
            pagesize:30,
            totalPage:0,
        }
    },
    beforeDestroy(){
    },
    beforeRouteEnter(to,from,next){
        if (!from.name) {
            //二级页面刷新时返回上级页面
            next(false)
            window.location.replace(`#/index`);
        }else{
            next()
        }
    },
    methods:{
        getFavorites(){
            this.page++;
            service.getUserFavorite({
                friendId:this.userId,
                page:this.page,
                pagesize:this.pagesize,
            }).then((res)=>{
                this.favoritesIsLoading = false;
                if (res.data.error_code==0) {
                    this.loadedFavorites=true;
                    this.userFavorites=this.userFavorites.concat(res.data.data.list);
                    this.totalPage=res.data.data.totalPage;
                    this.parseFavoritesByCollect()
                }
                if(this.totalPage<=this.page){
                    this.bottomAllLoaded = true;
                }
            })
        },
        parseFavoritesByCollect(){
            let groups={}
            for(let item of this.userFavorites){
                let cid=item.conversation_list[0].group_id;
                item.group_id=cid;
                let subject=item.conversation_list[0].subject;
                let send_ts=item.send_ts;
                item.send_ts=send_ts;
                if (groups[send_ts]) {
                    groups[send_ts].list.push(item)
                }else{
                    groups[send_ts]={
                        cid:cid,
                        send_ts:send_ts,
                        list:[item],
                    }
                }
            }
            var tempArr=[]
            this.sortedFavorites=[];
            for(let key in groups){
                tempArr.push(groups[key])
                this.sortedFavorites=this.sortedFavorites.concat(groups[key].list)
            }
            this.favoritesGroups=tempArr;
        },
        clickItem(g_index,f_index){
            //打开画廊
            let list = this.sortedFavorites;
            let index=this.getIndex(g_index,f_index);
            this.$store.commit('gallery/setGallery',{
                list:list,
                index:index
            })
            this.$nextTick(()=>{
                this.$router.push(`${this.$route.fullPath}/gallery`);
            })
        },
        getIndex(g_index,f_index){
            let index=0;
            for (var i = 0; i < g_index; i++) {
                index+=this.favoritesGroups[i].list.length
            }
            index+=parseInt(f_index);
            return index;
        },
    }
}

</script>
<style lang="scss">
.public_favorites_page{
    .user_favorites_container{
        overflow:auto;
        position:relative;
        flex: 1;
        .van_loading_spinner{
            display: flex;
            justify-content: center;
            align-items: center;

            .van-loading__spinner{
                width: 2.8rem;
                height: 2.8rem;
            }
        }

        .favorites_groups{
            margin-bottom:1rem;
            padding: .5rem .3rem 0;
            .favorites_group_item{
                margin-bottom:0.5rem;
                background-color:#fff;
                .group_subject{
                    color: #333;
                    line-height: 2;
                    font-size: 0.8rem;
                    padding: 0 0.2rem;
                }
                .file_item{
                    float:left;
                    width:25%;
                    border:1px solid #fff;
                    box-sizing:border-box;
                    position:relative;
                    .file_container{
                        position:relative;
                        padding-top:100%;
                        height:0;
                        background-color:#333;
                        .file_image{
                            max-width:100%;
                            max-height:100%;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%,-50%);
                        }
                    }
                }
            }
        }
        .no_data_txt{
            text-align: center;
            font-size: .8rem;
            color: #666;
            margin-top: 1rem;
        }
    }
}
</style>
