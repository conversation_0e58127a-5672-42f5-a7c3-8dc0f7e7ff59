<template>
	<div class="login_or_register_page second_level_page">
        <mrHeader>
            <template #title>
                {{lang.login_title}}
            </template>
        </mrHeader>
        <!-- <div v-if="loading" class="full_loading_spinner van_loading_spinner">
            <van-loading color="#00c59d" />
        </div> -->

        <span style="display:none"></span> <!-- 这个不能去掉 某些IOS机器 不加会导致 按钮不显示 ，具体原因未知-->
        <div class="container">
        	<template v-if="loginType==1">
                <p class="register_tip">{{lang.login_or_register_mobile}}</p>
                <mobile-international :mobile.sync="mobile_number" :internationalCode.sync="international_code" ref="mobile_international"></mobile-international>
                <div class="sms_verification_father 00">
                    <input  type="tel" v-model="verifyCode" class="commont_input get_sms_code_input" maxlength="6" :placeholder="lang.sms_verification_code">
                    <van-button v-loading="requesting||isValidating" color="#00c59d" class="get_sms_verification_code_btn" v-bind:disabled="sms_disable" @click.stop="loginByTracelessValid('mobile')">{{sms_button_content}}
                    </van-button>
                </div>
            </template>
            <template v-else-if="loginType==2">
                <p class="register_tip">{{lang.login_or_register_email}}</p>
                <input type="text" v-model="email" class="commont_input" ref="referral_code" :placeholder="lang.register_email">
                <div class="sms_verification_father 01">
                    <input  type="tel" v-model="verifyCode" class="commont_input get_sms_code_input" maxlength="6" :placeholder="lang.email_verification_code">
                    <van-button v-loading="requesting||isValidating" color="#00c59d" class="get_sms_verification_code_btn" v-bind:disabled="email_disable" @click.stop="loginByTracelessValid('email')">{{email_button_content}}
                    </van-button>
                </div>
            </template>
            <template v-else-if="loginType==3">
                <input type="text" v-model="account" class="commont_input" :placeholder="lang.register_account">
                <input type="password" v-model="password" class="commont_input" name="" maxlength="16" :placeholder="lang.register_password" @keyup.enter="loginByTracelessValid('account')">
                <div class="clearfix link_wraper">
                    <span class="to_forget_password primary_color fr" @click="toForgetPassword">{{lang.to_forget_password}}?</span>
                </div>
            </template>
            <template v-else-if="loginType==4">
                <login-qr-code></login-qr-code>
            </template>
            <button v-if="loginType==3" class="primary_bg register_btn" @click.stop="loginByTracelessValid('account')" v-loading="loading||isValidating">{{lang.login_title}}</button>
            <button v-else-if="loginType==1||loginType==2" class="primary_bg register_btn" @click="loginOrRegister">{{lang.login_title}}</button>
        </div>
        <mr-popup
        :visible.sync="isShowForgetChoose"
        :closeOnClockModal="true">
            <template v-if="isInternalNetworkEnv">
                <p class="title" >{{lang.not_surpport_forget}}</p>
                <div class="btns">
                    <button class="" @click="isShowForgetChoose=false">{{lang.confirm_txt}}</button>
                </div>
            </template>
            <template v-else>
                <p class="title" >{{lang.reset_password_way}}</p>
                <div class="btns">
                    <button class="primary_bg" @click="gotoForgetPassword(1)">{{lang.verify_with_mobile}}</button>
                    <button class="primary_bg" @click="gotoForgetPassword(2)">{{lang.verify_with_email}}</button>
                    <button class="" @click="isShowForgetChoose=false">{{lang.cancel_btn}}</button>
                </div>
            </template>
        </mr-popup>
        <ValidateCaptcha name="loginOrRegister" ref='validateCaptcha'></ValidateCaptcha>
    </div>
</template>
<script>
import service from '../service/service'
import { Toast, Button } from 'vant';
import base from '../lib/base'
import mrPopup from '../MRComponents/mrPopup.vue'
import ValidateCaptcha from '../MRComponents/ValidateCaptcha.vue'
import {
    parseImageListToLocal,
    handleAfterLogin,
    jumpRoute,
} from '../lib/common_base'
import Tool from '@/common/tool.js'
import {
    accountRegExp1,
    nicknameRegExp,
    passwordStrengthRegExp,
    referralCodeRegExp,
} from '@/common/regExpMapping.js'
import mobileInternational from '../components/mobileInternational.vue'
import loginQrCode from '../components/loginQrCode.vue'
export default {
    mixins: [base],
    name: 'loginOrRegister',
    components: {
        mobileInternational,
        mrPopup,
        loginQrCode,
        // VanLoading: Loading,
        VanButton: Button,
        ValidateCaptcha
    },
    data(){
        return {
            account:'',
            password:'',
            loginType:1,//1手机登录，2邮箱登录，3，账号密码登录
            mobile_number:'',
            email:'',
            international_code:'',
            imageCode:'',
            imageSVG:'',
            imageToken:'',
            verifyCode:'',
            sms_button_content:'',
            email_button_content:'',
            counter:0,
            timer:null,
            sms_disable:false,
            email_disable:false,
            loading:false,
            submitType:1,//1登录，2注册
            isShowForgetChoose:false,
            handlerAccountType:'',
            requesting:false,
            isValidating:false
        }
    },
    computed:{
        isInternalNetworkEnv(){
            return this.systemConfig.serverInfo.network_environment === 1
        },
        deviceInfo(){
            return this.$store.state.device
        },
    },
    created(){
        this.loginType=parseInt(this.$route.params.type);
        this.sms_button_content = this.lang.forget_password_getcode;
        this.email_button_content = this.lang.forget_password_get_email_code;
    },
    mounted(){

    },
    watch:{
    },
    methods:{
        display_count_down(){
            if (!this.timer){
                this.sms_disable = true;
                this.email_disable = true;
                this.counter = 89;
                this.sms_button_content = this.counter + 's';
                this.email_button_content = this.counter + 's';
                this.timer = setInterval(()=>{
                    this.counter--;
                    if (this.counter == 0){
                        this.counter = 0;
                        this.sms_button_content = this.lang.forget_password_getcode;
                        this.email_button_content = this.lang.forget_password_get_email_code;
                        clearInterval(this.timer);
                        this.timer = null;
                        this.sms_disable = false;
                        this.email_disable = false;
                    }else{
                        this.sms_button_content = this.counter + 's';
                        this.email_button_content = this.counter + 's';
                    }
                }, 1000);
            }
        },
        async getVerifyCode(accountType, afsCode){
            this.requesting = true
            let verifyType='getVerityCodeToMobile';
            let checkParams={};
            let params={};
            if (accountType==='mobile') {
                verifyType='getVerityCodeToMobile';
                checkParams={
                    account:this.mobile_number,
                    accountType:'mobile',
                    countryCode:this.international_code,
                }
                params={
                    mobile:this.mobile_number,
                    // imgToken:this.imageToken,
                    // imgCode:this.imageCode,
                    afsCode:afsCode,
                    countryCode:this.international_code,
                }
            }else{
                verifyType='getVerityCodeToEmail';
                checkParams={
                    account:this.email,
                    accountType:'email'
                }
                params={
                    email:this.email,
                    // imgToken:this.imageToken,
                    // imgCode:this.imageCode,
                    afsCode:afsCode
                }
            }
            // if (this.imageCode==='') {
            //     Toast(this.lang.image_code_empty)
            //     return ;
            // }
            try {
                const checkResult=await service.checkAccount(checkParams);
                if (checkResult.data.error_code===0) {
                    if (checkResult.data.data.existStatus) {
                        this.submitType=1;
                        params.type='login'
                    }else{
                        this.submitType=2;
                        params.type='register'
                    }
                }
                service[verifyType](params).then((res)=>{
                    this.requesting = false;
                    if (res.data.error_code===0) {
                        this.display_count_down();
                        this.isSecondaryValiding=false;
                    }else{
                    // const msg=this.lang[res.data.key]||this.lang.unknown_error
                    // Toast(msg)
                    }
                })
            } catch (error) {

            } finally{
                this.requesting = false;
            }

        },
        loginOrRegister(afsCode){
            if (this.submitType===1) {
                //登录
                if (this.loginType===1||this.loginType===2) {
                    this.loginByCode()
                } else if (this.loginType===3) {
                    this.loginByAccount(afsCode);
                }
            } else if (this.submitType===2) {
                //注册
                if (this.loginType===1||this.loginType===2) {
                    this.register()
                }
            }
        },
        loginByAccount(afsCode){
            if(this.account==''){
                Toast(this.lang.login_account_empty);
                return;
            }else if(this.password==''){
                Toast(this.lang.login_password_empty);
                return;
            }
            this.loading=true
            service.encryptPassword({
                pwd:this.password
            }).then((res)=>{
                if (res.data.error_code===0) {
                    const tokenParams={
                        loginName:this.account,
                        password:res.data.data.encryptStr,
                        language:window.localStorage.getItem('lang')||'CN',
                        clientType:this.systemConfig.clientType,
                        deviceId:this.deviceInfo.device_id,
                        afsCode:afsCode
                    }
                    service.getLoginToken(tokenParams).then((res)=>{
                        if (res.data.error_code===0) {
                            const result=res.data.data;
                            // result.need_code_login=true;
                            // result.cellphone='';
                            // result.email='';
                            this.twoFactorLogin(result);
                        }else{
                            // Toast(this.lang[res.data.key]||this.lang.unknown_error)
                        }
                        setTimeout(()=>{
                            this.loading=false
                        },1000)

                    }).catch((e)=>{
                        Toast(e);
                        this.loading=false
                    })
                }else{
                    // Toast(this.lang[res.data.key]||this.lang.unknown_error)
                    this.loading=false
                }
            }).catch((e)=>{
                Toast(e);
                this.loading=false
            })
        },
        twoFactorLogin(result){
            const needBind=result.cellphone==''&&result.email==''&&!this.isInternalNetworkEnv;
            if (result.userOutOfTrail) {
                //账号超过试用期，先去输推荐码
                Toast(this.lang.userOutOfTrail);
                this.$root.eventBus.$emit('setTempLoginTokenObj',result);
                setTimeout(()=>{
                    this.$router.replace(`/login/referral_code?token=${result.token}&isShowBack=1&emitFun=loginAfterReferral`)
                },200)
            } else if (result.need_code_login||needBind) {
                const token=encodeURIComponent(result.token)
                const cellphone=encodeURIComponent(result.cellphone)
                const email=encodeURIComponent(result.email)
                const target=`/login/login_verify?token=${token}&cellphone=${cellphone}&email=${email}`
                this.$router.replace(target)
            } else if (result.need_pwd_login) {
                Toast(this.lang.login_need_password);
                this.loginType=3;
            } else{
                this.loginWithToken(result.token);
            }
        },
        loginWithToken(loginToken){
            service.loginByToken({
                token:loginToken,
                deviceInfo:{
                    device_id:this.deviceInfo.device_id,
                    client_type:this.systemConfig.clientType
                }
            }).then((res)=>{
                this.beingLogin=false
                if (res.data.error_code===0) {

                    const user=res.data.data;
                    user.fromLogin=true
                    //获取localUrl需要
                    this.$store.commit('user/updateUser',{
                        uid:user.uid
                    });
                    parseImageListToLocal([user],'avatar')
                    this.setDefaultImg([user])
                    handleAfterLogin(user);
                    jumpRoute(1,'index');
                    // EnforceSequentialExecution(()=>{
                    //     this.back();
                    // },()=>{
                    //     this.$router.replace(`/index`)
                    // });
                    // setTimeout(()=>{

                    // },500)

                }else{
                    // const msg=this.lang[res.data.key]||this.lang.unknown_error
                    // Toast(msg)
                }
            })
        },

        toForgetPassword(){
            this.isShowForgetChoose=true;
        },
        gotoForgetPassword(type){
            this.isShowForgetChoose=false;
            this.$router.push(`/login/forget_password/${type}`)
        },
        register(){
            let registerParams={}
            if (this.loginType==1) {
                if (!this.$refs.mobile_international.validate()) {
                    return ;
                }
                registerParams.accountType='mobile';
                registerParams.countryCode=this.international_code;
                registerParams.account=this.mobile_number;
                registerParams.clientType=this.systemConfig.clientType;
                if (!this.isInternalNetworkEnv) {
                    if (this.verifyCode.length===0){
                        const tip='login_sms_verification_code_empty';
                        Tool.openMobileDialog(
                            {
                                message: this.lang[tip],

                            }
                        )
                        return ;
                    }
                    registerParams.code=this.verifyCode;
                }
            } else if (this.loginType==2) {
                registerParams.accountType='email';
                registerParams.account=this.email;
                registerParams.clientType=this.systemConfig.clientType;
                if (!this.isInternalNetworkEnv) {
                    if (this.verifyCode.length===0){
                        const tip='email_verification_code_empty';
                        Tool.openMobileDialog(
                            {
                                message: this.lang[tip],

                            }
                        )
                        return ;
                    }
                    registerParams.code=this.verifyCode;
                }
            }
            this.registerV2(registerParams);
        },
        registerV2(params){
            this.loading = true;
            service.registerV2(params).then((res)=> {
                this.loading = false;
                if (res.data.error_code===0) {
                    this.loading = true;
                    if (this.loginType==1||this.loginType==2) {
                        this.$store.commit('user/updateUser',{
                            fromRegister:true
                        })
                        if (this.functionsStatus.referralCode) {
                            this.back();
                            setTimeout(()=>{
                                this.$router.push(`/login/referral_code?token=${res.data.data.token}&isShowLogin=1&isAutoLogin=1`)
                            },200)
                        }else{
                            // 配置了关闭推荐码功能
                            this.loginWithToken(res.data.data.token)
                        }
                        return ;
                    }
                }else{
                }
            })
        },
        loginByCode(){
            let params={
                code:this.verifyCode,
                language:window.localStorage.getItem('lang')||'CN',
                clientType:this.systemConfig.clientType,
                deviceId:this.deviceInfo.device_id
            }
            if (this.loginType===1) {
                if (!this.$refs.mobile_international.validate()) {
                    return ;
                }
                if (this.verifyCode.length===0){
                    const tip='login_sms_verification_code_empty';
                    Tool.openMobileDialog(
                        {
                            message: this.lang[tip],

                        }
                    )
                    return ;
                }
                params.account=this.mobile_number;
                params.countryCode=this.international_code;
                params.accountType='mobile';
            } else if (this.loginType===2){
                if (!Tool.isEmail(this.email)) {
                    Toast(this.lang.email_is_invalid_input_again);
                    return;
                }
                if (this.verifyCode.length===0){
                    const tip='email_verification_code_empty';
                    Tool.openMobileDialog(
                        {
                            message: this.lang[tip],
                        }
                    )

                    return ;
                }
                params.account=this.email;
                params.accountType='email';
            }
            this.loading=true;
            service.getLoginTokenByCode(params).then((res)=>{
                console.log(res,'service.getLoginTokenByCode')
                if (res.data.error_code===0) {
                    const result=res.data.data;
                    // result.need_code_login=true;
                    // result.cellphone='';
                    // result.email='';
                    this.twoFactorLogin(result);
                }else{
                    // Toast(this.lang[res.data.key]||this.lang.unknown_error)
                }
                this.loading=false
            }).catch((e)=>{
                Toast(e);
                this.loading=false
            })
        },
        loginByTracelessValid:Tool.debounce(async function (login_type) {
            if(this.isInternalNetworkEnv){
                this.successVerifyByTraceless()
            }else{
                if(login_type == 'account'){
                    if(this.account==''){
                        Toast(this.lang.login_account_empty);
                        return;
                    }else if(this.password==''){
                        Toast(this.lang.login_password_empty);
                        return;
                    }
                }
                if(login_type == 'mobile'){
                    if (!this.$refs.mobile_international.validate()){
                        return;
                    }
                }
                if(login_type == 'email'){
                    if (!Tool.isEmail(this.email)) {
                        Toast(this.lang.email_is_invalid_input_again);
                        return;
                    }
                }

                try {
                    this.isValidating = true
                    const afsCode = await this.$refs['validateCaptcha'].validateCaptcha()
                    this.successVerifyByTraceless(afsCode)
                    this.isValidating = false
                } catch (error) {
                    this.isValidating = false
                    Toast(this.lang.network_error_tip);
                }
            }
        },500,true),
        successVerifyByTraceless(afsCode){
            if(this.loginType === 1){
                this.getVerifyCode('mobile',afsCode)
            }
            if(this.loginType === 2){
                this.getVerifyCode('email',afsCode)
            }
            if(this.loginType === 3){
                this.loginOrRegister(afsCode)
            }
        },
    }
}
</script>
<style lang="scss">
.login_or_register_page{
    background:#fff;
    height:100%;
    min-height:26rem;
    header{
        position: absolute;
        width: 100%;
    }
    .full_loading_spinner{
        position:absolute;
        width:100%;
        top:0;
        z-index:9;
    }
    .van_loading_spinner{
        display: flex;
        justify-content: center;
        align-items: center;

        .van-loading__spinner{
            width: 2.8rem;
            height: 2.8rem;
        }
    }
    .container{
        width: 100%;
        box-sizing: border-box;
        padding: 1rem;;
        max-height:100%;
        .register_tip{
            margin:0.5rem 0;
            font-size: 0.7rem;
            color: #666;
        }
        .to_forget_password{
            font-size:.7rem;
        }
        .sms_verification_father{
            position:relative;
            .svg_wrap{
                position:absolute;
                right:0;
                top:0;
                & > i {
                    width:4rem;
                    height:2rem;
                }
            }
        }
        .get_sms_verification_code_btn{
            position:absolute;
            right:0rem;
            bottom:0.3rem;
            display: block;
            min-width: 50%;
            border: none;
            font-size: 0.8rem;
            line-height: 1.5rem;
            height:2rem;
            margin: .5rem 0 .6rem;
            border-radius: .2rem;
            margin:auto;
            transform: translate3d(0px, 0px, 0px);
        }
        #register_male_and_female_father{
            position:relative;
            border-bottom:2px solid #c8c7cc;
        }
        .register_male_and_female{
            position:absolute;
            right:0rem;
            bottom:0.3rem;
            display: block;
            width: 80%;
            border: none;
            font-size: 0.8rem;
            line-height: 2rem;
            margin: 1rem 0 .6rem;
            border-radius: .2rem;
            margin:auto;
        }
        .toggle_validate{
            color:#00c59d;
            font-size:0.8rem
        }
        .register_btn{
            display: block;
            width: 100%;
            border: none;
            font-size: 1rem;
            line-height: 2rem;
            margin: 1rem 0 .6rem;
            border-radius: .2rem;
            transform: translate3d(0px, 0px, 0px);
        }
        .to_register,.to_forget_password,.to_login{
            font-size:.8rem;
        }
        .please-agree{
            font-size:.8rem;
        }
    }
}
#sc{
    margin: 0 !important;
    padding: 0 !important;
    #SM_BTN_1{
        width: 100% !important;
    }
    #sm-btn-bg{
        width: 100% !important;

    }
    #rectMask{
        width: 100% !important;
    }
    #SM_POP_1{
        width: 100% !important;
        left: 0!important;
    }
}


</style>
