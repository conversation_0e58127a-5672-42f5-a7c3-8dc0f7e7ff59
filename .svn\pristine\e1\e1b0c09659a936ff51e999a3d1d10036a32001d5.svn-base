<template>
    <div class="grading-review-container">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <el-form :inline="true" :model="filterForm" class="filter-form">
                <el-form-item :label="lang.assessment_name">
                    <el-select v-model="filterForm.testID" :placeholder="lang.input_select_tips" clearable>
                        <el-option
                            v-for="item in testListOptions"
                            :key="item.testID"
                            :label="item.title"
                            :value="item.testID"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.full_name">
                    <el-input v-model.trim="filterForm.studentName" :placeholder="lang.input_enter_tips" clearable></el-input>
                </el-form-item>
                <el-form-item :label="lang.place_of_work">
                    <el-input v-model.trim="filterForm.hospital" :placeholder="lang.input_enter_tips" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearch" icon="el-icon-search">{{ lang.query_btn }}</el-button>
                    <el-button @click="handleReset" icon="el-icon-refresh">{{ lang.register_reset_btn }}</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- Tab切换区域 -->
        <div class="tabs-section">
            <el-tabs
                v-model="activeTab"
                class="smart-tech-tabs smart-tech-tabs--small"
                @tab-click="handleTabClick"
                style="height: 100%; display: flex; flex-direction: column"
            >
                <el-tab-pane :label="`${lang.pending_correction}(${tabCounts.unFinished})`" name="unFinished">
                    <div class="table-wrapper" v-if="activeTab === 'unFinished'">
                        <el-table
                            v-loading="unFinishedAnswerInfo.loading"
                            :data="unFinishedAnswerInfo.data"
                            style="width: 100%"
                            border
                            stripe
                        >
                            <el-table-column
                                prop="testTitle"
                                :label="lang.assessment_name"
                                min-width="180"
                                show-overflow-tooltip
                            ></el-table-column>
                            <el-table-column :label="lang.full_name" min-width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.studentInfo ? scope.row.studentInfo.name : scope.row.studentName }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                :label="lang.place_of_work"
                                min-width="180"
                                show-overflow-tooltip
                            >
                                <template slot-scope="scope">
                                    {{ scope.row.studentInfo ? scope.row.studentInfo.hospital : scope.row.hospital }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="trainingType" :label="lang.assessment_type" min-width="170" align="center">
                                <template slot-scope="scope">
                                    <el-tag
                                        :type="getTrainingTypeTagType(scope.row.trainingType)"
                                        size="small"
                                    >
                                        {{ getTrainingTypeText(scope.row.trainingType) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createdAt" :label="lang.submission_time" min-width="160">
                                <template slot-scope="scope">
                                    {{ formatTime(scope.row.createdAt) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="useTime" :label="lang.test_duration" min-width="100">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.trainingType === SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD">-</span>
                                    <span v-else>{{ formatDurationFromSeconds(scope.row.useTime) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="lang.operation" min-width="120" align="center">
                                <template slot-scope="scope">
                                    <div class="operation-buttons-container">
                                        <el-button type="text" @click="goToGrading(scope.row)" class="option-btn"
                                            >{{lang.enter_correction}}</el-button
                                        >
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination-container">
                            <el-pagination
                                @size-change="(val) => handleSizeChange('unFinished', val)"
                                @current-change="(val) => handleCurrentChange('unFinished', val)"
                                :current-page="unFinishedAnswerInfo.page"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="unFinishedAnswerInfo.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="unFinishedAnswerInfo.total"
                            >
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="`${lang.corrected} (${tabCounts.finished})`" name="finished">
                    <div class="table-wrapper" v-if="activeTab === 'finished'">
                        <el-table
                            v-loading="finishedAnswerInfo.loading"
                            :data="finishedAnswerInfo.data"
                            style="width: 100%"
                            border
                            stripe
                        >
                            <el-table-column
                                prop="testTitle"
                                :label="lang.assessment_name"
                                min-width="180"
                                show-overflow-tooltip
                            ></el-table-column>
                            <el-table-column :label="lang.student_name" min-width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.studentInfo ? scope.row.studentInfo.name : scope.row.studentName }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                :label="lang.affiliated_hospital"
                                min-width="180"
                                show-overflow-tooltip
                            >
                                <template slot-scope="scope">
                                    {{ scope.row.studentInfo ? scope.row.studentInfo.hospital : scope.row.hospital }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="trainingType" :label="lang.assessment_type" min-width="120" align="center">
                                <template slot-scope="scope">
                                    <el-tag
                                        :type="getTrainingTypeTagType(scope.row.trainingType)"
                                        size="small"
                                    >
                                        {{ getTrainingTypeText(scope.row.trainingType) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column :label="lang.correction_time" min-width="160">
                                <template slot-scope="scope">
                                    {{ formatTime(scope.row.scoreTime * 1000) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="isPass" :label="lang.exam_result" min-width="100">
                                <template slot-scope="scope">
                                    <span :class="getResultClass(scope.row)">{{ getGradingResult(scope.row) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="lang.operation" min-width="100" align="center">
                                <template slot-scope="scope">
                                    <div class="operation-buttons-container">
                                        <el-button type="text" @click="viewGradedDetail(scope.row)" class="option-btn"
                                            >{{lang.view_details}}</el-button
                                        >
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="pagination-container">
                            <el-pagination
                                @size-change="(val) => handleSizeChange('finished', val)"
                                @current-change="(val) => handleCurrentChange('finished', val)"
                                :current-page="finishedAnswerInfo.page"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="finishedAnswerInfo.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="finishedAnswerInfo.total"
                            >
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane :label="`${lang.not_submitted} (${tabCounts.unSubmitted})`" name="unSubmitted">
                    <div class="table-wrapper" v-if="activeTab === 'unSubmitted'">
                        <el-table
                            v-loading="unSubmittedAnswerInfo.loading"
                            :data="unSubmittedAnswerInfo.data"
                            style="width: 100%"
                            border
                            stripe
                        >
                            <el-table-column
                                prop="testTitle"
                                :label="lang.assessment_name"
                                min-width="180"
                                show-overflow-tooltip
                            ></el-table-column>
                            <el-table-column :label="lang.student_name" min-width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.studentInfo ? scope.row.studentInfo.name : scope.row.studentName }}
                                </template>
                            </el-table-column>
                            <el-table-column
                                :label="lang.affiliated_hospital"
                                min-width="180"
                                show-overflow-tooltip
                            >
                                <template slot-scope="scope">
                                    {{ scope.row.studentInfo ? scope.row.studentInfo.hospital : scope.row.hospital }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="trainingType" :label="lang.assessment_type" min-width="120" align="center">
                                <template slot-scope="scope">
                                    <el-tag
                                        :type="getTrainingTypeTagType(scope.row.trainingType)"
                                        size="small"
                                    >
                                        {{ getTrainingTypeText(scope.row.trainingType) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="deadline" :label="capitalizeFirstLetter(lang.deadline)" min-width="160">
                                <template slot-scope="scope" v-if="scope.row.deadline">
                                    {{ formatTime(scope.row.deadline) }}
                                </template>
                            </el-table-column>
                            <!-- 无操作列 -->
                        </el-table>
                        <div class="pagination-container">
                            <el-pagination
                                @size-change="(val) => handleSizeChange('unSubmitted', val)"
                                @current-change="(val) => handleCurrentChange('unSubmitted', val)"
                                :current-page="unSubmittedAnswerInfo.page"
                                :page-sizes="[10, 20, 50, 100]"
                                :page-size="unSubmittedAnswerInfo.pageSize"
                                layout="total, sizes, prev, pager, next, jumper"
                                :total="unSubmittedAnswerInfo.total"
                            >
                            </el-pagination>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import service from "../../../service/service";
import base from "../../../lib/base";
import Tool from "@/common/tool.js";
import { SMART_TECH_TRAINING_TEST_TYPE } from "../../../lib/constants";
import { cloneDeep } from "lodash";

export default {
    mixins: [base],
    name: "GradingReview",
    components: {},
    data() {
        return {
            capitalizeFirstLetter: Tool.capitalizeFirstLetter,
            formatDurationFromSeconds: Tool.formatDurationFromSeconds,
            SMART_TECH_TRAINING_TEST_TYPE,
            filterForm: {
                testID: "",
                studentName: "",
                hospital: "",
            },
            testListOptions: [],
            activeTab: "unFinished",
            tabCounts: { unFinished: 0, finished: 0, unSubmitted: 0 }, // 从原型图获取初始数

            unFinishedAnswerInfo: {
                total: 0,
                data: [],
                page: 1,
                pageSize: 10,
                loading: false,
            },
            finishedAnswerInfo: {
                total: 0,
                data: [],
                page: 1,
                pageSize: 10,
                loading: false,
            },
            unSubmittedAnswerInfo: {
                total: 0,
                data: [],
                page: 1,
                pageSize: 10,
                loading: false,
            },
            trainingID: "",
        };
    },
    computed: {},
    watch: {
        $route: {
            handler(to, from) {
                // 当从批改详情页面返回到批改审核列表页面时，刷新数据
                console.log(to)
                if (to.name === "SmartTechTrainingGradingReview") {
                    this.$nextTick(() => {
                        this.getSupervisorUnfinishedAnswerList();
                        this.getSupervisorFinishedAnswerList();
                        this.getSupervisorUnSubmittedAnswerList();
                        this.getTrainingInfo();
                    });
                }
            },
            immediate: true,
        },
    },
    created() {
        this.trainingID = this.$route.params.trainingId;
    },
    mounted() {},
    methods: {
        handleSearch() {
            if (this.activeTab === "unFinished") {
                this.unFinishedAnswerInfo.page = 1;
                this.getSupervisorUnfinishedAnswerList();
            } else if (this.activeTab === "finished") {
                this.finishedAnswerInfo.page = 1;
                this.getSupervisorFinishedAnswerList();
            } else if (this.activeTab === "unSubmitted") {
                this.unSubmittedAnswerInfo.page = 1;
                this.getSupervisorUnSubmittedAnswerList();
            }
        },
        // 新增方法：不重置页码的数据加载
        loadCurrentTabData() {
            if (this.activeTab === "unFinished") {
                this.getSupervisorUnfinishedAnswerList();
            } else if (this.activeTab === "finished") {
                this.getSupervisorFinishedAnswerList();
            } else if (this.activeTab === "unSubmitted") {
                this.getSupervisorUnSubmittedAnswerList();
            }
        },
        handleReset() {
            this.filterForm = {
                testID: "",
                studentName: "",
                hospital: "",
            };
            // activeTab 不重置，或按需求选择是否重置到pending
            // this.activeTab = 'pending';
            this.handleSearch();
        },
        handleTabClick(tab) {
            this.activeTab = tab.name;
            this.handleSearch();
        },
        handleSizeChange(tabKey, newSize) {
            if (tabKey === "unFinished") {
                this.unFinishedAnswerInfo.pageSize = newSize;
                this.unFinishedAnswerInfo.page = 1; // 改变页面大小时重置到第一页
            } else if (tabKey === "finished") {
                this.finishedAnswerInfo.pageSize = newSize;
                this.finishedAnswerInfo.page = 1; // 改变页面大小时重置到第一页
            } else if (tabKey === "unSubmitted") {
                this.unSubmittedAnswerInfo.pageSize = newSize;
                this.unSubmittedAnswerInfo.page = 1; // 改变页面大小时重置到第一页
            }
            this.loadCurrentTabData();
        },
        handleCurrentChange(tabKey, newPage) {
            if (tabKey === "unFinished") {
                this.unFinishedAnswerInfo.page = newPage;
            } else if (tabKey === "finished") {
                this.finishedAnswerInfo.page = newPage;
            } else if (tabKey === "unSubmitted") {
                this.unSubmittedAnswerInfo.page = newPage;
            }
            this.loadCurrentTabData();
        },
        goToGrading(row) {
            console.log("进入批改:", row);
            // 根据考核类型跳转到不同的批改页面
            if (row.trainingType === this.SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD) {
                // 附件上传类型 - 跳转到附件批改页面
                Tool.loadModuleRouter({
                    name: 'SmartTechTrainingGradingReview_CorrectingUploadTestOverview',
                    params: {
                        ...this.$route.params,
                        testId: row.testID,
                        studentInfo: row.studentInfo
                    }
                });
            } else if (row.trainingType === this.SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ) {
                // 在线答题类型 - 跳转到在线批改页面
                Tool.loadModuleRouter({
                    name: 'SmartTechTrainingGradingReview_CorrectingOnlineTestOverview',
                    params: {
                        ...this.$route.params,
                        testId: row.testID,
                        studentInfo: row.studentInfo
                    }
                });
            } else {
                console.error('unknown training type', row.trainingType);
            }
        },
        viewGradedDetail(row) {
            console.log("查看详情:", row);
            this.goToGrading(row);
        },
        getResultClass(row) {
            if (row.isPass === true) {
                return "status-tag-passed-new";
            }
            if (row.isPass === false) {
                return "status-tag-failed-new";
            }
            return "status-tag-default-new";
        },
        getGradingResult(item) {
            if (item.isPass === true) {
                return this.lang.passed;
            } else if (item.isPass === false) {
                return this.lang.not_pass;
            }
        },
        getSupervisorUnfinishedAnswerList() {
            return new Promise((resolve, reject) => {
                this.unFinishedAnswerInfo.loading = true;
                const params = {
                    trainingID: this.trainingID,
                    page: this.unFinishedAnswerInfo.page,
                    pageSize: this.unFinishedAnswerInfo.pageSize,
                    studentName: this.filterForm.studentName,
                    hospital: this.filterForm.hospital,
                    testID: this.filterForm.testID,
                };
                service
                    .getSupervisorUnfinishedAnswerList(params)
                    .then((res) => {
                        console.log(res);
                        if (res.data.error_code === 0) {
                            // 处理返回的数据
                            const responseData = res.data.data;
                            this.unFinishedAnswerInfo.total = responseData.total || 0;
                            this.unFinishedAnswerInfo.page = responseData.page || 1;
                            this.unFinishedAnswerInfo.pageSize = responseData.pageSize || 10;

                            // 转换数据格式
                            this.unFinishedAnswerInfo.data = responseData.data;

                            // 更新tab计数
                            this.tabCounts.unFinished = responseData.total || 0;

                            resolve(res);
                        } else {
                            reject(res);
                        }
                    })
                    .catch((error) => {
                        console.error('get unfinished list failed:', error);
                        reject(error);
                    })
                    .finally(() => {
                        this.unFinishedAnswerInfo.loading = false;
                    });
            });
        },
        getSupervisorFinishedAnswerList() {
            return new Promise((resolve, reject) => {
                this.finishedAnswerInfo.loading = true;
                const params = {
                    trainingID: this.trainingID,
                    page: this.finishedAnswerInfo.page,
                    pageSize: this.finishedAnswerInfo.pageSize,
                    studentName: this.filterForm.studentName,
                    hospital: this.filterForm.hospital,
                    testID: this.filterForm.testID,
                };
                service
                    .getSupervisorFinishedAnswerList(params)
                    .then((res) => {
                        console.log(res);
                        if (res.data.error_code === 0) {
                            // 处理返回的数据
                            const responseData = res.data.data;
                            this.finishedAnswerInfo.total = responseData.total || 0;
                            this.finishedAnswerInfo.page = responseData.page || 1;
                            this.finishedAnswerInfo.pageSize = responseData.pageSize || 10;

                            // 转换数据格式 - 已批改数据可能字段略有不同
                            this.finishedAnswerInfo.data = responseData.data;

                            // 更新tab计数
                            this.tabCounts.finished = responseData.total || 0;

                            resolve(res);
                        } else {
                            reject(res);
                        }
                    })
                    .catch((error) => {
                        console.error('get finished list failed:', error);
                        reject(error);
                    })
                    .finally(() => {
                        this.finishedAnswerInfo.loading = false;
                    });
            });
        },
        getSupervisorUnSubmittedAnswerList() {
            return new Promise((resolve, reject) => {
                this.unSubmittedAnswerInfo.loading = true;
                const params = {
                    trainingID: this.trainingID,
                    page: this.unSubmittedAnswerInfo.page,
                    pageSize: this.unSubmittedAnswerInfo.pageSize,
                    studentName: this.filterForm.studentName,
                    hospital: this.filterForm.hospital,
                    testID: this.filterForm.testID,
                };
                service
                    .getSupervisorUnSubmittedAnswerList(params)
                    .then((res) => {
                        console.log(res);
                        if (res.data.error_code === 0) {
                            // 处理返回的数据
                            const responseData = res.data.data;
                            this.unSubmittedAnswerInfo.total = responseData.total || 0;
                            this.unSubmittedAnswerInfo.page = responseData.page || 1;
                            this.unSubmittedAnswerInfo.pageSize = responseData.pageSize || 10;

                            // 转换数据格式
                            this.unSubmittedAnswerInfo.data = this.formatUnSubmittedData(responseData.data || []);

                            // 更新tab计数
                            this.tabCounts.unSubmitted = responseData.total || 0;

                            resolve(res);
                        } else {
                            reject(res);
                        }
                    })
                    .catch((error) => {
                        console.error('get unsubmitted list failed:', error);
                        reject(error);
                    })
                    .finally(() => {
                        this.unSubmittedAnswerInfo.loading = false;
                    });
            });
        },
        getTrainingInfo(){
            service.getTrainingInfo({
                trainingID: this.trainingID
            }).then(res=>{
                if(res.data.error_code === 0){
                    this.testListOptions = res.data.data.testList || [];
                }else{
                    console.error(res.data.message || 'get training info failed');
                }
            }).catch(err=>{
                console.error('get training info failed', err);
            })
        },
        // 格式化未提交数据
        formatUnSubmittedData(dataArray) {
            return dataArray.map(item => {
                return {
                    id: item.uid,
                    testID: item.testID,
                    testTitle: item.testTitle,
                    studentName: item.name,
                    hospital: item.hospital,
                    studentInfo: item.studentInfo, // 确保studentInfo被保留
                    trainingType: item.type,
                    deadline: item.deadline *1000,
                    // 保留原始数据以供操作使用
                    _original: item
                };
            });
        },
        // 获取考核类型文本
        getTrainingTypeText(trainingType) {
            switch (trainingType) {
            case this.SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD:
                return this.lang.ATTACHMENT_UPLOAD;
            case this.SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ:
                return this.lang.ONLINE_QUIZ;
            default:
                return '';
            }
        },
        // 获取考核类型标签颜色
        getTrainingTypeTagType(trainingType) {
            switch (trainingType) {
            case this.SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD:
                return 'warning'; // 橙色标签
            case this.SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ:
                return 'primary'; // 蓝色标签
            default:
                return 'info'; // 灰色标签
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.grading-review-container {
    padding: 20px;
    background-color: #f5f7fa;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .filter-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        flex-shrink: 0;
        .filter-form {
            .el-form-item {
                margin-bottom: 0;
                margin-right: 15px;
            }
            .el-select {
                width: 200px;
            }
            .el-input {
                width: 200px;
            }
        }
    }

    .tabs-section {
        background-color: #fff;
        padding: 0 20px; // 与表格区域的左右padding对齐
        border-radius: 8px; // 现在tabs-section是主要的内容容器，给它完整的圆角
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); // 调整阴影使其作为主要容器
        margin-bottom: 0; // 保持
        position: relative; // 保持
        // top: 1px; // 不再需要微调，因为它是主要容器
        flex: 1; // 让tabs-section占据剩余空间
        display: flex; // 用于内部el-tabs的高度管理
        flex-direction: column; // 用于内部el-tabs的高度管理
        overflow: hidden; // 防止内容溢出

        ::v-deep .el-tabs {
            height: 100%;
            display: flex;
            flex-direction: column;

            .el-tabs__content {
                flex: 1;
                overflow: hidden;
                margin-top: 20px;
            }

            .el-tab-pane {
                height: 100%;
                display: flex;
                flex-direction: column;
            }
        }

        .table-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            ::v-deep .el-table {
                flex: 1;
                display: flex;
                flex-direction: column;

                .el-table__header-wrapper {
                    flex-shrink: 0;
                    position: sticky;
                    top: 0;
                    z-index: 10;
                    background: #fff;
                }

                .el-table__body-wrapper {
                    flex: 1;
                    overflow-y: auto;
                    min-height: 0; // 确保flex子元素可以收缩
                }

                .el-table__fixed,
                .el-table__fixed-right {
                    z-index: 9; // 确保固定列在表头下方
                }
            }

            .pagination-container {
                flex-shrink: 0;
                padding: 20px 0;
                text-align: right;
                background: #fff;
                border-top: 1px solid #ebeef5;
            }
        }
    }

    // .content-section 已被移除，其样式也一并移除或合并
}
</style>
