<template>
    <transition name="slide">
        <div class="ai-main_page second_level_page">
            <!-- 聊天主界面 -->
            <mrHeader @click-left="handleBack">
                <!-- 自定义标题 -->
                <template #title> {{ lang.MICO_AI_TITLE }} </template>
            </mrHeader>
            <div class="ai-main_container">
                <!-- 内容区域 -->
                <div class="content">
                    <!-- 根据当前选中的tabbar项显示对应的组件 -->
                    <div class="centered-content">
                        <keep-alive :exclude="/./">
                            <router-view></router-view>
                        </keep-alive>
                    </div>
                </div>
                <!-- 底部导航栏 -->
                <van-tabbar v-model="activeName" @change="handleTabChange">
                    <van-tabbar-item name="ai_chat">
                        <svg-icon icon-class="aiChat" slot="icon"/>
                        <p>{{ lang.ai_qa }}</p>
                    </van-tabbar-item>
                    <van-tabbar-item name="practice_overview">
                        <svg-icon icon-class="aiPractice" slot="icon"/>
                        <p>{{ lang.ai_clinical_thinking_training }}</p>
                    </van-tabbar-item>
                    <!-- <van-tabbar-item name="coming_soon" v-if="functionsStatus.ultrasoundQCReport">
                        <svg-icon icon-class="aiReport" slot="icon"/>
                        <p>{{ lang.ultrasonic_quality_control_report }}</p>
                    </van-tabbar-item> -->
                </van-tabbar>
            </div>
        </div>
    </transition>
</template>

<script>
import base from "../../lib/base";
import Tool from "@/common/tool";
import { Button, Field, Checkbox, Icon, Popup, Loading, Tabbar, TabbarItem } from "vant";
import { Toast } from "vant";
export default {
    mixins: [base],
    name: "AiChatPage",
    components: {
        [Button.name]: Button,
        [Field.name]: Field,
        [Checkbox.name]: Checkbox,
        [Icon.name]: Icon,
        [Popup.name]: Popup,
        [Loading.name]: Loading,
        [Tabbar.name]: Tabbar,
        [TabbarItem.name]: TabbarItem,
    },
    computed: {},
    watch: {
        $route: {
            immediate: true,
            handler(to) {
                this.setActiveTabByRoute(to.path);
            },
        },
    },
    created() {
        this.setActiveTabByRoute(this.$route.path);
    },
    data() {
        return {
            activeName: "ai_chat",
        };
    },
    mounted() {},

    beforeDestroy() {},
    methods: {
        handleBack() {
            this.$router.replace("/index");
        },
        // 处理标签页切换
        handleTabChange(tabName) {
            Tool.loadModuleRouter(`/index/ai_main/${tabName}`,'replace');
        },
        setActiveTabByRoute(path) {
            if (path.includes("/ai_main/ai_chat")) {
                this.activeName = "ai_chat";
            } else if (path.includes("/ai_main/practice_overview")) {
                this.activeName = "practice_overview";
            } else if (path.includes("/ai_main/coming_soon")) {
                this.activeName = "coming_soon";
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.ai-main_page {
    .ai-main_container {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;

        .content {
            flex: 1;
            overflow: auto;
            padding-bottom: 3.4rem; // 为底部导航栏留出空间

            .centered-content {
                display: flex;
                flex-direction: column;
                height: 100%;
                width: 100%;
            }

            .coming-soon {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                text-align: center;
                color: #999;
                font-size: 1rem;
            }
        }

        .van-tabbar {
            height: 3.4rem;
            box-sizing: border-box;
            background-color: #fff;
            user-select: none;
            -webkit-user-select: none;
            box-shadow: 0px -0.1rem 0.2rem rgba(0, 0, 0, 0.2);
            z-index: 2;
            width: 100%;
            display: flex;
            bottom: 0;

            :deep(.van-tabbar-item) {
                flex: 1;
                text-align: center;
                font-size: 0.5rem;
                color: #a7bcba;
                position: relative;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                .van-tabbar-item__icon {
                    font-size: 1.6rem;
                    i {
                        display: inline-block;
                        font-size: 1.4rem;
                        fill: #a7bcba;
                        width: 1.4rem;
                        height: 1.4rem;
                        margin-bottom: 0.1rem;
                    }
                }

                &.van-tabbar-item--active {
                    .van-tabbar-item__icon{
                        fill: #00c59d;
                        color: #00c59d;
                    }
                    .van-tabbar-item__text{
                        color: #00c59d;
                    }
                }
            }
        }
    }
}
</style>
