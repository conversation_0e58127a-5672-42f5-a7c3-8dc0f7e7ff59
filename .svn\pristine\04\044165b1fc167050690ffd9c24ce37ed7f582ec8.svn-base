// 智教培主题色
$smart-tech-theme-gradient: linear-gradient(79deg, #1f98f1 0%, #01bbd5 100%);

// 滚动条样式占位符
%smart-tech-scrollbar-style {
    &::-webkit-scrollbar {
        width: 4px;
        height: 8px;
    }
    &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 2px;

        &:hover {
            background: rgba(33, 150, 243, 0.5);
        }
    }
}
// AI主题滚动条样式
// 通用div滚动条
div {
    @extend %smart-tech-scrollbar-style;
}

// el-table__body-wrapper滚动条
.el-table__body-wrapper {
    @extend %smart-tech-scrollbar-style;
}

::v-deep .el-button--primary {
    background: $smart-tech-theme-gradient;
    border: none;
    color: #fff;
    &:hover,
    &:focus {
        background: linear-gradient(134deg, #7795e6 0%, #27c4c5 96%);
    }
    &.is-disabled,
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        &:hover,
        &:focus {
            background: $smart-tech-theme-gradient;
        }
    }
}
::v-deep .gradient_btn {
    background: $smart-tech-theme-gradient;
    border: none;
    color: #fff;
    &:hover,
    &:focus {
        background: linear-gradient(79deg, #1f98f1 0%, #01bbd5 100%);
    }
    &.is-disabled,
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        &:hover,
        &:focus {
            background: $smart-tech-theme-gradient;
        }
    }
}
::v-deep .el-table {
    border-radius: 6px;
    overflow: hidden;

    th {
        background: #d7dfe1;
        color: #000;
        font-weight: bold;
        padding: 6px 0;

        // 表头文本换行样式
        .cell {
            word-break: break-word; // 允许在单词内换行
            word-wrap: break-word; // 兼容性写法
            white-space: normal; // 允许换行
            line-height: 1.4; // 设置合适的行高
            hyphens: auto; // 自动添加连字符（英文）
            overflow-wrap: break-word; // 现代浏览器的换行属性

            // 针对英文文本的优化
            @supports (word-break: break-word) {
                word-break: break-word;
            }

            // 确保中文和英文都能正确换行
            &[lang="en"],
            &:lang(en) {
                word-break: break-word;
                hyphens: auto;
            }
        }
    }

    td {
        padding: 6px 0;
        color: #000;

        // 表格单元格内容换行样式
        .cell {
            word-break: break-word; // 允许在单词内换行
            word-wrap: break-word; // 兼容性写法
            white-space: normal; // 允许换行
            line-height: 1.4; // 设置合适的行高
            overflow-wrap: break-word; // 现代浏览器的换行属性

            // 针对英文文本的优化
            &[lang="en"],
            &:lang(en) {
                word-break: break-word;
                hyphens: auto;
            }
        }
    }

    .el-table__header-wrapper {
    }

    .el-table__body-wrapper {
        overflow-y: auto;
    }

    .el-button--text {
        padding: 0 8px;
        font-weight: 500;
    }
    .el-progress-bar__inner {
        background: $smart-tech-theme-gradient;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner {
        background: $smart-tech-theme-gradient;
        border-color: #6082e0;
    }
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
        background: $smart-tech-theme-gradient;
        border-color: #6082e0;
    }
    // 新增状态标签全局样式
    .status-tag-passed-new,
    .status-tag-failed-new,
    .status-tag-pending-new,
    .status-tag-unsubmitted-new,
    .status-tag-default-new {
        border-radius: 4px;
        font-size: 15px;
        color: #202226;
        text-align: center;
        line-height: 24px;
        font-weight: 400;
        padding: 1px 8px;
        display: inline-block;
        min-width: 70px;
        box-sizing: border-box;
    }

    .status-tag-passed-new {
        background: rgba(3, 203, 0, 0.3);
        border: 1px solid #00a124;
    }

    .status-tag-failed-new {
        background: rgba(255, 82, 25, 0.3);
        border: 1px solid #e5110a;
    }

    .status-tag-pending-new {
        background: rgba(255, 192, 0, 0.3);
        border: 1px solid #ff6900;
    }

    .status-tag-unsubmitted-new {
        background: rgba(202, 202, 202, 0.7);
        border: 1px solid #6c6c6c;
    }

    .status-tag-default-new {
        background: #f0f0f0;
        border: 1px solid #ccc;
        color: #555;
    }
    .operation-buttons-container {
        display: flex;
        flex-direction: row;
        align-items: flex-start; // 改为顶部对齐，适应换行
        flex-wrap: wrap; // 允许换行
        gap: 4px 8px; // 行间距4px，列间距8px
        justify-content: flex-start; // 左对齐
        line-height: 1.2; // 设置行高
        .option-btn {
            color: #409eff;
            flex-shrink: 0; // 防止按钮被压缩
            white-space: nowrap; // 防止按钮文字换行
            margin: 0; // 清除默认margin
            padding: 2px 4px; // 减小内边距以节省空间
            &:hover {
                color: #66b1ff;
            }
        }
    }
    .result-text {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 500;

        &.result-pass {
            background-color: #f0f9eb;
            color: #67c23a;
        }

        &.result-fail {
            background-color: #fef0f0;
            color: #f56c6c;
        }
    }
}

// 通用标签页样式
.smart-tech-tabs {
    flex: 1;
    height: 50px;
    :deep(.el-tabs__header) {
        margin-bottom: 0;
        border-bottom: none;

        .el-tabs__nav-wrap {
            &::after {
                display: none;
            }
        }

        .el-tabs__nav {
            border: none;
        }

        .el-tabs__item {
            // 通用item样式
            color: #000;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            appearance: none !important;
            &.is-active {
                font-weight: bold;
            }
        }

        .el-tabs__active-bar {
            // 通用active-bar样式
            background: $smart-tech-theme-gradient;
            border-radius: 3px;
        }
    }

    // 大尺寸 Tab
    &.smart-tech-tabs--large {
        :deep(.el-tabs__item) {
            height: 80px;
            line-height: 80px;
            font-size: 20px;
        }
        :deep(.el-tabs__active-bar) {
            height: 4px;
        }
    }

    // 小尺寸 Tab
    &.smart-tech-tabs--small {
        :deep(.el-tabs__item) {
            height: 50px;
            line-height: 50px;
            font-size: 14px;
        }
        :deep(.el-tabs__active-bar) {
            height: 3px; // 比大尺寸的细一点
        }
    }
    :deep(.el-tabs__content) {
        margin-top: 20px;
        .pagination-container {
            margin-top: 20px;
            text-align: right;
        }
    }
}
// el-pagination 样式
:deep(.el-pagination) {
    // 激活的页码
    .el-pager li.active {
        background: $smart-tech-theme-gradient;
        color: #fff !important; // 使用 !important 确保覆盖 Element UI 的默认样式
        border: none;
        border-radius: 2px;
        &:hover {
            color: #fff !important; // 保持激活态的hover文字颜色
        }
    }

    // 页码悬浮状态 (非激活、非禁用)
    .el-pager li:not(.disabled):not(.active):hover {
        color: #1f98f1 !important; // 主题渐变起始色, 使用 !important 确保覆盖
    }

    // 上一页/下一页按钮悬浮状态 (非禁用)
    .btn-prev:not([disabled]):hover,
    .btn-next:not([disabled]):hover {
        color: #1f98f1 !important; // 主题渐变起始色, 使用 !important 确保覆盖
    }
}
.custom_header {
    height: 60px;
    background-color: #fff;
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    z-index: 10;

    .back_btn {
        display: flex;
        align-items: center;
        cursor: pointer;
        color: #606266;
        font-size: 14px;
        transition: color 0.2s;
        height: 100%;
        i {
            margin-right: 5px;
            font-size: 16px;
        }

        &:hover {
            color: #409eff;
        }
    }
}

// 智教培表格文本换行优化
.smart-tech-table-text-wrap {
    // 通用文本换行样式类
    word-break: break-word !important;
    word-wrap: break-word !important;
    white-space: normal !important;
    line-height: 1.4 !important;
    overflow-wrap: break-word !important;
    hyphens: auto !important;

    // 确保在不同语言环境下都能正确换行
    &[lang="en"],
    &:lang(en) {
        word-break: break-word !important;
        hyphens: auto !important;
    }

    &[lang="zh"],
    &:lang(zh) {
        word-break: break-all !important;
    }
}

// 为智教培相关页面的表格添加全局样式
.onlineTestOverview_container,
.uploadTestOverview_container,
.correcting-online-test-overview-container,
.supervisor-management-container {
    .el-table {
        th .cell,
        td .cell {
            @extend .smart-tech-table-text-wrap;
        }

        // 特别处理表头，确保英文文本能够正确换行
        th {
            min-height: 40px; // 设置最小高度以容纳换行文本

            .cell {
                padding: 4px 8px; // 增加内边距
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                min-height: 32px; // 确保有足够空间显示换行文本
            }
        }

        // 表格内容单元格样式
        td .cell {
            padding: 4px 8px;
            min-height: 24px;
        }
    }
}
