<template>
    <div class="notify_wrapper">
        <template v-if="message.attendee_changed_info">
            <div v-if="message.attendee_changed_info.attendee_join_type == 1" class="system_notify longwrap">
                <span @click="openVisitingCard(message, 5)">{{ message.attendee_changed_info.nickname }}</span>
                {{ lang.search_join_tip }}
            </div>
            <div v-if="message.attendee_changed_info.attendee_join_type == 2" class="system_notify longwrap">
                <span
                    @click="openVisitingCard(message, 6)"
                    v-if="
                        attendeeList&&
                        attendeeList['attendee_' + message.attendee_changed_info.inviter_id]
                    "
                    >{{
                        attendeeList
                            ? attendeeList["attendee_" + message.attendee_changed_info.inviter_id].nickname
                            : "."
                    }}</span
                >
                {{ lang.invited_join_group_tip1 }}
                <span @click="openVisitingCard(message, 5)">{{ message.attendee_changed_info.nickname }}</span>
                {{ lang.invited_join_group_tip2 }}
            </div>
            <div v-if="message.attendee_changed_info.attendee_join_type == 3" class="system_notify longwrap">
                <span @click="openVisitingCard(message, 5)">{{ message.attendee_changed_info.nickname }}</span>
                {{ lang.scaned_tip }}
                <span @click="openVisitingCard(message, 6)">{{
                        attendeeList
                            ? attendeeList["attendee_" + message.attendee_changed_info.inviter_id].nickname
                            : "."
                    }}</span>
                {{ lang.join_group_by_qrcode_tips }}
            </div>
            <div v-if="message.attendee_changed_info.attendee_join_type == 4" class="system_notify longwrap">
                <span @click="openVisitingCard(message, 5)">{{ message.attendee_changed_info.nickname }}</span>
                {{ lang.wechat_invite_prefix }}
                <span @click="openVisitingCard(message, 6)">{{
                        attendeeList
                            ? attendeeList["attendee_" + message.attendee_changed_info.inviter_id].nickname
                            : "."
                    }}</span>
                {{ lang.wechat_invite_affix }}
            </div>
        </template>
    </div>
</template>

<script>
import base from "../../lib/base";
import {
    openVisitingCard,
} from '../../lib/common_base'
export default {
    name: "SysJoinAttendeeMsg",
    mixins: [base],
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        attendeeList:{
            type: Object,
            default:()=>{
                return null;
            }
        }
    },
    data(){
        return{
            openVisitingCard
        }
    }
};
</script>

<style></style>
