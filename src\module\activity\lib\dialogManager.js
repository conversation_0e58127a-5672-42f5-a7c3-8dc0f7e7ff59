/**
 * DialogManager - 弹出窗管理器
 * 单例模式实现，全局共用，管理所有弹窗的打开和关闭
 * 支持webview原生返回手势拦截
 */

import Tool from '@/common/tool'

class DialogManager {
    constructor() {
        // 存储所有注册的弹窗
        this.dialogs = new Map();
        // 存储事件监听器
        this.listeners = new Map();
    }

    /**
     * 获取DialogManager的单例实例
     * @returns {DialogManager} DialogManager的单例实例
     * @static
     */
    static getInstance() {
        if (!DialogManager.instance) {
            DialogManager.instance = new DialogManager();
        }
        return DialogManager.instance;
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     * @private
     */
    generateId() {
        return Tool.genID(3);
    }

    /**
     * 注册弹窗（即打开弹窗）
     * @param {Object} dialog - 弹窗实例
     * @param {Function} options.open - 打开弹窗的方法
     * @param {Function} options.close - 关闭弹窗的方法
     * @param {boolean} options.canCloseOnPopstate - 是否可以在popstate时关闭
     * @param {boolean} options.canClose - 是否可以关闭
     * @param {string} options.id - 可选，指定弹窗ID
     * @returns {string} 弹窗ID
     */
    register(dialog, { open, close, canCloseOnPopstate = true, canClose = true, id = null }) {
        const dialogId = id || this.generateId();

        // 检查是否已经注册了相同ID的弹窗
        if (id && this.dialogs.has(id)) {
            console.warn('DialogManager: 弹窗ID已存在，跳过注册:', id);
            return id;
        }

        // 存储弹窗及其操作方法，注册即表示打开
        this.dialogs.set(dialogId, {
            dialog,
            open,
            close,
            canCloseOnPopstate,
            canClose,
            isOpen: true  // 注册即打开
        });

        // 执行打开操作
        if (open) {
            try {
                open();
            } catch (error) {
                console.error('DialogManager: 执行弹窗打开操作时发生错误:', error);
            }
        }

        // 触发打开事件
        this.emit('open', dialogId, dialog);

        console.log('DialogManager: 弹窗已注册并打开:', dialogId);
        return dialogId;
    }

    /**
     * 注销弹窗（即关闭弹窗）
     * @param {string} id - 弹窗ID
     * @returns {boolean} 是否成功注销
     */
    unregister(id) {
        if (!this.dialogs.has(id)) {
            // 静默返回，避免重复注销时的警告日志
            return false;
        }

        const dialogInfo = this.dialogs.get(id);

        // 先从Map中删除，避免close回调中的递归调用
        const deleted = this.dialogs.delete(id);

        // 执行关闭操作
        if (dialogInfo.close && dialogInfo.canClose) {
            try {
                dialogInfo.close();
            } catch (error) {
                console.error('DialogManager: 执行弹窗关闭操作时发生错误:', error);
            }
        }

        // 触发关闭事件
        try {
            this.emit('close', id, dialogInfo.dialog);
        } catch (error) {
            console.error('DialogManager: 触发关闭事件时发生错误:', error);
        }

        console.log('DialogManager: 弹窗已注销并关闭:', id);
        return deleted;
    }



    /**
     * 关闭所有弹窗（注销所有弹窗）
     * @returns {number} 成功关闭的弹窗数量
     */
    closeAllDialogs() {
        let closedCount = 0;
        const dialogIds = Array.from(this.dialogs.keys());

        for (const id of dialogIds) {
            if (this.unregister(id)) {
                closedCount++;
            }
        }

        return closedCount;
    }

    /**
     * 处理路由变化
     * @param {Object} to - 目标路由
     * @param {Object} from - 来源路由
     * @param {Function} next - 路由控制函数
     */
    handleRouteChange(to, from, next) {
        const registeredDialogs = this.getRegisteredDialogs();

        // 如果没有注册的弹窗，直接允许路由通过
        if (registeredDialogs.length === 0) {
            next && next();
            return;
        }

        // 获取最后一个（最顶层的）弹窗
        const lastDialog = registeredDialogs[registeredDialogs.length - 1];
        const [lastDialogId, lastDialogInfo] = lastDialog;

        // 检查最后一个弹窗是否可以通过popstate关闭
        if (!lastDialogInfo.canCloseOnPopstate) {
            // 最后一个弹窗不能通过popstate关闭，直接阻止路由返回，不关闭弹窗
            console.log('DialogManager: 阻止路由返回，弹窗不允许popstate关闭:', lastDialogId);
            next && next(false);
            return;
        }

        // 最后一个弹窗可以关闭，关闭它但阻止路由返回
        console.log('DialogManager: 关闭最后一个弹窗，阻止路由返回:', lastDialogId);

        try {
            this.unregister(lastDialogId);
        } catch (error) {
            console.error('DialogManager: 关闭弹窗时发生错误:', error);
        }

        next && next(false);
    }

    /**
     * 获取当前已注册的所有弹窗（即打开的弹窗）
     * @returns {Array} 已注册的弹窗数组，每项为 [id, dialogInfo]
     */
    getRegisteredDialogs() {
        return Array.from(this.dialogs.entries());
    }

    /**
     * 向后兼容的方法名
     * @returns {Array} 已注册的弹窗数组，每项为 [id, dialogInfo]
     */
    getOpenDialogs() {
        return this.getRegisteredDialogs();
    }

    /**
     * 检查弹窗是否已注册（即是否打开）
     * @param {string} id - 弹窗ID
     * @returns {boolean} 是否已注册
     */
    isDialogRegistered(id) {
        return this.dialogs.has(id);
    }

    /**
     * 向后兼容的方法名
     * @param {string} id - 弹窗ID
     * @returns {boolean} 是否已注册
     */
    isDialogOpen(id) {
        return this.isDialogRegistered(id);
    }

    /**
     * 获取弹窗信息
     * @param {string} id - 弹窗ID
     * @returns {Object|null} 弹窗信息
     */
    getDialogById(id) {
        if (!this.dialogs.has(id)) {
            return null;
        }
        return this.dialogs.get(id);
    }

    /**
     * 检查是否有已注册的弹窗（即是否有打开的弹窗）
     * @returns {boolean} 是否有已注册的弹窗
     */
    hasRegisteredDialogs() {
        return this.dialogs.size > 0;
    }

    /**
     * 向后兼容的方法名
     * @returns {boolean} 是否有已注册的弹窗
     */
    hasOpenDialogs() {
        return this.hasRegisteredDialogs();
    }

    /**
     * 添加事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }

        this.listeners.get(event).push(callback);
    }

    /**
     * 移除事件监听器
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    off(event, callback) {
        if (!this.listeners.has(event)) {
            return;
        }

        const callbacks = this.listeners.get(event);
        const index = callbacks.indexOf(callback);

        if (index !== -1) {
            callbacks.splice(index, 1);
        }
    }

    /**
     * 触发事件
     * @param {string} event - 事件名称
     * @param {...any} args - 事件参数
     */
    emit(event, ...args) {
        if (!this.listeners.has(event)) {
            return;
        }

        for (const callback of this.listeners.get(event)) {
            callback(...args);
        }
    }

    /**
     * 从现有系统迁移弹窗（即注册并打开弹窗）
     * @param {Object} dialog - 弹窗实例
     * @param {string} id - 弹窗ID
     * @returns {string} 新的弹窗ID
     */
    migrateFromLegacy(dialog, id) {
        // 从现有弹窗实例中提取方法
        const open = dialog.showDialog ? dialog.showDialog.bind(dialog) : null;
        const close = dialog.closeDialog ? dialog.closeDialog.bind(dialog) : null;
        const canCloseOnPopstate = dialog.checkCanCloseOnPopstate ? dialog.checkCanCloseOnPopstate() : true;
        const canClose = dialog.checkCanClose ? dialog.checkCanClose() : true;

        // 注册到新系统（即打开弹窗）
        return this.register(dialog, { open, close, canCloseOnPopstate, canClose, id });
    }

    /**
     * 获取最后注册的弹窗（即最后打开的弹窗）
     * @returns {Array|null} [id, dialogInfo] 或 null
     */
    getLastRegisteredDialog() {
        const registeredDialogs = this.getRegisteredDialogs();
        return registeredDialogs.length > 0 ? registeredDialogs[registeredDialogs.length - 1] : null;
    }

    /**
     * 向后兼容的方法名
     * @returns {Array|null} [id, dialogInfo] 或 null
     */
    getLastOpenDialog() {
        return this.getLastRegisteredDialog();
    }

    /**
     * 关闭最后注册的弹窗（注销最后一个弹窗）
     * @returns {boolean} 是否成功关闭
     */
    closeLastDialog() {
        const lastDialog = this.getLastRegisteredDialog();
        if (lastDialog) {
            const [id] = lastDialog;
            return this.unregister(id);
        }
        return false;
    }

    /**
     * 设置弹窗属性
     * @param {string} id - 弹窗ID
     * @param {Object} properties - 要设置的属性
     * @returns {boolean} 是否成功设置
     */
    setDialogProperties(id, properties) {
        if (!this.dialogs.has(id)) {
            return false;
        }

        const dialogInfo = this.dialogs.get(id);
        Object.assign(dialogInfo, properties);
        return true;
    }

    /**
     * 销毁管理器，清理事件监听器
     */
    destroy() {
        // 关闭所有弹窗
        this.closeAllDialogs();
        // 清理所有数据
        this.dialogs.clear();
        this.listeners.clear();
    }

    /**
     * 获取管理器状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        return {
            totalDialogs: this.dialogs.size,
            registeredDialogs: this.dialogs.size,  // 注册的弹窗数量
        };
    }
}

// 导出单例实例
export default DialogManager.getInstance();
window.DialogManager = DialogManager.getInstance();
