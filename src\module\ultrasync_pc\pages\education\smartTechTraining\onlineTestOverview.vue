<template>
    <div class="onlineTestOverview_container">
        <div class="custom_header">
            <div class="back_btn" @click.stop="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body" ref="customBody" v-loading="loading">
            <template v-if="!loading">
                <div class="top_info_section">
                    <div class="icon_area">
                        <div class="placeholder_icon">
                            <i class="el-icon-collection"></i>
                        </div>
                    </div>
                    <div class="details_area">
                        <div class="detail_top">
                            <div class="detail_title">
                                {{ title }}
                            </div>
                        </div>
                        <div class="detail_bottom">
                            <div class="detail_info">
                                <div class="info_row">
                                    <span class="info_label">{{ lang.exam_questions_count }}:</span>
                                    <span class="info_value">{{ questionsCount }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.min_pass_questions }}:</span>
                                    <span class="info_value">{{ minPassQuestionsDisplay }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.exam_frequency }}:</span>
                                    <span class="info_value">{{ examDuration }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.exam_deadline }}:</span>
                                    <span class="info_value">{{ deadline }}</span>
                                </div>
                            </div>
                            <div class="detail_btns">
                                <el-button
                                    type="primary"
                                    @click="startTest"
                                    :loading="isStartingTest"
                                    :disabled="!canStartNewTest"
                                >
                                    {{ lang.start_test_button }}
                                </el-button>
                                <el-button
                                    v-if="examHistoryData && examHistoryData.length > 0"
                                    @click="viewDetails"
                                    type="primary"
                                    plain
                                    style="margin-left: 10px"
                                >
                                    {{ lang.view_details }}
                                </el-button>
                            </div>
                            <div
                                v-if="warningMessage.show"
                                :class="{
                                    'warning_text': warningMessage.type === 'warning',
                                    'success_text': warningMessage.type === 'success'
                                }"
                            >
                                <i :class="{
                                    'el-icon-warning': warningMessage.type === 'warning',
                                    'el-icon-success': warningMessage.type === 'success'
                                }"></i> {{ warningMessage.text }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content_section exam_history_section">
                    <h3>{{ lang.historical_records }}</h3>
                    <el-table :data="examHistoryData" style="width: 100%">
                        <el-table-column prop="createdAt" :label="capitalizeFirstLetter(lang.submission_time)">
                            <template slot-scope="scope">
                                {{ formatTime(scope.row.createdAt) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="scoreTime" :label="capitalizeFirstLetter(lang.correction_time)">
                            <template slot-scope="scope" v-if="scope.row.scoreTime">
                                {{ formatTime(scope.row.scoreTime * 1000) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="passCount" :label="capitalizeFirstLetter(lang.questions_passed_number)"></el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.correction_status)">
                            <template slot-scope="scope">
                                <span v-if="scope.row.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.UNCORRECTED">{{
                                    lang.Uncorrected
                                }}</span>
                                <span
                                    v-else-if="scope.row.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.CORRECTED"
                                    >{{ lang.corrected }}</span
                                >
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.exam_result)">
                            <template
                                slot-scope="scope"
                                v-if="scope.row.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.CORRECTED"
                            >
                                <span
                                    :class="[
                                        {
                                            'status-tag-passed-new': scope.row.isPass,
                                            'status-tag-failed-new': !scope.row.isPass,
                                        },
                                    ]"
                                    >{{ formatPassResult(scope.row.isPass) }}</span
                                >
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.operation)" width="200">
                            <template slot-scope="scope">
                                <div class="operation-buttons-container">
                                    <el-button
                                        @click="viewAnalysis(scope.row)"
                                        type="text"
                                        size="small"
                                        class="option-btn"
                                    >
                                        {{ lang.view_details }}
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../../lib/base";
import onlineTestOverviewMixins from "../../../lib/onlineTestOverviewMixins";
import {
    CLOUD_TEST_TYPE,
    SMART_TECH_TRAINING_ROLE,
    SMART_TECH_TRAINING_TEST_TYPE,
    SMART_TECH_TRAINING_TEST_RETRY_TYPE,
    SMART_TECH_TRAINING_TEST_CORRECT_STATUS,
} from "@/module/ultrasync_pc/lib/constants";
import Tool from "@/common/tool";
import service from "@/module/ultrasync_pc/service/service";
import moment from "moment";
export default {
    mixins: [base, onlineTestOverviewMixins],
    name: "OnlineTestOverview",
    components: {},
    data() {
        return {
            capitalizeFirstLetter: Tool.capitalizeFirstLetter,
            SMART_TECH_TRAINING_TEST_CORRECT_STATUS,
            loading: true,
            isStartingTest: false,
            title: "",
            deadline: "",
            questionsCount: 0,
            examDuration: "",
            passScore: "",
            examHistoryData: [],
            userRole: "",
            testInfo: {},
            testId: "",
            trainingId: "",
            pagerInfo: [],
            // 存储组装后的完整考试数据，包含批改记录
            processedTestData: null,
            studentInfo: {},
            minPassQuestions: "",
        };
    },
    computed: {
        // 判断是否超过截止时间
        isDeadlineExceeded() {
            if (!this.testInfo || !this.testInfo.deadline) {
                return false;
            }
            const currentTimeUnix = moment().unix();
            return currentTimeUnix > this.testInfo.deadline;
        },
        // 统一管理警告信息，优先显示考试已通过状态
        warningMessage() {
            // 首先检查考试已通过的情况，优先显示
            if (
                this.examHistoryData.length > 0 &&
                this.examHistoryData[0].status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.CORRECTED &&
                this.examHistoryData[0].isPass === true
            ) {
                return {
                    show: true,
                    text: this.lang.exam_already_passed,
                    type: "success"
                };
            }

            // 然后检查是否超过截止时间
            if (this.isDeadlineExceeded) {
                return {
                    show: true,
                    text: this.lang.exam_deadline_exceeded_tips,
                    type: "warning"
                };
            }

            // 检查maxRetry限制
            if (
                this.testInfo &&
                this.testInfo.maxRetry > 0 &&
                this.actualExamAttempts >= this.testInfo.maxRetry &&
                !this.hasUncorrectedExam
            ) {
                return {
                    show: true,
                    text: this.lang.max_retry_limit_reached_tips.replace("${1}", this.testInfo.maxRetry),
                    type: "warning"
                };
            }

            return {
                show: false,
                text: "",
                type: "warning"
            };
        },
        canStartNewTest() {
            // 如果超过截止时间，不允许开始考试
            if (this.isDeadlineExceeded) {
                return false;
            }
            if (this.loading) {
                return false;
            }
            // 如果没有历史记录，允许开始考试
            if (this.examHistoryData.length === 0) {
                return true;
            }
            // 如果最新一条记录是已批改且通过，不允许再次考试
            if (
                this.examHistoryData[0].status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.CORRECTED &&
                this.examHistoryData[0].isPass === true
            ) {
                return false;
            }

            // 检查maxRetry限制
            if (this.testInfo && this.testInfo.maxRetry > 0) {
                // 如果存在未批改的记录，允许继续该次考试
                if (this.hasUncorrectedExam) {
                    return true;
                }
                // 如果已批改的次数达到maxRetry限制，不允许新考试
                if (this.actualExamAttempts >= this.testInfo.maxRetry) {
                    return false;
                }
            }

            // 其他所有情况都允许开始考试（包括未批改、已批改且不通过等）
            return true;
        },
        examTypeText() {
            if (!this.testInfo || !this.testInfo.type) {
                return "";
            }
            switch (this.testInfo.type) {
            case SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD:
                return this.lang.ATTACHMENT_UPLOAD;
            case SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ:
                return this.lang.ONLINE_QUIZ;
            default:
                return "";
            }
        },
        maxRetryText() {
            if (!this.testInfo) {
                return "";
            }
            const timesStr = this.lang.number_times.replace("${1}", this.testInfo.maxRetry);
            return this.testInfo.maxRetry === 0 ? this.lang.unlimited_number_of_times : timesStr;
        },
        // 计算实际的考试尝试次数（只计算已批改的记录）
        actualExamAttempts() {
            if (!this.examHistoryData || this.examHistoryData.length === 0) {
                return 0;
            }
            return this.examHistoryData.filter(
                (record) => record.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.CORRECTED
            ).length;
        },
        // 检查是否存在未批改的考试记录
        hasUncorrectedExam() {
            if (!this.examHistoryData || this.examHistoryData.length === 0) {
                return false;
            }
            // 检查最新（第一条）记录是否为未批改状态
            return this.examHistoryData[0].status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.UNCORRECTED;
        },
        // 优化最小通过题数的显示
        minPassQuestionsDisplay() {
            if (this.minPassQuestions === 0) {
                return this.lang.require_all_questions_to_be_passed;
            }
            return this.minPassQuestions;
        },
    },
    watch: {
        $route: {
            handler(to) {
                if (to.name === "SmartTechTrainingExamProject_OnlineTestOverview") {
                    this.fetchPageData();
                }
            },
            immediate: true,
        },
    },
    created() {
        // console.log("created hook");
        // this.userRole = this.$route.params.role;
        // this.fetchPageData();
    },
    beforeDestroy() {},
    methods: {
        back() {
            if (this.$router) {
                this.$router.go(-1);
            } else {
                console.warn("Vue Router not found for back() method.");
            }
        },
        startTest() {
            // 如果存在未批改的考试，直接进入该考试，无需二次确认，也不会被计为新的尝试

            this.isStartingTest = true;

            // 并行获取考试信息和答题历史
            Promise.all([this.getTrainingTestInfoByTestId(), this.getTrainingTestAnswerHistory()])
                .then(([testData, historyResponse]) => {
                    this.$nextTick(() => {
                        if (this.canStartNewTest) {
                            const maxRetryText = this.maxRetryText;

                            let confirmMessage;
                            const currentAttempt = this.actualExamAttempts + 1;
                            console.log(currentAttempt);
                            confirmMessage = this.lang.current_exam_allows_submission_tips
                                .replace("${1}", maxRetryText)
                                .replace("${2}", currentAttempt);

                            this.$confirm(confirmMessage, this.lang.tip_title, {
                                confirmButtonText: this.lang.confirm_btn,
                                cancelButtonText: this.lang.cancel_btn,
                                type: "info",
                            })
                                .then(() => {
                                    // 用户确认后，先尝试加锁
                                    this.tryLockAndProceedToExam();
                                })
                                .catch(() => {
                                    // 用户取消，不执行任何操作
                                })
                                .finally(() => {
                                    this.isStartingTest = false;
                                });
                        }
                    });
                })
                .catch((error) => {
                    console.error("startTest - 获取考试数据失败:", error);

                    this.isStartingTest = false;
                });
        },
        // 尝试加锁并进入考试
        tryLockAndProceedToExam() {
            const params = {
                testID: this.testId,
                studentID: this.studentInfo?.uid,
            };

            // 调用加锁API
            service.keepAnswerLock(params)
                .then(result => {
                    if (result.data.error_code === 0) {
                        console.log('考试加锁成功:', result);
                        // 加锁成功，进入考试页面
                        this.proceedToExam();
                    } else {
                        console.error('考试加锁失败:', result);
                        // this.$message.error(result.data.message || '加锁失败，无法进入考试');
                    }
                })
                .catch(error => {
                    console.error('考试加锁失败:', error);
                    this.$message.error('加锁失败，无法进入考试');
                });
        },
        proceedToExam() {
            Tool.loadModuleRouter({
                name: "SmartTechTrainingExamProject_Exam",
                params: {
                    ...this.$route.params,
                    gradingData: this.processedTestData,
                    testData: this.testInfo,
                    pager_type: CLOUD_TEST_TYPE.ANSWER,
                },
            });
        },
        viewAnalysis(row) {
            console.log("View analysis for row:", row);

            // 从原始API数据中查找当前行对应的记录
            if (!this.examHistoryData || this.examHistoryData.length === 0) {
                return;
            }

            // 重新获取答题历史以确保数据完整
            service
                .getTrainingTestAnswerHistory({
                    testID: this.testId,
                })
                .then((res) => {
                    if (res.data.error_code === 0 && res.data.data && Array.isArray(res.data.data)) {
                        const allHistoryData = res.data.data;

                        // 根据创建时间匹配当前行的记录
                        const targetRecord = allHistoryData.find(
                            (record) => new Date(record.createdAt).getTime() === new Date(row.createdAt).getTime()
                        );

                        if (!targetRecord) {
                            console.error("未找到对应的考试记录");
                            return;
                        }

                        // 组装单条记录的数据
                        const assembledData = this.assembleSingleRecord(targetRecord);

                        if (!assembledData) {
                            console.error("数据组装失败");
                            return;
                        }

                        // 跳转到查看结果页面
                        Tool.loadModuleRouter({
                            name: "SmartTechTrainingExamProject_Exam",
                            params: {
                                ...this.$route.params,
                                pager_type: CLOUD_TEST_TYPE.VIEW_RESULT,
                                testData: this.testInfo,
                                gradingData: assembledData,
                                studentInfo: this.studentInfo,
                            },
                        });
                    } else {
                        console.error("获取考试历史数据失败");
                    }
                })
                .catch((error) => {
                    console.error("viewAnalysis - 获取数据失败:", error);
                });
        },
        updateExamDuration() {
            const attemptCount = this.examHistoryData.length;
            const maxRetry = this.maxRetryText;
            this.examDuration = `${attemptCount}/${maxRetry}`;
        },
        fetchPageData() {
            this.loading = true;
            // 确保总是从当前路由获取最新的参数
            const currentTestId = this.$route.params.testId;
            const currentTrainingId = this.$route.params.trainingId;
            this.userRole = this.$route.params.role; // 更新 userRole

            // 更新组件的 data 属性 testId 和 trainingId，以便 computed 和其他逻辑使用最新的值
            this.testId = currentTestId;
            this.trainingId = currentTrainingId;

            console.log(`Fetching data for testId: ${currentTestId}, trainingId: ${currentTrainingId}`);

            Promise.all([
                this.getTrainingTestInfoByTestId(), // 该方法内部使用 this.testId, this.trainingId
                this.getTrainingTestAnswerHistory(), // 该方法内部使用 this.testId
                this.getTrainingStudentInfo(), // 该方法内部使用 this.trainingId
            ]).finally(() => {
                this.loading = false;
                console.log("Data fetching complete.");
            });
        },
        getTrainingTestInfoByTestId() {
            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestInfoByTestId({
                        testID: this.testId,
                        trainingID: this.trainingId,
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0) {
                            this.testInfo = res.data.data;
                            // 更新页面显示数据
                            if (this.testInfo) {
                                this.title = this.testInfo.title;
                                this.questionsCount = this.testInfo.questionCount || 0;
                                this.minPassQuestions = this.testInfo.minPassQuestions || 0;
                                // 格式化截止时间
                                if (this.testInfo.deadline) {
                                    this.deadline = moment.unix(this.testInfo.deadline).format("YYYY-MM-DD");
                                }
                                this.pagerInfo = this.testInfo.pagerInfo || [];

                                this.updateExamDuration();
                            }
                            resolve(res.data.data);
                        } else {
                            this.$message.error(res.data.message);
                            reject(new Error(res.data.message));
                        }
                    })
                    .catch((error) => {
                        console.error("getTrainingTestInfoByTestId error:", error);
                        reject(error);
                    });
            });
        },
        getTrainingTestAnswerHistory() {
            return service
                .getTrainingTestAnswerHistory({
                    testID: this.testId,
                })
                .then((res) => {
                    console.log(res, "res");
                    if (res.data.error_code === 0 && res.data.data && Array.isArray(res.data.data)) {
                        // 处理考试历史数据
                        this.examHistoryData = res.data.data;

                        // 使用API返回的所有历史数据进行组装
                        if (res.data.data.length > 0) {
                            // 使用所有历史记录进行组装
                            this.processedTestData = this.assembleAllAnswerRecords(res.data.data);
                            console.log("API所有历史数据组装成功:", this.processedTestData);
                        } else {
                            console.warn("API返回空的答题历史数据");
                        }

                        // 更新考试次数
                        this.updateExamDuration();
                    }
                    return res;
                })
                .catch((err) => {
                    console.log(err, "err");
                    return err;
                });
        },
        formatPassResult(isPass) {
            return isPass ? this.lang.passed : this.lang.not_pass;
        },
        getTrainingStudentInfo() {
            service
                .getTrainingStudentInfo({
                    trainingID: this.trainingId,
                })
                .then((res) => {
                    const data = res.data;
                    if (data.error_code === 0) {
                        this.studentInfo = data.data;
                    }
                });
        },
        viewDetails() {
            let dataToPass = this.processedTestData;

            // If processed data is not ready, try to assemble it on the fly from raw history
            if (!dataToPass) {
                console.warn("processedTestData is not available, assembling from examHistoryData on the fly.");
                if (this.examHistoryData && this.examHistoryData.length > 0) {
                    dataToPass = this.assembleAllAnswerRecords(this.examHistoryData);
                }
            }

            if (!dataToPass) {
                console.error("没有可用的考试详情数据。");
                return;
            }

            Tool.loadModuleRouter({
                name: "SmartTechTrainingExamProject_Exam",
                params: {
                    ...this.$route.params,
                    pager_type: CLOUD_TEST_TYPE.VIEW_RESULT,
                    testData: this.testInfo,
                    gradingData: dataToPass,
                    studentInfo: this.studentInfo,
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.onlineTestOverview_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;

    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: auto;
        background-color: #f7f9fc;
    }
}

.top_info_section {
    display: flex;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    justify-content: center;
    padding-bottom: 60px;
    border-bottom: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .icon_area {
        margin-right: 25px;

        .placeholder_icon {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background-color: #ffd700;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #fff;

            i {
                font-size: 80px;
            }
        }
    }

    .details_area {
        display: flex;
        flex-direction: column;

        .detail_top {
            max-width: 700px;

            .detail_title {
                font-size: 22px;
                color: #303133;
                margin-bottom: 15px;
                font-weight: bold;
                margin-top: 15px;
            }
        }

        .detail_bottom {
            max-width: 700px;

            .detail_info {
                display: flex;
                flex-wrap: wrap;
                margin: 10px 0;

                // 每条信息
                .info_row {
                    width: 50%; // 两列
                    display: flex;
                    align-items: center; // 垂直居中
                    margin-bottom: 8px;
                    justify-content: flex-start; // 所有行都左对齐

                    .info_label {
                        min-width: 80px;
                        font-size: 14px;
                        color: #909399;
                        text-align: left;
                        margin-right: 5px;
                    }

                    .info_value {
                        font-size: 14px;
                        color: #303133;
                        font-weight: 500;
                    }
                }
            }

            .detail_btns {
                display: flex;
                margin-top: 10px;
                .el-button {
                    min-width: 120px;
                }
            }
            .warning_text {
                margin-top: 10px;
                color: #f56c6c;
                font-size: 14px;
                text-align: left;
            }

            .success_text {
                margin-top: 10px;
                color: #67C23A;
                font-size: 14px;
                text-align: left;
            }
        }
    }
}

.content_section {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-bottom: none;

    h3 {
        margin-bottom: 18px;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }
}
</style>
