<template>
    <div class="uploadTestOverview_container">
        <div class="custom_header">
            <div class="back_btn" @click.stop="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body" ref="customBody" v-loading="loading">
            <template v-if="!loading">
                <div class="top_info_section">
                    <div class="icon_area">
                        <div class="placeholder_icon">
                            <i class="el-icon-collection"></i>
                        </div>
                    </div>
                    <div class="details_area">
                        <div class="detail_left">
                            <div class="detail_title">{{ title }}</div>
                            <div class="detail_info">
                                <div class="info_row" v-if="deadline">
                                    <span class="info_label">{{ capitalizeFirstLetter(lang.deadline) }}:</span>
                                    <span class="info_value">{{ deadline }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.student_name }}:</span>
                                    <span class="info_value">{{ studentName }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.affiliated_hospital }}:</span>
                                    <span class="info_value">{{ hospital }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{ lang.submission_time }}:</span>
                                    <span class="info_value">{{ submitTime }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="detail_right" v-if="canStartCorrection">
                            <div class="detail_btns">
                                <div class="action_buttons_container">
                                    <el-button type="primary" class="action_btn" @click="handleSubmitCorrection(true)"
                                        :loading="isSubmittingCorrection"> {{ lang.admin_pass }} </el-button>
                                    <el-button plain class="action_btn" @click="handleSubmitCorrection(false)"
                                        :loading="isSubmittingCorrection"> {{ lang.not_pass }} </el-button>
                                </div>
                                <div class="comment-text-area">
                                    <label class="comment-label">{{lang.comment_optional_label}}:</label>
                                    <el-input type="textarea" v-model="correctingComment" :rows="3" :placeholder="lang.input_enter_tips"
                                        resize="none" maxlength="300" show-word-limit />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content_section review_history_section">
                    <h3>{{ lang.uploaded_files_list }}</h3>
                    <div class="table-container">
                        <el-table :data="reviewHistoryData" style="width: 100%" class="file-history-table">
                            <el-table-column :label="capitalizeFirstLetter(lang.upload_datetime)" width="160">
                                <template slot-scope="scope">
                                    <span>{{ formatTime(scope.row.createdAt) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="capitalizeFirstLetter(lang.fileName)" min-width="200" max-width="300">
                                <template slot-scope="scope">
                                    <el-tooltip :content="scope.row.fileName" placement="top"
                                        :disabled="!scope.row.fileName || scope.row.fileName.length <= 30"
                                        popper-class="file-name-tooltip">
                                        <div class="file-name-text">
                                            {{ scope.row.fileName || '--' }}
                                        </div>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column :label="capitalizeFirstLetter(lang.fileSize)" width="120">
                                <template slot-scope="scope">
                                    <span>{{ formatFileSize(scope.row.fileSize) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="capitalizeFirstLetter(lang.correction_status)" width="120">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.status === 1">{{ lang.Uncorrected }}</span>
                                    <span v-else-if="scope.row.status === 2">{{ lang.corrected }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="capitalizeFirstLetter(lang.exam_result)" width="100">
                                <template slot-scope="scope" v-if="scope.row.status === 2">
                                    <span :class="[
                                        {
                                            'status-tag-passed-new': scope.row.isPass,
                                            'status-tag-failed-new': !scope.row.isPass,
                                        },
                                    ]">{{ formatResult(scope.row.isPass) }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column :label="capitalizeFirstLetter(lang.gallery_navbar_comment)" min-width="200" max-width="400">
                                <template slot-scope="scope">
                                    <el-tooltip :content="scope.row.teacherRemark" placement="top"
                                        :disabled="!scope.row.teacherRemark || scope.row.teacherRemark.length <= 50"
                                        popper-class="teacher-remark-tooltip">
                                        <div class="teacher-remark-text">
                                            {{ scope.row.teacherRemark }}
                                        </div>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column :label="capitalizeFirstLetter(lang.operation)" width="120" fixed="right">
                                <template slot-scope="scope">
                                    <div class="operation-buttons-container">
                                        <el-button @click="handleDownloadFile(scope.row)" type="text"
                                            class="option-btn">
                                            <i class="el-icon-download"></i> {{ lang.download_title }}
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import base from "../../../lib/base";
import service from "../../../service/service";
import moment from "moment";
import Tool from "@/common/tool.js";
import {
    SMART_TECH_TRAINING_TEST_CORRECT_STATUS
} from "@/module/ultrasync_pc/lib/constants";
export default {
    mixins: [base],
    name: "UploadTestOverview",
    components: {},
    data() {
        return {
            capitalizeFirstLetter: Tool.capitalizeFirstLetter,
            SMART_TECH_TRAINING_TEST_CORRECT_STATUS,
            title: "",
            deadline: 0,
            reviewHistoryData: [],
            loading: false,
            userRole: "",
            testId: "",
            trainingId: "",
            testInfo: {},
            correctingComment: "", // 批改评语
            isSubmittingCorrection: false, // 是否正在提交批改结果
            // 学员信息
            studentName: "",
            hospital: "",
            submitTime: "",
            useTime: 0,
            studentInfo: null,
        };
    },
    computed: {
        // 获取最新的未批改提交记录
        latestUnmarkedSubmission() {
            if (!this.reviewHistoryData || this.reviewHistoryData.length === 0) {
                return null;
            }
            // 查找status为1（未批改）的最新记录
            return this.reviewHistoryData.find((item) => item.status === 1) || null;
        },
        // 判断是否可以开始批改
        canStartCorrection() {
            if (this.loading || this.reviewHistoryData.length === 0) {
                return true; // 数据加载中或无历史记录时允许批改
            }
            // 检查第一条记录的状态是否为未批改
            const firstRecord = this.reviewHistoryData[0];
            return firstRecord.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.UNCORRECTED;
        },
    },
    watch: {
        $route: {
            handler(to) {
                if (to.name === "SmartTechTrainingGradingReview_CorrectingUploadTestOverview"
                || to.name === "SmartTechTrainingStudentOverview_CorrectingUploadTestOverview") {
                    this.fetchPageData();
                }
            },
            immediate: true,
        },
    },
    async created() {

    },
    methods: {
        formatResult(isPass) {
            return isPass ? this.lang.passed : this.lang.not_pass;
        },
        async fetchPageData() {
            this.loading = true;
            this.userRole = this.$route.params.role;
            this.testId = this.$route.params.testId;
            this.trainingId = this.$route.params.trainingId;
            this.studentInfo = this.$route.params.studentInfo;

            if (this.studentInfo) {
                this.studentName = this.studentInfo.name || "";
                this.hospital = this.studentInfo.hospital || "";
            }

            try {
                await Promise.all([this.getTrainingTestInfoByTestId(), this.getTrainingTestAnswerHistory()]);
            } catch (error) {
                console.error("获取页面数据失败:", error);
                this.back();
            } finally {
                this.loading = false;
            }
        },
        back() {
            if (this.$router) {
                this.$router.go(-1);
            } else {
                console.warn("Vue Router not found for back() method.");
            }
        },

        handleDownloadFile(task) {
            console.log("Download file:", task);
            if (task && task.fileUrl) {
                if (Tool.checkAppClient('Cef')) {
                    // CEF环境使用下载管理器
                    let url = task.fileUrl;
                    let surl = window.location.origin;
                    if (url.indexOf("http") != 0) {
                        url = surl + "/" + url;
                    }
                    this.$root.eventBus.$emit("createDownLoadTask", {
                        downLoadUrlList: [{
                            src: url,
                            filename: task.fileName
                        }],
                        needsInputFileName: true,
                    });
                } else {
                    // 浏览器环境使用Tool方法
                    Tool.downloadFileByBrowser(task.fileUrl, task.fileName).catch(error => {
                        console.error("下载失败:", error);
                        this.$message.error("下载失败");
                    });
                }
            } else {
                console.error("file url missing");
                this.$message.error("文件链接缺失");
            }
        },
        handleSubmitCorrection(isPass) {
            this.isSubmittingCorrection = true;

            // 准备提交数据
            const correctionData = {
                answerID: this.reviewHistoryData[0]._id,
                isPass: isPass,
                teacherRemark: this.correctingComment.trim(),
            };

            console.log("提交批改数据：", correctionData);

            // 调用批改提交API
            service
                .submitTrainingUploadTestResult(correctionData)
                .then((res) => {
                    console.log("批改提交响应：", res);
                    if (res.data && res.data.error_code === 0) {
                        this.$message.success(this.lang.submitted_successfully);
                        // 重置表单
                        this.correctingComment = "";
                        // 刷新批改历史
                        this.getTrainingTestAnswerHistory();
                    } else {
                        console.error(res.data?.message || "submit correction result failed");
                    }
                })
                .catch((error) => {
                    console.error("submit correction result error", error);
                })
                .finally(() => {
                    this.isSubmittingCorrection = false;
                });
        },

        formatFileSize(bytes) {
            if (bytes === 0) {
                return "0 Bytes";
            }
            const k = 1024;
            const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
        },
        getTrainingTestInfoByTestId() {
            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestInfoByTestId({
                        testID: this.testId,
                        trainingID: this.trainingId,
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0) {
                            this.testInfo = res.data.data;
                            // 更新页面显示数据
                            if (this.testInfo) {
                                this.title = this.testInfo.title || "未命名考试";
                                this.questionsCount = this.testInfo.questionCount || 0;

                                // 格式化截止时间
                                if (this.testInfo.deadline) {
                                    this.deadline = moment.unix(this.testInfo.deadline).format("YYYY-MM-DD");
                                    console.log(this.deadline, "this.deadline");
                                }
                                this.pagerInfo = this.testInfo.pagerInfo || [];
                            }
                            resolve(res.data.data);
                        } else {
                            console.error(res.data.message || "get exam info failed");
                            reject(new Error(res.data.message || "get exam info failed"));
                        }
                    })
                    .catch((error) => {
                        console.error("getTrainingTestInfoByTestId error:", error);
                        reject(error);
                    });
            });
        },
        getTrainingTestAnswerHistory() {
            // 添加studentID参数
            const studentID = this.studentInfo?.uid || "";

            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestAnswerHistory({
                        testID: this.testId,
                        studentID: studentID
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0 && res.data.data && Array.isArray(res.data.data)) {
                            // 处理批改历史数据
                            this.reviewHistoryData = res.data.data.map((item) => {
                                // 从服务器返回的数据中提取文件信息
                                const fileInfo = item.answer && item.answer.length > 0 ? item.answer[0] : {};

                                return {
                                    fileName: fileInfo.fileName,
                                    fileSize: fileInfo.fileSize,
                                    fileUrl: fileInfo.fileUrl,
                                    ...item
                                };
                            });

                            this.useTime = this.reviewHistoryData[0].useTime || 0;
                            this.submitTime = this.formatTime(this.reviewHistoryData[0].createdAt)
                            resolve(this.reviewHistoryData);
                        } else {
                            // 如果没有数据，设置为空数组
                            this.reviewHistoryData = [];
                            resolve([]);
                        }
                    })
                    .catch((err) => {
                        console.error("get grading history failed", err);
                        this.reviewHistoryData = [];
                        reject(err);
                    });
            });
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.uploadTestOverview_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;
    z-index: 10;
    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: auto;
        background-color: #f7f9fc;
    }
}

.top_info_section {
    display: flex;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    justify-content: center;
    padding-bottom: 40px;
    border-bottom: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .icon_area {
        margin-right: 25px;

        .placeholder_icon {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background-color: #ffd700;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #fff;

            i {
                font-size: 80px;
            }
        }
    }

    .details_area {
        flex: 1;
        max-width: 1400px;
        display: flex;
        gap: 30px;
        align-items: flex-start;
        min-width: 0; // 防止flex子项溢出
        flex-wrap: wrap;
        .detail_left {
            min-width: 500px;
            .detail_title {
                font-size: 22px;
                color: #303133;
                margin-bottom: 15px;
                font-weight: bold;
                margin-top: 15px;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            .detail_info {
                display: flex;
                flex-wrap: wrap;
                margin: 10px 0;

                // 每条信息
                .info_row {
                    width: 50%; // 每行显示两个，每个占50%
                    display: flex;
                    align-items: flex-start; // 改为顶部对齐
                    margin-bottom: 8px;
                    justify-content: flex-start;
                    min-width: 0; // 防止内容溢出
                    padding-right: 8px;

                    .info_label {
                        min-width: 80px;
                        max-width: 160px;
                        font-size: 14px;
                        color: #909399;
                        text-align: left;
                        margin-right: 5px;
                        flex-shrink: 0; // 防止标签被压缩
                        // 限制单行显示
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .info_value {
                        font-size: 14px;
                        color: #303133;
                        font-weight: 500;
                        min-width: 0; // 允许文本换行
                        // 限制显示2行
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 2;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: 1.4;
                        max-height: 2.8em;
                        /* 2行的高度，1.4 * 2 = 2.8 */
                        word-break: break-word;
                        white-space: normal;
                    }
                }
            }
        }

        .detail_right {
            flex: 1;
            min-width: 500px;

            .detail_btns {
                display: flex;
                gap: 20px;
                align-items: center;

                .action_buttons_container {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .action_btn {
                        width: 150px;
                        margin-bottom: 10px;
                        margin-left: 0;
                    }
                }

                .comment-text-area {
                    flex: 1;
                    height: 140px;
                    display: flex;
                    flex-direction: column;

                    .comment-label {
                        font-size: 16px;
                        color: #202226;
                        text-align: left;
                        line-height: 22px;
                        font-weight: 500;
                        margin-bottom: 8px;
                    }

                    :deep(.el-textarea) {
                        flex: 1;

                        .el-textarea__inner {
                            height: 100%;
                            border: 1px solid #a8b0c7;
                            border-radius: 4px;
                        }
                    }
                }

                .el-button {
                    min-width: 120px;
                }
            }
        }
    }

    .upload_btn {
        margin-top: 22px;
        padding: 10px 22px;
        font-size: 15px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    ::v-deep .el-upload {
        width: 100%;
        text-align: center;

        .el-upload__tip {
            color: #909399;
            font-size: 14px;
            margin-top: 10px;
        }
    }

    .warning_text {
        margin-top: 10px;
        color: #f56c6c;
        font-size: 14px;
        text-align: left;
    }

    // 批改操作相关样式
    .correcting_actions_container {
        .correcting_result_section {
            .el-button.result-selected {
                background-color: #409eff !important;
                border-color: #409eff !important;
                color: #ffffff !important;

                &.el-button--plain {
                    background-color: #409eff !important;
                    border-color: #409eff !important;
                    color: #ffffff !important;
                }
            }
        }
    }
}

.content_section {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-bottom: none;

    h3 {
        margin-bottom: 18px;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }

    // 表格容器样式
    .table-container {
        overflow-x: auto;
        overflow-y: hidden;

        .file-history-table {
            min-width: 1000px; // 确保表格有最小宽度

            // 文件名文本样式
            .file-name-text {
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.4;
                max-height: 2.8em;
                /* 2行的高度，1.4 * 2 = 2.8 */
                word-break: break-all;
                cursor: pointer;
                white-space: normal;
            }
        }
    }

    // 教师评语文本样式
    .teacher-remark-text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
        max-height: 2.8em;
        /* 2行的高度，1.4 * 2 = 2.8 */
        word-break: break-word;
        cursor: pointer;
        white-space: normal;
    }
}
</style>
<style lang="scss">
.teacher-remark-tooltip {
    max-width: 800px !important;
    word-break: break-word;
    white-space: pre-wrap;
    margin-right: 30px;
}

.file-name-tooltip {
    max-width: 600px !important;
    word-break: break-all;
    white-space: pre-wrap;
}
</style>
