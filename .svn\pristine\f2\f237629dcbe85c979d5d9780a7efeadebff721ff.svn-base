<template>
    <div class="exam_bank" v-loading="loadingPage">
        <div class="search_container">
            <el-input
            :placeholder="lang.homework_search_key"
            prefix-icon="el-icon-search"
            @input="debounceSearch"
            v-model="searchKey"
            ></el-input>
            
            <el-button size="medium" type="primary" @click="handleUploadClick">{{lang.homework.upload.upload_excel}}</el-button>
        </div>
        <template v-if="examList.length">
            <div class="cloud_exam_item" v-for="exam of examList" :key="exam.paperID">
                <div class="cloud_exam_item_card">
                    <p class="exam_title">{{exam.paperInfo.title}}</p>
                    <p class="exam_author">{{exam.paperInfo.author}}</p>
                    <div class="exam_detail" >
                        <div class="exam_type_icon">
                            <template v-if="exam.paperInfo.contentType">
                                <img class="title_icon" :src="`static/resource_pc/images/homework_type${exam.paperInfo.contentType}.png`">
                                <p>{{lang['homework_type'+exam.paperInfo.contentType]}}</p>
                            </template>
                            <template v-else>
                                <img class="title_icon" src="static/resource_pc/images/homework_type5.png">
                                <p>{{lang.homework_type5}}</p>
                            </template>
                            
                        </div>
                        <div class="exam_description">
                            <p>{{lang.paper_total_score}}：{{exam.paperInfo.score}}{{lang.point_tip}}</p>
                            <p>{{lang.paper_question_count}}：{{exam.paperInfo.questionCount}}</p>
                            <p>{{lang.creation_time}}：{{exam.paperInfo.createdAt | showData}}</p>
                        </div>
                    </div>
                    <div class="operation_modal">
                        <el-button type="primary" size="small" @click="enterExam(exam.paperID)">{{lang.view_homework}}</el-button>
                    </div>
                </div>
            </div>
        </template>
        <no-data v-else :text="lang.no_data_txt"></no-data>
        <el-pagination
          v-show="examList.length"
          background
          :current-page="queryForm.pageNo"
          :layout="layout"
          :page-size="queryForm.pageSize"
          :total="examListTotal"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          style="margin-top:20px"
        />
        <upload-dialog 
            :show.sync="showUploadDialog"
        />
    </div>
</template>
<script>
import base from '../../../lib/base';
import service from '../../../service/service.js'
import moment from 'moment';
import NoData from "../../../MRComponents/noData.vue";
import Tool from '@/common/tool.js'
import '../../../lib/excelToPaper.js'
import UploadDialog from './uploadDialog.vue'

export default {
    mixins: [base],
    name: 'cloudExamList',
    components: {
        NoData,
        UploadDialog
    },
    data(){
        return {
            cid:0,
            layout: 'total, sizes, prev, pager, next, jumper',
            queryForm:{
                pageNo:1,
                pageSize:30,
            },
            examListTotal:0,
            examList:[],
            loadingPage:false,
            searchKey:'',
            debounceSearch:null,
            uploading:false,
            showUploadDialog: false
        }
    },
    filters:{
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm")
        }
    },
    computed: {
        uploadedPaper() {
            return this.$store.state.homework.uploadedPaper;
        }
    },
    created(){
        this.cid = parseInt(this.$route.params.cid);
        this.debounceSearch = Tool.debounce(this.fetchData,600);
    },
    mounted(){
        this.fetchData();
        this.$root.eventBus.$on('refreshPaperList', this.fetchData);
    },
    watch:{
        
    },
    methods:{
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.queryForm.pageNo = val
            this.fetchData()
        },
        fetchData(){
            this.loadingPage = true;
            service.getPaperList({
                gid:this.cid,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                searchKey:this.searchKey,
            }).then(res=>{
                this.loadingPage = false;
                if (res.data.error_code === 0) {
                    this.examListTotal = res.data.data.total;
                    this.examList = res.data.data.data;
                }
            })
        },
        enterExam(id){
            if (id === 'preview') {
                // 预览模式
                this.$router.push({
                    path: this.$route.fullPath + '/exam/5/preview',
                });
            } else {
                this.$router.push(this.$route.fullPath + '/exam/1/' + id);
            }
        },
        handleUploadClick() {
            this.showUploadDialog = true;
        },
    },
    destroyed(){
        this.$root.eventBus.$off('refreshPaperList', this.fetchData);
    },

}

</script>
<style lang="scss">
.exam_bank{
    .search_container {
        display: flex;
        align-items: center;
        gap: 20px;
        
        .el-input {
            flex: 1;
        }
        
        .exam_bank_upload {
            flex-shrink: 0;
        }
    }
}
</style>
