<template>
    <transition name="fade" appear>
    	<div class="remark_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.set_remark}}
                </template>
            </mrHeader>
            <div class="container">
            	<p class="modify_tip">{{lang.remark_text}}</p>
				<input type="text" class="commont_input" v-model="remark" maxlength="16">
				<button class="primary_bg modify_remark_btn" @click="submit">{{lang.save_txt}}</button>
            </div>
    	</div>
    </transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import { cloneDeep } from 'lodash'
export default {
    mixins: [base],
    name: 'RemarkPage',
    components: {},
    data(){
        return {
            remark:''
        }
    },
    computed:{
        personalObj(){
            let obj=cloneDeep(this.$store.state.relationship.personalObj);
            if (!obj.userid) {
                //attendeeList的id被占用，只能用userid
                obj.userid=obj.id;
            }
            return obj
        },
    },
    mounted(){
        this.remark=this.$store.state.friendList.remarkMap[this.personalObj.userid];
    },
    methods:{
        submit(){
            window.main_screen.setFriendRemark({
                fid:this.personalObj.userid,
                alias:this.remark
            },(res)=>{
                if (res.error_code===0) {
                    this.$store.commit('friendList/updateRemark',{
                        fid:res.data.friend_id,
                        alias:res.data.alias
                    })
                    this.back()
                }
            })
        }
    }
}
</script>
<style lang="scss">
.remark_page{
	.container{
        background: #fff;
		margin:.8rem;
		.modify_tip{
			font-size:.8rem;
			color:#707070;
			margin:.1rem 0;
		}
		input{
			background-color:transparent;
			margin:0;
			color:#333;
			transform: translateZ(0px);
		}
		.modify_remark_btn{
			display: block;
		    width: 100%;
		    border: none;
		    font-size: 1rem;
		    line-height: 2rem;
		    margin: 1rem 0 .6rem;
		    border-radius: .2rem;
		}
	}
}
</style>
