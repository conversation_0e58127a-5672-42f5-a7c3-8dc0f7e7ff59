<template>
  <transition name="slide">
        <div class="scan_to_login third_level_page">
            <mrHeader>
                <template #title>
                    {{lang.scan_to_login}}
                </template>
            </mrHeader>
            <div class="scan_to_login_container" v-loading="logging">
                <div class="login_card">
                    <div class="login_card_top">
                        <i class="iconfont icon-Computer"></i>
                        <div class="title">{{lang.login_to_pc}}</div>
                        <van-checkbox v-model="isAutoLogin" checked-color="#00c59d">
                            <span class="please_agree">{{lang.auto_login_device}}</span>
                        </van-checkbox>
                    </div>

                    <div class="login_card_bottom">
                        <van-checkbox v-model="isAgreePrivacyPolicy" checked-color="#00c59d">
                            <span class="please_agree">{{lang.please_agree}}</span>
                            <span @click.stop="toProtocol" class="primary_color">{{lang.ultrasync_privacy_protocol}}</span>
                        </van-checkbox>
                        <van-button size="small" color="#00c59d" class="login_btn" @click="login">{{lang.login_title}}</van-button>
                        <span class="cancel_login" @click="back">{{lang.cancel_login}}</span>
                    </div>
                </div>
            </div>
            <mr-popup
            :visible.sync="isShowAgreeProtocol"
            :closeOnClockModal="true">
                <div class="title">
                    <van-checkbox v-model="isAgreePrivacyPolicy" checked-color="#00c59d">
                        <span class="please_agree">{{lang.please_agree}}</span>
                        <span @click.stop="toProtocol" class="primary_color">{{lang.ultrasync_privacy_protocol}}</span>
                    </van-checkbox>
                </div>
                <div class="btns">
                    <button class="primary_bg" @click="agreeAndContinue">{{lang.confirm_txt}}</button>
                </div>
            </mr-popup>
            <router-view></router-view>
        </div>
    </transition>
</template>

<script>
import service from '../service/service'
import base from '../lib/base'
import { Toast, Button, Checkbox } from 'vant';
import mrPopup from '../MRComponents/mrPopup.vue'
import Tool from '@/common/tool'
import { goPrivacyPolicy } from '../lib/common_base'
export default {
    mixins: [base],
    name: 'scan_to_login',
    components:{
        mrPopup,
        VanButton: Button,
        VanCheckbox: Checkbox,
    },
    data(){
        return{
            isAutoLogin:true,
            qr_id:'',
            logging: false,
            isAgreePrivacyPolicy:false,
            isShowAgreeProtocol:false,
            continueCallback:null,
        }
    },
    mounted(){
        this.$nextTick(() => {
            this.qr_id = this.$route.params.id;
        })
    },
    methods:{
        // autoLogin(){
        //     this.isAutoLogin = !this.isAutoLogin
        //     console.log("autologin",this.isAutoLogin);
        // },
        login(){
            if (!this.isAgreePrivacyPolicy) {
                this.isShowAgreeProtocol=true;
                this.continueCallback=this.login;
                return;
            }
            this.logging = true;
            this.generateLoginTokenByQrCode()
        },
        generateLoginTokenByQrCode(){
            const params = {
                qrcode:this.qr_id,
                autoLogin:this.isAutoLogin
            }
            service.generateLoginTokenByQrCode(params).then((res)=>{
                this.logging = false;
                this.back()
                if(res.data.error_code!==0){
                    let key = res.data.key;
                    Toast(this.lang[key])
                }
            }).catch((e)=>{
                console.log("error:",e);
                this.logging=false;
                this.back();
            })
        },
        toProtocol(){
            goPrivacyPolicy()
        },
        agreeAndContinue(){
            if (this.isAgreePrivacyPolicy) {
                this.isShowAgreeProtocol=false;
                this.continueCallback&&this.continueCallback();
            }else{
                this.toastAgree();
            }
        },
        toastAgree(){
            Toast(this.lang.read_and_agree);
        }
    }
}
</script>

<style lang="scss">
    .scan_to_login{
        background-color:#eee;

        .scan_to_login_container{
            margin:0.5rem 0.5rem;
            font-size: 0.8rem;
            .login_card{
                display: flex;
                background-color: #ffffff8c;
                margin: 1rem;
                padding: 2rem 0;
                border-radius: 0.5rem;
                justify-content: space-between;
                flex-direction: column;
                align-items: center;
                height: 60vh;
                min-width: 250px;
                min-height: 250px;
                .login_card_top,.login_card_bottom{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .cancel_login{
                        color: #5A8181;
                        font-size: 0.7rem;
                    }
                    .van-checkbox__icon {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        .van-icon{
                            width: .8rem;
                            height: .8rem;
                            line-height: .8rem;
                        }
                    }

                    .van-checkbox__label{
                        font-size: .6rem;
                    }
                }
                .privacy_policy{
                    font-size:0.6rem;
                    display: flex;
                    align-items: center;
                    .please_agree{
                        margin-left: 0.3rem;
                    }
                }
                .icon-Computer{
                    font-size: 4rem;
                    color: #666;
                }
                .title{
                    font-size: 1rem;
                    margin-bottom: 1rem;
                }
                .login_btn{
                    margin:1rem;
                    display: block;
                    border: none;
                    width: 10rem;
                    height: 2.2rem;
                    font-size: 0.8rem;
                    line-height: 2rem;
                    border-radius: .2rem;
                }
            }
        }
    }
</style>
