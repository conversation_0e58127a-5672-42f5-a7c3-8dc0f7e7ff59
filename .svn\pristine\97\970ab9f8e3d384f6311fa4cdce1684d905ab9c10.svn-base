<template>
<div class="cloud_exam_entry">
    <div class="custom_header">
        <img class="title_icon" src="static/resource_pc/images/homework_icon.png">
        <p class="title">{{lang.cloud_exam}}</p>
        <div class="tabs">
            <div class="tab_item" @click="changeTab('exam_incomplete')" :class="{active:activeName === 'exam_incomplete'}">{{lang.exam_incomplete}}
                <span v-show="showUnfinishedUnread" class="unread"></span>
            </div>
            <div class="tab_item" @click="changeTab('exam_completed')" :class="{active:activeName === 'exam_completed'}">{{lang.exam_completed}}
                <span v-show="showCorrectedUnread" class="unread"></span>
            </div>
            <div class="tab_item" @click="changeTab('exam_bank')" :class="{active:activeName === 'exam_bank'}">{{lang.exam_bank}}</div>
            <div class="tab_item" @click="changeTab('arranged_exam')" v-if="isTeacher" :class="{active:activeName === 'arranged_exam'}">{{lang.arranged_exam}}</div>
            <div class="tab_item" @click="changeTab('correcting_exam')" v-if="isTeacher" :class="{active:activeName === 'correcting_exam'}">{{lang.correcting_exam}}
                <span v-show="showUnCorrectedUnread" class="unread"></span>
            </div>
        </div>
        <i class="iconfont iconcuohao" @click="back"></i>
    </div>
    <div class="cloud_exam_page custom_body">
        <div class="custom_container">
            <exam-incomplete-list v-show="activeName === 'exam_incomplete'"></exam-incomplete-list>
            <exam-completed-list v-show="activeName === 'exam_completed'"></exam-completed-list>
            <exam-bank v-show="activeName === 'exam_bank'"></exam-bank>
            <exam-arranged-list v-show="activeName === 'arranged_exam'"></exam-arranged-list>
            <exam-correcting-list v-show="activeName === 'correcting_exam'"></exam-correcting-list>
        </div>
    </div>
    <router-view></router-view>
</div>
</template>
<script>
import base from '../../../lib/base';
import service from '../../../service/service.js'
import examCompletedList from './examCompletedList.vue'
import examIncompleteList from './examIncompleteList.vue'
import examCorrectingList from './examCorrectingList.vue'
import examBank from './examBank.vue'
import examArrangedList from './examArrangedList.vue'
export default {
    mixins: [base],
    name: 'cloudExam',
    components: {
        examCompletedList,
        examCorrectingList,
        examBank,
        examArrangedList,
        examIncompleteList,
    },
    data(){
        return {
            activeName:'exam_incomplete',
            cid:0,
            isTeacher:false,
        }
    },
    computed:{
        showUnfinishedUnread(){
            if (this.cid) {
                return this.$store.state.homework.conversationUnfinish[this.cid] !== undefined
            }else{
                return this.$store.state.homework.globalUnfinish > 0 
            }
        },
        showUnCorrectedUnread(){
            if (this.cid) {
                return this.$store.state.homework.conversationUnCorrect[this.cid] !== undefined
            }else{
                return this.$store.state.homework.globalUnCorrect !== undefined
            }
        },
        showCorrectedUnread(){
            if (this.cid) {
                return this.$store.state.homework.conversationCorrected[this.cid] !== undefined
            }else{
                return this.$store.state.homework.globalCorrected !== undefined && this.$store.state.homework.globalCorrected > 0
            }
        },
    },
    created(){
        this.cid = parseInt(this.$route.params.cid);
        this.activeName = this.$route.query.active || this.$store.state.homework.activeTab || 'exam_incomplete';
        if (!this.$route.query.active && this.$store.state.homework.activeTab !== 'exam_incomplete') {
            this.$store.commit('homework/setActiveTab', 'exam_incomplete');
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.checkIsTeacher();
        })
    },
    watch:{

    },
    methods:{
        changeTab(name){
            this.activeName = name;
        },
        checkIsTeacher(){
            service.checkIsTeacher().then(res => {
                if (res.data.error_code === 0) {
                    this.isTeacher = res.data.data.isTeacher
                }
            })
        }
    },
    destroyed(){
    },

}

</script>
<style lang="scss">
.cloud_exam_entry{
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-dialog:not(.self_define_height) {
        margin-top: 5vh !important;
        height: 80% !important;
    }
    .el-dialog{
        border-radius: 6px;
    }
    .el-dialog__header{
        display: none !important;
    }
    .el-dialog__body{
        height: 100% !important;
        padding: 0 !important;
        display: flex;
        flex-direction: column;
    }
    .cloud_exam_page{
        overflow:auto;
        overflow-x:hidden;
        position: relative;
        .container{
            position:relative;
            height:100%;
            .multicenter_list{
                margin-top: 10px;
                .box-card{
                    margin-bottom: 10px;
                    cursor: pointer;
                    .tag{
                        color: #f00;
                        background: #f4ce9e;
                        border-radius: 8px;
                        padding: 4px 10px;
                        font-size: 12px;
                        margin-left: 20px;
                    }
                }
            }
        }
        .correcting_exam_list,.arranged_exam_list,.exam_bank,.completed_exam_list,.incompleted_exam_list{
            display: flex;
            flex-wrap: wrap;
        }
        .search_container{
            width: 100%;
            padding: 10px 30px 10px 16px;
        }
        .cloud_exam_item{
            width: 33%;
            padding: 16px;
            align-items: center;
            .cloud_exam_item_card{
                position: relative;
                padding: 20px;
                background-color: #fff;
                border-radius: 8px;
                cursor: pointer;
                .operation_modal{
                    display: none;
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,.4);
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    border-radius: 8px;
                    button{
                        background: #00c59d;
                        padding: 10px 25px;
                        border-color: #00c59d;
                        font-size: 14px;
                    }
                }
                &:hover .operation_modal{
                    display: flex;
                }
                .unread{
                    position: absolute;
                    right: 4px;
                    top: 4px;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #f00;
                }
            }
            .exam_title{
                font-size: 16px;
                color: #000;
            }
            .exam_author{
                line-height: 2;
                color: #999;
            }
            .exam_detail{
                border-top: 1px solid #eee;
                padding-top: 15px;
                display: flex;
                .exam_type_icon{
                    position: relative;
                    width: 62px;
                    height: 90px;
                    img{
                        display: block;
                    }
                    p{
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        width: 100%;
                        color: #fff;
                        height: 28px;
                        line-height: 28px;
                        text-align: center;
                    }
                }
                .exam_description{
                    padding: 10px 20px;
                    p{
                        margin-bottom: 8px;
                        color: #666;
                        span{
                            font-weight: bold;
                            color: #000;
                        }
                    }
                }
            }
            .operation_btns{
                display: flex;
                padding-top: 10px;
                border-top: 1px solid #eee;
                button{
                    flex:1;
                }
            }
        }

    }
    .el-pagination{
        width: 100%;
        .el-pager li:not(.disabled).active{
            background-color: #00c59d;
        }
    }
    .primary_btn{
        background-color: #00c59d;
        color: #fff;
        padding: 14px 34px;
    }
    .gradient_btn{
        background-image: linear-gradient(to right, #7478e6, #00c59d);
        color: #fff;
        padding: 14px 34px;
    }
    .error_btn{
        padding: 14px 34px;
        background-color: #ff675c;
        color: #fff;
    
    }
    .danger_btn{
        padding: 14px 34px;
        background-color: #fff;
        border:1px solid #ff675c;
        color: #ff675c;
        font-weight: bold;
    }
    .el-button--small{
        padding: 10px 25px;
    }
    .el-button.is-disabled,.el-button.is-disabled:hover{
        background-color: #d7dbde;
        color: #fff;
    }
    .custom_header{
        display: flex;
        align-items: center;
        border-bottom: 2px solid #eee;
        padding: 20px 20px 14px;
        .title{
            font-size: 18px;
            margin: 0 10px;
            font-weight: bold;
        }
        .iconcuohao{
            position: absolute;
            right: 20px;
            top: 16px;
            font-size: 24px;
            color: #888;
            cursor: pointer;
        }
        .tabs{
            display: flex;
            margin-left: 40px;
            .tab_item{
                margin: 0 6px;
                border-radius: 4px;
                padding: 6px 24px;
                background-color: rgb(235,239,242);
                cursor: pointer;
                position: relative;
                &.active{
                    color: #fff;
                    background-color: rgb(0,197,157);
                }
                .unread{
                    position: absolute;
                    right: 10px;
                    top: 4px;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background: #f00;
                }
            }
        }
    }
    .custom_body{
        flex:1;
        padding: 20px;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .correct_exam_detail{
            display: flex;
            color: #333;
            padding-bottom: 20px;
            .exam_detail_left{
                flex: 4;
                display: flex;
                justify-content: space-between;
                padding: 0 20px;
                font-size: 16px;
                align-items: center;
            }
            .exam_detail_right{
                flex:6;
                display: flex;
                justify-content: flex-end;
                font-size: 16px;
                align-items: center;
                .exam_detail_item{
                    margin-right: 30px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    span{
                        margin: 0 8px;
                    }
                    .assignment_score{
                        color: #00c59d;
                        font-size: 40px;
                        font-weight: 100;
                    }
                }
                button{
                    width: 180px;
                }
            }
        }
    }
    .custom_container{
        flex:1;
        border-radius: 4px;
        padding: 24px;
        background-color: rgb(235,239,242);
        overflow: auto;
    }
}
</style>
