<template>
    <div class="report-detail-container">
        <!-- 顶部导航条 -->
        <div class="report-navbar">
            <button class="back-btn" @click="back">返回</button>
        </div>
        <div class="report-content">
            <!-- 左侧：医院抬头+B超图片+文字说明 -->
            <div class="report-left">
                <img class="ultrasound-img" :src="reportData.ultrasoundImage" alt="B超图片" />
            </div>
            <!-- 右侧：质控结论及建议 -->
            <div class="report-right">
                <div class="qc-title-row">
                    <span class="qc-title">{{ reportData.title }}</span>
                </div>
                <div class="qc-score-row">
                    <span class="qc-score">{{ reportData.score }}分</span>
                    <span class="qc-full">（满分{{ reportData.maxScore }}分）</span>
                    <span class="qc-status" :class="reportData.status == '合格' ? 'qualified' : 'unqualified'">{{ reportData.status }}</span>
                </div>

                <div class="qc-table-container">
                    <table class="qc-table">
                        <thead>
                            <tr>
                                <th class="qc-standard-col">质控标准</th>
                                <th class="qc-score-col">质控评分</th>
                                <th class="qc-analysis-col">质控分析</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 质控类别循环 -->
                            <template v-for="(category, categoryKey) in reportData.qcCategories">
                                <tr class="qc-category-row" :key="`category-${categoryKey}`">
                                    <td colspan="3">
                                        <div class="qc-category-header" @click="toggleCategory(categoryKey)">
                                            <i class="qc-expand-icon" :class="{ 'expanded': expandedCategories[categoryKey] }">▼</i>
                                            <span class="qc-category-title">{{ category.name }}</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr v-for="item in category.items" :key="item.id" v-show="expandedCategories[categoryKey]" class="qc-item-row">
                                    <td class="qc-item-name">{{ item.name }}</td>
                                    <td class="qc-item-score" :class="item.score >= 0 ? 'positive' : 'negative'">{{ item.score >= 0 ? '+' : '' }}{{ item.score }}</td>
                                    <td class="qc-item-analysis">{{ item.analysis }}</td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <div class="qc-suggestions">
                    <div class="qc-suggestions-title">{{ reportData.suggestions.title }}</div>
                    <ol class="qc-suggestions-list">
                        <li v-for="(suggestion, index) in reportData.suggestions.items" :key="index">{{ suggestion }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../../../lib/base"
// import service from
export default {
    mixins: [base],
    name: "ReportDetail",
    data() {
        return {
            expandedCategories: {
                diagnosis: true,
                birads: true
            },
            reportData: {
                title: "质控结论及建议",
                score: 70,
                maxScore: 100,
                status: "合格", // 合格 | 不合格
                ultrasoundImage: "/static/resource_pc/images/body_base_2.png",
                qcCategories: {
                    diagnosis: {
                        name: "诊断结论规范性",
                        items: [
                            {
                                id: 1,
                                name: "避免乳腺增生症诊断",
                                score: -10,
                                analysis: "质控要求避免\"诊断不出现\"乳腺增生\"或相关表述"
                            },
                            {
                                id: 2,
                                name: "特殊周期标注（青春期、哺乳期）",
                                score: -10,
                                analysis: "未标注特殊周期，哺乳期未描述导管/腺体情况"
                            }
                        ]
                    },
                    birads: {
                        name: "BI-RADS分类准确性",
                        items: [
                            {
                                id: 3,
                                name: "典型病变病理标注",
                                score: -5,
                                analysis: "质控要求避免\"诊断不出现\"乳腺增生\"或相关表述"
                            },
                            {
                                id: 4,
                                name: "BI-RADS 0类慎用原则",
                                score: -5,
                                analysis: "不符合条件诊断0类，应诊断类未诊断"
                            }
                        ]
                    }
                },
                suggestions: {
                    title: "修改意见",
                    items: [
                        "删除\"增生结节可能\"，改为\"双侧乳腺实性结节，符合BI-RADS 3类\"。",
                        "针对名额乳腺淋巴结的具体体位及层面见乳头径线（如\"右乳X点区，距乳头Xmm\"）。"
                    ]
                }
            }
        }
    },
    methods: {
        toggleCategory(category) {
            this.expandedCategories[category] = !this.expandedCategories[category];
        },
        // 可以添加更新报告数据的方法
        updateReportData(newData) {
            this.reportData = { ...this.reportData, ...newData };
        },
        // 计算总分的方法
        calculateTotalScore() {
            let totalScore = this.reportData.maxScore;
            Object.values(this.reportData.qcCategories).forEach(category => {
                category.items.forEach(item => {
                    totalScore += item.score;
                });
            });
            return totalScore;
        },
        // 根据分数自动判断状态
        getStatusByScore(score) {
            return score >= 60 ? '合格' : '不合格';
        }
    },
    computed: {
        // 计算当前总分
        currentTotalScore() {
            return this.calculateTotalScore();
        },
        // 计算当前状态
        currentStatus() {
            return this.getStatusByScore(this.currentTotalScore);
        }
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss";

.report-detail-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: absolute;
    z-index: 5;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding-top: 20px;
    padding-left: 20px;
    .report-navbar {
        flex-shrink: 0;
        width: 100%;
        display: flex;
        align-items: center;
        background: #fff;
        box-sizing: border-box;
        .back-btn {
            background: #ffffff;
            border: 1px solid #d8d8d8;
            border-radius: 5px;
            width: 120px;
            height: 32px;
            font-size: 16px;
            color: #000000;
            padding: 0 20px;
            cursor: pointer;
            outline: none;
        }
    }

    .report-content {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 32px 64px;
        gap: 40px;

        .report-left {
            flex: 3;
            min-width: 300px;
            height: 100%;
            overflow-y: hidden;
            flex-shrink: 0;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
            padding: 24px 16px;
            box-sizing: border-box;

            .ultrasound-img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                display: block;
                margin: 0 auto;
                border: 1px solid #e0e0e0;
            }
        }

        .report-right {
            flex: 7;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
            padding: 12px 24px 32px 40px;
            box-sizing: border-box;
            height: 100%;
            overflow-y: auto;
            .qc-title-row {
                margin-bottom: 12px;
            }
            .qc-title {
                font-size: 20px;
                color: #000000;
                font-weight: 700;
                margin-bottom: 0;
            }
            .qc-score-row {
                display: flex;
                align-items: baseline;
                gap: 12px;
                margin-bottom: 24px;
            }
            .qc-score {
                font-size: 18px;
                color: #222;
                font-weight: 700;
            }
            .qc-full {
                font-size: 16px;
                color: rgba(0, 0, 0, 0.46);
                font-weight: 700;
            }
            .qc-status {
                font-size: 14px;
                font-weight: 600;
                padding: 6px 12px;
                border-radius: 8px;
                margin-left: 16px;

                &.qualified {
                    background-color: #e7f7e7;
                    color: #52c41a;
                    border: 1px solid #95de64;
                }

                &.unqualified {
                    background-color: #fff2e8;
                    color: #fa8c16;
                    border: 1px solid #ffbb96;
                }
            }
            .qc-table-container {
                .qc-table {
                    width: 100%;
                    border-collapse: collapse;
                    border: 1px solid #e8e8e8;
                    font-size: 14px;

                    thead {
                        th {
                            background-color: #d7dfe1;
                            padding: 12px 16px;
                            text-align: center;
                            font-weight: 600;
                            color: #333;
                            border: 1px solid #d4dbe6;

                            &.qc-standard-col {
                                width: 35%;
                            }
                            &.qc-score-col {
                                width: 15%;
                            }
                            &.qc-analysis-col {
                                width: 50%;
                            }
                        }
                    }

                    tbody {
                        .qc-category-row {
                            td {
                                padding: 0;
                                border: 1px solid #e8e8e8;

                                .qc-category-header {
                                    padding: 12px 16px;
                                    background-color: #f5f8fa;
                                    cursor: pointer;
                                    display: flex;
                                    align-items: center;

                                    // &:hover {
                                    //     background-color: #f0f0f0;
                                    // }

                                    .qc-expand-icon {
                                        margin-right: 8px;
                                        transition: transform 0.3s ease;
                                        font-style: normal;
                                        font-size: 12px;
                                        color: #666;

                                        &.expanded {
                                            transform: rotate(180deg);
                                        }
                                    }

                                    .qc-category-title {
                                        font-weight: 600;
                                        color: #333;
                                    }
                                }
                            }
                        }

                        .qc-item-row {
                            td {
                                padding: 12px 16px;
                                border: 1px solid #e8e8e8;
                                vertical-align: top;

                                &.qc-item-name {
                                    color: #333;
                                    font-weight: 500;
                                    padding-left: 32px;
                                }

                                &.qc-item-score {
                                    text-align: center;
                                    font-weight: 600;

                                    &.negative {
                                        color: #ff4d4f;
                                    }

                                    &.positive {
                                        color: #52c41a;
                                    }
                                }

                                &.qc-item-analysis {
                                    color: #666;
                                    line-height: 1.5;
                                }
                            }
                        }
                    }
                }
            }

            .qc-suggestions {
                margin-top: 24px;

                .qc-suggestions-title {
                    font-size: 16px;
                    color: #333;
                    font-weight: 600;
                    margin-bottom: 12px;
                    background-color: #d7dfe1;
                    padding: 8px 12px;
                    // border-left: 4px solid #d7dfe1;
                }

                .qc-suggestions-list {
                    margin: 0;
                    padding-left: 20px;

                    li {
                        font-size: 14px;
                        color: #333;
                        line-height: 1.6;
                        margin-bottom: 8px;
                        padding: 8px 0;
                    }
                }
            }
        }
    }
}
</style>
