<template>
    <transition name="slide">
        <div class="groupsets_page second_level_page">
            <mrHeader>
                <template #title>
                    {{ lang.my_groupset }}
                </template>
                <template #right>
                    <i class="iconfont icon-plus1" @click="createGroupset"></i>
                </template>
            </mrHeader>
            <div class="groupsets_list">
                <van-collapse v-model="activeNames">
                    <van-collapse-item :title="lang.my_created_groupset" name="myGroupSet">
                        <div class="container" v-loading="!isMyGroupSetInit">
                            <div
                                class="groupset_item clearfix"
                                v-for="groupset of groupsetList"
                                :key="'groupset' + groupset.id"
                                @click="openGroupset(groupset)"
                            >
                                <mr-avatar
                                    :url="getLocalAvatar(groupset)"
                                    :origin_url="groupset.avatar"
                                    :showOnlineState="false"
                                    :key="groupset.avatar"
                                ></mr-avatar>
                                <p class="longwrap">{{ groupset.subject }}</p>
                            </div>
                            <van-empty :description="lang.no_data_txt" v-if="groupsetList.length === 0" />
                        </div>
                    </van-collapse-item>
                    <van-collapse-item :title="lang.my_manage_groupset" name="myManagerGroupSet">
                        <div class="container" v-loading="!isManagerGroupSetInit">
                            <div
                                class="groupset_item clearfix"
                                v-for="groupset of groupsetManagerList"
                                :key="'groupset' + groupset.id"
                                @click="openGroupset(groupset)"
                            >
                                <mr-avatar
                                    :url="getLocalAvatar(groupset)"
                                    :origin_url="groupset.avatar"
                                    :showOnlineState="false"
                                    :key="groupset.avatar"
                                ></mr-avatar>
                                <p class="longwrap">{{ groupset.subject }}</p>
                            </div>
                            <van-empty :description="lang.no_data_txt" v-if="groupsetManagerList.length === 0" />
                        </div>
                    </van-collapse-item>
                </van-collapse>
            </div>

            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import { Toast } from "vant";
import base from "../lib/base";
import groupsetTool from "../lib/groupsetTool";
import { getLocalAvatar } from "../lib/common_base";
import { Empty } from "vant";
import { Collapse, CollapseItem } from "vant";
export default {
    mixins: [base, groupsetTool],
    name: "groupsetsPage",
    components: {
        VanEmpty: Empty,
        VanCollapse: Collapse,
        VanCollapseItem: CollapseItem,
    },
    data() {
        return {
            getLocalAvatar,
            activeNames: ["myGroupSet", "myManagerGroupSet"],
        };
    },
    computed: {
        groupsetList() {
            return this.$store.state.groupset.list;
        },
        groupsetManagerList() {
            return this.$store.state.groupset.managerList;
        },
        isMyGroupSetInit() {
            return this.$store.state.groupset.isMyGroupSetInit;
        },
        isManagerGroupSetInit() {
            return this.$store.state.groupset.isManagerGroupSetInit;
        },
    },
    mounted() {

    },
    methods: {
        openGroupset(groupset) {
            this.$router.push(`/index/groupsets/detail/${groupset.id}`);
        },
        createGroupset() {
            this.$router.push(`/index/groupsets/edit_groupset/1/1`);
        },
    },
};
</script>
<style lang="scss" scoped>
.groupsets_page {
    height: 100%;
    background-color: #fff;
    .groupsets_list{
        flex:1;
        overflow-y:auto;
    }
    .icon-plus1 {
        font-size: 1.2rem;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        width: 2.4rem;
        text-align: center;
        height: 100%;
    }
    .container {
        height: calc(100% - 2.95rem);
    }
    .groupset_item {
        background-color: #fff;
        border-bottom: 1px solid #eee;
        padding: 0.3rem 0;
        margin: 0 1rem 0 0.7rem;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        & > p {
            line-height: 2rem;
            padding-left: 0.5rem;
            color: #333;
        }
    }
}
</style>
