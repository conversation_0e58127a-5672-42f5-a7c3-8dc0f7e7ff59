<template>
    <div class="live_management_container">
        <LiveRoom ref="liveManageLiveRoom"></LiveRoom>
        <LiveRoomWeb ref="liveManageLiveRoomWeb" v-if="isPCBrowser"></LiveRoomWeb>
        <div class="live_management">
            <div class="live_menu">
                <div class="menu-title">{{ lang.live }}</div>
                <el-menu :default-active="currentIndex" @select="selectMenu" class="live_menu-vertical">
                    <el-menu-item index="1">
                        <i class="el-icon-video-camera-solid"></i>
                        <span class="unread" v-if="liveCount">{{ liveCount }}</span>
                        <span slot="title">{{ lang.my_live }}</span>
                    </el-menu-item>
                    <!-- <el-menu-item index="2">
                        <i class="el-icon-document"></i>
                        <span slot="title">动态</span>
                    </el-menu-item> -->
                    <el-menu-item index="2">
                        <i class="el-icon-time"></i>
                        <!-- <span class="unread" v-if="liveCount">{{liveCount}}</span> -->
                        <span slot="title">{{ lang.live_playback }}</span>
                    </el-menu-item>
                    <!-- <el-menu-item index="4">
                        <i class="el-icon-setting"></i>
                        <span slot="title">我的直播</span>
                    </el-menu-item> -->
                </el-menu>
            </div>
            <div class="live_man_container">
                <template v-if="currentIndex === '1'">
                    <div class="live_content">
                        <div>
                            <el-button type="primary" class="add_btn" @click="showAddDialog">
                                {{ lang.booking_live }}
                            </el-button>
                        </div>
                        <div class="liev_man_body" ref="live_man_body">
                            <el-tabs
                                v-model="activeLiveStatus"
                                @tab-click="handleClickLiveStatusTab"
                                class="ai-chat-tabs"
                            >
                                <el-tab-pane :label="lang.my_booking_live" name="first">
                                    <div class="live_man_box" ref="live_creator_box">
                                        <no-Data v-if="liveCreatorList.length === 0" :text="lang.no_data_txt"></no-Data>
                                        <div class="live_flex_container">
                                            <div v-for="item in liveCreatorList" :key="item.id" class="live_flex_item">
                                                <el-card shadow="hover" class="live_card" ref="live_card">
                                                    <div class="live_image_box" @click="enterRealTimeVideo(item)">
                                                        <img
                                                            :src="limitImageSize(item.cover_image, 300)"
                                                            v-if="item.cover_image"
                                                        />
                                                        <img src="static/resource_pc/images/live_cover.png" v-else />
                                                    </div>
                                                    <div class="card-content">
                                                        <div class="card-top-text">
                                                            <el-popover
                                                                placement="bottom-start"
                                                                trigger="hover"
                                                                popper-class="toolbar_item card-text-tips"
                                                                class="card-title"
                                                            >
                                                                <span>{{ item.topic }}</span>
                                                                <div
                                                                    class="textEllipsis card-title-text"
                                                                    slot="reference"
                                                                >
                                                                    {{ item.topic }}
                                                                </div>
                                                            </el-popover>
                                                        </div>
                                                        <div class="card-top-status">
                                                            <span
                                                                class="card-tips-status0"
                                                                v-if="
                                                                    item.status ===
                                                                        systemConfig.liveManagement.waiting &&
                                                                    !item.is_will_start
                                                                "
                                                                >{{ lang.waiting }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status1"
                                                                v-if="
                                                                    item.status ===
                                                                        systemConfig.liveManagement.waiting &&
                                                                    item.is_will_start
                                                                "
                                                                >{{ lang.begin_in_minute }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status2"
                                                                v-if="
                                                                    item.status === systemConfig.liveManagement.starting
                                                                "
                                                                >{{ lang.live_broadcasting }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status3"
                                                                v-if="item.status === systemConfig.liveManagement.end"
                                                                >{{ lang.live_broadcast_end }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status3"
                                                                v-if="
                                                                    item.status === systemConfig.liveManagement.cancel
                                                                "
                                                                >{{ lang.live_broadcast_cancel }}</span
                                                            >
                                                        </div>
                                                        <el-popover
                                                            placement="bottom-start"
                                                            trigger="hover"
                                                            popper-class="toolbar_item card-text-tips"
                                                            :disabled="!item.description"
                                                        >
                                                            <p class="card-desc textEllipsis">{{ item.description }}</p>
                                                            <div slot="reference" class="textEllipsis card-desc">
                                                                {{ item.description }}
                                                            </div>
                                                        </el-popover>
                                                    </div>
                                                    <div class="card-date">
                                                        <span class="card-date-text"
                                                            ><i class="el-icon-date"></i
                                                            >{{ formatTime(item.start_ts) }}</span
                                                        >
                                                        <span class="card-date-space">-</span>
                                                        <span class="card-date-text">{{
                                                            formatTime(item.end_ts)
                                                        }}</span>
                                                    </div>
                                                    <div class="card-info textEllipsis">
                                                        {{ lang.moderator }}：{{ item.creatorInfo.nickname }}
                                                    </div>
                                                    <div class="card-tools">
                                                        <el-popover
                                                            placement="bottom"
                                                            trigger="hover"
                                                            popper-class="toolbar_item"
                                                            class="toolbar_item_span"
                                                            v-if="canHandleLive(item.status)"
                                                        >
                                                            <span>{{ lang.conference_seeding }}</span>
                                                            <el-button
                                                                circle
                                                                slot="reference"
                                                                @click="clickStartUltrasoundFromLive(item)"
                                                                ><i class="fl icon iconfont iconchaoshengbo"></i
                                                            ></el-button>
                                                        </el-popover>
                                                        <!-- <el-popover
                                                            placement="bottom"
                                                            trigger="hover"
                                                            popper-class="toolbar_item"
                                                            class="toolbar_item_span"
                                                            v-if="canHandleLive(item.status)"
                                                            >
                                                            <span>{{lang.desktop_direct_seeding}}</span>
                                                            <el-button circle slot="reference" @click="clickStartConferenceFromLive(item)"><i class="fl icon iconfont iconPC"></i></el-button>
                                                        </el-popover>
                                                        <el-popover
                                                            placement="bottom"
                                                            trigger="hover"
                                                            popper-class="toolbar_item"
                                                            class="toolbar_item_span"
                                                            v-if="canHandleLive(item.status)"
                                                            >
                                                            <span>{{lang.camera_direct_seeding}}</span>
                                                            <el-button circle slot="reference" @click="clickStartCameraFromLive(item)"><i class="fl icon iconfont iconjiankong"></i></el-button>
                                                        </el-popover> -->
                                                        <el-popover
                                                            placement="bottom"
                                                            trigger="hover"
                                                            popper-class="toolbar_item"
                                                            class="toolbar_item_span"
                                                            v-if="canHandleLive(item.status)"
                                                        >
                                                            <span>{{ lang.edit_txt }}</span>
                                                            <el-button
                                                                icon="el-icon-edit"
                                                                circle
                                                                @click="showAddDialog($event, item)"
                                                                slot="reference"
                                                            ></el-button>
                                                        </el-popover>
                                                        <el-popover
                                                            placement="bottom"
                                                            trigger="hover"
                                                            @show="getQrcode(item)"
                                                            class="toolbar_item_span"
                                                            popper-class="qrcode_tontent_popover"
                                                            v-if="canHandleLive(item.status)"
                                                        >
                                                            <el-button
                                                                icon="el-icon-share"
                                                                circle
                                                                slot="reference"
                                                            ></el-button>
                                                            <div class="qrcode_tontent">
                                                                <div class="qrcode" :id="'qrCode_' + item.id"></div>
                                                                <div class="qrcode-address">
                                                                    <p class="address-text">{{ getQrcodeAddress(item) }}</p>
                                                                    <el-button
                                                                        type="primary"
                                                                        size="mini"
                                                                        class="copy-btn"
                                                                        @click="copyLiveAddress(item)"
                                                                    >
                                                                        <i class="el-icon-document-copy"></i>
                                                                        {{ lang.copy_link }}
                                                                    </el-button>
                                                                </div>
                                                            </div>
                                                        </el-popover>
                                                        <el-popover
                                                            placement="bottom"
                                                            trigger="hover"
                                                            popper-class="toolbar_item"
                                                            class="toolbar_item_span"
                                                            v-if="canHandleLive(item.status)"
                                                        >
                                                            <span>{{ lang.action_delete_text }}</span>
                                                            <el-button
                                                                type="danger"
                                                                icon="el-icon-delete"
                                                                circle
                                                                slot="reference"
                                                                @click="deleteLiveInvite(item)"
                                                            ></el-button>
                                                        </el-popover>
                                                    </div>
                                                </el-card>
                                            </div>
                                        </div>
                                    </div>
                                    <el-pagination
                                        background
                                        layout="prev, pager, next"
                                        :current-page="liveCreatorForm.page"
                                        :page-size="liveCreatorForm.pageSize"
                                        :total="liveCreatorForm.total"
                                        @current-change="handleLiveCreatorFormCurrentChange"
                                        hide-on-single-page
                                        class="live_pagination"
                                    >
                                    </el-pagination>
                                </el-tab-pane>
                                <el-tab-pane :label="lang.available_live" name="second">
                                    <div class="live_man_box" ref="live_attendee_box">
                                        <no-Data v-if="liveAttendeeList.length == 0" :text="lang.no_data_txt"></no-Data>
                                        <el-row :gutter="20">
                                            <el-col
                                                v-for="item in liveAttendeeList"
                                                :key="item.id"
                                                :xs="24"
                                                :sm="24"
                                                :md="12"
                                                :lg="8"
                                                :xl="8"
                                                class="live_item"
                                            >
                                                <el-card shadow="hover" class="live_card">
                                                    <div class="live_image_box" @click="enterRealTimeVideo(item)">
                                                        <i
                                                            class="icon iconfont iconvideo_fill_light"
                                                            v-if="item.status === systemConfig.liveManagement.waiting"
                                                        ></i>
                                                        <img
                                                            :src="limitImageSize(item.cover_image, 300)"
                                                            v-if="item.cover_image"
                                                        />
                                                        <img src="static/resource_pc/images/live_cover.png" v-else />
                                                    </div>
                                                    <div class="card-content">
                                                        <div class="card-top-text">
                                                            <el-popover
                                                                placement="bottom-start"
                                                                trigger="hover"
                                                                popper-class="toolbar_item card-text-tips"
                                                                class="card-title"
                                                            >
                                                                <span>{{ item.topic }}</span>
                                                                <div
                                                                    class="textEllipsis card-title-text"
                                                                    slot="reference"
                                                                >
                                                                    {{ item.topic }}
                                                                </div>
                                                            </el-popover>
                                                        </div>
                                                        <div class="card-top-status">
                                                            <span
                                                                class="card-tips-status0"
                                                                v-if="
                                                                    item.status ===
                                                                        systemConfig.liveManagement.waiting &&
                                                                    !item.is_will_start
                                                                "
                                                                >{{ lang.waiting }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status1"
                                                                v-if="
                                                                    item.status ===
                                                                        systemConfig.liveManagement.waiting &&
                                                                    item.is_will_start
                                                                "
                                                                >{{ lang.begin_in_minute }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status2"
                                                                v-if="
                                                                    item.status === systemConfig.liveManagement.starting
                                                                "
                                                                >{{ lang.live_broadcasting }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status3"
                                                                v-if="item.status === systemConfig.liveManagement.end"
                                                                >{{ lang.live_broadcast_end }}</span
                                                            >
                                                            <span
                                                                class="card-tips-status3"
                                                                v-if="
                                                                    item.status === systemConfig.liveManagement.cancel
                                                                "
                                                                >{{ lang.live_broadcast_cancel }}</span
                                                            >
                                                        </div>
                                                        <el-popover
                                                            placement="bottom-start"
                                                            trigger="hover"
                                                            popper-class="toolbar_item card-text-tips"
                                                        >
                                                            <span>{{ item.description }}</span>
                                                            <div slot="reference" class="textEllipsis card-desc">
                                                                {{ item.description }}
                                                            </div>
                                                        </el-popover>
                                                    </div>
                                                    <div class="card-date">
                                                        <span class="card-date-text"
                                                            ><i class="el-icon-date"></i
                                                            >{{ formatTime(item.start_ts) }}</span
                                                        >
                                                        <span class="card-date-space">-</span>
                                                        <span class="card-date-text">{{
                                                            formatTime(item.end_ts)
                                                        }}</span>
                                                    </div>
                                                    <div class="card-info textEllipsis">
                                                        {{ lang.moderator }}：{{ item.creatorInfo.nickname }}
                                                    </div>
                                                    <div class="card-tools">
                                                        <el-popover
                                                            placement="bottom"
                                                            trigger="hover"
                                                            @show="getQrcode(item)"
                                                            class="toolbar_item_span"
                                                            popper-class="qrcode_tontent_popover"
                                                            v-if="canHandleLive(item.status)"
                                                        >
                                                            <el-button
                                                                icon="el-icon-share"
                                                                circle
                                                                slot="reference"
                                                            ></el-button>
                                                            <div class="qrcode_tontent">
                                                                <div class="qrcode" :id="'qrCode_' + item.id"></div>
                                                                <div class="qrcode-address">
                                                                    <p class="address-text">{{ getQrcodeAddress(item) }}</p>
                                                                    <el-button
                                                                        type="primary"
                                                                        size="mini"
                                                                        class="copy-btn"
                                                                        @click="copyLiveAddress(item)"
                                                                    >
                                                                        <i class="el-icon-document-copy"></i>
                                                                        {{ lang.copy_link }}
                                                                    </el-button>
                                                                </div>
                                                            </div>
                                                        </el-popover>
                                                    </div>
                                                </el-card>
                                            </el-col>
                                        </el-row>
                                    </div>
                                    <el-pagination
                                        background
                                        layout="prev, pager, next"
                                        :current-page="liveAttendeeForm.page"
                                        :page-size="liveAttendeeForm.pageSize"
                                        :total="liveAttendeeForm.total"
                                        @current-change="handleLiveAttendeeFormCurrentChange"
                                        hide-on-single-page
                                        class="live_pagination"
                                    >
                                    </el-pagination>
                                </el-tab-pane>
                            </el-tabs>
                        </div>
                    </div>
                </template>

                <template v-if="currentIndex === '2'">
                    <div class="live_content live_content_history">
                        <div class="live_content_title"><i class="el-icon-menu"></i>{{ lang.live_playback }}</div>
                        <div class="live_man_box" ref="live_history_box">
                            <no-Data v-if="liveHistoryList.length == 0" :text="lang.no_data_txt"></no-Data>
                            <el-row :gutter="20">
                                <el-col
                                    v-for="item in liveHistoryList"
                                    :key="item.id"
                                    :xs="24"
                                    :sm="24"
                                    :md="12"
                                    :lg="8"
                                    :xl="8"
                                    class="live_item"
                                >
                                    <el-card shadow="hover" class="live_card">
                                        <div class="live_image_box" @click="openHistoryGallery(item)">
                                            <i class="icon iconfont iconvideo_fill_light"></i>
                                            <img :src="limitImageSize(item.cover_image, 300)" v-if="item.cover_image" />
                                            <img src="static/resource_pc/images/live_cover.png" v-else />
                                        </div>
                                        <div class="card-info">
                                            <span class="memberNum">
                                                <!-- <i class="el-icon-view"></i>1000 -->
                                            </span>
                                            <span class="duringTime">{{
                                                getDateDiff(item.real_start_ts, item.real_end_ts)
                                            }}</span>
                                        </div>
                                        <div class="card-content">
                                            <div class="card-top-text">
                                                <el-popover
                                                    placement="bottom-start"
                                                    trigger="hover"
                                                    popper-class="toolbar_item card-text-tips"
                                                    class="card-title"
                                                >
                                                    <span>{{ item.topic }}</span>
                                                    <div class="textEllipsis card-title-text" slot="reference">
                                                        {{ item.topic }}
                                                    </div>
                                                </el-popover>
                                            </div>
                                            <el-popover
                                                placement="bottom-start"
                                                trigger="hover"
                                                popper-class="toolbar_item card-text-tips"
                                            >
                                                <span>{{ item.description }}</span>
                                                <div slot="reference" class="textEllipsis card-desc">
                                                    {{ item.description }}
                                                </div>
                                            </el-popover>
                                        </div>
                                        <div class="card-date">
                                            <span class="card-date-text"
                                                ><i class="el-icon-date"></i>{{ formatTime(item.real_start_ts) }}</span
                                            >
                                            <span class="card-date-space">-</span>
                                            <span class="card-date-text">{{ formatTime(item.real_end_ts) }}</span>
                                        </div>
                                        <div class="card-info textEllipsis">
                                            {{ lang.moderator }}：{{ item.creatorInfo.nickname }}
                                        </div>
                                    </el-card>
                                </el-col>
                            </el-row>
                        </div>

                        <el-pagination
                            background
                            layout="prev, pager, next"
                            :current-page="liveHistoryForm.page"
                            :page-size="liveHistoryForm.pageSize"
                            :total="liveHistoryForm.total"
                            @current-change="handleLiveHistoryFormCurrentChange"
                            hide-on-single-page
                            class="live_pagination"
                        >
                        </el-pagination>
                    </div>
                </template>
                <!-- <div class="live_man_form">
                    <el-form
                    ref="form"
                    :inline="true"
                    label-width="80px"
                    :model="queryForm"
                    @submit.native.prevent
                    >
                        <el-form-item label="直播名称">
                            <el-input v-model="queryForm.title" placeholder="名称" />
                        </el-form-item>
                        <el-form-item label="直播时间">
                            <el-date-picker
                            v-model="queryForm.datetime"
                            placeholder="选择日期时间"
                            type="datetime"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button
                            icon="el-icon-search"
                            native-type="submit"
                            type="primary"
                            @click="handleQuery"
                            >
                            查询
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div> -->
                <!-- <el-dialog
                    class="addDialog"
                    :title="addTitle"
                    :visible.sync="addDialogFormVisible"
                    :modal="false"
                    @closed="closeAddDialogFormVisible"
                    :close-on-click-modal="false"
                >

                    <el-form ref="addForm" label-width="120px" :model="addForm" :rules="rules">
                        <el-form-item :label="lang.live_theme" prop="title">
                            <el-input
                                v-model="addForm.title"
                                :maxlength="48"
                                @blur="addForm.title = $event.target.value.trim()"
                            />
                        </el-form-item>
                        <el-form-item :label="lang.cover_upload" prop="imageUrl">
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :on-change="handleAvatarChange"
                                :auto-upload="false"
                                :action="''">
                                <div v-if="addForm.imageUrl" class="avatar-uploader-image">
                                    <span class="el-upload-list__item-actions" @click.stop>
                                        <el-button icon="el-icon-delete" type="text" @click="handleAvatarRemove"></el-button>
                                    </span>
                                    <img :src="addForm.imageUrl" class="avatar">
                                </div>
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>

                                <el-progress type="circle" :percentage="addForm.percentage" v-if="addForm.progressShow"></el-progress>
                            </el-upload>
                            <span class="upload-tips" v-if="!addForm.imageUrl">{{lang.only_jpg_png}}</span>
                        </el-form-item>
                        <el-form-item :label="lang.reserved_conference_date_tip" prop="dateTime">
                            <el-date-picker
                                v-model="addForm.dateTime"
                                type="datetimerange"
                                :range-separator="lang.date_to"
                                :start-placeholder="lang.start_time"
                                :end-placeholder="lang.end_time"
                                format="yyyy-MM-dd HH:mm"
                                value-format="yyyy-MM-dd HH:mm"
                                :picker-options="pickerOptions"
                                style="width:100%">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item :label="lang.send_invitation">
                            <el-cascader
                                :placeholder="lang.search_invite_member"
                                :options="inviteList"
                                :props="{ multiple: true }"
                                filterable
                                style="width:100%"
                                v-model="addForm.inviteMemberList"
                                @remove-tag="removeInviteMemberTag"
                                id="inviteCascader">
                            </el-cascader>
                        </el-form-item>
                        <el-form-item :label="lang.describe" prop="description">
                            <el-input
                                v-model="addForm.description"
                                :maxlength="255"
                                @blur="addForm.description = $event.target.value.trim()"
                            />
                        </el-form-item>
                    </el-form>

                    <template #footer>
                        <el-button @click="closeAddDialogFormVisible">{{lang.cancel_btn}}</el-button>
                        <el-button type="primary" @click="addLiveSubmit" :disabled="submitLock">{{lang.confirm_txt}}</el-button>
                    </template>
                </el-dialog> -->
                <CommonDialog
                    :title="addTitle"
                    :show.sync="addDialogFormVisible"
                    width="800px"
                    height="auto"
                    @closed="closeAddDialogFormVisible"
                    @submit="addLiveSubmit"
                    :isSubmitting="submitLock"
                    :submitText="lang.confirm_txt"
                    append-to-body
                    class="add_live_management_dialog"
                >
                    <el-form
                        label-position="top"
                        ref="addForm"
                        label-width="80px"
                        :model="addForm"
                        class="edit_form"
                        :rules="rules"
                    >
                        <el-form-item :label="lang.reserved_conference_subject" prop="subject">
                            <el-input v-model="addForm.subject" maxlength="36" show-word-limit></el-input>
                        </el-form-item>
                        <el-form-item :label="lang.describe">
                            <el-input
                                v-model="addForm.description"
                                type="textarea"
                                :autosize="{ minRows: 2, maxRows: 4 }"
                                maxlength="70"
                                show-word-limit
                                clearable
                            ></el-input>
                        </el-form-item>
                        <el-form-item :label="lang.reserved_conference_date_tip" prop="dateTime">
                            <el-date-picker
                                v-model="addForm.dateTime"
                                type="datetimerange"
                                :range-separator="lang.date_to"
                                :start-placeholder="lang.start_time"
                                :end-placeholder="lang.end_time"
                                format="yyyy-MM-dd HH:mm"
                                value-format="yyyy-MM-dd HH:mm"
                                :picker-options="pickerOptions"
                                style="width: 100%"
                            >
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item :label="lang.send_invitation">
                            <el-cascader
                                ref="cascader"
                                :placeholder="lang.search_invite_member"
                                :options="inviteList"
                                :props="{ multiple: true }"
                                filterable
                                style="width: 100%"
                                v-model="addForm.inviteMemberList"
                                @remove-tag="removeInviteMemberTag"
                                id="inviteCascader"
                                @change="handleInviteMemberChange"
                            >
                            </el-cascader>
                        </el-form-item>
                        <el-form-item :label="lang.cover_upload" prop="imageUrl">
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :on-change="handleAvatarChange"
                                :auto-upload="false"
                                :action="''"
                            >
                                <div v-if="addForm.imageUrl" class="avatar-uploader-image">
                                    <span class="el-upload-list__item-actions">
                                        <el-button
                                            icon="el-icon-delete"
                                            type="text"
                                            @click.stop="handleAvatarRemove"
                                        ></el-button>
                                        <el-button
                                            icon="el-icon-edit"
                                            type="text"
                                            @click.stop="openCropperModal"
                                        ></el-button>
                                    </span>
                                    <img :src="addForm.imageUrl" class="avatar" />
                                </div>
                                <i v-else class="el-icon-plus avatar-uploader-icon" slot="trigger"></i>
                                <el-progress
                                    type="circle"
                                    :percentage="addForm.percentage"
                                    v-if="addForm.progressShow"
                                ></el-progress>
                            </el-upload>
                            <span class="upload-tips" v-if="!addForm.imageUrl">{{ lang.only_jpg_png }}</span>
                        </el-form-item>
                    </el-form>
                </CommonDialog>
                <CropperImage
                    :show.sync="cropperVisible"
                    v-if="cropperVisible"
                    @afterCropper="afterCropper"
                    ref="cropper"
                >
                </CropperImage>
                <!-- 引入 baseGallery 组件 -->
                <BaseGallery
                    ref="baseGallery"
                    :loading="galleryLoading"
                >
                </BaseGallery>
            </div>
        </div>
    </div>
</template>
<script>
import base from "../../lib/base";
import sendMessage from "../../lib/sendMessage";
import Tool from "@/common/tool.js";
import noData from "../../MRComponents/noData.vue";
import { cloneDeep } from "lodash";
import { joinRoom, resetRequestJoining } from "../../lib/liveConference";
import { getDateDiff, getMoreResourceList } from "../../lib/common_base";
import CommonDialog from "../../MRComponents/commonDialog.vue";
import CropperImage from "../../MRComponents/cropperImage.vue";
import { uploadFile } from "@/common/oss/index.js";
import moment from "moment";
import LiveRoom from "../../components/live/liveRoom";
import LiveRoomWeb from "../../components/live/liveRoomWeb";
import BaseGallery from "../../MRComponents/baseGallery.vue";
export default {
    mixins: [base, sendMessage],
    name: "LiveManagement",
    components: {
        noData,
        CommonDialog,
        CropperImage,
        LiveRoom,
        LiveRoomWeb,
        BaseGallery,
    },
    data() {
        return {
            joinRoom,
            getDateDiff,
            getMoreResourceList,
            liveCreatorForm: {
                page: 1,
                pageSize: 12,
                total: 0,
            },
            liveAttendeeForm: {
                page: 1,
                pageSize: 12,
                total: 0,
            },
            liveHistoryForm: {
                page: 1,
                pageSize: 12,
                total: 0,
            },
            // liveHistoryList:[
            //     {
            //         title:'直播主题',
            //         desc:'如何成为一个大佬',
            //         group_id:3062
            //     }
            // ],
            addDialogFormVisible: false,
            addTitle: "",
            addForm: {
                subject: "",
                dateTime: [],
                groupArr: [],
                friendArr: [],
                groupSetArr: [],
                description: "",
                imageUrl: "",
                inviteMemberList: [],
                currentInviteMemberList: [],
                file: null,
                cover_image: "",
                live_id: "",
                originDateTime: [],
                percentage: 0,
                progressShow: false,
                originCoverImage: "",
            },
            rules: {
                // subject: [{ required: true, trigger: 'blur', message: this.lang.enterLiveName }],
                // dateTime: [{ required: true, trigger: 'blur', message: '请选择直播时间' }],
                // description: [{ required: true, trigger: 'blur', message: '请输入直播描述' }],
                // imageUrl: [{ required: true, trigger: 'blur', message: '请上传直播封面' }],
            },
            currentIndex: "1",
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 8.64e7;
                },
            },
            inviteList: [],
            conversation: null,
            cid: 0, //当前操作的直播id
            currentAction: "", //当前操作行为
            activeLiveStatus: "first",
            liveCreatorList: [],
            liveAttendeeList: [],
            liveHistoryList: [],
            submitLock: false, //提交锁
            handleItem: null, //当前操作项
            cropperVisible: false,
            currentGalleryList: [],
            galleryLoading: false,
            galleryUpdateTimer: null,
        };
    },
    computed: {
        ConversationConfig() {
            return this.$store.state.systemConfig.ConversationConfig;
        },
        friendList() {
            return this.$store.state.friendList.list;
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        groupList() {
            return this.$store.state.groupList;
        },
        groupsetList() {
            return this.$store.state.groupset.list;
        },
        groupPublicState() {
            return this.$store.state.systemConfig.groupPublicState;
        },
        isWorkStation() {
            return this.isCef && Tool.ifAppWorkstationClientType(this.systemConfig.clientType);
        },
        liveCount() {
            if (Object.keys(this.$store.state.notifications.liveCount).length > 0) {
                return (
                    this.$store.state.notifications.liveCount.startCount +
                    this.$store.state.notifications.liveCount.waittingCount
                );
            } else {
                return 0;
            }
        },
        LiveConferenceData() {
            return (
                (this.$store.state.liveConference[this.cid] &&
                    this.$store.state.liveConference[this.cid].LiveConferenceData) ||
                {}
            );
        },
    },
    created() {
        this.initCreatorList();
        this.initAttendeeList();
        this.autoOpenLive();
        this.updateLiveCount();
    },
    mounted() {
        this.rules = {
            subject: [{ required: true, trigger: "blur", message: this.lang.enter_live_name }],
            dateTime: [
                // { required: true, trigger: 'change', message: this.lang.select_live_time },
                { validator: this.validateDate, trigger: "change" },
            ],
            // description: [{ required: true, trigger: 'blur', message: this.lang.enter_live_des }],
            // imageUrl: [{ required: false, trigger: 'blur', message: this.lang.upload_live_cover }],
        };
        this.$root.eventBus
            .$off("refreshLiveManagementList")
            .$on("refreshLiveManagementList", this.refreshLiveManagementList);
        this.$root.eventBus
            .$off("HandleNotifyJoinChannelAuxFromLiveManagement")
            .$on("HandleNotifyJoinChannelAuxFromLiveManagement", this.HandleNotifyJoinChannelAuxFromLiveManagement);
        this.$root.eventBus
            .$off("HandleNotifyLeaveChannelAuxFromLiveManagement")
            .$on("HandleNotifyLeaveChannelAuxFromLiveManagement", this.HandleNotifyLeaveChannelAuxFromLiveManagement);
        this.$root.eventBus
            .$off("HandleDisconnectAuxFromLiveManagement")
            .$on("HandleDisconnectAuxFromLiveManagement", this.HandleDisconnectAuxFromLiveManagement);
    },
    beforeDestroy() {
        // 清理画廊更新定时器
        if (this.galleryUpdateTimer) {
            clearInterval(this.galleryUpdateTimer);
            this.galleryUpdateTimer = null;
        }
    },
    methods: {
        validateDate(rule, value, callback) {
            if (!value) {
                callback(new Error(this.lang.select_live_time));
            } else {
                if (this.addTitle === this.lang.edit_live && String(value) === String(this.addForm.originDateTime)) {
                    // 编辑模式时，若时间和原时间保持一致 不做校验
                    callback();
                } else {
                    console.log(moment(value[0], "YYYY-MM-DD hh:mm").valueOf(), 111);

                    if (moment(value[0], "YYYY-MM-DD hh:mm").valueOf() <= new Date().getTime()) {
                        callback(new Error(this.lang.reserved_starttime_illegal));
                    } else if (
                        moment(value[0], "YYYY-MM-DD hh:mm").valueOf() >= moment(value[1], "YYYY-MM-DD hh:mm").valueOf()
                    ) {
                        callback(new Error(this.lang.end_time_greater_tips));
                    }
                }
                callback();
            }
        },
        handleClickLiveStatusTab(val) {
            if (this.$refs.live_creator_box) {
                this.$refs.live_creator_box.scrollTo(0, 0);
            }
            if (this.$refs.live_attendee_box) {
                this.$refs.live_attendee_box.scrollTo(0, 0);
            }
        },
        refreshLiveManagementList() {
            this.handleLiveCreatorFormCurrentChange();
            this.handleLiveAttendeeFormCurrentChange();
        },
        autoOpenLive() {
            const live_info = this.$route.query.live_info && JSON.parse(this.$route.query.live_info);
            if (!live_info) {
                return;
            }
            let obj = {
                id: live_info.live_id,
                creator_id: live_info.creator_id,
            };
            this.handleItem = obj;
            live_info.id = live_info.live_id;
            window.main_screen.getLiveInfoById({ live_id: live_info.live_id }, (liveRes) => {
                if (!liveRes.error_code) {
                    window.main_screen.checkJoinLiveStatus({ live_id: live_info.live_id }, async (joinRes) => {
                        if (!joinRes.error_code) {
                            if (!joinRes.data.enterAuth) {
                                this.$message.error(this.lang.no_permission_to_open_room);
                                return;
                            }
                            if (!joinRes.data.groupEnterStatus) {
                                //不在群内
                                await this.requestAddLiveGroup(liveRes.data); // 先加入直播间，再进入会话
                            }
                            if (this.canHandleLive(liveRes.data.status)) {
                                //预约中直播中
                                // this.currentAction = 'enterRealTimeVideo'

                                if (liveRes.data.creator_id === this.user.uid) {
                                    //本人发起的直播 不进去
                                    return;
                                }

                                this.openConversation(liveRes.data.group_id, 10, null, (is_suc, conversation) => {
                                    if (!is_suc) {
                                        return;
                                    }
                                    this.cid = conversation.id;
                                    this.conversation = this.conversationList[this.cid];
                                    if (
                                        this.getConferenceState(this.cid) &&
                                        liveRes.data.status === this.systemConfig.liveManagement.starting
                                    ) {
                                        this.liveManageStartJoinRoom({ main: 0, aux: 1, isSender: 0 }, this.cid);
                                    }
                                });
                            } else if (liveRes.data.status === this.systemConfig.liveManagement.end) {
                                //直播结束，直接打开直播回看
                                this.selectMenu("2");
                                this.openGallery(liveRes.data);
                            } else {
                                this.openGallery(liveRes.data);
                            }
                        } else {
                            this.$message.error(joinRes.error_msg);
                        }
                    });
                } else {
                    this.$message.error(liveRes.error_msg);
                }
            });
        },
        async initCreatorList() {
            this.liveCreatorForm.page = 1;
            const res = await this.getLiveCreatorList();
            this.liveCreatorList = res.data;
            this.liveCreatorForm.total = res.total;
        },
        async initAttendeeList() {
            this.liveAttendeeForm.page = 1;
            const res = await this.getLiveAttendeeList();
            this.liveAttendeeList = res.data;
            this.liveAttendeeForm.total = res.total;
        },
        async initHistoryList() {
            this.liveHistoryForm.page = 1;
            const res = await this.getLiveHistoryList();
            this.liveHistoryList = res.data;
            this.liveHistoryForm.total = res.total;
        },
        getLiveCreatorList() {
            return new Promise((resolve, reject) => {
                window.main_screen.getLiveCreatorList(this.liveCreatorForm, (res) => {
                    if (!res.error_code) {
                        if (this.$refs.live_creator_box) {
                            this.$refs.live_creator_box.scrollTo(0, 0);
                        }
                        console.log("getLiveCreatorList", res.data);
                        resolve(res.data);
                    } else {
                        this.$message.error(res.error_msg);
                        reject(false);
                    }
                });
            });
        },
        getLiveAttendeeList() {
            return new Promise((resolve, reject) => {
                window.main_screen.getLiveAttendeeList(this.liveAttendeeForm, (res) => {
                    if (!res.error_code) {
                        if (this.$refs.live_attendee_box) {
                            this.$refs.live_attendee_box.scrollTo(0, 0);
                        }
                        console.log("getLiveAttendeeList", res.data);
                        resolve(res.data);
                    } else {
                        this.$message.error(res.error_msg);
                        reject(false);
                    }
                });
            });
        },
        getLiveHistoryList() {
            return new Promise((resolve, reject) => {
                window.main_screen.getLiveHistoryList(this.liveHistoryForm, (res) => {
                    if (!res.error_code) {
                        if (this.$refs.live_history_box) {
                            this.$refs.live_history_box.scrollTo(0, 0);
                        }
                        resolve(res.data);
                    } else {
                        this.$message.error(res.error_msg);
                        reject(false);
                    }
                });
            });
        },
        async handleLiveCreatorFormCurrentChange(val) {
            this.liveCreatorForm.page = val;
            const res = await this.getLiveCreatorList();
            this.liveCreatorList = res.data;
        },
        async handleLiveAttendeeFormCurrentChange(val) {
            this.liveAttendeeForm.page = val;
            const res = await this.getLiveAttendeeList();
            this.liveAttendeeList = res.data;
        },
        async handleLiveHistoryFormCurrentChange(val) {
            this.liveHistoryForm.page = val;
            const res = await this.getLiveHistoryList();
            this.liveHistoryList = res.data;
        },
        showAddDialog(event, row) {
            this.inviteList = cloneDeep(this.handleConcatList(this.friendList, this.groupList, this.groupsetList));
            if (!row) {
                this.addTitle = this.lang.booking_live;
                this.addForm.subject = this.setDefaultSubject();
                this.setDefaultTime();
            } else {
                this.addTitle = this.lang.edit_live;
                this.addForm.subject = row.topic;
                this.addForm.dateTime = [this.formatTime(row.start_ts), this.formatTime(row.end_ts)];
                this.addForm.originDateTime = [this.formatTime(row.start_ts), this.formatTime(row.end_ts)];
                this.addForm.cover_image = row.cover_image;
                this.addForm.description = row.description;
                this.addForm.imageUrl = row.cover_image;
                this.addForm.inviteMemberList = [];
                this.addForm.live_id = row.id;
                row.inviterList.forEach((item) => {
                    let type = "";
                    if (item.rc_type === 1) {
                        //好友
                        type = "friend";
                        this.inviteList[0].children.map((element) => {
                            if (item.rc_id === Number(element.value)) {
                                element.disabled = true;
                            }
                        });
                    } else if (item.rc_type === 2) {
                        //群组
                        type = "group";
                        this.inviteList[1].children.map((element) => {
                            if (item.rc_id === Number(element.value)) {
                                element.disabled = true;
                            }
                        });
                    }
                    this.addForm.inviteMemberList.push([type, item.rc_id]);
                    this.addForm.currentInviteMemberList.push([type, item.rc_id]);
                });
            }
            this.addDialogFormVisible = true;
            this.$nextTick(() => {
                document.querySelector("#inviteCascader .el-input__inner").setAttribute("placeholder", ""); // 解决el-cascader组件 placeholder重叠问题
            });
        },
        closeAddDialogFormVisible() {
            this.$refs["addForm"].resetFields();
            this.addForm = {
                subject: "",
                dateTime: [],
                groupArr: [],
                friendArr: [],
                groupSetArr: [],
                description: "",
                imageUrl: "",
                inviteMemberList: [],
                currentInviteMemberList: [],
                file: null,
                cover_image: "",
                live_id: "",
                originDateTime: [],
                percentage: 0,
                progressShow: false,
            };
            this.addDialogFormVisible = false;
        },
        setDefaultTime() {
            //设置默认日期为今天最近一小时
            let today = new Date();
            // this.reservedDate=today;
            let year = today.getFullYear();
            let month = today.getMonth();
            let day = today.getDate();
            let hours = today.getHours();
            let minutes = today.getMinutes();
            if (minutes >= 30) {
                this.addForm.dateTime.push(this.formatTime(new Date(year, month, day, hours + 1, 0)));
                this.addForm.dateTime.push(this.formatTime(new Date(year, month, day, hours + 2, 0)));
            } else {
                this.addForm.dateTime.push(this.formatTime(new Date(year, month, day, hours, 30)));
                this.addForm.dateTime.push(this.formatTime(new Date(year, month, day, hours + 1, 30)));
            }
        },
        async uploadLiveCoverImage() {
            return new Promise(async (resolve, reject) => {
                let file = this.addForm.file;
                let form = new FormData();
                form.append("file", file.raw);
                let uploadConfig = this.systemConfig.serverInfo.file_upload_config;
                let format = Tool.getFileType(file.name);
                let filePath = `${uploadConfig.subDir.liveCoverImage}/${new Date().getTime()}.${format}`;
                this.addForm.progressShow = true;
                this.addForm.percentage = 0;

                uploadFile({
                    bucket: uploadConfig.ossInfo.bucket,
                    filePath: filePath,
                    file: file.raw,
                    callback: (event, data) => {
                        if ("complete" == event) {
                            this.addForm.cover_image = filePath;
                            setTimeout(() => {
                                this.addForm.progressShow = false;
                                resolve(true);
                            }, 600);
                        } else if ("progress" === event) {
                            // this.$set(this.addForm,'percentage',data)
                            this.addForm.percentage = data;
                        } else if ("error" == event) {
                            this.addForm.progressShow = false;
                            this.addForm.percentage = 0;
                            reject(false);
                        }
                    },
                });
            });
        },
        addLiveSubmit() {
            this.$refs["addForm"].validate(async (valid) => {
                if (valid) {
                    if (this.addForm.progressShow) {
                        this.$message.error(this.lang.file_in_progress);
                        return;
                    }
                    // 表单验证通过后立即设置loading状态
                    this.submitLock = true;
                    if (this.addForm.imageUrl.includes("blob")) {
                        try {
                            await this.uploadLiveCoverImage();
                        } catch (error) {
                            console.error(error);
                            this.submitLock = false;
                            return;
                        }
                    }
                    let gid_list = [];
                    let uid_list = [];
                    this.addForm.inviteMemberList.forEach((element) => {
                        if (element[0] === "group") {
                            gid_list.push(Number(element[1]));
                        }
                        if (element[0] === "friend") {
                            uid_list.push(Number(element[1]));
                        }
                    });
                    let params = {
                        topic: this.addForm.subject,
                        description: this.addForm.description,
                        cover_image: this.addForm.cover_image,
                        time_str: `${moment(this.addForm.dateTime[0], "YYYY-MM-DD hh:mm").format()} - ${moment(
                            this.addForm.dateTime[1],
                            "YYYY-MM-DD hh:mm"
                        ).format()}`,
                        gid_list,
                        uid_list,
                    };
                    if (this.addTitle === this.lang.edit_live) {
                        //编辑模式
                        params.live_id = this.addForm.live_id;
                        console.log(params);
                        window.main_screen.updateLiveBroadcast(params, (res) => {
                            if (!res.error_code) {
                                this.$message.success(this.lang.update_success_text);
                                this.closeAddDialogFormVisible();
                                this.addDialogFormVisible = false;
                                this.handleLiveCreatorFormCurrentChange(this.liveCreatorForm.page);
                            } else {
                                // this.$message.error(res.error_msg)
                            }
                            setTimeout(() => {
                                this.submitLock = false;
                            }, 300);
                        });
                    } else {
                        //新增

                        window.main_screen.createLiveBroadcast(params, (res) => {
                            if (!res.error_code) {
                                this.$message.success(this.lang.operate_success);
                                this.closeAddDialogFormVisible();
                                this.addDialogFormVisible = false;
                                this.initCreatorList();
                                if (uid_list.length === 0 && gid_list.length === 0) {
                                    this.$root.eventBus.$emit("updateLiveCount", { needRefresh: false });
                                }
                            } else {
                                console.error(res);
                                // this.$message.error(res.error_msg)
                            }
                            setTimeout(() => {
                                this.submitLock = false;
                            }, 300);
                        });
                    }
                } else {
                    this.$nextTick(() => {
                        let dom = document.querySelector(".el-form-item__error");
                        console.log(dom);
                        dom.scrollIntoView && dom.scrollIntoView(true);
                    });
                }
            });
        },
        async liveManageStartJoinRoom({ main = 0, aux = 0, isSender = 0, from = "live_training" }, cid) {
            console.log('liveManageStartJoinRoom', this.isPCBrowser);
            const defaultPushWay = window.localStorage.getItem("defaultPushWay");
            this.$nextTick(() => {
                if (this.isPCBrowser) {
                    this.$refs.liveManageLiveRoomWeb.startJoinRoom({
                        main,
                        aux,
                        isSender,
                        videoSource: defaultPushWay ? defaultPushWay : "doppler",
                        from,
                        cid,
                    });
                } else {
                    this.$refs.liveManageLiveRoom.startJoinRoom({
                        main,
                        aux,
                        isSender,
                        videoSource: defaultPushWay ? defaultPushWay : "doppler",
                        from,
                        cid,
                    });
                }
            });

            this.$store.commit("liveConference/updateConferenceState", {
                cid: this.cid,
                obj: {
                    liveAddress: this.getLiveAddress(),
                },
            });
        },
        preHandleRecordMode(conversation) {
            return new Promise((resolve, reject) => {
                let data = {
                    gid: conversation.id,
                    record_mode: 1,
                };
                let timer = setTimeout(() => {
                    reject("preHandleRecordMode time out");
                }, 10000);
                this.conversationList[conversation.id].socket.emit("edit_record_mode", data, (is_succ, data) => {
                    if (is_succ) {
                        //修改成功
                        this.$store.commit("conversationList/updateIsLiveRecord", {
                            cid: conversation.id,
                            record_mode: 1,
                        });
                        resolve(true);
                    } else {
                        //修改失败
                        reject(false);
                    }
                    clearTimeout(timer);
                    timer = null;
                });
            });
        },
        clickStartUltrasoundFromLive(item) {
            // if(Tool.checkAppClient('Browser')){
            //     return this.$message.error(this.lang.use_app_tip)
            // }
            this.openConversation(item.group_id, 10, null, async (is_suc, conversation) => {
                if (!is_suc) {
                    return;
                }
                this.cid = conversation.id;
                this.conversation = this.conversationList[this.cid];
                this.handleItem = item;
                await this.preHandleRecordMode(conversation);
                await this.liveManageStartJoinRoom({ main: 1, aux: 1, isSender: 1 }, this.cid);
                setTimeout(() => {
                    this.handleLiveCreatorFormCurrentChange();
                }, 2000);
            });
        },
        async selectMenu(index) {
            this.currentIndex = index;
            if (this.currentIndex === "2") {
                await this.initHistoryList();
            }
        },
        handleAvatarChange(file) {
            if (this.beforeAvatarUpload(file)) {
                this.addForm.imageUrl = URL.createObjectURL(file.raw);
                this.addForm.file = file;
                if (file.status === "ready") {
                }
            }
        },
        handleAvatarRemove() {
            this.addForm.imageUrl = "";
            this.addForm.cover_image = "";
            this.addForm.file = null;
        },
        beforeAvatarUpload(file) {
            const isJPG = file.raw.type === "image/jpeg";
            const isPNG = file.raw.type === "image/png";
            const isLt10M = file.raw.size / 1024 / 1024 < 10;

            if (!isJPG && !isPNG) {
                this.$message.error(this.lang.upload_forbidden_file_type_text);
                return false;
            }
            if (!isLt10M) {
                this.$message.error(`${this.lang.upload_max_text} 10M`);
                return false;
            }
            return true;
            // return (isJPG || isPNG) && isLt10M;
        },
        handleConcatList(oFriendList, oGroupList, oGroupsetList) {
            let friendList = cloneDeep(oFriendList);
            friendList.map((item) => {
                item.value = item.id;
                if (this.remarkMap[item.id]) {
                    item.label = `${this.remarkMap[item.id]}(${item.nickname})`;
                } else {
                    item.label = item.nickname;
                }
            });
            let groupList = cloneDeep(oGroupList);
            groupList.map((item) => {
                item.value = item.id;
                item.label = item.subject;
            });
            let groupSetList = oGroupsetList;
            groupSetList.map((item) => {
                item.value = item.id;
                item.label = item.subject;
            });
            let inviteList = [
                {
                    value: "friend",
                    label: this.lang.friend,
                    children: friendList,
                },
                {
                    value: "group",
                    label: this.lang.group,
                    children: groupList,
                },
                // {
                //     value: 'groupSet',
                //     label: this.lang.groupset_text,
                //     children:groupSetList
                // }
            ];
            return inviteList;
        },
        deleteLiveInvite(item) {
            this.$MessageBox.alert(this.lang.dissolve_live, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_txt,
                callback: (action) => {
                    if (action === "confirm") {
                        window.main_screen.cancelLiveBroadcast({ live_id: item.id }, (res) => {
                            if (!res.error_code) {
                                this.handleLiveCreatorFormCurrentChange(this.liveCreatorForm.page);
                                this.$root.eventBus.$emit("updateLiveCount", { needRefresh: false });
                            } else {
                                // this.$message.error(res.error_msg)
                                console.error(res);
                            }
                        });
                    }
                },
            });
        },
        enterRealTimeVideo(item) {
            this.handleItem = item;
            window.main_screen.checkJoinLiveStatus({ live_id: item.id }, async (res) => {
                console.log(res);
                if (!res.error_code) {
                    if (!res.data.enterAuth) {
                        this.$message.error(this.lang.no_permission_to_open_room);
                        return;
                    }
                    if (!res.data.groupEnterStatus) {
                        //不在群内
                        await this.requestAddLiveGroup(item); // 先加入直播间，再进入会话
                    }
                    if (this.canHandleLive(item.status)) {
                        if (item.creator_id === this.user.uid) {
                            //本人发起的直播 不进去
                            return;
                        }
                        this.openConversation(item.group_id, 10, null, (is_suc, conversation) => {
                            if (!is_suc) {
                                return;
                            }
                            this.cid = conversation.id;
                            this.conversation = this.conversationList[this.cid];
                            this.liveManageStartJoinRoom({ main: 0, aux: 1, isSender: 0 }, this.cid);
                        });
                    } else {
                        this.openGallery(item);
                    }
                } else {
                    this.$message.error(res.error_msg);
                }
            });
        },
        handleEnterRealTimeVideo() {
            var cid = this.cid;
            this.$nextTick(() => {
                this.galleryLoading = true;
                this.openConversation(cid, 10, null, async () => {
                    try {
                        if (this.conversationList[cid] && this.conversationList[cid].galleryObj.gallery_list.length > 0) {
                            this.currentGalleryList = this.conversationList[cid].galleryObj.gallery_list;
                        } else {
                            this.currentGalleryList = await this.getMoreResourceList(0, cid);
                        }
                        if (this.currentGalleryList.length === 0) {
                            this.galleryLoading = false;
                            return;
                        }

                        // 转换数据格式以适配 baseGallery 组件
                        const galleryFiles = this.currentGalleryList.map(item => {
                            return {
                                url: item.ultrasound_url,
                                thumbnail: item.coverUrl,
                            };
                        });
                        this.galleryLoading = false;

                        // 使用 baseGallery 组件打开画廊
                        this.$refs.baseGallery.openGallery(galleryFiles, 0);

                    } catch (error) {
                        console.error('打开画廊失败:', error);
                        this.galleryLoading = false;
                        this.$message.error('打开画廊失败');
                    }
                });
            });
        },
        deduplicateArrayByResourceId(array) {
            const seen = new Set();
            const deduplicatedArray = [];

            for (let i = 0; i < array.length; i++) {
                const item = array[i];
                const resourceId = item.resource_id;

                if (!seen.has(resourceId)) {
                    seen.add(resourceId);
                    deduplicatedArray.push(item);
                }
            }

            return deduplicatedArray;
        },
        requestAddLiveGroup(item) {
            return new Promise((resolve, reject) => {
                let is_public = item.is_public;
                let gid = item.group_id;
                window.main_screen.applyJoinGroup(
                    {
                        mark: "",
                        gid: gid,
                        inviterID: 0,
                        source: 1,
                    },
                    (res) => {
                        if (res.error_code == 0) {
                            resolve(true);
                        } else {
                            reject(false);
                        }
                    }
                );
            });
        },
        openHistoryGallery(item) {
            window.main_screen.checkJoinLiveStatus({ live_id: item.id }, async (res) => {
                if (!res.error_code) {
                    if (!res.data.enterAuth) {
                        this.$message.error(this.lang.no_permission_to_open_room);
                        return;
                    }
                    if (!res.data.groupEnterStatus) {
                        //不在群内
                        await this.requestAddLiveGroup(item); // 先加入直播间，再进入会话
                    }
                    this.openGallery(item);
                } else {
                    this.$message.error(res.error_msg);
                }
            });
        },
        openGallery(file) {
            console.log(file.group_id);
            this.$nextTick(() => {
                let cid = file.group_id;
                this.cid = cid;
                this.galleryLoading = true;
                this.openConversation(cid, 10, null, async () => {
                    try {
                        if (this.conversationList[cid] && this.conversationList[cid].galleryObj.gallery_list.length > 0) {
                            this.currentGalleryList = this.conversationList[cid].galleryObj.gallery_list;
                        } else {
                            this.currentGalleryList = await this.getMoreResourceList(0, cid);
                        }
                        if (this.currentGalleryList.length === 0) {
                            this.$message.error(this.lang.live_has_no_playback);
                            return;
                        }

                        // 转换数据格式以适配 baseGallery 组件
                        const galleryFiles = this.currentGalleryList.map(item => {
                            return {
                                url: item.ultrasound_url,
                                thumbnail: item.coverUrl,
                            };
                        });
                        // 使用 baseGallery 组件打开画廊
                        this.$refs.baseGallery.openGallery(galleryFiles, 0);
                        this.galleryLoading = false;

                    } catch (error) {
                        console.error('打开画廊失败:', error);
                        this.galleryLoading = false;
                        this.$message.error('打开画廊失败');
                    }
                });
            });
        },

        // 根据资源类型确定文件类型
        getFileTypeFromResource(item) {
            if (item.msg_type === this.systemConfig.msg_type.RealTimeVideoReview ||
                item.msg_type === this.systemConfig.msg_type.Video ||
                item.msg_type === this.systemConfig.msg_type.Cine ||
                item.ultrasound_url) {
                return 'video';
            } else if (item.msg_type === this.systemConfig.msg_type.Image) {
                return 'image';
            } else if (item.msg_type === this.systemConfig.msg_type.PDF) {
                return 'pdf';
            } else if (item.msg_type === this.systemConfig.msg_type.DCM) {
                return 'dcm';
            }
            // 默认根据URL扩展名判断
            return 'video'; // 默认为视频类型
        },
        canHandleLive(status) {
            return (
                status === this.systemConfig.liveManagement.waiting ||
                status === this.systemConfig.liveManagement.starting
            );
        },
        getQrcodeAddress(item) {
            let live_Info = `live_id=${item.id}`;
            let creator_Info = `creator_id=${item.creator_id}`;
            let str = window.btoa(`${live_Info}#####${creator_Info}`);
            let serverInfo = this.systemConfig.serverInfo;
            console.error(this.systemConfig.server_type.protocol,this.systemConfig.server_type.host,this.systemConfig.server_type.port);
            let url =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.port;
            return Tool.transferLocationToCe(`${url}/activity/activity.html#/webLive/${str}`);
        },
        getQrcode(item) {
            let id = "qrCode_" + item.id;
            document.getElementById(id).innerHTML = "";
            var qrcode = new window.QRCode(document.getElementById(id), {
                text: this.getQrcodeAddress(item),
                width: 220,
                height: 220,
            });
        },
        async copyLiveAddress(item) {
            const liveAddress = this.getQrcodeAddress(item);
            if (!liveAddress) {
                this.$message.warning(this.lang.copy_text_fail);
                return;
            }

            try {
                const success = await Tool.copyToClipboard(liveAddress);
                if (success) {
                    this.$message.success(this.lang.copy_text_success);
                } else {
                    this.$message.error(this.lang.copy_text_fail);
                }
            } catch (err) {
                console.error('复制失败:', err);
                this.$message.error(this.lang.copy_text_fail);
            }
        },
        removeInviteMemberTag(val) {
            if (this.addTitle === this.lang.edit_live) {
                //编辑模式下不允许删除
                let isOldMember = false;
                this.addForm.currentInviteMemberList.forEach((element) => {
                    if (val[0] === element[0] && Number(val[1]) === Number(element[1])) {
                        isOldMember = true;
                    }
                });
                isOldMember &&
                    this.addForm.inviteMemberList.push(val) &&
                    this.$message.error(this.lang.not_support_del_invite_members);
            }
        },
        //删除搜索值
        handleInviteMemberChange() {
            this.$refs["cascader"].inputValue = "";
        },
        getLiveAddress() {
            let live_Info = `live_id=${this.handleItem.id}`;
            let creator_Info = `creator_id=${this.handleItem.creator_id}`;
            let str = window.btoa(`${live_Info}#####${creator_Info}`);
            return str;
        },
        openCropperModal() {
            this.cropperVisible = true;
            this.$nextTick(() => {
                this.$refs.cropper.loadImageUrl(this.addForm.imageUrl);
            });
        },
        afterCropper(imageUrl, file) {
            this.addForm.imageUrl = imageUrl;
            this.addForm.file = file;
        },
        setDefaultSubject() {
            if (this.user.nickname) {
                return `${this.user.nickname}${this.lang.initiated_live_broadcast}`;
            }
        },
        getConferenceState(cid) {
            let conferenceState =
                this.$store.state.liveConference[cid] && this.$store.state.liveConference[cid].conferenceState;
            return conferenceState;
        },
        updateLiveCount() {
            this.$root.eventBus.$emit("updateLiveCount", { needRefresh: false });
        },
    },
};
</script>
<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss";
.live_management_container {
    width: 100%;
    height: 100%;

    .live_management {
        display: flex;
        flex-direction: row;
        height: 100%;
        overflow: hidden;
        width: 100%;
        background-color: #f5f5f7; /* iOS 背景色 */

        .addDialog {
            .el-dialog {
                width: calc(100% - 250px);
                height: calc(100% - 450px);
                max-width: 800px;
                .upload-tips {
                    position: absolute;
                    bottom: 9px;
                    left: 26px;
                    font-size: 12px;
                    color: red;
                }
            }
        }

        .live_menu {
            width: 220px;
            height: 100%;
            background: #fff;
            border-right: 0.5px solid rgba(60, 60, 67, 0.1); /* iOS 分割线 */

            .live_menu-vertical {
                border-right: none;

                .el-menu-item {
                    position: relative;
                    padding-left: 20px !important;
                    height: 50px;
                    line-height: 50px;
                    margin: 4px 12px; /* 添加左右留白 */
                    border-radius: 10px; /* iOS 圆角 */

                    &.is-active {
                        background-color: rgba(0, 122, 255, 0.05); /* 更淡的蓝色半透明背景 */
                        color: #007aff; /* iOS 蓝色 */
                        font-weight: 500;
                    }

                    &:hover {
                        background-color: rgba(0, 0, 0, 0.03);
                    }

                    .unread {
                        position: absolute;
                        right: 16px;
                        top: 14px;
                        background: #ff3b30; /* iOS 红色 */
                        color: #fff;
                        width: 20px;
                        height: 20px;
                        line-height: 20px;
                        font-size: 12px;
                        text-align: center;
                        border-radius: 10px; /* iOS 圆角 */
                        font-weight: 600;
                    }

                    i {
                        color: #8e8e93; /* iOS 图标颜色 */
                    }

                    &.is-active i {
                        color: #007aff; /* iOS 蓝色 */
                    }
                }
            }

            .menu-title {
                font-size: 22px;
                font-weight: 600;
                padding: 20px 16px;
                text-align: left;
                color: #1c1c1e; /* iOS 文字颜色 */
            }
        }

        .live_man_container {
            flex: 1;
            height: 100%;
            overflow: hidden;

            .live_content {
                padding: 30px;
                display: flex;
                flex-direction: column;
                height: 100%;
                .liev_man_body {
                    flex: 1;
                    height: 0;
                    margin-top: 20px;

                    .el-tabs {
                        height: 100%;
                        display: flex;
                        flex-direction: column;

                        .el-tabs__content,
                        .el-tab-pane {
                            height: 100%;

                            .live_man_box {
                                overflow-y: auto;
                                height: calc(100% - 61px);
                                overflow-x: hidden;
                                margin-bottom: 20px;
                                padding: 0 2px; /* 为阴影留出空间 */
                            }
                        }
                        :deep(.el-tabs__content) {
                            flex: 1;
                            overflow-y: hidden;
                        }
                    }
                }

                .live_content_title {
                    margin-bottom: 20px;
                    font-size: 20px;
                    font-weight: 600;
                    color: #1c1c1e; /* iOS 文字颜色 */

                    i {
                        margin-right: 8px;
                        color: #8e8e93; /* iOS 图标颜色 */
                    }
                }
            }

            .live_content_history {
                height: 100%;

                .live_man_box {
                    overflow-y: auto;
                    height: calc(100% - 100px);
                    overflow-x: hidden;
                    margin-bottom: 20px;
                    padding: 0 2px; /* 为阴影留出空间 */
                }
            }

            .live_man_form {
                display: flex;
                align-items: center;
                padding: 20px 20px 0 20px;
                background: #fff;
                border-radius: 16px; /* iOS 圆角 */
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05); /* iOS 阴影 */

                .el-form-item__content {
                    width: 221px !important;

                    .el-select,
                    .el-input,
                    .el-date-editor,
                    .el-checkbox-group {
                        width: 100%;
                    }
                }
            }

            .header_title {
                margin: 10px 0;
                font-size: 18px;
                font-weight: 600;
                color: #1c1c1e; /* iOS 文字颜色 */
            }

            .live_item {
                padding-bottom: 16px;
                min-width: 310px;
            }

            .live_flex_container {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(310px, 1fr));
                gap: 24px; /* 增加间距 */
            }

            .live_flex_item {
                box-sizing: border-box;
            }

            .el-card {
                border-radius: 16px; /* iOS 圆角 */
                border: none;
                overflow: hidden;
                transition: transform 0.3s, box-shadow 0.3s;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05); /* iOS 轻阴影 */

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
                }

                &__body {
                    position: relative;
                    padding: 16px;
                    width: 100%;

                    .live_image_box {
                        width: 100%;
                        display: flex;
                        justify-content: center;
                        position: relative;
                        overflow: hidden;
                        padding: 5px 0;
                        cursor: pointer;
                        border-radius: 12px; /* iOS 圆角 */

                        .iconvideo_fill_light {
                            position: absolute;
                            color: #fff;
                            font-size: 50px;
                            left: 50%;
                            top: 50%;
                            transform: translate3d(-50%, -50%, 0);
                        }

                        img {
                            width: auto;
                            height: 138px;
                            border-radius: 12px; /* iOS 圆角 */
                        }
                    }

                    .card-date {
                        display: flex;
                        font-size: 14px;
                        color: #8e8e93; /* iOS 次要文本颜色 */
                        white-space: nowrap;
                        margin: 12px 0 6px;

                        .card-date-text {
                            flex: 0 1 auto;
                            white-space: nowrap;

                            i {
                                margin-right: 4px;
                            }
                        }

                        .card-date-space {
                            width: 16px;
                            flex: 0 0 16px;
                            text-align: center;
                            color: #8e8e93; /* iOS 次要文本颜色 */
                        }
                    }

                    .card-info {
                        display: flex;
                        justify-content: space-between;
                        font-size: 14px;
                        color: #8e8e93; /* iOS 次要文本颜色 */
                        line-height: 2;
                    }

                    .card-content {
                        padding: 12px 0 6px;

                        .card-top-text {
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 8px;

                            .card-title {
                                flex: 1;
                                width: 0;

                                .card-title-text {
                                    text-align: left;
                                    font-size: 17px;
                                    font-weight: 600;
                                    color: #1c1c1e; /* iOS 文字颜色 */
                                }
                            }
                        }

                        .card-top-status {
                            text-align: right;

                            span {
                                font-size: 13px;
                                width: 90px;
                                text-align: right;
                                flex-shrink: 0;
                            }

                            .card-tips-status0 {
                                color: #007aff; /* iOS 蓝色 */
                                background-color: rgba(0, 122, 255, 0.1);
                            }

                            .card-tips-status1 {
                                color: #ff9500; /* iOS 橙色 */
                                background-color: rgba(255, 149, 0, 0.1);
                            }

                            .card-tips-status2 {
                                color: #34c759; /* iOS 绿色 */
                                background-color: rgba(52, 199, 89, 0.1);
                            }

                            .card-tips-status3 {
                                color: #ff3b30; /* iOS 红色 */
                                background-color: rgba(255, 59, 48, 0.1);
                            }
                        }

                        .card-desc {
                            line-height: 1.5;
                            font-size: 15px;
                            color: #8e8e93; /* iOS 次要文本颜色 */
                            display: block;
                            height: 28px;
                        }
                    }

                    .card-tools {
                        display: flex;
                        justify-content: flex-end;
                        margin-top: 16px;
                        height: 36px;
                        border-top: 0.5px solid rgba(60, 60, 67, 0.1); /* iOS 分割线 */
                        padding-top: 12px;

                        .toolbar_item_span {
                            margin-left: 8px;

                            .el-button.is-circle {
                                padding: 8px;
                                border: none;
                                background-color: rgba(142, 142, 147, 0.1); /* iOS 按钮背景 */
                                color: #8e8e93; /* iOS 次要文本颜色 */

                                &:hover {
                                    background-color: rgba(142, 142, 147, 0.2);
                                }

                                &.el-button--danger {
                                    background-color: rgba(255, 59, 48, 0.1);
                                    color: #ff3b30; /* iOS 红色 */

                                    &:hover {
                                        background-color: rgba(255, 59, 48, 0.2);
                                    }
                                }
                            }
                        }
                    }
                }

                &.add-live-btn {
                    font-size: 60px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    background-color: rgba(0, 122, 255, 0.05);
                    color: #007aff; /* iOS 蓝色 */

                    &:hover {
                        background-color: rgba(0, 122, 255, 0.1);
                    }
                }
            }
        }
        /* iOS风格分页 */
        .live_pagination {
            margin-top: 20px;

            .el-pagination {
                text-align: center;

                .btn-prev,
                .btn-next {
                    background-color: #fff;
                    color: #007aff; /* iOS 蓝色 */
                    border-radius: 50%;
                    border: none;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

                    &:disabled {
                        color: #c8c8cc; /* iOS 禁用颜色 */
                        background-color: #f5f5f7; /* iOS 背景色 */
                    }
                }

                .el-pager li {
                    border-radius: 50%;
                    background-color: #fff;
                    color: #8e8e93; /* iOS 次要文本颜色 */
                    margin: 0 4px;
                    border: none;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                    font-weight: normal;

                    &.active {
                        background-color: #007aff; /* iOS 蓝色 */
                        color: #fff;
                    }
                }
            }
        }
    }
}

/* 二维码内容样式 */
.qrcode_tontent {
    width: 280px;
    height: auto;
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    border-radius: 16px; /* iOS 圆角 */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* iOS 阴影 */

    .qrcode {
        margin-bottom: 15px;

        canvas {
            border-radius: 8px; /* iOS 圆角 */
            overflow: hidden;
        }
    }

    .qrcode-address {
        width: 100%;
        margin-top: 12px;

        .address-text {
            color: #6b7280;
            font-size: 12px;
            word-break: break-all;
            margin: 0 0 12px 0;
            padding: 8px;
            background: #f8fafc;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
            line-height: 1.4;
            text-align: center;
        }

        .copy-btn {
            width: 100%;
            border-radius: 6px;
            font-size: 12px;
            padding: 8px 12px;
            transition: all 0.2s ease;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
            }

            &.is-loading {
                transform: none;
            }

            .el-icon-document-copy {
                margin-right: 4px;
            }
        }
    }

    p {
        font-size: 14px;
        color: #8e8e93; /* iOS 次要文本颜色 */
        text-align: center;
        word-break: break-all;
        margin: 0;
        padding: 0 10px;
    }
}

.card-text-tips {
    max-width: 350px;
    border-radius: 12px; /* iOS 圆角 */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* iOS 阴影 */
    padding: 12px;

    p {
        margin: 0;
    }
}
</style>
<style lang="scss">
.add_live_management_dialog {
    .edit_form {
        .inline-form-item {
            display: flex;
            justify-content: space-between;

            .el-form-item {
                margin-right: 20px;

                &:last-child {
                    margin-right: 0;
                }
            }

            input[type="number"]::-webkit-inner-spin-button,
            input[type="number"]::-webkit-outer-spin-button {
                -webkit-appearance: none;
            }
        }

        .el-textarea__inner {
            padding: 5px 40px 5px 15px !important;
        }

        .avatar-uploader {
            line-height: 0;
            border: 1px dashed rgba(60, 60, 67, 0.3); /* iOS 边框颜色 */
            border-radius: 12px; /* iOS 圆角 */

            .el-progress {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate3d(-50%, -50%, 0);

                .el-progress__text {
                    color: #fff;
                }
            }

            .el-upload {
                width: 100%;
                border: none !important;

                .avatar-uploader-icon {
                    width: 100%;
                }
            }

            .avatar-uploader-image {
                width: 100%;
                height: 200px;

                .avatar {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    border-radius: 12px; /* iOS 圆角 */
                }

                .el-upload-list__item-actions {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                    cursor: default;
                    text-align: center;
                    color: #fff;
                    opacity: 0;
                    font-size: 20px;
                    background-color: rgba(0, 0, 0, 0.5);
                    transition: opacity 0.3s;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    border-radius: 12px; /* iOS 圆角 */

                    &:hover {
                        opacity: 1;
                    }

                    .el-button {
                        font-size: 26px;
                        color: #fff;
                    }
                }
            }

            .el-upload {
                cursor: pointer;
                position: relative;
                border-width: 1px;
                border-style: dashed;
                border-color: rgba(60, 60, 67, 0.3); /* iOS 边框颜色 */
                border-image: initial;
                border-radius: 12px; /* iOS 圆角 */
                overflow: hidden;
            }

            .avatar-uploader-icon {
                font-size: 28px;
                color: #8e8e93; /* iOS 次要文本颜色 */
                width: 178px;
                height: 178px;
                line-height: 178px;
                text-align: center;
            }

            .avatar {
                width: 178px;
                height: 178px;
            }
        }
    }
}
</style>
