<template>
    <div class="forget_page second_level_page">
        <mrHeader>
            <template #title>
                {{lang.forget_password_title}}
            </template>
        </mrHeader>
        <div v-if="beingGetcode" class="full_loading_spinner van_loading_spinner">
            <van-loading color="#00c59d" />
        </div>
        <div class="container">
            <p class="app_name">{{lang.app_name}}</p>
            <!-- <input type="text" class="commont_input" v-model="mobile_phone_number" maxlength="11" :placeholder="lang.register_mobile"> -->
            <template v-if="step===0">
                <button class="primary_bg forget_password_btn" @click="setVerifyType(1)">{{lang.verify_with_mobile}}</button>
                <button class="primary_bg forget_password_btn" @click="setVerifyType(2)">{{lang.verify_with_email}}</button>
                <!-- <div class="clearfix">
                    <span class="to_login primary_color fl" @click="back">{{lang.login_title}}</span>
                    <router-link to="/login/register" replace class="to_register primary_color fr">{{lang.to_register}}</router-link>
                </div> -->
            </template>
            <template v-if="step===1">

                <div v-if="validateType==1">
                    <mobile-international :mobile.sync="mobile_phone_number" :internationalCode.sync="international_code" ref="mobile_international"></mobile-international>
                </div>
                <div v-else-if="validateType==2">
                    <input type="text" v-model="email" class="commont_input" ref="referral_code" :placeholder="lang.register_email">
                </div>
                <button v-if="validateType==1" v-bind:disabled="beingGetcode" class="primary_bg forget_password_btn" @click.stop="loginByTracelessValid('mobile')">{{lang.forget_password_getcode}}</button>
                <button v-if="validateType==2" v-bind:disabled="beingGetcode" class="primary_bg forget_password_btn" @click.stop="loginByTracelessValid('email')">{{lang.forget_password_get_email_code}}</button>
            </template>
            <template v-if="step===2">
                <input type="password" v-model="password" class="commont_input" name="" maxlength="16" :placeholder="lang.register_password">
                <input type="password" v-model="confirm_password" class="commont_input" name="" maxlength="16" :placeholder="lang.register_confirm_password">
                <div class="sms_verification_father">
                    <input  type="tel" v-model="verifyCode" class="commont_input" maxlength="6" :placeholder="validateType==1?lang.sms_verification_code:lang.email_verification_code">
                    <van-button color="#00c59d" class="get_verification_code_again" v-bind:disabled="counter>0" @click="getCodeAgain">{{lang.retrieve_sms_verification_code}} {{counter>0?`(${counter})`:''}}</van-button>
                </div>
                <van-button color="#00c59d" class="forget_password_btn" @click="resetPassword" v-loading="isReseting">{{lang.user_reset_password_button}}</van-button>
            </template>
        </div>
        <ValidateCaptcha name="ForgetPassword" ref='validateCaptcha'></ValidateCaptcha>
    </div>
</template>
<script>
import service from '../service/service'
import base from '../lib/base'
import { Toast, Loading, Button } from 'vant';
import mobileInternational from '../components/mobileInternational.vue'
import ValidateCaptcha from '../MRComponents/ValidateCaptcha.vue'
import Tool from '@/common/tool.js'
import {
    passwordStrengthRegExp,
} from '@/common/regExpMapping.js'
export default {
    mixins: [base],
    name: 'ForgetPassword',
    components: {
        mobileInternational,
        ValidateCaptcha,
        VanLoading: Loading,
        VanButton: Button,
    },
    data(){
        return {
            mobile_phone_number:'',
            email:'',
            international_code:'',
            sms_verification_code:'',
            // user_id:'',
            index:'',
            beingGetcode:false,
            validateType:0,//1ÊÖ»úÑéÖ¤,2ÓÊÏäÑéÖ¤
            step:1,
            imageCode:'',
            imageSVG:'',
            timer:null,
            counter:0,
            verifyCode:'',
            isReseting:false,
            password:'',
            confirm_password:'',
        }
    },
    mounted(){
        this.validateType=this.$route.params.type
        this.sms_button_content = this.lang.forget_password_getcode;
        this.email_button_content = this.lang.forget_password_get_email_code;
    },
    methods:{
        setVerifyType(type){
            this.validateType=type;
            this.step=1;
        },
        getVerifyCode(accountType, afsCode){
            let verifyType='getVerityCodeToMobile';
            let params={}
            if (accountType==='mobile') {
                verifyType='getVerityCodeToMobile';
                params={
                    mobile:this.mobile_phone_number,
                    // imgToken:this.imageToken,
                    // imgCode:this.imageCode,
                    afsCode:afsCode,
                    countryCode:this.international_code,
                    type:'changePassword',
                }
            }else{
                verifyType='getVerityCodeToEmail';
                params={
                    email:this.email,
                    // imgToken:this.imageToken,
                    // imgCode:this.imageCode,
                    afsCode:afsCode,
                    type:'changePassword',
                }
            }
            this.beingGetcode=true;
            service[verifyType](params).then((res)=>{
                if (res.data.error_code===0) {
                    this.step=2;
                    this.display_count_down();
                }else{
                    // const msg=this.lang[res.data.key]||this.lang.unknown_error
                    // Toast(msg)
                }
                this.beingGetcode=false;
            })
        },
        getCodeAgain(){
            this.step=1;
            this.verifyCode=''
        },
        resetPassword(){
            if (!passwordStrengthRegExp.test(this.password)){
                Tool.openMobileDialog(
                    {
                        message:this.lang.enhanced_password_format_tip,
                    }
                )
            }else if (this.confirm_password != this.password){
                Tool.openMobileDialog(
                    {
                        message:this.lang.confirm_password_and_password_not_same,
                    }
                )
            }else if (this.verifyCode.length===0){
                const tip=this.validateType==1?'login_sms_verification_code_empty':'email_verification_code_empty';
                Tool.openMobileDialog(
                    {
                        message:this.lang[tip],
                    }
                )

            }else{
                this.isReseting=true;
                service.encryptPassword({
                    pwd:this.password
                }).then((res)=>{
                    if (res.data.error_code===0) {
                        const params={
                            account:this.validateType==1?this.mobile_phone_number:this.email,
                            accountType:this.validateType==1?'mobile':'email',
                            code:this.verifyCode,
                            newPassword:res.data.data.encryptStr
                        }
                        service.resetPasswordByCode(params).then((res)=>{
                            this.isReseting=false;
                            if (res.data.error_code===0) {
                                Toast(this.lang.modify_password_success)
                                this.back()
                            }else{
                                // const msg=this.lang[res.data.key]||this.lang.unknown_error
                                // Toast(msg)
                            }
                        })
                    }else{
                        this.isReseting=false;
                        // const msg=this.lang[res.data.key]||this.lang.unknown_error
                        // Toast(msg)
                    }
                })
            }
        },
        display_count_down(){
            if (!this.timer){
                this.counter = 89;
                this.timer = setInterval(()=>{
                    this.counter--;
                    if (this.counter == 0){
                        this.counter = 0;
                        clearInterval(this.timer);
                        this.timer = null;
                    }
                }, 1000);
            }
        },
        loginByTracelessValid:Tool.debounce(async function (login_type) {
            if(login_type == 'mobile'){
                if (!this.$refs.mobile_international.validate()){
                    return;
                }
                const afsCode = await this.$refs['validateCaptcha'].validateCaptcha()
                this.successVerifyByTraceless(afsCode)
            }
            if(login_type == 'email'){
                if (!Tool.isEmail(this.email)) {
                    Toast(this.lang.email_is_invalid_input_again);
                    return;
                }
                const afsCode = await this.$refs['validateCaptcha'].validateCaptcha()
                this.successVerifyByTraceless(afsCode)
            }
        },500,true),
        successVerifyByTraceless(afsCode){
            if(this.validateType==1){
                this.getVerifyCode('mobile',afsCode)
            }
            if(this.validateType==2){
                this.getVerifyCode('email',afsCode)
            }
        }
    }
}

</script>
<style lang="scss">
.forget_page{
    background-color: #fff;
    min-height:26rem;
    .full_loading_spinner{
        position:absolute;
        width:100%;
        top:0;
    }

    .van_loading_spinner{
        display: flex;
        justify-content: center;
        align-items: center;

        .van-loading__spinner{
            width: 2.8rem;
            height: 2.8rem;
        }
    }

    .container{
        margin:1rem;
        .app_name{
            font-size:2rem;
            color:#707070;
            margin:1rem 0;
        }
        .sms_verification_father{
            position:relative;
            .svg_wrap{
                position:absolute;
                right:0;
                top:0;
                & > i {
                    width:4rem;
                    height:2rem;
                }
            }
            .get_verification_code_again{
                position:absolute;
                right:0;
                top:0;
                font-size: 0.8rem;
            }
        }
        .forget_password_btn{
            display: block;
            width: 100%;
            border: none;
            font-size: 1rem;
            line-height: 2rem;
            margin: 1rem 0 .6rem;
            border-radius: .2rem;
            transform: translate3d(0px, 0px, 0px);
        }
        .to_register,.to_forget_password,.to_login{
            font-size:.8rem;
        }
    }
}
</style>
