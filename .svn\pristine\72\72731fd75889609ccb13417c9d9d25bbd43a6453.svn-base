<template>
    <div class="education-cloud-exam">
        <cloud-exam-entry></cloud-exam-entry>
    </div>
</template>

<script>
import base from "../../lib/base";
import cloudExamEntry from '../cloudExam/components/cloudExamEntry.vue'
export default {
    mixins: [base],
    name: "EducationCloudExam",
    components: {
        cloudExamEntry
    },
    data() {
        return {};
    },
    created() {
        console.log("云考核页面已加载");
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/education.scss';

.education-cloud-exam {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
    ::v-deep .cloud_exam_entry > .custom_header .iconcuohao{
        display: none;
    }
}

.coming-soon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #303133;
        font-weight: 600;
        margin-bottom: 20px;
    }

    p {
        font-size: 16px;
        color: #909399;
        font-weight: 400;
    }
}
</style>
