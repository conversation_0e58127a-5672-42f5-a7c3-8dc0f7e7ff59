<template>
    <router-view></router-view>
</template>
<script>
import languages from "@/common/language";
import vConsole from "vconsole";
import { Toast } from "vant";
import Tool from "@/common/tool.js";
export default {
    mixins: [],
    name: "app",
    components: {},
    data() {
        return {
            lang: this.$store.state.language,
            systemConfig: this.$store.state.systemConfig,
        };
    },
    created() {
        let systemConfig = this.$store.state.systemConfig;
        const isCE = process.env.VUE_APP_PROJECT_NOV === "CE";
        this.$store.commit("globalParams/updateGlobalParams", {
            isCE: isCE,
        });
        const osName = this.getOSName();
        this.$store.commit("globalParams/updateGlobalParams", {
            osName: osName,
        });
        let { server_type, appVersion } = this.getServerType();
        console.log(server_type, appVersion, "getServerType");
        this.$store.commit("systemConfig/updateSystemConfig", {
            server_type: server_type,
            appVersion: appVersion,
        });
        this.$root.platformToast = Toast;
        if (isCE) {
            //CE版本处理
            Tool.replaceAppNameToLanguages(languages);
        }
        //全局变量，用与语言切换引起的token清空问题
        let localLanguage = window.localStorage.getItem("lang");
        let lang = localLanguage || "CN";
        if (isCE) {
            lang = localLanguage || "EN";
        }
        this.$store.commit("language/setLanguage", languages[lang]);
        this.$store.commit("language/setLanguage", { currentLanguage: lang });
    },
    beforeMount() {},
    mounted() {

    },
    methods: {
        getOSName() {
            let ua = navigator.userAgent.toLowerCase();
            let os_name = "unKnow";
            if (ua.match(/android|adr/i)) {
                os_name = "android";
            } else if (ua.match(/iphone|ipad|ipod/i)) {
                os_name = "ios";
            } else if (ua.match(/(Windows NT|Macintosh|Linux)/i)) {
                os_name = "windows";
            }
            return os_name;
        },
        // 根据窗口大小判断是否为移动端
        isMobileDevice() {
            // 使用窗口宽度来判断，768px作为分界点
            return window.innerWidth <= 768;
        },
        // 初始化vconsole的方法
        initVConsole() {
            // 只在移动端才初始化vconsole
            if (this.isMobileDevice()) {
                this.$root.isInitVConsole = true;
                window.vConsole = new vConsole();
            }
        },
        getServerType() {
            //在线包d
            let server_type = {
                hostname: window.location.hostname,
                protocol: "https://",
                host: window.location.host,
                port: ":443",
                enable_sms_identification: true,
                websocket_protocol: "wss://",
                websocket_prot: ":443",
            };
            let appVersion = "Prod";
            if (window.location.href.indexOf("localhost") > -1) {
                appVersion = "Dev";
                server_type.port = ":" + window.location.port;
                server_type.protocol = "http://";
                server_type.websocket_prot = ":" + window.location.port;
                server_type.host = window.location.hostname;
            } else if (Tool.isDev()) {
                this.initVConsole();
                appVersion = "Dev";
            } else if (Tool.isBeta()) {
                appVersion = "Beta";
            } else if (!!window.location.hostname.match(/^((25[0-5]|2[0-4]\d|[01]?\d\d?)($|(?!\.$)\.)){4}$/)) {
                server_type.port = ":" + window.location.port;
                server_type.websocket_prot = ":" + window.location.port;
                server_type.host = window.location.hostname;
                server_type.protocol = window.location.protocol + "//";
                this.initVConsole();
            }
            return { server_type, appVersion };
        },
    },
};
</script>
<style lang="scss">
* {
    touch-action: pan-y; //这个是重点如果不加新版谷歌会忽略掉touch方法
    word-break: break-word;
}
*:not(input, textarea) {
    //兼容IOS下 长按会弹出默认交互问题
    -webkit-touch-callout: none;
    -webkit-user-select: none; /* Disable selection/Copy of UIWebView */
    touch-callout: none;
}
.prevent_ios_longtouch {
    pointer-events: none;
}
.index_header {
    align-items: center;
    .svg_app_title {
        top: -0.15rem;
    }
}
.page-tab-container {
    height: calc(100% - 6.35rem);
}
#__vconsole {
    user-select: none;
    -webkit-user-select: none;
    touch-callout: none;
    -webkit-touch-callout: none;
}

.van-switch {
    width: 1.6rem !important;
    height: 1rem !important;

    .van-switch__node {
        top: -0.15rem !important;
        left: -0.5rem !important;
        width: 1.2rem !important;
        height: 1.2rem !important;
    }
}

.more_panel {
    position: absolute;
    width: 1.75rem;
    height: 100%;
    right: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .svg_icon_more {
        width: 0.95rem;
        height: 0.2rem;
        font-size: 0.24rem;
        color: #fff;
    }
}
.textEllipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.van-button--primary {
    background-color: #00c59d !important;
    border: 1px solid #00c59d !important;
}
.van-button--plain {
    background-color: #fff !important;
}
.van-button--plain.van-button--primary {
    color: #00c59d !important;
}
.network_unavailable {
    line-height: 1.4rem;
    font-size: 0.7rem;
    color: #333;
    background-color: #f9d5d5;
    padding: 0.3rem;
    & > i {
        font-size: 1rem;
        color: #ff6759;
    }
}
.van-toast .van-toast__text{
    font-size: .8rem;
}
.van-loading__circular circle{
    stroke-width: .15rem;
}
body{
    line-height: 1.4;
    font-size: 20px;
    color: #333;
    &.el-popup-parent--hidden {
        padding-right: 0 !important;
    }
    .el-loading-mask{
        background-color: rgba(255,255,255,.5);
        .el-loading-spinner .path{
            stroke: #6b908e;
            stroke-width:4px;
        }
        &.is-fullscreen{
            .el-loading-text{
                color: #000;
                font-size: 24px;
            }
        }
    }
    .el-message{
        top:30% !important;
    }
    .el-message-box__message p{
        word-break: break-word;
    }
    .el-dialog__wrapper{
        // position:absolute;
        background:rgba(47, 47, 47,.7);
        .el-dialog{
            margin:0;
            margin:20vh auto 0;
            top:0;
            left:0;
            right:0;
            height:60%;
            .main_btn{
                background-color:#5A817E;
                border:1px solid #5A817E;
                padding:6px 14px;
                font-size:16px;
                color:#fff;
            }
            .el-input__inner,.el-textarea__inner{
                border-color:#999;
            }
            .el-input__inner:focus{
                outline:none;
                border-color:#5A817E;
            }
            .el-dialog__header{
                padding:4px 14px;
                height:36px;
                border-bottom:1px solid #ccc;
                .el-dialog__headerbtn{
                    top: 0px;
                    right: 4px;
                    font-size: 28px;
                    height: 36px;
                    i{
                        color: #333;
                        font-weight: bolder;
                    }
                    i:hover{
                        color:#7fa09e
                    }
                }
            }
            .el-dialog__body{
                padding:14px 14px;
                height:calc(100% - 36px);
                overflow:auto;
                position:relative;
            }
            .el-dialog__footer{
                padding:14px 14px;
                background: inherit;
            }

        }
    }
    .el-select{
        *::selection{
            background:#fff;
        }
    }
}
* {
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    vertical-align: baseline;
    background: 0 0;
    box-sizing: border-box;
    word-break: break-word;
}
</style>
