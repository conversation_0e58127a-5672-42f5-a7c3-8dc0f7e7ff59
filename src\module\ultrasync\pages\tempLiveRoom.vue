<template>
    <div class="temp_live_room_container">
        <mrHeader v-if="!isScreenHorizontal">
            <template #title>
                {{ lang.live_room }}
            </template>
        </mrHeader>
        <div class="live_player_container" :class="[isScreenHorizontal ? 'horizontal' : '']">
            <div class="player-box">
                <div id="player-box-1">
                    <CustomPlayer
                        :isOnline="currentSubscribedMain.video"
                        :hasMuteBtn="false"
                        :offlineTips="lang.weblive_live_not_yet"
                    >
                        <template>
                            <div id="remoteMainPlayer"></div>
                        </template>
                    </CustomPlayer>
                </div>
                <div id="player-box-2" v-draggable @click="swapVideoContent" v-show="checkShowSmallPlayerBox">
                    <CustomPlayer
                        :isOnline="currentSubscribedAux.video"
                        :hasMuteBtn="false"
                        :offlineTips="lang.weblive_live_not_yet"
                    >
                        <template>
                            <div id="remoteAuxPlayer"></div>
                        </template>
                    </CustomPlayer>
                </div>
            </div>
            <div class="controls">
                <div class="con_left"></div>
                <div class="con_right">
                    <div>
                        <i class="iconfont icon-JC_045 icon" v-if="isScreenHorizontal" @click="toggleOrientation"></i>
                        <i class="iconfont icon-JC_046 icon" v-else @click="toggleOrientation"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import CustomPlayer from "../MRComponents/customPlayer.vue";
import service from "../service/service";
import Tool from "@/common/tool.js";
import base from "../lib/base";
import { Dialog, Toast } from "vant";
export default {
    mixins: [base],
    data() {
        return {
            tmpToken: "",
            currentStatus: -1,
            live_id: 0,
            creator_id: 0,
            title: "",
            dec: "",
            start_ts: "",
            end_ts: "",
            webLiveInterval: null,
            mainInfoInterval: null,
            agoraClient: null,
            agoraOptions: {
                appid: "",
                token: "",
                channel: "",
                uid: null,
                uuid:0
            },
            localTracks: {
                videoTrack: null,
                audioTrack: null,
            },
            remoteUsers: {},
            channel_id: 0,
            currentSubscribeUidList: [], //从服务器请求的 当前应当订阅的Uidlist
            currentSubscribedMain: { video: false, id: 0, play: false, event: null },
            currentSubscribedAux: { video: false, id: 0, play: false, event: null },
            currentMute: false,
            isSupport: false,
            ajaxServer: false,
            currentLayout: ["main", "aux"],
            isScreenHorizontal: false,
            AgoraRTC:null,
            joinLiveStamp:0,
            livingStatus: 0, //是否直播中
            getChannelStatusInterval:null
        };
    },
    components: {
        CustomPlayer,
    },
    computed: {
        lang() {
            return this.$store.state.language;
        },
        currentLanguage() {
            return this.lang.currentLanguage;
        },
        checkShowSmallPlayerBox() {
            if (this.currentLayout[1] === "aux") {
                if (this.currentSubscribedAux.video) {
                    return true;
                }
            } else {
                if (this.currentSubscribedMain.video) {
                    return true;
                }
            }
            return false;
        },
        myNickName(){
            return this.user.nickname
        }
    },
    beforeRouteLeave(to, from, next) {
        if (this.isScreenHorizontal) {
            window.CWorkstationCommunicationMng.changeOrientation();
            next(false);
        } else {
            next(true);
        }
    },
    async created() {
        this.ajaxServer =
            this.systemConfig.server_type.protocol +
            this.systemConfig.server_type.host +
            this.systemConfig.server_type.port;
    },
    async mounted() {
        // const res = this.checkAgoraSystemRequirements();
        let params = this.$route.params.id;
        let arr = window.atob(params).split("#####");
        arr.forEach((item) => {
            if (item.includes("live_id")) {
                this.live_id = Number(item.split("=")[1]);
            }
            if (item.includes("creator_id")) {
                this.creator_id = Number(item.split("=")[1]);
            }
            if (item.includes("channel_id")) {
                this.agoraOptions.channel = item.split("=")[1];
            }
        });
        if ((!this.live_id || this.live_id === 0) && (!this.agoraOptions.channel || this.agoraOptions.channel == 0)) {
            Toast(this.lang.error_live_address_tip);
            return;
        }
        if (this.agoraOptions.channel) {
            const data = await this.getChannelInfo();
            this.initAgoraRTC(data);
        } else {
            this.webLiveInterval = setInterval(() => {
                this.getWebLiveInfo();
            }, 2000);
            this.getWebLiveInfo();
        }
        this.main_info = this.lang.switch_video_to_aux;
        this.aux_info = this.lang.switch_video_to_main;
        this.initHandleOrientationEvent();
    },
    beforeDestroy() {
        this.Leave();
    },
    methods: {
        initHandleOrientationEvent() {
            document
                .querySelector('meta[name="viewport"]')
                .setAttribute("content", "width=device-width, initial-scale=1,user-scalable=no");
            this.mediaQuery = window.matchMedia("(orientation: portrait)"); // 横屏方向
            this.mediaQuery.addListener(this.checkOrientation);
            this.checkOrientation();
        },
        checkOrientation() {
            if (window.matchMedia("(orientation: portrait)").matches) {
                // 当前为竖屏状态
                console.log("竖屏");
                this.isScreenHorizontal = false;
            } else if (window.matchMedia("(orientation: landscape)").matches) {
                // 当前为横屏状态
                console.log("横屏");
                this.isScreenHorizontal = true;
            }
            this.resetPlayer2Position();
        },
        toggleOrientation(){
            window.CWorkstationCommunicationMng.changeOrientation();
        },
        checkAgoraSystemRequirements() {
            let res = this.AgoraRTC.checkSystemRequirements();
            // this.isSupport = res;
            // if(window.location.protocol.indexOf('sw')>-1){
            //     this.isSupport = true
            // }
            this.isSupport = true;
            return this.isSupport;
        },
        initAgoraRTC(channelData) {
            //创建本地客户端RTC
            if(!this.AgoraRTC){
                this.AgoraRTC = require('agora-rtc-sdk-ng')
            }
            this.agoraOptions.appid = channelData.appid;
            this.agoraOptions.token = channelData.token;
            this.agoraOptions.uid = Number(channelData.uid);
            this.agoraOptions.uuid = channelData.uuid
            this.agoraClient = this.AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
            const {isLocal,localAgoraIP} = Tool.getAgoraProxyInfo()
            if(isLocal){
            // let t = JSON.parse(`{\"log\": {}, \"report\": {} , \"accessPoints\": {\"serverList\": [\"${localAgoraIP}\"], \"domain\": \"${ca_domain}"}}`);
            // this.agoraClient.setLocalAccessPointsV2(t);
                this.AgoraRTC.setParameter("JOIN_WITH_FALLBACK_SIGNAL_PROXY", false);
                this.AgoraRTC.setParameter("JOIN_WITH_FALLBACK_MEDIA_PROXY", false);
                this.AgoraRTC.setParameter('CONNECT_GATEWAY_WITHOUT_DOMAIN',true)
                this.AgoraRTC.setParameter("WEBCS_DOMAIN",[`${localAgoraIP}`]);
                this.AgoraRTC.setParameter("EVENT_REPORT_DOMAIN",`${localAgoraIP}:6443`);
                this.AgoraRTC.setParameter("LOG_UPLOAD_SERVER",[`${localAgoraIP}:6444`]);
            }
            this.AgoraRTC.onAutoplayFailed = (e) => {
                console.log("onAutoplayFailed", e);
                Tool.openMobileDialog(
                    {
                        message:this.lang.not_group_member_live_tips,
                        confirm:()=>{
                            if (!this.currentMute) {
                                try {
                                    Object.keys(this.remoteUsers).forEach((uid) => {
                                        this.remoteUsers[uid].audioTrack&&this.remoteUsers[uid].audioTrack.stop();
                                        this.remoteUsers[uid].audioTrack&&this.remoteUsers[uid].audioTrack.play();
                                    });
                                } catch (error) {
                                    console.error(error);
                                }
                            }

                            this.currentSubscribedMain.id &&
                        this.remoteUsers[this.currentSubscribedMain.id].videoTrack.play("remoteMainPlayer", {
                            fit: "contain",
                        });
                        }
                    }
                )
            };
            this.agoraClient.on("user-published", this.handleUserPublished);
            this.agoraClient.on("user-unpublished", this.handleUserUnPublished);
            this.agoraClient.on("connection-state-change", this.handleConnectionStateChange);
            this.handleChanelStatusChange();
            this.getChannelStatusInterval = setInterval(() => {
                this.handleChanelStatusChange();
            }, 3000);
        },
        playMainVideo() {
            if (this.currentSubscribedMain["play"] === false) {
                if (this.currentSubscribedMain.id) {
                    this.remoteUsers[this.currentSubscribedMain.id].videoTrack.play("remoteMainPlayer", {
                        fit: "contain",
                    });
                    this.currentSubscribedMain["play"] = true;
                    const video = document.querySelector("#remoteMainPlayer video");
                    video.setAttribute('playsinline', 'true');
                    video.setAttribute('x5-playsinline','true')
                    video.setAttribute('webkit-playsinline','true')
                    // this.addEventListenerVideo("main");
                }
            }
        },
        playAuxVideo() {
            if (this.currentSubscribedAux["play"] === false) {
                if (this.currentSubscribedAux.id) {
                    this.remoteUsers[this.currentSubscribedAux.id].videoTrack.play("remoteAuxPlayer", {
                        fit: "contain",
                    });
                    this.currentSubscribedAux["play"] = true;
                    const video = document.querySelector("#remoteAuxPlayer video");
                    video.setAttribute('playsinline', 'true');
                    video.setAttribute('x5-playsinline','true')
                    video.setAttribute('webkit-playsinline','true')
                    // this.addEventListenerVideo("aux");
                }
            }
        },
        addEventListenerVideo(type) {
            if (type === "main" && !this.currentSubscribedMain["event"]) {
                const video = document.querySelector("#remoteMainPlayer video");
                video.addEventListener("pause", () => {
                    console.log("视频暂停");
                    this.currentSubscribedMain["play"] = false;
                });
                this.currentSubscribedMain["event"] = true;
            } else if (type === "aux" && !this.currentSubscribedAux["event"]) {
                const video = document.querySelector("#remoteAuxPlayer video");
                video.addEventListener("pause", () => {
                    console.log("视频暂停");
                    this.currentSubscribedAux["play"] = false;
                });
                this.currentSubscribedAux["event"] = true;
            }
        },
        async joinRoom() {
            [this.localTracks.audioTrack, this.localTracks.videoTrack] = await Promise.all([
                // join the channel
                this.agoraClient.join(
                    this.agoraOptions.appid,
                    this.agoraOptions.channel,
                    this.agoraOptions.token || null,
                    this.agoraOptions.uid
                ),
                // 使用麦克风和摄像头
                // AgoraRTC.createMicrophoneAudioTrack(),
                // AgoraRTC.createCameraVideoTrack()
            ]);
            this.joinLiveStamp = Date.now()
            this.mainInfoInterval = setInterval(() => {
                this.getCurrentSubscribeUidList();
            }, 3000);
            this.getCurrentSubscribeUidList();
        },
        async handleUserPublished(user, mediaType) {
            // console.info(console.info(user,mediaType))
            const uid = user.uid;
            this.remoteUsers[uid] = user;
            if (mediaType === "audio") {
                try {
                    await this.agoraClient.subscribe(user, "audio");
                    if (!this.currentMute) {
                        user.audioTrack.play();
                    }
                } catch (error) {
                    console.error(error, "audio");
                }
            }
        },
        async handleUserUnPublished(user, mediaType) {
            const uid = user.uid;

            if (mediaType === "audio") {
                await this.agoraClient.unsubscribe(user, mediaType);
            } else if (mediaType === "video") {
                if (uid === this.currentSubscribedMain.id) {
                    await this.agoraClient.unsubscribe(user, mediaType);
                    this.currentSubscribedMain[mediaType] = false;
                    this.currentSubscribedMain["play"] = false;
                } else if (uid === this.currentSubscribedAux.id) {
                    await this.agoraClient.unsubscribe(user, mediaType);
                    this.currentSubscribedAux[mediaType] = false;
                    this.currentSubscribedAux["play"] = false;
                }
            }
        },
        async subscribeMain(user) {
            if (user.uid !== this.currentSubscribedMain.id) {
                if (this.currentSubscribedMain.id) {
                    await this.agoraClient.unsubscribe(this.remoteUsers[this.currentSubscribedMain.id]);
                    this.currentSubscribedMain.id = 0;
                }
            }

            try {
                if (!this.currentSubscribedMain.video) {
                    await this.agoraClient.subscribe(user, "video");
                    // user.videoTrack.play("remoteMainPlayer", { fit: "contain" });
                    this.currentSubscribedMain["video"] = true;
                    this.currentSubscribedMain.id = user.uid;
                    this.playMainVideo();
                }
            } catch (error) {
                console.error(error);
            }

            // if (mediaType === 'video') {

            // }
        },
        async subscribeAux(user) {
            if (user.uid !== this.currentSubscribedAux.id) {
                if (this.currentSubscribedAux.id) {
                    await this.agoraClient.unsubscribe(this.remoteUsers[this.currentSubscribedAux.id]);
                    this.currentSubscribedAux.id = 0;
                    this.currentSubscribedAux.video = false;
                }
            }
            try {
                if (!this.currentSubscribedAux.video) {
                    await this.agoraClient.subscribe(user, "video");
                    // user.videoTrack.play("remoteAuxPlayer", { fit: "contain" });
                    this.currentSubscribedAux["video"] = true;
                    this.currentSubscribedAux.id = user.uid;
                    this.playAuxVideo();
                }
            } catch (error) {
                console.error(error, "video");
            }
        },
        // 客户离开信道
        async Leave() {
            this.remoteUsers = {};
            this.joinLiveStamp = 0
            this.livingStatus = 2;
            clearInterval(this.webLiveInterval);
            clearInterval(this.mainInfoInterval);
            clearInterval(this.getChannelStatusInterval);
            this.webLiveInterval = null;
            this.mainInfoInterval = null;
            this.getChannelStatusInterval = null;
            await this.agoraClient.leave();
            console.log("客户离开信道成功");
        },
        async getWebLiveInfo() {
            let params = {
                videoType: this.type,
                id: this.live_id,
                tmpToken: this.tmpToken,
                creator_id: this.creator_id,
            };
            const { data } = await service.getWebLiveInfo(params);
            console.log("getWebLiveInfo", data);
            if(data.error_code === 0){
                let liveInfo = data.data.liveInfo.extends.livingInfo;
                this.currentStatus = data.data.liveInfo.status;
                this.title = data.data.liveInfo.topic;
                this.start_ts = data.data.liveInfo.start_ts;
                this.end_ts = data.data.liveInfo.end_ts;
                this.dec = data.data.liveInfo.description;
                this.tmpToken = data.data.tmpToken;
                if (this.currentStatus === this.systemConfig.liveManagement.starting) {
                    if (!this.agoraOptions.channel && data.data.channelId) {
                        this.agoraOptions.channel = data.data.channelId;
                        const channelData = await this.getChannelInfo();
                        this.initAgoraRTC(channelData);
                    }
                }
            }

        },
        gotoDownLoadApp() {
            const ajaxServer =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.port;
            let surl =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.port +
                "/";
            let url = Tool.transferLocationToCe(surl + `activity/activity.html#/qr_install_app`);
            if (url.indexOf("https") < 0) {
                url = url.replace("http", "https");
            }
            url = url.replace("8111", "443");
            window.open(url, "_");
        },
        gotoDownLoadClient() {
            let url = this.serverInfo.installer_url;
            if (url.indexOf("https") < 0) {
                url = url.replace("http", "https");
            }
            url = url.replace("8111", "443");
            window.open(url, "_");
        },
        toggleMute(mute) {
            this.currentMute = mute;
            if (mute) {
                this.currentSubscribedAux.id && this.remoteUsers[this.currentSubscribedAux.id].audioTrack.stop();
            } else {
                this.currentSubscribedAux.id && this.remoteUsers[this.currentSubscribedAux.id].audioTrack.play();
            }
        },
        getChannelInfo() {
            return new Promise((resolve, reject) => {
                let params = {
                    channelId: this.agoraOptions.channel,
                };
                service.getChannelInfoByVisitor(this.ajaxServer, params).then((res) => {
                    console.info(console.info(res, "getChannelInfoByVisitor"));
                    if (!res.data.error_code) {
                        resolve(res.data.data);
                    } else {
                        console.error(res.data);
                        // alert('获取token信息失败')
                    }
                });
            });
        },
        getCurrentSubscribeUidList() {
            return new Promise((resolve, reject) => {
                let params = {
                    channelId: this.agoraOptions.channel,
                };
                service.getCurrentSubscribeUidList(this.ajaxServer, params).then((res) => {
                    if (!res.data.error_code) {
                        this.handleSubscribe(res.data.data);
                        this.currentSubscribeUidList = res.data.data;

                        resolve(res.data.data);
                    } else {
                        resolve([]);
                    }
                });
            });
        },
        handleSubscribe(list = []) {
            if (list[0]) {
                if (this.remoteUsers[list[0]]) {
                    this.subscribeMain(this.remoteUsers[list[0]]);
                }
            }
            if (list[1]) {
                if (this.remoteUsers[list[1]]) {
                    this.subscribeAux(this.remoteUsers[list[1]]);
                }
            }
        },
        handleConnectionStateChange(curState, revState, reason) {
            console.log("handleConnectionStateChange", curState, revState, reason);
            if (curState === "DISCONNECTED") {
                this.Leave();
            }
        },
        async handleChanelStatusChange() {
            const res = await this.getChannelStatus();
            let lastStatus = this.livingStatus;
            if (lastStatus === 1 && !res.status) {
                this.Leave();
            } else if(lastStatus === 0 && res.status) {
                this.joinRoom()
            }
            this.livingStatus = res.status ? 1 : 0;
        },
        getChannelStatus() {
            return new Promise((resolve, reject) => {
                let duration = 0
                if(this.joinLiveStamp){
                    duration = Date.now()-this.joinLiveStamp
                }
                let params = {
                    channelId: this.agoraOptions.channel,
                    nickname:this.myNickName,
                    uuid:this.agoraOptions.uuid,
                    agoraUid:this.agoraOptions.uid,
                    duration,
                };
                service.getChannelStatus(this.ajaxServer, params).then((res) => {
                    console.log(res, "getChannelStatus");
                    if (!res.data.error_code) {
                        resolve(res.data.data);
                    } else {
                        console.error(res.data);
                        // alert('获取token信息失败')
                    }
                });
            });
        },
        swapArrayElements(arr) {
            [arr[0], arr[1]] = [arr[1], arr[0]];
        },
        swapVideoContent() {
            const node1 = document.getElementById("player-box-1");
            const node2 = document.getElementById("player-box-2");
            Tool.swapChildren(node1, node2);
            this.swapArrayElements(this.currentLayout);
            console.log(this.currentLayout);
        },
        resetPlayer2Position() {
            const node2 = document.getElementById("player-box-2");
            node2.style.right = 0;
            node2.style.top = 0;
            node2.style.left = "auto";
            node2.style.bottom = "auto";
            console.log(node2.style);
        },
    },
};
</script>
<style lang="scss" scoped>
.temp_live_room_container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 99;
    background: #000;
    .live_header {
        position: relative;
        .header_title {
            color: #fff;
            text-align: center;
            font-size: 0.8rem;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            .header_title_text {
                width: 70%;
            }
        }
        .right_area {
            position: absolute;
            right: 0.3rem;
            top: 50%;
            transform: translateY(-50%);
            .close_btn {
                font-size: 1.5rem;
            }
        }
    }
    .status-content {
        display: flex;
        justify-content: flex-end;
        padding: 10px;
        font-size: 12px;
        span {
            width: 57px;
            text-align: right;
            flex-shrink: 0;
        }
        .date {
            color: #0c7bea;
        }
        .card-tips-status0 {
            color: #0c7bea;
        }
        .card-tips-status1 {
            color: #c4a20a;
        }
        .card-tips-status2 {
            color: #3dc40a;
        }
        .card-tips-status3 {
            color: #fb1212;
        }
    }
    .live_player_container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden !important;
        height: 20rem;
        .player-box {
            position: relative;
            flex: 1;
            #player-box-1 {
                width: 100%;
                height: 100%;
                background: #0c7bea;
                position: absolute;
                left: 0;
                top: 0;
            }
            #player-box-2 {
                width: 6rem;
                height: 6rem;
                position: absolute;
                right: 0;
                top: 0;
                background: #c4a20a;
            }
        }
        .controls {
            width: 100%;
            height: 2rem;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            padding: 0 0.5rem;
            box-sizing: border-box;
            i {
                font-size: 1.5rem !important;
                color: white !important;
            }
            .con_right {
                display: flex;
            }
        }
        &.horizontal {
            .controls {
                height: 1.1rem;
                i {
                    font-size: 0.8rem !important;
                }
            }
            .player-box {
                #player-box-2 {
                    width: 4rem;
                    height: 4rem;
                }
            }
        }
        #remoteMainPlayer,
        #remoteAuxPlayer {
            flex: 1;
            // height: 150px;
            // max-height: 80vh;
        }
    }
}
</style>
