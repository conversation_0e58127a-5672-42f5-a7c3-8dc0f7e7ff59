<template>
    <div class="exam-project-container" v-loading="loading">
        <div class="exam-list" v-if="examProjects.length > 0">
            <!-- 修改 v-for 循环以适应新的列表项结构 -->
            <div v-for="project in examProjects" :key="project.id" class="exam-item" @click="enterExamDetail(project)">
                <div class="item-icon">
                    <i class="el-icon-files"></i>
                    <!-- 使用Element UI标准图标作为占位符 -->
                </div>
                <div class="item-content">
                    <div class="title-line-wrapper">
                        <div class="item-title">{{ project.title }}</div>
                        <div class="item-type">
                            <el-tag type="primary" effect="plain" size="small">
                                {{ getTestTypeText(project.testType) }}
                            </el-tag>
                        </div>
                    </div>
                </div>
                <div class="item-status">
                    <!-- 左右排列展示通过状态和进度信息 -->
                    <div class="status-info">
                        <div
                            class="result-value-tag"
                            :class="getPassStatusClass(project.isPass)"
                        >
                            {{ formatPassResult(project.isPass) }}
                        </div>
                        <div class="progress-info">
                            <el-progress
                                type="circle"
                                :percentage="project.progress"
                                :width="60"
                                :stroke-width="5"
                                :color="getOnlineQuizProgressColor(project.progress)"
                                :show-text="true"
                            ></el-progress>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 无数据提示 -->
        <div class="empty-state" v-else>
            <i class="el-icon-document"></i>
            <p>{{ lang.no_data_txt }}</p>
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../../lib/base";
import { SMART_TECH_TRAINING_TEST_TYPE, SMART_TECH_TRAINING_ROLE } from "../../../lib/constants";
import Tool from "@/common/tool";
import service from "../../../service/service";

export default {
    mixins: [base],
    name: "ExamProject",
    data() {
        return {
            loading: false,
            SMART_TECH_TRAINING_TEST_TYPE: SMART_TECH_TRAINING_TEST_TYPE,
            userRole: '',
            trainingId: '',
            examProjects: [],
            studentInfo:{}
        };
    },
    watch: {
        $route: {
            handler(to) {
                console.log("to", to);
                if (to.name === "SmartTechTrainingExamProject") {
                    this.$nextTick(() => {
                        this.getTestListByTrainingId();
                    });
                }
            },
            immediate: true,
        },
    },
    created() {
        this.userRole = this.$route.params.role;
        this.trainingId = this.$route.params.trainingId;
    },
    methods: {
        getTestTypeText(type) {
            switch (type) {
            case SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD:
                return this.lang.ATTACHMENT_UPLOAD;
            case SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ:
                return this.lang.ONLINE_QUIZ;
            default:
                return "";
            }
        },
        formatPassResult(isPass) {
            return isPass ? this.lang.passed : this.lang.not_pass;
        },
        // 获取通过状态CSS类名
        getPassStatusClass(isPass) {
            if (isPass === true) {
                return "status-tag-passed-new";
            } else if (isPass === false) {
                return "status-tag-failed-new";
            }
            return "status-tag-default-new"; // 默认返回值
        },
        // 点击卡片进入相应考核项目的方法
        enterExamDetail(project) {
            // 根据项目类型跳转到不同的考核页面
            console.log("进入考核项目:", project);
            if(this.userRole === SMART_TECH_TRAINING_ROLE.STUDENT){
                if(project.testType === SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD){
                    Tool.loadModuleRouter({
                        name:'SmartTechTrainingExamProject_UploadTestOverview',
                        params:{
                            ...this.$route.params,
                            testId:project.id,
                            studentInfo:this.studentInfo,
                        }
                    });
                }else if(project.testType === SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ){
                    Tool.loadModuleRouter({
                        name:'SmartTechTrainingExamProject_OnlineTestOverview',
                        params:{
                            ...this.$route.params,
                            testId:project.id,
                            studentInfo:this.studentInfo,
                        }
                    });
                }
            }else{
                this.$router.push(this.$route.fullPath + '/correcting_test_overview');
            }

            // 可以根据项目ID或类型跳转到不同页面
            // this.$router.push({ path: `/smartTechTraining/exam/${project.id}` });
        },
        getOnlineQuizProgressColor(progress) {
            if (progress === 100) {
                return "#67C23A"; // 绿色 (Element UI Success)
            } else if (progress >= 20 && progress < 100) {
                return "#E6A23C"; // 黄色 (Element UI Warning)
            } else {
                // progress < 20 (包括 0)
                return "#F56C6C"; // 红色 (Element UI Danger)
            }
        },
        getTestListByTrainingId(){
            this.loading = true;
            service.getTestListByTrainingId({
                trainingID:this.$route.params.trainingId
            }).then(res=>{
                this.loading = false;
                console.log(res,'res');
                // 确保返回数据有效且包含testList数组
                if(res.data.error_code === 0){
                    const data = res.data.data;
                    this.studentInfo = {
                        hospital:data?.hospital,
                        name:data?.name,
                        id:data?.id,
                        sex:data?.sex,
                        uuid:data?.uuid,
                        _id:data?._id,
                    }
                    const testList = res.data.data?.testList;
                    if (Array.isArray(testList)) {
                    // 将API返回的数据适配到examProjects
                        this.examProjects = testList.map(item => {
                            return {
                                id: item.testID , // 尝试使用备选字段
                                title: item.title || '',
                                progress: item.isPass ? 100 : item.progress,
                                testType: item.type || '',
                                isPass: item.isPass, // 直接使用原始isPass值
                            };
                        });
                        console.log(this.examProjects,'this.examProjects');
                    } else {
                        // 如果没有有效数据，将examProjects设为空数组
                        this.examProjects = [];
                    }
                }
            }).catch(err=>{
                this.loading = false;
                console.log(err,'err');
                this.examProjects = []; // 错误时确保examProjects为空
            });
        }
    },

};
</script>

<style lang="scss" scoped>
.exam-project-container {
    padding: 20px;
    background-color: #f7f8fa; /* 假设图片背景色偏白或浅灰 */
    position: relative;
    height: 100%;

    .exam-list {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05); /* 细微阴影 */
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;
        .exam-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #eeeeee; /* 分割线 */
            cursor: pointer;
            transition: background-color 0.2s;

            &:last-child {
                border-bottom: none; /* 最后一个项目无下边框 */
            }

            &:hover {
                background-color: #f9f9f9;
            }

            .item-icon {
                margin-right: 20px;
                font-size: 32px; /* 调整图标大小 */
                color: #cccccc; /* 占位图标颜色 */
                flex-shrink: 0;
            }

            .item-content {
                flex-grow: 1;
                margin-right: 20px;

                .title-line-wrapper {
                    display: flex;
                    align-items: center;
                    margin-bottom: 6px;

                    .item-title {
                        font-size: 16px;
                        font-weight: 500;
                        color: #333333;
                        line-height: 1.4;
                        margin-right: 8px;
                    }

                    .item-type {
                        line-height: 0;
                    }
                }
            }

            .item-status {
                display: flex;
                align-items: center;
                flex-shrink: 0;
                width: 180px; /* 增加宽度以容纳更多内容 */
                justify-content: flex-start; /* 从左开始对齐内容 */

                .status-info {
                    display: flex;
                    flex-direction: row; /* 改为水平排列 */
                    align-items: center;
                    width: 100%;
                    justify-content: space-between; /* 左右分布 */

                    .result-value-tag {
                        border-radius: 4px;
                        font-size: 15px;
                        color: #202226;
                        text-align: center;
                        line-height: 24px;
                        font-weight: 400;
                        padding: 1px 8px;
                        display: inline-block;
                        min-width: 70px;
                        box-sizing: border-box;

                        &.status-tag-passed-new {
                            background: rgba(3, 203, 0, 0.3);
                            border: 1px solid #00a124;
                        }

                        &.status-tag-failed-new {
                            background: rgba(255, 82, 25, 0.3);
                            border: 1px solid #e5110a;
                        }

                        &.status-tag-default-new {
                            background: #f0f0f0;
                            border: 1px solid #ccc;
                            color: #555;
                        }
                    }

                    .progress-info {
                        display: flex;
                        align-items: center;
                    }
                }

                /* el-progress 已经在模板中设置了大小, 这里可以微调 */
                .el-progress {
                    margin-right: 8px;

                    // 调整圆圈内文字大小
                    ::v-deep .el-progress__text {
                        font-size: 14px !important;
                        font-weight: 600;
                    }
                }
            }
        }
    }

    .page-title {
        margin-top: 0;
        margin-bottom: 20px;
        color: #333;
        font-size: 22px;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 0;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

        i {
            font-size: 48px;
            color: #dcdfe6;
            margin-bottom: 16px;
        }

        p {
            color: #909399;
            font-size: 16px;
        }
    }
}
</style>
