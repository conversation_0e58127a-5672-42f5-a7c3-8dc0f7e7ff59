<template>
    <div class="correcting-online-test-overview-container">
        <div class="custom_header">
            <div class="back_btn" @click.stop="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body" ref="customBody" v-loading="loading">
            <template v-if="!loading">
                <div class="top_info_section">
                    <div class="icon_area">
                        <div class="placeholder_icon">
                            <i class="el-icon-collection"></i>
                        </div>
                    </div>
                    <div class="details_area">
                        <div class="detail_top">
                            <div class="detail_title">
                                {{ title }}
                            </div>
                        </div>
                        <div class="detail_bottom">
                            <div class="detail_info">
                                <div class="info_row">
                                    <span class="info_label">{{lang.exam_questions_count}}:</span>
                                    <span class="info_value">{{ questionsCount }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{lang.min_pass_questions}}:</span>
                                    <span class="info_value">{{ minPassQuestionsDisplay }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{capitalizeFirstLetter(lang.deadline)}}:</span>
                                    <span class="info_value">{{ deadline }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{lang.student_name}}:</span>
                                    <span class="info_value">{{ studentName }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{lang.affiliated_hospital}}:</span>
                                    <span class="info_value">{{ hospital }}</span>
                                </div>
                                <div class="info_row">
                                    <span class="info_label">{{lang.test_duration}}:</span>
                                    <span class="info_value">{{ formatUseTime }}</span>
                                </div>
                            </div>
                            <div class="detail_btns">
                                <!-- 未批改状态显示进入批改按钮 -->
                                <el-button
                                    v-if="canStartCorrection"
                                    type="primary"
                                    @click="enterGradingMode"
                                >
                                    {{ lang.enter_correction }}
                                </el-button>
                                <!-- 已批改状态显示查看详情按钮 -->
                                <el-button
                                    v-else
                                    type="primary"
                                    @click="viewDetail"
                                >
                                    {{ lang.view_details }}
                                </el-button>
                            </div>
                            <div
                                v-if="warningMessage.show"
                                class="warning_text"
                            >
                                <i class="el-icon-warning"></i> {{ warningMessage.text }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 学员答题历史 -->
                <div class="content_section exam_history_section">
                    <h3>{{ lang.answer_record }}</h3>
                    <el-table :data="examHistoryData" style="width: 100%">
                        <el-table-column prop="createdAt" :label="capitalizeFirstLetter(lang.submission_time)">
                            <template slot-scope="scope">
                                {{  formatTime(scope.row.createdAt) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="scoreTime" :label="capitalizeFirstLetter(lang.correction_time)">
                            <template slot-scope="scope" v-if="scope.row.scoreTime">
                                {{  formatTime(scope.row.scoreTime *1000) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="passCount" :label="capitalizeFirstLetter(lang.questions_passed_number)"></el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.correction_status)">
                            <template slot-scope="scope">
                                <span v-if="scope.row.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.UNCORRECTED">{{ lang.Uncorrected }}</span>
                                <span v-else-if="scope.row.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.CORRECTED">{{ lang.corrected }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="isPass" :label="capitalizeFirstLetter(lang.exam_result)">
                            <template slot-scope="scope" v-if="scope.row.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.CORRECTED">
                                <span
                                    :class="[
                                        {
                                            'status-tag-passed-new': scope.row.isPass === true,
                                            'status-tag-failed-new': scope.row.isPass === false,
                                        },
                                    ]"
                                >{{ formatPassResult(scope.row.isPass) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.operation)" width="200">
                            <template slot-scope="scope">
                                <div class="operation-buttons-container">
                                    <el-button
                                        @click="viewAnalysis(scope.row)"
                                        type="text"
                                        size="small"
                                        class="option-btn"
                                    >
                                        {{ lang.view_details }}
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../../lib/base";
import onlineTestOverviewMixins from "../../../lib/onlineTestOverviewMixins";
import {
    CLOUD_TEST_TYPE,
    SMART_TECH_TRAINING_ROLE,
    SMART_TECH_TRAINING_TEST_TYPE,
    SMART_TECH_TRAINING_TEST_RETRY_TYPE,
    SMART_TECH_TRAINING_TEST_CORRECT_STATUS
} from "@/module/ultrasync_pc/lib/constants";
import Tool from "@/common/tool";
import service from "@/module/ultrasync_pc/service/service";
import moment from "moment";

export default {
    mixins: [base, onlineTestOverviewMixins],
    name: "CorrectingOnlineTestOverview",
    components: {},
    data() {
        return {
            capitalizeFirstLetter: Tool.capitalizeFirstLetter,
            SMART_TECH_TRAINING_TEST_CORRECT_STATUS,
            loading: true,
            title: "",
            deadline: "",
            questionsCount: 0,
            examDuration: "",
            examMode: "",
            passScore: "",
            examHistoryData: [],
            userRole: "",
            testInfo: {},
            testId: "",
            trainingId: "",
            pagerInfo: [],

            // 学员信息
            studentName: "",
            hospital: "",
            submitTime: "",
            useTime: 0,
            // 存储从API获取并组装后的完整数据
            processedGradingData: null,
            studentInfo: null,
            minPassQuestions:""
        };
    },
    computed: {
        formatUseTime() {
            if (!this.useTime) {
                return "0分钟";
            }
            return Tool.formatDurationFromSeconds(this.useTime);
        },
        // 判断是否可以开始批改
        canStartCorrection() {
            if (this.loading || this.examHistoryData.length === 0) {
                return true; // 数据加载中或无历史记录时允许批改
            }
            // 检查第一条记录的状态是否为未批改
            const firstRecord = this.examHistoryData[0];
            return firstRecord.status === this.SMART_TECH_TRAINING_TEST_CORRECT_STATUS.UNCORRECTED;
        },
        // 统一管理警告信息
        warningMessage() {
            // 由于现在按钮根据状态动态显示，不再需要警告信息
            return {
                show: false,
                text: ""
            };
        },
        // 动态计算考试路由名称
        examRouteName() {
            const currentRouteName = this.$route.name;
            if (currentRouteName === "SmartTechTrainingStudentOverview_CorrectingOnlineTestOverview") {
                return "SmartTechTrainingStudentOverview_Exam";
            } else if (currentRouteName === "SmartTechTrainingGradingReview_CorrectingOnlineTestOverview") {
                return "SmartTechTrainingGradingReview_Exam";
            }
            // 默认返回批改审核的路由（向后兼容）
            return "SmartTechTrainingGradingReview_Exam";
        },
        // 优化最小通过题数的显示
        minPassQuestionsDisplay() {
            if (this.minPassQuestions === 0) {
                return "要求所有题目全部通过";
            }
            return this.minPassQuestions;
        },
    },
    watch: {
        $route: {
            handler(to) {
                if (to.name === "SmartTechTrainingGradingReview_CorrectingOnlineTestOverview"
                || to.name === "SmartTechTrainingStudentOverview_CorrectingOnlineTestOverview") {
                    this.$nextTick(()=>{
                        this.fetchPageData();
                    })

                }
            },
            immediate: true,
        },
    },
    created() {

        this.studentInfo = this.$route.params.studentInfo;
    },
    beforeDestroy() {},
    methods: {
        back() {
            if (this.$router) {
                this.$router.go(-1);
            } else {
                console.warn("Vue Router not found for back() method.");
            }
        },

        enterGradingMode() {
            // 优先使用从API获取的原始数据，如果没有则使用路由数据
            let dataToUse = this.processedGradingData;

            if (!dataToUse) {
                console.error("no available grading data");
                return;
            }

            // 先尝试加锁，成功后再进入批改页面
            this.tryLockAndProceedToGrading(dataToUse);
        },

        // 尝试加锁并进入批改
        tryLockAndProceedToGrading(gradingData) {
            const params = {
                testID: this.testId,
                studentID: this.studentInfo?.uid,
            };

            // 调用加锁API
            service.keepAnswerLock(params)
                .then(result => {
                    if (result.data.error_code === 0) {
                        console.log('批改加锁成功:', result);
                        // 加锁成功，进入批改页面
                        this.proceedToGrading(gradingData);
                    } else {
                        console.error('批改加锁失败:', result);
                        // this.$message.error(result.data.message || '加锁失败，无法进入批改');
                    }
                })
                .catch(error => {
                    console.error('批改加锁失败:', error);
                    this.$message.error('加锁失败，无法进入批改');
                });
        },

        // 进入详细批改页面
        proceedToGrading(gradingData) {
            Tool.loadModuleRouter({
                name: this.examRouteName,
                params: {
                    ...this.$route.params,
                    pager_type: CLOUD_TEST_TYPE.CORRECT,
                    testData: this.testInfo,
                    gradingData: gradingData,
                    studentInfo: this.studentInfo,
                },
            });
        },

        viewDetail() {
            // 优先使用从API组装的数据，如果没有则使用路由数据作为备选
            let dataToUse = this.processedGradingData;
            // 查看已批改的详情页面
            Tool.loadModuleRouter({
                name: this.examRouteName,
                params: {
                    ...this.$route.params,
                    pager_type: CLOUD_TEST_TYPE.VIEW_RESULT,
                    testData: this.testInfo,
                    gradingData: dataToUse,
                    studentInfo:this.studentInfo,
                },
            });
        },

        viewAnalysis(row) {
            console.log("View analysis for row:", row);

            // 从原始API数据中查找当前行对应的记录
            if (!this.examHistoryData || this.examHistoryData.length === 0) {
                console.error("no exam history data");
                return;
            }

            const allHistoryData = this.examHistoryData;

            // 根据创建时间匹配当前行的记录
            const targetRecord = allHistoryData.find(record =>
                new Date(record.createdAt).getTime() === new Date(row.createdAt).getTime()
            );

            if (!targetRecord) {
                console.error("no corresponding exam record");
                return;
            }

            // 组装单条记录的数据
            const assembledData = this.assembleSingleRecord(targetRecord);

            if (!assembledData) {
                console.error("data assembly failed");
                return;
            }

            // 跳转到查看结果页面
            Tool.loadModuleRouter({
                name: this.examRouteName,
                params: {
                    ...this.$route.params,
                    pager_type: CLOUD_TEST_TYPE.VIEW_RESULT,
                    testData: this.testInfo,
                    gradingData: assembledData,
                    studentInfo: this.studentInfo,
                },
            });
        },

        fetchPageData() {
            this.loading = true;

            // 从路由参数获取ID
            const currentTestId = this.$route.params.testId;
            const currentTrainingId = this.$route.params.trainingId;

            this.testId = currentTestId;
            this.trainingId = currentTrainingId;

            if (this.studentInfo) {
                this.studentName = this.studentInfo.name || "";
                this.hospital = this.studentInfo.hospital || "";
            }

            console.log(`Fetching grading data for testId: ${currentTestId}, trainingId: ${currentTrainingId}`);

            Promise.all([
                this.getTrainingTestInfoByTestId(),
                this.getTrainingTestAnswerHistory()
            ]).finally(() => {
                this.loading = false;
                console.log("Grading data fetching complete.");
            });
        },

        getTrainingTestInfoByTestId() {
            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestInfoByTestId({
                        testID: this.testId,
                        trainingID: this.trainingId,
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0) {
                            this.testInfo = res.data.data;

                            if (this.testInfo) {
                                this.title = this.testInfo.title || "未命名考试";
                                this.questionsCount = this.testInfo.questionCount || 0;
                                this.minPassQuestions = this.testInfo.minPassQuestions || 0;
                                // 格式化截止时间
                                if (this.testInfo.deadline) {
                                    this.deadline = moment.unix(this.testInfo.deadline).format("YYYY-MM-DD");
                                }
                                this.pagerInfo = this.testInfo.pagerInfo || [];

                                // 处理考试模式
                                this.examMode =
                                    this.testInfo.retryType === SMART_TECH_TRAINING_TEST_RETRY_TYPE.MULTIPLE
                                        ? "多次作答"
                                        : "单次作答";
                            }
                            resolve(res.data.data);
                        } else {
                            console.error(res.data.message || "get exam info failed");
                            reject(new Error(res.data.message || "get exam info failed"));
                        }
                    })
                    .catch((error) => {
                        console.error("getTrainingTestInfoByTestId error:", error);
                        reject(error);
                    });
            });
        },

        getTrainingTestAnswerHistory() {
            // 添加studentID参数
            const studentID = this.studentInfo?.uid;

            return service
                .getTrainingTestAnswerHistory({
                    testID: this.testId,
                    studentID: studentID
                })
                .then((res) => {
                    console.log(res, "res");
                    if (res.data.error_code === 0 && res.data.data && Array.isArray(res.data.data)) {
                        // 处理考试历史数据
                        this.examHistoryData = res.data.data

                        // 使用API返回的所有历史数据进行组装
                        if (res.data.data.length > 0) {
                            // 使用所有历史记录进行组装
                            this.useTime = this.examHistoryData[0].useTime || 0;
                            this.submitTime = this.formatTime(this.examHistoryData[0].createdAt)
                            this.processedGradingData = this.assembleAllAnswerRecords(res.data.data);
                            console.log("API所有历史数据组装成功:", this.processedGradingData);
                        } else {
                            console.warn("API returned empty answer history data");
                        }
                    }
                    return res;
                })
                .catch((err) => {
                    console.log(err, "err");
                    return err;
                });
        },

        formatPassResult(isPass) {
            return isPass ? this.lang.passed : this.lang.not_pass;
        },

    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.correcting-online-test-overview-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;
    z-index: 10;
    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: auto;
        background-color: #f7f9fc;
    }
}

.top_info_section {
    display: flex;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    justify-content: center;
    padding-bottom: 40px;
    border-bottom: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .icon_area {
        margin-right: 25px;

        .placeholder_icon {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background-color: #ffd700;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #fff;

            i {
                font-size: 80px;
            }
        }
    }

    .detail_bottom {
            max-width: 700px;

            .detail_info {
                display: flex;
                flex-wrap: wrap;
                margin: 10px 0;

                // 每条信息
                .info_row {
                    width: 50%; // 两列
                    display: flex;
                    align-items: center; // 垂直居中
                    margin-bottom: 8px;
                    justify-content: flex-start; // 所有行都左对齐

                    .info_label {
                        min-width: 80px;
                        font-size: 14px;
                        color: #909399;
                        text-align: left;
                        margin-right: 5px;
                    }

                    .info_value {
                        font-size: 14px;
                        color: #303133;
                        font-weight: 500;
                    }
                }
            }

            .detail_btns {
                display: flex;
                margin-top: 10px;
                .el-button {
                    min-width: 120px;
                }
            }
            .warning_text {
                margin-top: 10px;
                color: #F56C6C;
                font-size: 14px;
                text-align: left;
            }
        }
}

.content_section {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-bottom: none;

    h3 {
        margin-bottom: 18px;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }
}


</style>
