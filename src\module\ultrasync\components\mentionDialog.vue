<template>
    <transition name="slide">
        <div class="mention_dialog fourth_level_page">
            <mrHeader @click-left="closeDialog">
                <template #title>
                    {{lang.mention_dialog_title}}
                </template>
                <template #right>
                    <span class="add_attendee_btn" @click="submit" :class="{enable:enable}">{{lang.confirm_txt}}({{groupUser.length}})</span>
                </template>
            </mrHeader>
            <div class="group_setting_container">
                <ContactSelectList :optionList="checkOption" v-model="groupUser" v-if="activatedComponent"></ContactSelectList>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import ContactSelectList from "./contactSelectList.vue";
export default {
    mixins: [base],
    name: 'mentionSelectDialog',
    props:['show','callback'],
    components: {
        ContactSelectList
    },
    data(){
        return {
            cid:this.$route.params.cid,
            groupUser:[],
            activatedComponent:true
        }
    },
    beforeD<PERSON>roy(){
    },
    mounted(){
        this.$nextTick(()=>{
        })
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{}
        },
        attendeeList(){
            return this.conversation.attendeeList
        },
        attendeeArray(){
            let arr=[];
            for(let key in this.attendeeList){
                if (this.attendeeList[key].attendeeState!=0) {
                    arr.push(this.attendeeList[key])
                }
            }
            return arr
        },
        checkOption(){
            var arr=[]
            for(let item of this.attendeeArray){
                if (item.userid==this.user.uid) {
                    continue;
                }
                let option={}
                option.label=this.remarkMap[item.userid]||item.alias_name||item.nickname
                option.value=item.userid
                option.avatar=item.avatar
                arr.push(option)
            }
            arr.unshift({label:this.lang.everyone,value:'all'})
            return arr;
        },
        enable(){
            return this.groupUser.length>0
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
    },
    methods:{
        submit(){
            if (this.enable) {
                let arr=[]
                for(let userId of this.groupUser){
                    const user = this.checkOption.find((item)=>item.value === userId)
                    console.log(user)
                    // if(user&&user.label){
                    //     arr.push(user.label)
                    // }
                    arr.push({
                        uid: user.value,
                        nickname: user.label,
                    });

                }
                this.callback&&this.callback(arr)
                this.$emit('update:show', false);
            }
        },
        closeDialog(){
            this.$emit('update:show', false);
        }
    }
}

</script>
<style lang="scss">
.mention_dialog{
    .choose_list{
        background:#fff;
        .group_user_item{
            padding:0.5rem;
        }
    }
    .group_setting_container{
        flex:1;
        overflow: hidden;
    }
    .add_attendee_btn{
        width: 4rem;
        color: #fff;
        opacity: 0.6;
        font-size: 0.7rem;
        text-align: right;
        &.enable{
            opacity:1;
        }
    }
}
</style>
