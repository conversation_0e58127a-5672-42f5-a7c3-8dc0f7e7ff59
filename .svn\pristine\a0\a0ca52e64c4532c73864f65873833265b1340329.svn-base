<template>
    <transition name="slide">
        <div class="group_managers_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.group_join_verify_btn}}
                </template>
            </mrHeader>
            <div class="group_managers_container" v-loading="loading">
                <div class="no_data_txt" v-if="verifyList.length===0">{{lang.no_data_txt}}</div>
                <van-list
                    ref="loadmore"
                    v-model="applyListIsLoading"
                    @load="loadApplyList"
                    :finished="bottomAllLoaded"
                    :loading-text="lang.bottom_loading_text"
                    offset="0"
                >
                    <div v-for="(item,index) in verifyList" class="group_join_verify_item" :key="index">
                        <mr-avatar :url="getLocalAvatar(item.userInfo)" :key="item._id" :radius="2"  @click.native="openVisitingCard(item.userInfo,2)"></mr-avatar>
                        <div class="item_body">
                            <p class="item_nickname">{{item.userInfo.nickname|applyTitle}}</p>
                            <p class="item_description">{{item.mark}}</p>
                        </div>
                        <van-button v-if="item.status===0" class="confirm_btn" type="primary" size="small" @click="agreeApply(item)">{{lang.agree_txt}}</van-button>
                        <span v-else-if="item.status===1" class="verify_status">{{lang.group_apply_pass}}</span>
                        <span v-else-if="item.status===2" class="verify_status">{{lang.group_apply_expire}}</span>
                    </div>
                </van-list>
            </div>
            <keep-alive>
                <router-view></router-view>
            </keep-alive>
        </div>
    </transition>
</template>
<script>
import { Button, List } from 'vant'
import base from '../lib/base'
import {openVisitingCard,getLocalAvatar} from '../lib/common_base'
export default {
    mixins: [base],
    name: 'groupJoinVerify',
    components: {
        VanButton: Button,
        VanList: List
    },
    data(){
        return {
            getLocalAvatar,
            cid:this.$route.params.cid,
            loading:false,
            verifyList:[
            ],
            bottomAllLoaded:false,
            applyListIsLoading: false,
            currentPage:0,
        }
    },
    beforeDestroy(){
    },
    mounted(){
        // this.initList();
    },
    activated(){
        this.cid=this.$route.params.cid;
        this.initList();
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{galleryObj:{},iworksList:{}}
        },
    },
    filters:{
        applyTitle(nickname){
            return window.vm.$store.state.language.group_join_apply_tip.replace('${1}',nickname);
        }
    },
    methods:{
        openVisitingCard(messages,type){
            openVisitingCard(messages,type)
        },
        initList(){
            this.loading=true;
            this.currentPage = 0;
            this.bottomAllLoaded = false;
            this.verifyList = []
            this.loadApplyList();
            this.readApplyJoin();
        },
        agreeApply(item){
            window.main_screen.conversation_list[this.cid].agreeApplyJoin({
                applyID:item._id
            },(res)=>{
                if(res.error_code === 0){
                    item.status = 1;
                }else{
                }
            })
        },
        loadApplyList(){
            this.currentPage++;
            window.main_screen.conversation_list[this.cid].getApplyList({
                page:this.currentPage,
            },(res)=>{
                this.loading=false;
                this.applyListIsLoading = false;
                if(res.error_code === 0){
                    this.verifyList = this.verifyList.concat(res.data.list);
                    if (this.verifyList.length === res.data.total) {
                        this.bottomAllLoaded = true;
                    }
                }else{
                    this.bottomAllLoaded = true;
                }
            })
        },
        readApplyJoin(){
            window.main_screen.conversation_list[this.cid].readApplyJoin({},(res)=>{});
            this.$store.commit('conversationList/updateConversation',{
                cid:this.cid,
                key:'applyCount',
                value:0,
            });
        }
    }
}
</script>
<style lang="scss">
.group_managers_page{
    .group_managers_container{
        overflow:auto;
        height:calc(100% - 2.2rem);
        .group_join_verify_item{
            display: flex;
            align-items: center;
            margin: .5rem;
            border-bottom: 1px solid #eee;
            padding: 0 0.5rem 0.5rem;

            .confirm_btn{
                font-size: 0.6rem;
            }

            .item_body{
                flex:1;
                padding: 0 .7rem;
                .item_nickname{
                    font-size: .8rem;
                }
                .item_description{
                    font-size: .7rem;
                }
            }
            .verify_status{
                font-size: .7rem;
            }
        }
        .no_data_txt{
            text-align: center;
            font-size: .8rem;
            color: #333;
            margin-top: 1rem;
        }
    }
}
</style>
