<template>
    <div class="realtime_video_page third_level_page">
		<div class="video_page">
        <i class="icon iconfont icon-back close_video_page" @click="closeVideo"></i>
        <video class="main_video" :src="mainVideoSrc" autoplay="autoplay" controls @timeupdate="timeupdate" ref="mainVideo">{{lang.unsupport_video_text}}</video>
        <video class="gesture_video" v-show="gestrueVideoSrc!=''" controls :src="gestrueVideoSrc" ref="gestrueVideo">{{lang.unsupport_video_text}}</video>
        </div>
	</div>
</template>

<script>
import base from '../lib/base'
import { Toast } from 'vant';
export default {
    mixins: [base],
    name: 'RealtimeVideoPage',
    components: {},
    data(){
        return {
            cid:this.$route.params.cid,
            mainVideoSrc:'',
            gestrueVideoSrc:'',
        }
    },
    mounted(){
        var that = this;
        this.$nextTick(()=>{
            this.mainVideoSrc = this.conversation.hls_ultrasound_video;
            this.gestrueVideoSrc = this.conversation.hls_gesture_video;
            console.log("** hls_ultrasound_video: " + this.mainVideoSrc);
            console.log("** hls_gesture_video: " + this.gestrueVideoSrc);

            this.$refs.mainVideo.addEventListener('play',function(){
                that.$refs.gestrueVideo.pause();
            });
            this.$refs.gestrueVideo.addEventListener('play',function(){
                that.$refs.mainVideo.pause();
            });

            this.$root.eventBus.$off('on_close_realtime_video').$on('on_close_realtime_video',function(){
                if(/realtimeVideo/.test(that.$route.fullPath)){
                    setTimeout(()=>{
                        that.closeVideo();
                    },200)
                }

            });

        })
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{};
        },
    },
    methods:{
        closeVideo(){
            this.$refs.mainVideo.pause();
            this.$refs.gestrueVideo.pause();
            this.$root.eventBus.$emit('closeRecieveDestop')
            this.back();
        },
        timeupdate(e){
            console.log('timeupdate',e)
        },
    },
}
</script>

<style lang="scss">
	.realtime_video_page{
		.video_page{
            position:absolute;
            width:100%;
            height:100%;
            top:0;
            background-color: #333;
            z-index:20;
            .main_video{
                width:100%;
                height:auto;
                background-color:#000;
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
            }
            .gesture_video{
                width:8rem;
                height:auto;
                background-color:#000;
                position:absolute;
                top:.5rem;
                right:.5rem;
            }
            .close_video_page{
                position: absolute;
                top: .3rem;
                left: .3rem;
                font-size: 1.3rem;
                z-index:1;
                color: #fff;
            }
        }
	}
</style>
