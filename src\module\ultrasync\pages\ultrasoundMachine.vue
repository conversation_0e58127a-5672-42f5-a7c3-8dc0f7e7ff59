<template>
    <div class="machine_page second_level_page">
        <mrHeader>
            <template #title>
                {{lang.ultrasound_device_title}}
            </template>
            <template #right>
                <span v-show="ultrasoundMachine.isConnecting" class="setting" @click="showSetting">{{lang.setting_title}}</span>
                <transition name="fade">
                    <div class="machine-popup" v-show="popupSetting">
                        <p @click="openInfo">{{lang.machine_info}}</p>
                        <p @click="disconnectMachine">{{lang.disconnet_machine_btn}}</p>
                    </div>
                </transition>
                <div class="full_modal" v-show="showModal" @click="closeModal"></div>
            </template>
        </mrHeader>
        <div class="machine_container">
            <div v-show="loadingLocalImg" class="full_loading_spinner van_loading_spinner">
                <van-loading color="#00c59d" />
            </div>
            <div class="initation" v-if="onlyMachineConnect&&ultrasoundMachine.tabIndex==-1">
                <div v-show="!hideTransfer" class="modal_item" @click="toggleTab(1)">{{lang.transfer_helper_text}}</div>
                <div v-show="!hideExamBrowser" class="modal_item" @click="toggleTab(2)">{{lang.exam_browse_text}}</div>
            </div>
            <div class="main" v-else>
                <div class="tabs">
                    <div v-show="bothConnect" @click="toggleTab(0)" class="tab_item" :class="{active:ultrasoundMachine.tabIndex==0}">{{lang.control_panel}}</div>
                    <div v-show="!hideTransfer&&ultrasoundMachine.isConnecting" @click="toggleTab(1)" class="tab_item" :class="{active:ultrasoundMachine.tabIndex==1}">{{lang.transfer_helper_text}}</div>
                    <div v-show="!hideExamBrowser&&ultrasoundMachine.isConnecting" @click="toggleTab(2)" class="tab_item" :class="{active:ultrasoundMachine.tabIndex==2}">{{lang.exam_browse_text}}</div>
                </div>
                <div class="transfer_helper" v-show="ultrasoundMachine.tabIndex==1">
                    <div class="machine_name">{{ultrasoundMachine.ultrasound_machine_name}}</div>
                    <div class="image_list_container">
                        <div v-for="(item,index) of ultrasoundMachineTransferList" class="image_list" :key="index">
                            <p>{{formatTime(item.exam_time)}}</p>
                            <div class="clearfix">
                                <div v-for="(img,j_index) of item.image_list" class="file_item" :key="j_index">
                                    <div class="file_container">
                                        <img :src="img.url_local"  class="file_image needsclick" @click="openGallery(index,j_index)">
                                        <i v-if="img.msg_type==systemConfig.msg_type.Cine" class="icon iconfont icon-videofill"></i>
                                    </div>
                                    <van-checkbox-group
                                        v-model="img.checked"
                                    >
                                        <van-checkbox :name="index+'-'+j_index" @click.stop="checkImg(index,j_index)" checked-color="#00c59d"></van-checkbox>
                                    </van-checkbox-group>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="footer">
                        <div v-show="user.uid" class="item" @click="openTransmit">
                            {{lang.transmit_title}}
                        </div>
                        <div v-show="user.uid" class="item" @click="localImage">
                            {{lang.machine_local_btn}}
                        </div>
                        <div class="item" @click="destroyImage">
                            {{lang.machine_destroy_btn}}
                        </div>
                        <!-- <div class="item" v-show="isSelectAll" @click="cancelSelectAll">
                            {{lang.cancel_select_all}}
                        </div>
                        <div class="item" v-show="!isSelectAll" @click="selectAll">
                            {{lang.select_all}}
                        </div> -->
                    </div>
                </div>
                <div class="exam_browse" v-show="ultrasoundMachine.tabIndex==2">
                    <div class="search_bar" style="display:none;">
                        <p class="search_text">
                            {{searchText}}
                        </p>
                        <div class="search_condition">
                            <div class="search_condition_item">
                                {{lang.exam_id}}:
                                <input type="text" class="search_condition_text" :placeholder="lang.exam_id">
                            </div>
                            <div class="search_condition_item">
                                {{lang.exam_type}}:
                            </div>
                            <div class="search_condition_item">
                                {{lang.exam_time}}:
                                <div>
                                    <input type="text" class="search_condition_date" :value="getPickerDate(examStarDate)" readonly @click.stop="openExamDatePicker($event,1)">
                                    <span>{{lang.date_to}}</span>
                                    <input type="text" class="search_condition_date"  :value="getPickerDate(examEndDate)" readonly @click.stop="openExamDatePicker($event,2)">
                                    <van-popup ref="examStarPicker" v-model="showExamStarPicker" position="bottom">
                                        <van-datetime-picker
                                            v-model="examTempDate"
                                            type="date"
                                            @confirm="handleStarConfirm"
                                            @cancel="handleStarCancel"
                                        />
                                    </van-popup>
                                    <van-popup ref="examEndPicker" v-model="showExamEndPicker" position="bottom">
                                        <van-datetime-picker
                                            v-model="examTempDate"
                                            type="date"
                                            @confirm="handleEndConfirm"
                                            @cancel="handleEndCancel"
                                        />
                                    </van-popup>
                                </div>
                            </div>
                            <div class="search_condition_item">
                                {{lang.patient_name}}:
                                <input type="text" class="search_condition_text" :placeholder="lang.patient_name">
                            </div>
                            <div class="search_condition_item">
                                {{lang.patient_sex}}:
                            </div>
                            <div class="search_condition_item">
                                {{lang.patient_age}}:
                                <input type="num" class="search_condition_text" :placeholder="lang.patient_age">
                            </div>
                        </div>
                        <span class="reflesh_btn">{{lang.exam_flesh_btn}}</span>
                    </div>
                    <div class="exam_list">
                        <div v-for="(item,index) of ultrasoundMachineExamList" class="exam_item" :key="index">
                            <div class="exam_title" @click="toggleImageList(item,index)">
                                <i class="iconfont icon-down" v-show="!item.open"></i>
                                <i class="iconfont icon-up" v-show="item.open"></i>
                                <div class="clearfix">
                                    <p class="left_item">{{lang.exam_id}}: {{item.patient_id}}</p>
                                    <p class="right_item">{{lang.exam_type}}: {{item.exam_mode}}</p>
                                </div>
                                <p>{{lang.exam_time}}: {{formatTime(item.exam_time)}}</p>
                                <div class="clearfix">
                                    <p  class="left_item">{{lang.patient_name}}: {{item.patient_name}}</p>
                                    <p  class="right_item">{{lang.patient_sex}}: {{item.patient_sex}}</p>
                                </div>

                                <p>{{lang.patient_age}}:{{item.patient_age}}</p>
                            </div>
                            <div class="exam_image_list clearfix" v-show="item.open">
                                <div v-show="!item.finishImageList" class="loading_image">
                                    <van-loading color="#00c59d" />
                                </div>
                                <div v-for="(img,img_index) of item.image_list" class="file_item" :key="img_index">
                                    <div class="file_container">
                                        <img :src="img.url_local"  class="file_image needsclick" @click="openExamLocalImage(item,img_index)">
                                        <i v-if="img.msg_type==systemConfig.msg_type.Cine" class="icon iconfont icon-videofill"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-show="loadingMoreExam" class="loading">
                            <van-loading color="#00c59d" />
                        </div>

                    </div>
                </div>
                <div v-show="onlyDeviceConnect||ultrasoundMachine.tabIndex==0" class="device_container">
                    <div v-show="loading" class="full_loading_spinner van_loading_spinner">
                        <van-loading color="#00c59d" />
                    </div>
                    <div v-for="deviceCtrl in deviceCtrlList" class="device_ctrl_item" :key="deviceCtrl.device_id">
                        <div class="device_ctrl_bar" @click="toggleDeviceOpen(deviceCtrl)">
                            <span>{{lang.device_bar_type[deviceCtrl.client_type]+'_'+deviceCtrl.device_id}}</span>
                            <i v-if="deviceCtrl.open" class="iconfont icon-up"></i>
                            <i v-if="!deviceCtrl.open" class="iconfont icon-down"></i>
                        </div>
                        <div v-show="deviceCtrl.open" class="device_ctrl_body">
                            <!-- <div class="current_conversation" v-show="deviceCtrl.client_type == systemConfig.client_type.AppUltraSyncBox||deviceCtrl.client_type == systemConfig.client_type.Doppler">
                                <div class="left">{{lang.current_conversation}}</div>
                                <div class="right">{{getSessionName(deviceCtrl)}}</div>
                                <span @click="openToggleConversation">
                                    <i class="iconfont icon-edit"></i>
                                </span>
                            </div> -->
                            <template v-if="deviceCtrl.client_type == systemConfig.client_type.Doppler">
                                <div class="binding_container">
                                    <div class="department_item">
                                        <div class="label">{{lang.statistic.using_departments}}:</div>
                                        <input type="text" class="input_text" v-model="using_department" :placeholder="lang.statistic.using_departments" @click.stop="searchDepartment($event,'using')">
                                    </div>
                                    <div class="department_item">
                                        <div class="label">{{lang.statistic.owner_departments}}:</div>
                                        <input type="text" class="input_text" v-model="owner_department" :placeholder="lang.statistic.owner_departments" @click.stop="searchDepartment($event,'owner')">
                                    </div>
                                    <button class="primary_bg binding_btn" @click="deviceBinding(deviceCtrl)">{{lang.device_binding}}</button>
                                </div>
                            </template>
                            <!-- <template v-if="deviceCtrl.client_type == systemConfig.client_type.AppUltraSyncBox">
                                <div class="patient_info" >
                                    <div class="exam_operators clearfix">
                                        <span @click="openTogglePatient(true)" class="btn fl">
                                            {{lang.device_create_exam}}
                                        </span>
                                        <span class="btn fl edit" @click="openTogglePatient(false)">{{lang.edit_txt}}</span>
                                        <i @click="shutdown" class="btn iconfont icon-guanji fr"></i>
                                        <i @click="openStorageSetting" class="btn iconfont icon-setting fr"></i>
                                    </div>
                                    <div class="patient_id">
                                        <span>{{lang.patient_id}}: </span>
                                        <span class="content">
                                            {{deviceCtrl.patient_id}}
                                        </span>
                                    </div>
                                    <div class="patient_name">
                                        <span>{{lang.patient_name}}: </span>
                                        <span>{{deviceCtrl.patient_name}}</span>
                                    </div>
                                </div>
                                <div class="save_operator">
                                    <div class="single" @click="saveSingleFrame">{{lang.save_single_frame}}</div>
                                    <div @click="saveMultiFrame">{{saveMultiFrameStr}}</div>
                                </div>
                                <p class="count_tip">{{lang.device_save_picture_num}}{{getFrameNum(deviceCtrl.saved_list)}}  {{lang.piece_tip}}      {{lang.device_save_video_num}}: {{getCineNum(deviceCtrl.saved_list)}}  {{lang.device_save_video_unit}}</p>
                                <div class="saved_image_list clearfix">
                                    <div v-for="(file,index) of deviceCtrl.saved_list"  class="file_item" @click="openSavedImageGallery(index,deviceCtrl.saved_list)" :key="index">
                                        <div v-if="file.msg_type==systemConfig.msg_type.Frame||file.msg_type==systemConfig.msg_type.OBAI" class="file_container">
                                            <img class="file_image" :src="file.realUrl">
                                        </div>
                                        <div v-if="file.msg_type==systemConfig.msg_type.Cine" class="file_container">
                                            <img class="file_image" :src="file.realUrl">
                                            <i class="icon iconfont icon-videofill"></i>
                                        </div>
                                    </div>
                                </div>
                            </template> -->
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="showInfo" v-show="showInfo">
            <p>{{lang.machine_info_name}}：{{ultrasoundMachine.ultrasound_machine_name}}</p>
            <p>{{lang.machine_info_type}}：{{ultrasoundMachine.ultrasound_machine_type}}</p>
            <p>{{lang.machine_info_serial}}：{{ultrasoundMachine.ultrasound_machine_serial}}</p>
        </div>
        <router-view></router-view>
    </div>

</template>
<script>
import base from '../lib/base';
import { Toast, Loading, Popup, DatetimePicker, Checkbox, CheckboxGroup } from 'vant';
import Tool from '@/common/tool'
import {getLocalImgUrl,getPickerDate,sendTransferLocal,sendTransferExam} from '../lib/common_base'
export default {
    mixins: [base],
    name: 'UltrasoundMachine',
    components: {
        VanLoading: Loading,
        VanPopup: Popup,
        VanDatetimePicker: DatetimePicker,
        VanCheckbox: Checkbox,
        VanCheckboxGroup: CheckboxGroup
    },
    data(){
        return {
            popupSetting:false,
            showModal:false,
            showInfo:false,
            indexObj:{},
            loadingLocalImg:false,
            getLocalListObj:{},
            searchText:'',
            loadingMoreExam:false,
            showExamStarPicker: false,
            showExamEndPicker: false,
            examStarDate:new Date(),
            examEndDate:new Date(),
            examTempDate:new Date(),
            examMaxDate:new Date(),
            examMinDate:new Date(),
            device:{},
            //deviceCtrl属性
            editId:false,
            temp_id:'',
            loading:false,
            chatList:this.$store.state.chatList.list,
            friendList:this.$store.state.friendList.list,
            groupList:this.$store.state.groupList,
            updateCurSessionTimer: '',
            updatePatientInfoTimer: '',
            updateFtpInfoTimer: '',
            saveSingleFrameTimer: '',
            saveMultiFrameStartTimer: '',
            saveMultiFrameStopTimer: '',
            shutdownTimer: '',
            saveMultiFrameStr: '',
            systemConfig: this.$store.state.systemConfig,
            timeout: 20000,
            createExam:false,
            hideExamBrowser:false,//屏蔽检查浏览功能
            hideTransfer:false,//屏蔽一键转发功能
            galleryTempArr:[],//一键转发拍平所有数组
            usingDepartmentSelectList: [{name: '1', id: 1}, {name: '1', id: 2}, {name: '1', id: 3}, {name: '1', id: 4}, {name: '1', id: 5}],
            ownerDepartmentSelectList: [{name: '2', id: 1}, {name: '1', id: 2}, {name: '1', id: 3}],
            using_department: '', //
            owner_department: '',
        }
    },
    beforeRouteEnter(to,from,next){
        if (!from.name) {
            //二级页面刷新时返回上级页面
            next(false)
            window.location.replace(`#/index`);
        }else{
            next()
        }
    },
    computed:{
        ultrasoundMachine(){
            this.parseFunctionList()
            return this.$store.state.ultrasoundMachine
        },
        deviceCtrlList(){
            return this.parseObjToArr(this.$store.state.deviceCtrl)
        },
        onlyMachineConnect(){
            return this.ultrasoundMachine.isConnecting&&this.deviceCtrlList.length==0;
        },
        onlyDeviceConnect(){
            return this.deviceCtrlList.length>0&&!this.ultrasoundMachine.isConnecting;
        },
        bothConnect(){
            return this.deviceCtrlList.length>0&&this.ultrasoundMachine.isConnecting;
        },
        ultrasoundMachineTransferList(){
            let list=this.ultrasoundMachine.transferList
            list.sort((a,b)=>{
                //按日期倒序排列
                let time_a=new Date(a.exam_time.replace(/-/g,"/"))
                let time_b=new Date(b.exam_time.replace(/-/g,"/"))
                return time_b-time_a;
            })
            this.parseGalleryList(list);
            return list;
        },
        ultrasoundMachineExamList(){
            let examTotal=this.ultrasoundMachine.examTotal;
            let examList=this.ultrasoundMachine.examList
            if (examTotal>examList.length) {
                this.loadingMoreExam=true;
            }else{
                this.loadingMoreExam=false;
            }
            return examList
        },
        tempList(){
            let temp=[];
            let transferList=this.ultrasoundMachineTransferList;
            for(let i=0;i<transferList.length;i++){
                for(let j=0;j<transferList[i].image_list.length;j++){
                    if (transferList[i].image_list[j].checked) {
                        temp.push(i+'-'+j);
                    }
                }
            }
            return temp;
        },
        isSelectAll(){
            return this.tempList.length==this.galleryTempArr.length
        },
    },
    watch:{
        bothConnect(){
            this.autoToggleTab();
        },
    },
    beforeDestroy(){
        // this.disconnectMachine();
    },
    mounted(){
        var that = this;
        this.saveMultiFrameStr = this.lang.save_multi_frame;//设置"存多帧"的初始字符串
        for(var key in this.$store.state.deviceCtrl){//设置当前设备
            var value = this.$store.state.deviceCtrl[key];
            if(value.open){
                this.device = value;
            }
        }
        this.$nextTick(()=>{
            // var that=this;
            if (this.bothConnect&&this.ultrasoundMachine.tabIndex==-1) {
                this.toggleTab(0);
            }
            // else if(this.hideExamBrowser){
            //     this.toggleTab(1);
            // }
            this.$root.eventBus.$off('machineTransmit').$on('machineTransmit',this.machineTransmit)
            this.$root.eventBus.$off('machineGalleryTransmit').$on('machineGalleryTransmit',this.machineGalleryTransmit)
            this.searchText=this.lang.search_all_text;
            this.$root.eventBus.$off('exitMachinePage').$on('exitMachinePage',()=>{
                if (this.$route.meta.inMachinePage) {
                    console.log('exitMachinePage')
                    this.back();
                }

            })
            this.$root.eventBus.$off('loadExamImage').$on('loadExamImage',this.loadExamImage)

            //我的设备模块
            this.$root.eventBus.$off('deviceToggleConversation').$on('deviceToggleConversation',this.deviceToggleConversation);
            this.$root.eventBus.$off('deviceTogglePatient').$on('deviceTogglePatient',this.deviceTogglePatient);
            this.$root.eventBus.$off('deviceToggleFTP').$on('deviceToggleFTP',this.deviceToggleFTP);
            this.$root.eventBus.$off('notifyUpdateDeviceCurSession').$on('notifyUpdateDeviceCurSession',function(){
                clearTimeout(that.updateCurSessionTimer);
                that.loading=false;
                Toast(that.lang.update_success_text);
            });
            this.$root.eventBus.$off('notifyUpdateDevicePatientInfo').$on('notifyUpdateDevicePatientInfo',function(data){
                clearTimeout(that.updatePatientInfoTimer);
                that.loading=false;
                Toast(that.lang.update_success_text);
                that.$store.commit('deviceCtrl/updateDeviceCtrl',{saved_list: []});
            });
            this.$root.eventBus.$off('notifyUpdateDeviceFtpInfo').$on('notifyUpdateDeviceFtpInfo', function(){
                clearTimeout(that.updateFtpInfoTimer);
                that.loading=false;
                Toast(that.lang.update_success_text);
            });
            this.$root.eventBus.$off('notifyDeviceSaveSingleFrame').$on('notifyDeviceSaveSingleFrame', function(data){
                clearTimeout(that.saveSingleFrameTimer);
                that.loading=false;
                if (data.error==0) {
                    var obj={
                        msg_type:that.systemConfig.msg_type.Frame,
                        loaded:true,
                        url:data.ftp_path,
                        device_id:data.device_id
                    }
                    obj.realUrl=getLocalImgUrl(data.ftp_path);
                    that.$store.commit('deviceCtrl/addSavedList',obj);
                }

            });
            this.$root.eventBus.$off('notifyDeviceSaveMultiFrame').$on('notifyDeviceSaveMultiFrame', function(data){
                if(1 == data.is_start){
                    clearTimeout(that.saveMultiFrameStartTimer);
                }else{
                    clearTimeout(that.saveMultiFrameStopTimer);
                }

                that.loading=false;
                if(0 == data.error){
                    if(1 == data.is_start){//存多帧开始成功
                        that.saveMultiFrameStr = that.lang.save_multi_frame_stop;
                        Toast(that.lang.save_multi_frame_start);
                    }else{ //存多帧结束成功
                        that.saveMultiFrameStr = that.lang.save_multi_frame;
                        Toast(that.lang.save_multi_frame_succ);
                        var obj={
                            msg_type:that.systemConfig.msg_type.Cine,
                            loaded:true,
                            mainVideoSrc:data.ftp_path,
                            device_id:data.device_id
                        }
                        obj.realUrl='static/resource/images/poster_video.png'
                        that.$store.commit('deviceCtrl/addSavedList',obj);
                    }
                }else{
                    if(1 == data.is_start){//存多帧开始失败
                        that.saveMultiFrameStr = that.lang.save_multi_frame;
                        Toast(that.lang.save_multi_frame_fail + " " + data.error_info);
                    }else{ //存多帧结束失败
                        that.saveMultiFrameStr = that.lang.save_multi_frame;
                        Toast(that.lang.save_multi_frame_fail + " " + data.error_info);
                    }
                }
            });
            this.$root.eventBus.$off('notifyDeviceShutdown').$on('notifyDeviceShutdown', function(){
                clearTimeout(that.shutdownTimer);
                that.loading=false;
            });
        })
    },
    methods:{
        disconnectMachine(){
            //断开机器连接
            this.back();
            window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
            this.$store.commit('ultrasoundMachine/destroyMachine')
        },
        checkImg(i,j){
            // let image=this.$refs['checkItem_'+i+'-'+j][0];
            // image.click();
            let image=this.ultrasoundMachine.transferList[i].image_list[j];
            if (!image.checked) {
                if (this.tempList.length>=20) {
                    //一次最多转发20张
                    Toast(this.lang.max_transfer_image);
                    return ;
                }
            }
            this.$store.commit('ultrasoundMachine/toggleImageChecked',{
                index:i,
                j_index:j
            })

        },
        openGallery(i,j){
            //将图片从二维打平成一维数组
            let index=0;
            for(let i_temp=0;i_temp<i;i_temp++){
                index+=this.ultrasoundMachine.transferList[i_temp].image_list.length;
            }
            index+=j;
            //打开画廊
            this.$store.commit('gallery/setGallery',{
                list:this.galleryTempArr,
                index:index
            })
            this.$nextTick(()=>{
                if (this.user.uid) {
                    this.$router.push(`/index/ultrasound_machine/gallery`)
                }else{
                    this.$router.push(`/scan_device/ultrasound_machine/gallery`)
                }
            })

        },
        showSetting(){
            this.popupSetting=true;
            this.showModal=true;
        },
        closeModal(){
            this.popupSetting=false;
            this.showModal=false;
            this.showInfo=false;
        },
        openInfo(){
            this.popupSetting=false;
            this.showModal=true;
            this.showInfo=true;
        },
        destroyImage(){
            let arr=this.getSelectImages();
            if (arr.length==0) {
                Toast(this.lang.transmit_less_tip)
                return ;
            }
            Tool.openMobileDialog(
                {
                    message:this.lang.machine_destroy_confirm,
                    showRejectButton:true,
                    confirm:()=>{
                        window.CWorkstationCommunicationMng.notifyUltrasoundMachineDestroyImages({list:arr});
                        this.clearTempList();
                        this.deleteTransferImage();
                    }
                }
            )
        },
        localImage(){
            let arr=this.getSelectImages();
            if (arr.length==0) {
                Toast(this.lang.transmit_less_tip)
                return ;
            }
            Tool.openMobileDialog(
                {
                    message:this.lang.machine_local_confirm,
                    showRejectButton:true,
                    confirm:()=>{
                        window.CWorkstationCommunicationMng.notifyUltrasoundMachineLocalStorageImages({list:arr});
                        this.clearTempList();
                        this.deleteTransferImage();
                    }
                }
            )
        },
        getSelectImages(){
            let arr=[];
            this.indexObj={};
            let examObj={};
            for(let checked of this.tempList){
                let index=checked.split('-')[0];
                let j_index=checked.split('-')[1];
                let exam=this.ultrasoundMachine.transferList[index]
                if (this.indexObj[index]) {
                    this.indexObj[index].push(j_index);
                    examObj[exam.exam_id].push({
                        img_id:exam.image_list[j_index].img_id,
                        msg_type:exam.image_list[j_index].msg_type,
                        path:exam.image_list[j_index].path||''
                    })
                }else{
                    this.indexObj[index]=[j_index];
                    examObj[exam.exam_id]=[{
                        img_id:exam.image_list[j_index].img_id,
                        msg_type:exam.image_list[j_index].msg_type,
                        path:exam.image_list[j_index].path||''
                    }];
                }
            }
            for(let key in examObj){
                arr.push({
                    exam_id:key,
                    image_list:examObj[key]
                })
            }
            return arr;
        },
        deleteTransferImage(){
            this.$store.commit('ultrasoundMachine/deleteTransferImage',this.indexObj);
        },
        selectAll(){
            let list=this.ultrasoundMachine.transferList;
            for(let i=0;i<list.length;i++){
                for(let j=0;j<list[i].image_list.length;j++){
                    let image=this.$refs['checkItem_'+i+'-'+j][0];
                    if (!image.checked) {
                        image.click();
                    }
                }
            }
        },
        cancelSelectAll(){
            this.clearTempList();
        },
        clearTempList(){
            for(let item of this.tempList){
                let i=item.split('-')[0];
                let j=item.split('-')[1];
                this.checkImg(i,j);
            }
        },
        openTransmit(){
            let arr=this.getSelectImages();
            if (arr.length==0) {
                Toast(this.lang.transmit_less_tip)
            }else{
                this.$router.push(`/index/ultrasound_machine/transmit`)
            }
        },
        toggleTab(index){
            var that=this;
            //切换tab，如果数据未初始化，则初始化数据，否则不处理
            this.$store.commit('ultrasoundMachine/updateMachine',{
                tabIndex:index
            });
            if (index==1) {
                if (!that.ultrasoundMachine.initTransfer) {
                    that.$store.commit('ultrasoundMachine/updateMachine',{
                        initTransfer:true
                    });
                    window.CWorkstationCommunicationMng.queryUltrasoundMachineImageList()
                }
            }else if(index==2){
                if (!that.ultrasoundMachine.initExam) {
                    that.$store.commit('ultrasoundMachine/updateMachine',{
                        initExam:true
                    });
                    window.CWorkstationCommunicationMng.ULinkQueryExamListEx();
                }
            }

        },
        toggleImageList(exam,index){
            //切换图像列表显示
            let open=exam.open;
            this.$store.commit('ultrasoundMachine/toggleOpenExamImageList',{
                serial_id:exam.serial_id,
                open:!open
            });
            if (!exam.initImageList) {

                window.vm.$store.commit('ultrasoundMachine/initImageListLoading',{
                    serial_id:exam.serial_id,
                });
                window.CWorkstationCommunicationMng.ULinkQueryImageListEx({
                    serial_id:exam.serial_id,
                    patient_id:exam.patient_id,
                    study_id:exam.study_id,
                })
            }
        },
        loadExamImage(index){
            let img=this.readingExam.image_list[index];
            let option ={
                patient_id:this.readingExam.patient_id,
                study_id:this.readingExam.study_id,
                serial_id:this.readingExam.serial_id,
                image_list:[img]
            }
            window.CWorkstationCommunicationMng.ULinkLoadExamImageList(option);
        },
        openExamLocalImage(exam,imgIndex){
            this.readingExam=exam;
            //打开画廊
            this.$store.commit('gallery/setGallery',{
                list:exam.image_list,
                index:imgIndex
            })
            if (this.user.uid) {
                this.$router.push(`/index/ultrasound_machine/gallery`)
            }else{
                this.$router.push(`/scan_device/ultrasound_machine/gallery`)
            }

        },
        machineTransmit(data,arr){
            let that=this;
            console.log('emit machineTransmit')
            //一键转发
            if (that.$store.state.systemConfig.clientType == 5) {
                Toast(that.lang.use_app_tip)
                return;
            }
            if (!arr) {
                //从画廊转发会传入arr
                arr=that.getSelectImages();
            }
            let json={
                ImageList:arr,
                SessionId:data.cid,
                DataSource:2
            }
            that.$root.transferExamQueue[data.cid||'f-'+data.id]=json
            if (that.conversationList[data.cid]) {
                //会话已开启则直接转发
                sendTransferExam(data.cid)
            }else{
                //会话未开启则开启会话,开启会话后检查待转发队列
                if (data.cid) {
                    that.openConversation(data.cid,7)
                }else{
                    that.openConversation(data.id,3)
                }

            }
            Toast(that.lang.machine_transmit_tip);
            that.clearTempList();
            that.deleteTransferImage();
        },
        machineGalleryTransmit(data){
            //一键转发或检查浏览进入画廊转发
            let gallery=this.$store.state.gallery;
            let img=gallery.list[gallery.index];
            if (this.ultrasoundMachine.tabIndex==1||this.ultrasoundMachine.tabIndex==2) {
                //一键转发进入画廊转发
                this.machineTransmit(data,[{
                    exam_id:img.exam_id,
                    image_list:[img]
                }])
            }else if(false||this.ultrasoundMachine.tabIndex==2){
                //已修改方案，检查浏览与一键转发走同样的接口
                //检查浏览进入画廊转发
                this.$root.transferLocalQueue[data.cid||'f-'+data.id]=img;
                if (this.conversationList[data.cid]) {
                    //会话已开启则直接转发
                    sendTransferLocal(data.cid)
                }else{
                    //会话未开启则开启会话,开启会话后检查待转发队列
                    if (data.cid) {
                        this.openConversation(data.cid,7)
                    }else{
                        this.openConversation(data.id,3)
                    }
                }
                Toast(this.lang.machine_transmit_tip);
            }else{
                //other todo
            }
        },
        openExamDatePicker(event,index){
            event.target.blur();
            if (index==1) {
                this.examTempDate=this.examStarDate
                this.showExamStarPicker = true;
            }else{
                this.examTempDate=this.examEndDate
                this.showExamEndPicker = true;
            }
        },
        handleStarConfirm(value){
            this.showExamStarPicker = false;
            if (value<=this.examEndDate) {
                this.examStarDate=value;
            }else{
                Toast(this.lang.exam_date_less_tip)
            }
        },
        handleStarCancel(){
            this.showExamStarPicker = false;
        },
        handleEndConfirm(value){
            this.showExamEndPicker = false;
            if (value>=this.examStarDate) {
                this.examEndDate=value;
            }else{
                Toast(this.lang.exam_date_more_tip)
            }
        },
        handleEndCancel(){
            this.showExamEndPicker = false;
        },
        openToggleConversation(){
            this.$router.push(`/index/ultrasound_machine/toggle_conversation`)
        },
        deviceToggleConversation(item){
            this.loading=true;
            this.updateCurSessionTimer=setTimeout(()=>{
                this.loading=false;
                Toast(this.lang.requestTimeout + " 1");
            }, this.timeout)

            if(item.id){
                item.id = parseInt(item.id);
            }
            if(item.cid){
                item.cid = parseInt(item.cid);
            }
            console.log("----------deviceToggleConversation");
            console.log(item);
            var controller = window.main_screen.controller;

            if(item.cid){ //群id
                controller.emit("ctrl_device_event", {
                    event_name:"ctrl_device_cur_session",
                    device_id:this.device.device_id,
                    cur_session_id: item.cid,
                    cur_session_type: 0,
                    subject:item.subject
                });
            }else{ //uid
                controller.emit("ctrl_device_event", {
                    event_name:"ctrl_device_cur_session",
                    device_id:this.device.device_id,
                    cur_session_id: item.id,
                    cur_session_type: 1,
                    subject:item.subject
                });
            }

        },
        deviceTogglePatient(data){
            var that = this;
            this.loading=true;
            this.updatePatientInfoTimer=setTimeout(()=>{
                this.loading=false;
                Toast(this.lang.requestTimeout + " 2");
            }, this.timeout)
            console.log("----------deviceTogglePatient");
            console.log(data);
            var controller = window.main_screen.controller;
            this.$store.commit('deviceCtrl/updateDeviceCtrl',{patient_id: data.patient_id, patient_name: data.patient_name});
            data.event_name = "ctrl_device_patient_info";
            if (data.canEditPatientId) {
                this.createExam=true;
            }
            data.device_id = this.device.device_id,
            controller.emit("ctrl_device_event", data);
        },
        deviceToggleFTP(data){
            this.loading=true;
            var controller = window.main_screen.controller;
            this.updateFtpInfoTimer=setTimeout(()=>{
                this.loading=false;
                Toast(this.lang.requestTimeout  + " 3");
            }, this.timeout);

            console.log("----------deviceToggleFTP");
            console.log(data);
            data.event_name = "ctrl_device_ftp_info";
            data.device_id = this.device.device_id,
            controller.emit("ctrl_device_event", data);
        },
        openTogglePatient(editId){
            let type=editId?'new':'edit/'+this.device.device_id
            this.$router.push(`/index/ultrasound_machine/patient/${type}`)
        },
        openStorageSetting(){
            this.$router.push(`/index/ultrasound_machine/storage/${this.device.device_id}`)
        },
        isSaveInfoEnough(){
            if(this.device.patient_id && this.device.patient_id.length>0 && this.device.ftp_path && this.device.ftp_path.length >0){
                return true;
            }else{
                return false;
            }
        },
        saveSingleFrame(){
            if (this.lang.save_multi_frame_stop == this.saveMultiFrameStr) {
                Toast(this.lang.stop_multi_frame_first)
                return
            }
            if(this.isSaveInfoEnough()){
                this.loading=true;
                this.saveSingleFrameTimer=setTimeout(()=>{
                    this.loading=false;
                    Toast(this.lang.requestTimeout  + " 4");
                }, this.timeout);
                var path = this.device.patient_id + "_" + this.device.patient_name ;
                var controller = window.main_screen.controller;
                controller.emit("ctrl_device_event", {
                    event_name:"ctrl_device_save_single_frame",
                    device_id:this.device.device_id,
                    path:path
                });
            }else{
                Toast(this.lang.inputPatientOrFtpInfo);
            }
        },
        saveMultiFrame(){
            var controller = window.main_screen.controller;
            if(this.lang.save_multi_frame == this.saveMultiFrameStr){ //开始存多帧
                if(this.isSaveInfoEnough()){
                    this.loading=true;
                    this.saveMultiFrameStartTimer=setTimeout(()=>{
                        this.loading=false;
                        Toast(this.lang.requestTimeout  + " 5");
                    },this.timeout);
                    var path = this.device.patient_id + "_" + this.device.patient_name ;
                    controller.emit("ctrl_device_event", {
                        event_name:"ctrl_device_save_multi_frame",
                        device_id:this.device.device_id,
                        path:path,
                        is_start:1
                    });
                }else{
                    Toast(this.lang.inputPatientOrFtpInfo);
                }
            }else if(this.lang.save_multi_frame_stop == this.saveMultiFrameStr){ //停止存多帧
                this.loading=true;
                this.saveMultiFrameStopTimer=setTimeout(()=>{
                    this.loading=false;
                    Toast(this.lang.requestTimeout + " 6");
                },2*this.timeout);
                var path = this.device.patient_id + "_" + this.device.patient_name ;
                controller.emit("ctrl_device_event", {
                    event_name:"ctrl_device_save_multi_frame",
                    device_id:this.device.device_id,
                    path:path,
                    is_start:0
                });
            }
        },
        shutdown(){
            Tool.openMobileDialog(
                {
                    message:this.lang.shutdown_confirm,
                    showRejectButton:true,
                    confirm:()=>{
                        this.loading=true;
                        this.shutdownTimer=setTimeout(()=>{
                            this.loading=false;
                            Toast(this.lang.requestTimeout);
                        }, this.timeout);
                        var controller = window.main_screen.controller;
                        controller.emit("ctrl_device_event", {
                            event_name:"ctrl_device_shutdown",
                            device_id:this.device.device_id,
                        });
                    }
                }
            )
        },
        autoToggleTab(){
            if (this.bothConnect) {
                if (this.ultrasoundMachine.tabIndex==-1) {
                    //多账号与直连都存在且未选中tab时自动选中控制面板
                    this.toggleTab(0);
                }
            } else if(this.onlyDeviceConnect){
                //只有多账号时选中控制面板
                this.toggleTab(0);
            } else if(this.onlyMachineConnect){
                //只有直连时判断一键转发和检查浏览是否初始化过
                if (this.ultrasoundMachine.initExam) {
                    this.toggleTab(2);
                } else if(this.ultrasoundMachine.initTransfer){
                    this.toggleTab(1);
                }else{
                    this.toggleTab(-1);
                }
            }
        },
        getExamSavedList(deviceCtrl){
            var controller = window.main_screen.controller;
            controller.emit("ctrl_device_event", {
                event_name:"get_patient_exam_list",
                device_id:this.device.device_id,
                patient_id:deviceCtrl.patient_id,
                patient_name:deviceCtrl.patient_name
            });
        },
        openSavedImageGallery(index,list){
            this.$store.commit('gallery/setGallery',{
                list:list,
                index:index
            })
            this.$nextTick(()=>{
                this.$router.push(`/index/ultrasound_machine/saved_image_gallery`)
            })
        },
        parseFunctionList(){
            console.log('parseFunctionList---------')
            //由扫码决定显示功能模块
            let list=this.$store.state.ultrasoundMachine.ultrasound_function_list;
            if (list==undefined) {
                //不指定功能给全部功能
                return ;
            }
            let transfer=0x01;
            let examBrowse=0x02;
            if (!(list&transfer)) {
                this.hideTransfer=true;
            }
            if (!(list&examBrowse)) {
                this.hideExamBrowser=true
            }
        },
        parseGalleryList(list){
            let length=this.galleryTempArr.length;
            this.galleryTempArr.splice(0,length);
            for(let item of list){
                for(let image of item.image_list){
                    image.msg_type=image.img_type;
                    image.loaded=true;
                    image.exam_id=item.exam_id;
                    var aImgSrcArr = image.url_local.split("thumbnail");
                    if (image.msg_type==this.systemConfig.msg_type.Frame) {
                        image.realUrl=aImgSrcArr[0]+ "SingleFrame.jpg";    //封面帧
                    }else if(image.msg_type==this.systemConfig.msg_type.OBAI){
                        image.realUrl=aImgSrcArr[0]+ "ScreenShot.jpg";    //封面帧
                    }else{
                        image.realUrl=aImgSrcArr[0]+ "DevicePoster.jpg";    //封面帧
                    }
                    //用于下载原图失败时的重试
                    image.tempRealUrl=image.realUrl
                    this.galleryTempArr.push(image)
                }
            }
        },
        getSessionName(device){
            var sid = device.cur_session_id;
            var stype = device.cur_session_type;
            var sName = "";

            if(0 == stype){ //群id
                for(let chatItem of this.chatList){
                    if (sid==chatItem.cid) {
                        sName = chatItem.subject;
                        break;
                    }
                }
                if("" == sName){
                    for(let groupItem of this.groupList){
                        if (sid==groupItem.id) {
                            sName = groupItem.subject;
                            break;
                        }
                    }
                }
            }else{//用戶id
                for(let item of this.friendList){
                    if(sid == item.id){
                        sName = item.alias||item.nickname;
                    }
                }
            }
            return sName;
        },
        getFrameNum(list){
            let num=0;
            for(let image of list){
                if (image.msg_type==this.systemConfig.msg_type.Frame||image.msg_type==this.systemConfig.msg_type.OBAI) {
                    num++
                }
            }
            return num
        },
        getCineNum(list){
            let num=0;
            for(let image of list){
                if (image.msg_type==this.systemConfig.msg_type.Cine) {
                    num++
                }
            }
            return num
        },
        toggleDeviceOpen(deviceCtrl){
            let open=!deviceCtrl.open;
            for(let item of this.deviceCtrlList){
                this.$store.commit('deviceCtrl/updateDeviceCtrl',{
                    open:false,
                    device_id:item.device_id
                });
            }
            if (open) {
                this.$store.commit('deviceCtrl/updateDeviceCtrl',{
                    open:true,
                    device_id:deviceCtrl.device_id
                });
                this.device=deviceCtrl
                if (deviceCtrl.first_init) {
                    this.$store.commit('deviceCtrl/updateDeviceCtrl',{
                        first_init:false,
                        saved_list:[],
                        device_id:deviceCtrl.device_id
                    });
                    if (deviceCtrl.patient_id) {
                        this.getExamSavedList(deviceCtrl);
                    }
                }

            }else{
                this.device={}
            }
        },
        getPickerDate(date){
            return getPickerDate(date)
        },
        deviceBinding(device){
            const router = `/index/ultrasound_machine/device_binding/${device.device_id}?client_type=6&using_department=${this.using_department}&owner_department=${this.owner_department}`;
            this.$router.push(router);
        },
        searchDepartment(event,type) {
            event.target.blur();
            this.$root.eventBus.$once('selectDepartment', ({type, departmentName}) => {
                if (type === 'using') {
                    this.using_department = departmentName
                } else {
                    this.owner_department = departmentName
                }
            })
            this.$router.push(`/index/ultrasound_machine/search_department?type=${type}`);
        }
    }
}

</script>
<style lang="scss">
.machine_page{
    word-break: break-word;
    .setting{
        text-align: right;
        width: 4rem;
    }
    .machine-popup{
        position:absolute;
        left:auto;
        top:2.2rem;
        right:0.2rem;
        width: 8rem;
        border-radius: 8px;
        transform: translate(0, 0);
        color:#333;
        text-align:left;
        padding:.2rem .5rem;
        font-weight:normal;
        z-index:1001;
        background-color:#fff;
        p{
            line-height:2.4;
            display:block;
            border-bottom:1px solid #ddd;
        }
        p:last-child{
            border:none;
        }
        &::before{
            width:0;
            height:0;
            border-bottom:.5rem solid #fff;
            border-left:.5rem solid transparent;
            border-right:.5rem solid transparent;
            content: '';
            position: absolute;
            top: -0.5rem;
            right: .4rem;
        }
    }
    .machine_container{
        height:calc(100% - 2.2rem);
        position:relative;

        .van-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }

        .van_loading_spinner{
            display: flex;
            justify-content: center;
            align-items: center;

            .van-loading__spinner{
                width: 2.8rem;
                height: 2.8rem;
            }
        }

        .initation{
            margin: .8rem 0;
            background-color: #fff;
            .modal_item{
                display: block;
                background-color: #fff;
                padding: .6rem .6rem;
                color: #444;
                margin: 0 .6rem;
                border-bottom: 1px solid #ddd;
                &:last-child{
                    border: none;
                }
            }
        }
        .main{
            height:100%;
            position:relative;
            display:flex;
            flex-direction: column;
            .tabs{
                display: flex;
                flex-direction: row;
                text-align: center;
                font-size: 0.8rem;
                color: #333;
                background: #fff;
                line-height: 1.5rem;
                .tab_item{
                    flex: 1;
                    border-bottom: 0.1rem solid #fff;
                    &.active{
                        border-bottom:0.1rem solid #00c59d;
                        color:#00c59d;
                    }
                }
            }
            .transfer_helper,.exam_browse{
                position:relative;
                flex:1;
                overflow:auto;
                width: 100%;
                .image_list_container{
                    position:absolute;
                    top:1.4rem;
                    width: 100%;
                    overflow: auto;
                    bottom: 2rem;
                }
                .image_list,.exam_image_list{
                    margin-bottom:0.5rem;
                    background-color: #fff;
                    .loading_image{
                        width: 36px;
                        margin: 10px auto;
                    }
                    .file_item{
                        float:left;
                        width:25%;
                        border:1px solid #fff;
                        box-sizing:border-box;
                        position:relative;
                        .file_container{
                            position:relative;
                            padding-top:100%;
                            height:0;
                            background-color:#333;
                            overflow:hidden;
                            .file_image{
                                max-width:100%;
                                max-height:100%;
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%,-50%);
                            }
                            .icon-videofill{
                                font-size: 1rem;
                                color: #00c59d;
                                position: absolute;
                                bottom: .2rem;
                                left: .3rem;
                            }
                            .review_time{
                                position: absolute;
                                bottom: 1rem;
                                font-size: 1rem;
                                color: #fff;
                                transform: scale(0.4);
                                width: 200%;
                                left: -50%;
                                text-align: center;
                                white-space: nowrap;
                            }
                        }
                        .van-checkbox-group {
                            max-height: 200px;
                            overflow: auto;
                            position: absolute;
                            width: 100%;
                            height: 100%;
                            border-radius: 0.75rem;
                            top: 0px;
                            right: 0px;
                            z-index: 1000;

                            .van-checkbox{
                                width: 100%;
                                height: 100%;
                                padding: 6px;
                                box-sizing: border-box;
                                justify-content: flex-end;
                                align-items: flex-end;
                            }
                        }
                    }
                }
                .machine_name{
                    text-align: center;
                    position: absolute;
                    line-height: 1.4rem;
                    height: 1.4rem;
                    width: 100%;
                    top:0;
                    font-size: 0.9rem;
                    background: #f4f4f4;
                }
                .footer{
                    position:absolute;
                    bottom:0;
                    width:100%;
                    height:2rem;
                    background-color:#fff;
                    color:#00c59d;
                    border-top: 1px solid #ddd;
                    display:flex;
                    line-height: 2rem;
                    text-align: center;
                    font-size: 0.8rem;
                    .item{
                        flex:1;
                    }
                }
                .search_bar{
                    color: #666;
                    background: #fff;
                    border-top: 1px solid #ddd;
                    border-bottom: 1px solid #ddd;
                    font-size: 0.8rem;
                    line-height: 1.4rem;
                    height:1.4rem;
                    box-sizing:border-box;
                    position:relative;
                    .search_text{
                        padding: 0 0.6rem;
                    }
                    .search_condition{
                        padding:0.3rem 0.6rem;
                        position: absolute;
                        top:1.3rem;
                        background-color: #fff;
                        width: 100%;
                        z-index: 999;
                        box-sizing: border-box;
                        color: #333;
                        max-height: 12rem;
                        border-bottom: 1px solid #ddd;
                        overflow:auto;
                        .search_condition_item{
                            margin: 10px;
                            .van-picker__confirm {
                                color: #00c59d;
                                font-size: 0.8rem;
                            }
                            .van-picker__cancel {
                                font-size: 0.8rem;
                            }
                            .search_condition_text{
                                width: 100%;
                                box-sizing: border-box;
                                height: 1.4rem;
                                border: 1px solid #ccc;
                                padding: 0 .4rem;
                                font-size: .8rem;
                                border-radius: 4px;
                                display: block;
                            }
                            .search_condition_date{
                                width: 40%;
                                box-sizing: border-box;
                                height: 1.4rem;
                                border: 1px solid #ccc;
                                padding: 0 .4rem;
                                font-size: .8rem;
                                border-radius: 4px;
                                display: inline-block;
                            }
                        }
                    }
                    .reflesh_btn{
                        display:inline-block;
                        background:#00c59d;
                        color:#fff;
                        position: absolute;
                        right: 0.3rem;
                        top: 0.1rem;
                        font-size: 0.7rem;
                        padding: 0 0.4rem;
                        border-radius: 0.3rem;
                        line-height: 1.1rem;
                    }
                }
                .exam_list{
                    height:calc(100% - 1.4rem);
                    border-top: 1px solid #f4f4f4;
                    box-sizing: border-box;
                    .loading{
                        display: inline-block;
                        margin-left: 50%;
                        transform: translateX(-50%);
                        margin-top: 1rem;
                    }
                    .exam_item{
                        padding: 0.4rem;
                        margin-top: 0.3rem;
                        background-color: #fff;
                        .exam_title{
                            font-size: 0.8rem;
                            color: #333;
                            line-height: 1.6;
                            position:relative;
                            .left_item{
                                float:left;
                                width:60%;
                            }
                            .right_item{
                                float:left;
                                width:40%;
                            }
                            i{
                                position: absolute;
                                right: 0.4rem;
                                bottom: 0;
                                font-size: 1.2rem;
                                line-height: 1;
                                color: #666;
                            }
                        }
                    }
                }
            }
            .exam_browse{
                padding-top:0;
                .exam_image_list{
                    border-top: 1px solid #aaa;
                    padding-top: 0.2rem;
                    margin-top: 0.2rem;
                }
            }
            .device_container{
                position:relative;
                flex:1;
                overflow:auto;
                width: 100%;
                padding-top: 0.4rem;
                .device_ctrl_item{
                    background: #fff;
                    border-bottom: 1px solid #eee;
                    margin-bottom: 0.4rem;
                    .device_ctrl_bar{
                        border-bottom: 1px solid #eee;
                        line-height: 2;
                        padding: 0 0.4rem;
                        position: relative;
                        i{
                            position: absolute;
                            right: 0.5rem;
                            top: 0;
                            font-size: 1rem;
                        }
                    }
                    .binding_container{
                        padding: 0 1rem;
                        .department_item{
                            margin: 10px 0 10px 0;
                            display:flex;
                            flex-direction:column;
                            .input_text{
                                float: right
                            }
                        }
                    }
                    .binding_btn{
                        display: block;
                        width: 100%;
                        border: none;
                        font-size: 1rem;
                        line-height: 2rem;
                        margin: 1rem 0 .6rem;
                        border-radius: .2rem;
                    }
                }
                .current_conversation{
                    display:flex;
                    background: #f3f3f3;
                    line-height: 2;
                    padding: 0 0.8rem;
                    margin: 0.8rem 0;
                    color: #333;
                    position:relative;
                    .left{
                        margin-right:1.2rem;
                    }
                    .right{
                        flex:1;
                    }
                    span{
                        i{
                            font-size: 1rem;
                            background: #00c59d;
                            color: #fff;
                            padding: .2rem .3rem;
                            margin-left: .4rem;
                            border-radius: .3rem;
                        }
                    }
                }
                .patient_info{
                    background: #f3f3f3;
                    padding: 0 0.8rem;
                    margin: 0.8rem 0;
                    color: #333;
                    position: relative;
                    .exam_operators{
                        padding:.5rem 0 0;
                        .btn{
                            font-size:0.8rem;
                            background: #00c59d;
                            color: #fff;
                            padding: .2rem .4rem;
                            margin-right: .4rem;
                            border-radius: .3rem;
                        }
                        .icon-guanji{
                            font-size: 1.5rem;
                            padding:0;
                            line-height:1;
                            color:#ea1b2a;
                            background: #fff;
                        }
                        .icon-setting{
                            font-size: 1rem;
                            padding:0 .3rem;
                            line-height:1.5;
                        }
                        .end{
                            background:#FF6759;
                        }
                        .edit{
                            background:#FFB144;
                        }
                    }
                    .patient_id{
                        border-bottom: 1px solid #ddd;
                        line-height: 2;
                        display: flex;
                        .content{
                            flex: 1;
                            position:relative;
                            word-break: break-all;
                            .patient_id_input{
                                width:100%;
                                position:absolute;
                                z-index:-1;
                                left: 0;
                                top: 0;
                                border: none;
                                height: 100%;
                                background: #fff;
                                padding:0;
                                font-size:1rem;
                                color:#333;
                                &.editId{
                                    z-index:99;
                                }
                            }
                        }

                    }
                    .patient_name{
                        line-height: 2;
                    }
                }
                .storage_setting{
                    background: #fff;
                    padding: 0 0.8rem;
                    margin: 0.8rem 0;
                    color: #333;
                    position: relative;
                    span{
                        i{
                            font-size: 1rem;
                            background: #00c59d;
                            color: #fff;
                            padding: .2rem .3rem;
                            margin-left: .4rem;
                            border-radius: .3rem;
                        }
                    }
                    .storage_name,.storage_password,.is_anonymous,.storage_port{
                        border-bottom: 1px solid #ddd;
                        line-height: 2;
                        display: flex;
                        .ftp_name{
                            flex:1;
                        }
                    }
                    .password{
                        border: none;
                        line-height: 2rem;
                        font-size: 1rem;
                        display: block;
                        color: #333;
                    }
                    .modify_tip{
                            word-break: keep-all
                    }
                    .storage_path{
                        line-height: 2;
                        display: flex;
                    }
                }
                .save_operator{
                    padding: 0 2rem;
                    margin: 1rem 0 0.5rem;
                    display:flex;
                    text-align:center;
                    color:#fff;
                    .single{
                        margin-right:2rem;
                    }
                    & > div{
                        line-height:2rem;
                        border-radius:0.3rem;
                        flex:1;
                        background:#00c59d;
                    }
                }
                .count_tip{
                    padding: 0 0.4rem;
                    font-size: 0.8rem;
                    line-height: 2;
                }
                .saved_image_list{
                    padding:0 0.4rem;
                    .file_item{
                        float:left;
                        width:25%;
                        border:1px solid #fff;
                        box-sizing:border-box;
                        position:relative;
                        .file_container{
                            position:relative;
                            padding-top:100%;
                            height:0;
                            background-color:#333;
                            overflow:hidden;
                            .file_image{
                                max-width:100%;
                                max-height:100%;
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%,-50%);
                            }
                            .icon-videofill{
                                font-size: 1rem;
                                color: #00c59d;
                                position: absolute;
                                bottom: .2rem;
                                left: .3rem;
                            }
                        }
                    }
                }
            }
        }
    }
    .showInfo{
        position:absolute;
        z-index:1001;
        background-color:#fff;
        width:100%;
        bottom:0;
        padding: 0.5rem;
        color: #666;
    }
    .full_modal{
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: .5;
        background: #000;
        z-index:1000;
    }
}
</style>
