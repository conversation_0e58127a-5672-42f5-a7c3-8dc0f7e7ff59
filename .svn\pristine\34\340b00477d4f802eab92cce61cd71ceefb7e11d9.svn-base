<template>
    <div style="overflow:hidden">
        <div
            v-for="(file, index) of source.list"
            :class="['file_item', `file_item_span${span}`]"
            @click="clickItem(index)"
            :key="file.resource_id + file.file_id"
        >
            <v-touch @press="pressMessage">
                <div class="file_container">
                    <common-image :fileItem="file" :isMini="span > 2"></common-image>
                </div>
                <i
                    class="iconfont icon-comment1"
                    v-show="
                        gallery.commentObj[file.resource_id] &&
                        gallery.commentObj[file.resource_id].comment_list.length
                    "
                >
                    <span>{{
                        gallery.commentObj[file.resource_id] &&
                        gallery.commentObj[file.resource_id].comment_list.length
                    }}</span>
                </i>
            </v-touch>
        </div>

    </div>
</template>

<script>
import base from "../lib/base";
export default {
    mixins: [base],
    name: 'item-component',
    props: {
        index: { // index of current item
            type: Number
        },
        source: { // here is: {uid: 'unique_1', text: 'abc'}
            type: Object,
            default () {
                return {}
            }
        },
        span: {
            type: Number,
            default: 4,
        },
    },
    methods:{
        clickItem(){

        },
        pressMessage(){

        }
    }
}
</script>
