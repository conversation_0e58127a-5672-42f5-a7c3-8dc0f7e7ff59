<template>
    <div class="student-management-container">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <el-form :inline="true" :model="studentFilters" class="filter-form">
                <el-form-item :label="lang.place_of_work">
                    <el-input v-model="studentFilters.hospital" :placeholder="lang.input_enter_tips" clearable></el-input>
                </el-form-item>
                <el-form-item :label="lang.full_name">
                    <el-input v-model="studentFilters.name" :placeholder="lang.input_enter_tips" clearable></el-input>
                </el-form-item>
                <el-form-item :label="lang.student_status">
                    <el-select v-model="studentFilters.status" :placeholder="lang.input_select_tips" clearable>
                        <el-option v-for="(value, key) in statusOptions" :key="key" :label="value.label" :value="value.value"></el-option>
                        <!-- 根据实际状态添加更多 -->
                    </el-select>
                </el-form-item>
                <el-form-item label="Supervisor">
                    <el-select
                        v-model="studentFilters.supervisorID"
                        :placeholder="lang.input_enter_tips"
                        filterable
                        remote
                        clearable
                        :remote-method="remoteSearch"
                        :loading="searchLoading"
                        ref="userSelectRef"
                        @clear="handleSelectClear()"
                        @change="handleSelectChange"
                        @visible-change="refreshUserSelectRef()"
                    >
                        <el-option
                            v-for="item in searchResults"
                            :key="item.uid"
                            :label="formatUserName(item)"
                            :value="item.uid">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="handleStudentSearch">{{ lang.query_btn }}</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 学员列表 -->
        <div class="content-section">
            <el-table
                :data="studentList"
                border
                stripe
                v-loading="loadingStudents"
                height="calc(100vh - 350px)"
                :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#303133', fontWeight: 'bold' }"
            >
                <el-table-column prop="account" :label="lang.register_account" width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="name" :label="lang.full_name" width="100" show-overflow-tooltip></el-table-column>
                <el-table-column prop="hospital" :label="lang.place_of_work" min-width="150" show-overflow-tooltip></el-table-column>
                <el-table-column prop="createdAt" :label="lang.admin_register_date" width="160">
                    <template slot-scope="scope">
                        {{ formatDateTime(scope.row.createdAt) }}
                    </template>
                </el-table-column>
                <el-table-column prop="supervisorName" label="Supervisor" min-width="120">
                    <template slot-scope="scope">
                        <template v-if="scope.row.supervisorList && scope.row.supervisorList.length > 0">
                            <el-tag
                                v-for="(supervisor, index) in scope.row.supervisorList"
                                :key="index"
                                size="small"
                                type="info"
                                class="supervisor-tag"
                                style="margin-right: 5px; margin-bottom: 5px;"
                            >
                                {{ supervisor.nickname || '' }}
                            </el-tag>
                        </template>
                        <span v-else>-</span>
                    </template>
                </el-table-column>
                <el-table-column prop="uuid" label="IC/Passport" width="120" show-overflow-tooltip></el-table-column>

                <el-table-column prop="MMCNo" label="MMC No." width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="email" :label="lang.register_email" min-width="180" show-overflow-tooltip></el-table-column>
                <el-table-column prop="job" :label="lang.job_description" min-width="120" show-overflow-tooltip></el-table-column>
                <el-table-column prop="description" :label="lang.remark_text" min-width="150" show-overflow-tooltip></el-table-column>
                <el-table-column prop="status" :label="lang.status" width="160">
                    <template slot-scope="scope">
                        <span :class="getStudentStatusClass(scope.row.status)">
                            {{ SMART_TECH_TRAINING_STUDENT_STATUS_LANG_MAP[scope.row.status] }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column :label="lang.operation" width="160" min-width="140">
                    <template slot-scope="scope">
                        <div class="operation-buttons-container">
                            <!-- 申请中状态：显示同意和拒绝按钮 -->
                            <template v-if="Number(scope.row.status) === SMART_TECH_TRAINING_STUDENT_STATUS.APPLYING">
                                <el-button
                                    type="text"
                                    class="option-btn approve-btn"
                                    @click="handleApproveStudent(scope.row)"
                                >
                                    {{ lang.agree_txt }}
                                </el-button>
                                <el-button
                                    type="text"
                                    class="option-btn reject-btn"
                                    @click="handleRejectStudent(scope.row)"
                                >
                                    {{ lang.reject_btn }}
                                </el-button>
                            </template>

                            <!-- 已通过状态：显示编辑和禁用按钮 -->
                            <template v-else-if="Number(scope.row.status) === SMART_TECH_TRAINING_STUDENT_STATUS.PASSED">
                                <el-button
                                    type="text"
                                    class="option-btn"
                                    @click="handleEditStudent(scope.row)"
                                >
                                    {{ lang.edit_txt }}
                                </el-button>
                                <el-button
                                    type="text"
                                    class="option-btn disable-btn"
                                    @click="handleDisableStudent(scope.row)"
                                >
                                    {{ lang.admin_disabled }}
                                </el-button>
                            </template>

                            <!-- 已禁用状态：显示编辑和启用按钮 -->
                            <template v-else-if="Number(scope.row.status) === SMART_TECH_TRAINING_STUDENT_STATUS.DISABLED">
                                <el-button
                                    type="text"
                                    class="option-btn"
                                    @click="handleEditStudent(scope.row)"
                                >
                                    {{ lang.edit_txt }}
                                </el-button>
                                <el-button
                                    type="text"
                                    class="option-btn enable-btn"
                                    @click="handleEnableStudent(scope.row)"
                                >
                                    {{ lang.enable }}
                                </el-button>
                            </template>

                            <!-- 已拒绝状态：不显示任何操作按钮 -->
                            <template v-else-if="Number(scope.row.status) === SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED">
                                <span class="no-operation"></span>
                            </template>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="studentFilters.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="studentFilters.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalCount">
                </el-pagination>
            </div>
        </div>

        <!-- 编辑学员弹窗 -->
        <common-dialog
            :show.sync="editDialogVisible"
            :title="lang.edit_student_information"
            @submit="handleSubmitEditStudent"
            @cancel="handleCancelEditStudent"
            :submitText="lang.confirm_txt"
            width="500px"
        >
            <el-form :model="editingStudent" ref="editStudentForm" label-width="80px">
                <el-form-item label="Supervisor" prop="supervisorIDList">
                    <el-select
                        v-model="editingStudent.supervisorIDList"
                        :placeholder="lang.input_enter_tips"
                        filterable
                        remote
                        clearable
                        multiple
                        :remote-method="remoteSearchForEdit"
                        :loading="editSearchLoading"
                        style="width: 100%;"
                        ref="editUserSelectRef"
                        @clear="handleEditSelectClear()"
                        @change="handleEditSelectChange"
                        @visible-change="refreshEditUserSelectRef"
                    >
                        <el-option
                            v-for="item in editSearchResults"
                            :key="item.uid"
                            :label="formatUserName(item)"
                            :value="item.uid">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.remark_text" prop="description">
                    <el-input
                        v-model="editingStudent.description"
                        type="textarea"
                        :rows="3"
                        :placeholder="lang.input_enter_tips"
                        style="width: 100%;"
                    ></el-input>
                </el-form-item>
            </el-form>
        </common-dialog>
    </div>
</template>

<script>

import base from "../../../lib/base";
import commonDialog from '@/module/ultrasync_pc/MRComponents/commonDialog.vue';
import service from '../../../service/service.js';
import moment from 'moment';
import {SMART_TECH_TRAINING_STUDENT_STATUS,SEX} from '../../../lib/constants';
import Tool from '@/common/tool.js';
export default {
    name: "StudentManagementContent",
    mixins: [base],
    components: {
        commonDialog
    },
    data() {
        return {
            SMART_TECH_TRAINING_STUDENT_STATUS,
            SEX,
            studentFilters: {
                name: '',
                hospital: '',
                page: 1,
                pageSize: 20,
                status: '',
                supervisorID: ''
            },

            supervisorList: [],
            searchResults: [], // 用于筛选区域的督导搜索结果
            searchLoading: false, // 筛选区域的督导搜索加载状态
            editSearchResults: [], // 用于编辑弹窗的督导搜索结果
            editSearchLoading: false, // 编辑弹窗的督导搜索加载状态

            studentList: [],
            loadingStudents: false,
            editDialogVisible: false,
            editingStudent: {
                supervisorIDList: [],
                description: '',
            },
            currentEditingOriginalStudent: null,
            totalCount: 0, // 总记录数，用于分页
            trainingID:'',
        };
    },
    computed: {
        statusOptions() {
            const statusOptions = [];
            for (const [key, value] of Object.entries(SMART_TECH_TRAINING_STUDENT_STATUS)) {
                if (typeof value !== 'number') {
                    statusOptions.push({
                        value: key,
                        label: this.SMART_TECH_TRAINING_STUDENT_STATUS_LANG_MAP[key]
                    });
                }
            }
            return statusOptions;
        },
        SMART_TECH_TRAINING_STUDENT_STATUS_LANG_MAP(){
            return {
                [SMART_TECH_TRAINING_STUDENT_STATUS.APPLYING]: this.lang.applying_txt,
                [SMART_TECH_TRAINING_STUDENT_STATUS.PASSED]: this.lang.normal_use,
                [SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED]: this.lang.Rejected,
                [SMART_TECH_TRAINING_STUDENT_STATUS.DISABLED]: this.lang.Disabled,
            }
        },
        sexLanguageMap(){
            return {
                [SEX.MALE]: this.lang.male,
                [SEX.FEMALE]: this.lang.female,
                [SEX.UNKNOWN]: this.lang.unknow_text
            }
        }
    },
    watch: {
        $route: {
            handler(to, from) {
                // 当从批改详情页面返回到批改审核列表页面时，刷新数据
                console.log(to)
                if (to.name === "SmartTechTrainingStudentManagement") {
                    // 获取督导列表并初始化搜索结果
                    this.$nextTick(() => {
                        this.getTrainingSupervisorList().then(() => {
                            this.searchResults = this.supervisorList;
                            this.editSearchResults = this.supervisorList;
                        });
                        this.handleStudentSearch();
                    });
                }
            },
            immediate: true,
        },
    },
    created() {

        // 从 URL 获取参数
        this.trainingID = this.$route.params.trainingId

    },

    mounted() {
    },
    methods: {
        getStudentStatusClass(status) {
            if (status === SMART_TECH_TRAINING_STUDENT_STATUS.PASSED) {
                return 'status-tag-passed-new';
            }
            if (status === SMART_TECH_TRAINING_STUDENT_STATUS.DISABLED) {
                return 'status-tag-failed-new';
            }
            if (status === SMART_TECH_TRAINING_STUDENT_STATUS.APPLYING) {
                return 'status-tag-pending-new'; // 申请中状态使用黄色样式
            }
            if (status === SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED) {
                return 'status-tag-failed-new'; // 拒绝状态使用红色样式
            }
            return 'status-tag-default-new'; // 默认样式
        },
        handleStudentSearch() {
            if (!this.trainingID) {
                this.$message.error('training_id_cannot_be_empty');
                return;
            }

            this.loadingStudents = true;

            // 准备API参数，只包含API需要的字段
            const params = {
                trainingID: this.trainingID,
                page: this.studentFilters.page,
                pageSize: this.studentFilters.pageSize
            };
            if (this.studentFilters.name) {
                params.name = this.studentFilters.name;
            }
            if (this.studentFilters.hospital) {
                params.hospital = this.studentFilters.hospital;
            }
            if (this.studentFilters.status) {
                params.status = this.studentFilters.status;
            }
            if (this.studentFilters.supervisorID) {
                params.supervisorID = this.studentFilters.supervisorID;
            }

            // 调用API
            service.getTrainingStudentList(params)
                .then(result => {

                    const res = result.data;
                    if (res.error_code === 0) {
                        // 从 result.data.data 获取学生列表数据
                        const studentData = res.data.data  || [];
                        console.log(studentData,'studentData')
                        // 映射API返回的字段到表格所需的格式
                        this.studentList = studentData;
                        // 从 result.data.total 获取总数量
                        this.totalCount = res.data.total || 0;
                    } else {
                        this.$message.error('get student list failed');
                    }
                })
                .catch(err => {
                    console.error('get student list failed:', err);
                    this.$message.error('get student list failed');
                })
                .finally(() => {
                    this.loadingStudents = false;
                });
        },
        handleEditStudent(student) {
            this.currentEditingOriginalStudent = student; // 保存原始对象引用
            this.editingStudent = {
                ...student, // 浅拷贝一份，避免直接修改列表数据
                supervisorIDList: student.supervisorList.map(supervisor => supervisor.id),
                description: student.description || ''
            };
            // 重置编辑弹窗的搜索结果，确保显示完整的督导列表
            this.editSearchResults = this.supervisorList;
            this.editDialogVisible = true;
        },
        // 同意学生申请
        handleApproveStudent(student) {
            this.$confirm(this.lang.do_you_want_to_approve_student_tips, this.lang.tips, {
                confirmButtonText: this.lang.confirm_btn,
                cancelButtonText: this.lang.cancel_btn,
                type: 'warning'
            }).then(() => {
                this.updateStudentStatus(student, SMART_TECH_TRAINING_STUDENT_STATUS.PASSED);
            });
        },

        // 拒绝学生申请
        handleRejectStudent(student) {
            this.$confirm(this.lang.do_you_want_to_reject_student_tips, this.lang.tips, {
                confirmButtonText: this.lang.confirm_btn,
                cancelButtonText: this.lang.cancel_btn,
                type: 'warning'
            }).then(() => {
                this.updateStudentStatus(student, SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED);
            });
        },

        // 禁用学生
        handleDisableStudent(student) {
            this.$confirm(this.lang.do_you_want_to_disable_this_student_tips, this.lang.tips, {
                confirmButtonText: this.lang.confirm_btn,
                cancelButtonText: this.lang.cancel_btn,
                type: 'warning'
            }).then(() => {
                this.updateStudentStatus(student, SMART_TECH_TRAINING_STUDENT_STATUS.DISABLED);
            });
        },

        // 启用学生
        handleEnableStudent(student) {
            this.$confirm(this.lang.do_you_want_to_activate_student_tips, this.lang.tips, {
                confirmButtonText: this.lang.confirm_btn,
                cancelButtonText: this.lang.cancel_btn,
                type: 'warning'
            }).then(() => {
                this.updateStudentStatus(student, SMART_TECH_TRAINING_STUDENT_STATUS.PASSED);
            });
        },

        // 统一的更新学生状态方法
        updateStudentStatus(student, newStatus) {
            // 显示loading状态
            this.loadingStudents = true;

            // 调用API更新学员状态
            service.updateTrainingStudentStatus({
                trainingID: this.trainingID,
                studentID: student.uid,
                status: newStatus
            }).then(res => {
                if (res.data.error_code === 0) {
                    this.$message.success(this.lang.operate_success);
                    // 刷新学员列表
                    this.handleStudentSearch();
                } else {
                    this.$message.error(this.lang.operate_failed);
                }
            }).catch(error => {
                console.error('update student status failed:', error);
                this.$message.error(this.lang.operate_failed);
            }).finally(() => {
                this.loadingStudents = false;
            });
        },
        handleSubmitEditStudent() {
            this.$refs.editStudentForm.validate(async (valid) => {
                if (valid) {

                    console.log(this.editingStudent,'editingStudent');

                    await this.setSupervisorToStudent();
                    this.editDialogVisible = false;
                } else {
                    console.log('error submit!!');
                    this.$message.error(this.lang.submission_failed);
                    return false;
                }
            });
        },
        handleCancelEditStudent() {
            this.editDialogVisible = false;
        },

        // 处理分页大小变化
        handleSizeChange(size) {
            this.studentFilters.pageSize = size;
            this.studentFilters.page = 1; // 重置到第一页
            this.handleStudentSearch();
        },

        // 处理页码变化
        handleCurrentChange(page) {
            this.studentFilters.page = page;
            this.handleStudentSearch();
        },

        // 格式化日期时间
        formatDateTime(dateTimeStr) {
            if (!dateTimeStr) {
                return '';
            }
            return moment(dateTimeStr).format('YYYY-MM-DD HH:mm:ss');
        },

        // 远程搜索方法（防抖处理后的入口函数）
        remoteSearch(query) {
            this.searchLoading = true;
            if (query === '') {
                // 如果查询为空，显示所有督导
                this.searchResults = this.supervisorList;
            } else {
                // 否则根据查询条件筛选
                this.searchResults = this.supervisorList.filter(supervisor => {
                    const nickname = supervisor.nickname || '';
                    return nickname.toLowerCase().includes(query.toLowerCase());
                });
            }
            this.searchLoading = false;
        },


        // 格式化督导显示名称
        formatUserName(supervisor) {
            if (!supervisor) {
                return '';
            }
            return supervisor.nickname || '';
        },

        // 从督导列表中获取督导名称
        getSupervisorName(supervisorList) {
            if (!supervisorList || !Array.isArray(supervisorList) || supervisorList.length === 0) {
                return '';
            }
            // 如果有多个督导，返回第一个督导的名称
            let supervisorNames = [];
            supervisorList.forEach(supervisor => {
                supervisorNames.push(supervisor.nickname);
            });
            return supervisorNames.join(',');
        },
        getTrainingSupervisorList(){
            return new Promise((resolve,reject)=>{
                this.loading = true;
                const params={
                    trainingID:this.trainingID,
                    page: 1,
                    pageSize: 99999999
                }
                service.getTrainingSupervisorList(params).then(res=>{
                    if(res.data.error_code === 0){
                        // 处理新的分页数据结构
                        const { total, page, pageSize, data } = res.data.data;
                        // 处理督导列表数据
                        this.supervisorList = data
                        console.log(this.supervisorList,'this.supervisorList')
                        resolve(res)
                    }else{
                        reject(res)
                    }
                    this.loading = false;
                },err=>{
                    reject(err)
                    this.loading = false;
                })
            })
        },
        handleSelectClear(){
            this.searchResults=this.supervisorList;
            this.$nextTick(() => {
                const selectComponent = this.$refs.userSelectRef;
                if (selectComponent && typeof selectComponent.focus === 'function') {
                    selectComponent.focus();
                }
            });
        },
        handleEditSelectClear(){
            this.editSearchResults=this.supervisorList;
            this.$nextTick(() => {
                const selectComponent = this.$refs.editUserSelectRef;
                if (selectComponent && typeof selectComponent.focus === 'function') {
                    selectComponent.focus();
                }
            });
        },
        // 编辑弹窗中的远程搜索方法（防抖处理后的入口函数）
        remoteSearchForEdit(query) {
            this.editSearchLoading = true;
            if (query === '') {
                // 如果查询为空，显示所有督导
                this.editSearchResults = this.supervisorList;
            } else {
                // 否则根据查询条件筛选
                this.editSearchResults = this.supervisorList.filter(supervisor => {
                    const nickname = supervisor.nickname || '';
                    return nickname.toLowerCase().includes(query.toLowerCase());
                });
            }
            this.editSearchLoading = false;
        },
        refreshUserSelectRef(val){
            if(!val){
                setTimeout(() => {
                    this.searchResults=this.supervisorList;
                }, 600);
            }
        },
        refreshEditUserSelectRef(val){
            if(!val){
                setTimeout(() => {
                    this.editSearchResults=this.supervisorList;
                }, 600);
            }
        },
        // 处理选择项变化，清除输入文本
        handleSelectChange(value) {
            console.log(value,'value');
        },
        // 处理编辑弹窗选择项变化，清除输入文本
        handleEditSelectChange(value) {
            console.log(value,'value');
            console.log(this.editingStudent,'editingStudent');
        },
        setSupervisorToStudent(){
            service.setSupervisorToStudent({
                trainingID: this.trainingID,
                studentID: this.currentEditingOriginalStudent.uid,
                supervisorIDList: this.editingStudent.supervisorIDList,
                description: this.editingStudent.description
            }).then(result => {
                if(result.data.error_code === 0){
                    this.$message.success(this.lang.operate_success);
                    this.editDialogVisible = false;
                    this.handleStudentSearch();
                }else{
                    this.$message.error(this.lang.operate_failed);
                }
            }).catch(error => {
                this.$message.error(this.lang.operate_failed);
            });
        }
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.student-management-container {
    padding: 20px;
    background-color: #f5f7fa;
    // min-height: calc(100vh - 50px); // 将由flex控制高度
    height: 100%; // 确保容器占满父容器高度
    overflow: hidden; // 防止内部滚动条影响布局
    display: flex;
    flex-direction: column;

    .filter-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        flex-shrink: 0; // 防止筛选区被压缩
        .filter-form {
            .el-form-item {
                margin-bottom: 0; // 保持紧凑
                margin-right: 15px;
            }
            // 可以根据需要调整el-select和el-input的宽度，如果默认宽度不合适
            // .el-select {
            //     width: 200px;
            // }
            // .el-input {
            //     width: 200px;
            // }
        }
    }

    .content-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
        flex: 1; // 占据剩余空间
        display: flex; // 为内部表格和分页的布局做准备
        flex-direction: column;
        overflow: hidden; // 内部自行处理滚动

        .el-table {
            flex: 1; // 表格占据content-section的主要空间

            // 确保表格内部滚动正常工作
            ::v-deep .el-table__body-wrapper {
                overflow-y: auto;
                overflow-x: auto;
            }

            // 固定表头样式
            ::v-deep .el-table__header-wrapper {
                overflow: visible;
            }

            // 表格单元格内容过长时的处理
            ::v-deep .el-table__cell {
                word-break: break-word;
                white-space: normal;
            }

            // 表格行高度调整
            ::v-deep .el-table__row {
                height: auto;
                min-height: 50px;
            }

            // 表格边框和间距优化
            ::v-deep .el-table td,
            ::v-deep .el-table th {
                padding: 8px 10px;
                border-right: 1px solid #EBEEF5;
            }

            // 水平滚动条样式
            ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
                height: 8px;
            }

            ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 4px;
            }

            ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 4px;
            }

            ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }
        }

        // 分页容器样式
        .pagination-container {
            margin-top: 20px;
            text-align: right;
            flex-shrink: 0; // 防止分页被压缩
        }
    }

    // 操作按钮样式
    .operation-buttons-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .option-btn {
            padding: 4px 8px;
            font-size: 12px;

            &.approve-btn {
                color: #409EFF;
                &:hover {
                    color: #66b1ff;
                }
            }

            &.reject-btn {
                color: #F56C6C;
                &:hover {
                    color: #f78989;
                }
            }

            &.disable-btn {
                color: #F56C6C;
                &:hover {
                    color: #f78989;
                }
            }

            &.enable-btn {
                color: #409EFF;
                &:hover {
                    color: #66b1ff;
                }
            }
        }

        .no-operation {
            color: #C0C4CC;
            font-size: 12px;
            padding: 4px 8px;
        }
    }

    // Supervisor标签样式优化
    .supervisor-tag {
        display: inline-block;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
