<template>
    <div class="modify_basic_info_page third_level_page">
        <mrHeader>
            <template #title>
                {{lang.modify_basic_info_text}}
            </template>
        </mrHeader>
        <div class="container">
            <div class="form">
                <div class="modify_basic_info_row clearfix">
                    <!-- <div class="modify_basic_info_label">{{lang.nickname}}</div> -->
                    <!-- <textarea rows="1"  v-model="nickname" class="modify_basic_info_input" maxlength="16" :placeholder="lang.nickname"></textarea> -->
                    <van-field v-model="nickname" :label="lang.nickname" :placeholder="lang.nickname" maxlength="16"/>
                </div>
                <!-- <div class="modify_basic_info_unchanged_row clearfix">
                    <div class="modify_basic_info_unchanged_label">{{lang.register_account}}</div>
                    <input type="text" v-model="account" class="modify_basic_info_unchanged_input" maxlength="16" readonly/>
                    <button class="primary_bg reset_login_name_btn" @click="resetLoginName">{{lang.edit_txt}}</button>
                </div>

                <div v-show="mobile_phone_show" class="modify_basic_info_unchanged_row clearfix">
                    <div class="modify_basic_info_unchanged_label">{{lang.register_mobile}}</div>
                    <input type="text" v-model="mobileShow" class="modify_basic_info_unchanged_input" maxlength="11" :placeholder="lang.register_mobile" readonly/>
                    <button class="primary_bg reset_mobile_btn" @click="modifyBinding(1)">{{lang.edit_txt}}</button>
                </div>
                <div class="modify_basic_info_unchanged_row clearfix">
                    <div class="modify_basic_info_unchanged_label">{{lang.register_email}}</div>
                    <input type="text" v-model="emailShow" class="modify_basic_info_unchanged_input" :placeholder="lang.register_email" readonly/>
                    <button class="primary_bg reset_mobile_btn" @click="modifyBinding(2)">{{lang.edit_txt}}</button>
                </div>
                <div class="modify_basic_info_row clearfix">
                    <div class="modify_basic_info_label">{{lang.scan_room_hospital}}</div>
                    <div class="modify_basic_info_input selecter_wraper longwrap" @click="gotoSelectHosptal">
                        <p class="longwrap" v-if="hospitalSelected">{{hospitalsFilter(hospitalSelected)}}</p>
                        <p v-else class="placeholder">{{this.lang.select_hospital}}</p>
                    </div>
                    <i class="iconfont svg_icon_entry icon-entry"></i>
                </div>
                <div class="modify_basic_info_row clearfix">
                    <div class="modify_basic_info_label">{{lang.register_sex}}</div>
                    <p class="modify_basic_info_input selecter_wraper">
                        <mr-selecter
                            :options="optionList"
                            :seletedId="optionSelected"
                            :callback="optionSelectChanged"
                        ></mr-selecter>
                    </p>
                </div> -->

            </div>
            <div class="save_btn_container">
                <button class="primary_bg  save_btn" @click="modify_current_basic_info">{{lang.save_txt}}</button>
            </div>
        </div>
        <router-view></router-view>
    </div>
</template>
<script>
// import mrSelecter from '../components/mrSelecter'
import base from '../lib/base'
import service from '../service/service'
import { Field, Toast } from 'vant';
import {
    accountRegExp1,
    nicknameRegExp,
} from '@/common/regExpMapping.js'
export default {
    mixins: [base],
    name: 'ModifyBasicInfo',
    components: {
        VanField:Field,
    },
    data(){
    	return {
            nickname:'',
    	}
    },
    computed:{
    },
    mounted(){
        let that = this;
        this.nickname = this.user.nickname;

    },
    destroyed:function(){
    },
    methods:{

        modify_current_basic_info(){
            // var accountRegexp=/^[a-zA-Z0-9][_a-zA-Z0-9]{3,15}$/;
            // var accountRegexp2=/^[0-9]+$/;
            // var regexp = /^1[0-9]{10}$/;
            if (this.nickname == ''){
                Toast(this.lang.nickname_should_not_be_empty);
            }else{
                var that = this;
                var input_data = {};
                var is_edit = false;
                if (that.user.nickname != that.nickname) {
                    input_data.nickname = that.nickname;
                    is_edit = true;
                }
                if (!is_edit) {
                    that.$router.go(-1);
                }else{
                    window.main_screen.commitUserBasicInfoModify(input_data, function(is_succ, information){
                        if(is_succ){
                            that.changeDefaultImg(input_data);
                            that.$store.commit('user/updateUser',input_data)
                            if (input_data.username) {
                                window.localStorage.setItem('account',input_data.username)
                                that.user.login_name=input_data.username
                            }

                            if (input_data.hospital) {
                                that.user.hospital_id=input_data.hospital;
                            }

                            Toast(that.lang.modify_basic_info_success);
                            that.$store.commit('chatList/updateFriendToChatList',that.user)
                            that.$store.commit('conversationList/updateFriendToConversationList',that.user)
                            if (input_data.nickname) {
                                //修改名称触发判断修改默认头像
                                that.$root.eventBus.$emit('ifCreateUserAvatar',1)
                            }
                            that.$router.go(-1);
                        }else{
                            if (information == "database_err") {
                                Toast(that.lang.modify_password_fail_database_err);
                            } else if (information == "name_repeated") {
                                Toast(that.lang.personal_information_fail_name_repeated);
                            } else if (information == "name_pure_numbers") {
                                Toast(that.lang.personal_information_fail_name_pure_numbers);
                            } else if (information == "email_repeated") {
                                Toast(that.lang.personal_information_fail_email_repeated);
                            } else if (information == "nickname_repeated") {
                                Toast(that.lang.personal_information_fail_nickname_repeated);
                            } else if (information == "mobile_phone_repeated") {
                                Toast(that.lang.personal_information_fail_mobile_phone_repeated);
                            }
                        }
                    })
                }
            }
        },
    }
}

</script>
<style lang="scss">
	.modify_basic_info_page{
		.container{
            overflow: hidden;
            width: 100%;
            .form{
                padding:0 .8rem;
                background:#fff;
                margin-top:1rem;
                border-top: 1px solid #c8c7cc;
                border-bottom: 1px solid #c8c7cc;
            }
            .modify_basic_info_unchanged_row{
                border-bottom: 1px solid #c8c7cc;
                display:flex;
                flex-direction:row;
                font-size:0.8rem;
                position:relative;
                padding: 0.1rem 0;
                align-items: center;
                .modify_basic_info_unchanged_label{
                    line-height: 1.7rem;
                    font-size: .8rem;
                    margin-right:1rem;
                    min-width:25%;
                    color: #444;
                }
                .modify_basic_info_unchanged_input{
                    flex:1;
                    border: none;
                    font-size: .8rem;
                    padding: .5rem 0;
                    display: block;
                    color: #777;
                }
            }
            .modify_basic_info_row{
                    border-bottom: 1px solid #c8c7cc;
                    display:flex;
                    flex-direction:row;
                    position: relative;
                    font-size:0.8rem;
                    padding: 0.1rem 0;
                .svg_icon_entry{
                    position:absolute;
                    right:1rem;
                    top:2em;
                fill:#aaa;
                color:#aaa;
                    width: 0.5rem;
                    height: 100%;
                }
                .placeholder{
                    color: #777;
                }
                .modify_basic_info_label{
                        line-height: 2rem;
                        font-size: .8rem;
                        margin-right:1rem;
                        min-width:2rem;
                        color: #444;
                        flex-shrink: 0;
                }
                .modify_basic_info_input{
                    flex:1;
                    border: none;
                    font-size: .8rem;
                    line-height:1rem;
                    padding: .5rem 0;
                    display: block;
                    &.selecter_wraper{
                        line-height:2rem;
                        padding:0;
                        height:2rem;
                    }
                }
                &:last-child{
                    border-bottom:none;
                }
            }
            #register_male_and_female_father{
                position:relative;
                border-bottom:2px solid #c8c7cc;
            }
			.register_male_and_female{
				position:absolute;
				right:0rem;
				bottom:0.3rem;
				display: block;
				width: 80%;
				border: none;
				font-size: 0.8rem;
				line-height: 2rem;
				margin: 1rem 0 .6rem;
				border-radius: .2rem;
				margin:auto;
			}
		}
        .save_btn_container{
            margin:0 .5rem;
            .save_btn{
                display: block;
                width: 100%;
                border: none;
                font-size: 1rem;
                line-height: 2rem;
                margin: 1rem 0 .6rem;
                border-radius: .2rem;
            }
        }
        .reset_mobile_btn,.reset_login_name_btn{
            position:absolute;
            right:0rem;
//             bottom:0rem;
            min-width: 20%;
            height:1.9rem;
            border: none;
            font-size: 0.8rem;
            line-height: 2rem;
            border-radius: .2rem;
        }
	}
</style>
