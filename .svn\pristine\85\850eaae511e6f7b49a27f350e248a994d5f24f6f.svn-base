<template>
  <div class="main-page">
    <headerBar />
    <section class="main-container">
        <aside class="left-content">
            <leftSideBar />
        </aside>
        <main class="right-content">
            <router-view keep-alive/>
        </main>
    </section>
    <cookies-notification v-if="isCookiesNotificationShow"/>
    <safe-auth></safe-auth>
    <download-progress-bar></download-progress-bar>
    <DownLoadManager></DownLoadManager>
    <LivingNotifyDialog v-model="showLivingNotifyDialog" :livingGroupInfo="livingGroupInfo" @close="handleDialogClose"></LivingNotifyDialog>
    <professional-identity-dialog :show.sync="showProfessionalIdentityDialog" @success="handleProfessionalIdentitySuccess"></professional-identity-dialog>
    <init-organization ref="init_organization"></init-organization>
    <user-avatar ref="user_avatar"></user-avatar>
    <group-avatar></group-avatar>
    <qr-scan-action></qr-scan-action>
    <audio id="message_notify" src='static/resource_pc/audio/notify.wav'></audio>
    <div class="network_unavailable" v-show="loadingConfig.networkUnavailable">{{lang.network_unavailable}}</div>
  </div>
</template>

<script>
import headerBar from '../components/headerBar'
import leftSideBar from '../components/leftSideBar'
import cookiesNotification from '../components/cookiesNotification.vue'
import safeAuth from '../components/safeAuth.vue'
import downloadProgressBar from '../components/downloadProgressBar'
import DownLoadManager from '../components/downLoadManager.vue'
import LivingNotifyDialog from '../components/live/livingNotifyDialog.vue'
import ProfessionalIdentityDialog from '../components/professionalIdentityDialog.vue'
import InitOrganization from '../components/initOrganization.vue'
import userAvatar from '../components/userAvatar.vue'
import groupAvatar from '../components/groupAvatar.vue'
import qrScanAction from '../components/qrScanAction.vue'
import Tool from '@/common/tool.js'
import AutoLoginManager from '../lib/autoLoginManager'
import MainScreenManager from '../lib/mainScreenManager'
import ConversationManager from '../lib/conversationManager'
import EventListenerManager from '../lib/eventListenerManager'
import base from '../lib/base'
import groupsetTool from '../lib/groupsetTool'
import sendMessage from '../lib/sendMessage'
import iworksTool from '../lib/iworksTool';
import {parseImageListToLocal,destroyAllConference,} from '../lib/common_base'
export default {
    mixins: [base, groupsetTool, sendMessage, iworksTool],
    name: 'MainPage',
    components: {
        headerBar,
        leftSideBar,
        cookiesNotification,
        safeAuth,
        downloadProgressBar,
        DownLoadManager,
        LivingNotifyDialog,
        ProfessionalIdentityDialog,
        InitOrganization,
        userAvatar,
        groupAvatar,
        qrScanAction
    },
    computed:{
        loadingConfig(){
            return this.$store.state.loadingConfig
        }
    },
    data() {
        return {
            isCookiesNotificationShow: false,
            showLivingNotifyDialog: false,
            livingGroupInfo: {},
            showProfessionalIdentityDialog: false,
            autoLoginManager: null,
            mainScreenManager: null,
            eventListenerManager: null,
            conversationManager: null,
            // DOM事件监听器引用，用于清理
            keyupHandler: null,
            messageHandler: null,
            beforeunloadHandler: null
        }
    },
    created() {
        // 初始化管理器
        this.autoLoginManager = new AutoLoginManager(this)
        this.mainScreenManager = new MainScreenManager(this)
        this.conversationManager = new ConversationManager(this)
        this.eventListenerManager = new EventListenerManager(this, this.autoLoginManager, this.mainScreenManager)
        this.debounceUpdateLiveCount = Tool.debounce(this.updateLiveCount,1000)
    },
    mounted() {
        this.$nextTick(() => {

            this.isCookiesNotificationShow = Tool.checkAppClient('Browser')
            //恢复窗口大小
            window.CWorkstationCommunicationMng.navigationShowNormalOrMaximize();
            this.$root.eventBus.$off('NotifyShowConfirmDialog').$on('NotifyShowConfirmDialog',this.NotifyShowConfirmDialog);
            var that=this;
            this.$root.eventBus.$off('notifyStartupOption').$on('notifyStartupOption', function (data) {
                window.g_extend_info = data;
                console.log('notifyStartupOption---',data)
                if (data) {
                    //第三方登录
                    window.localStorage.setItem('password','');
                    if(that.user.fromLogin){
                        //已登录
                        if (!window.main_screen) {
                            that.initPage();
                        }
                        var option = Tool.parseStartupOption(data);
                        if (that.user.outer_id && 0 < that.user.outer_id.length && that.user.outer_id == option.outer_id) {
                            //同账号,打开会话
                            that.startConversationWithStartupOption();
                        } else {
                            //不同账号,重新登录
                            that.$router.replace(`/login`);
                            that.$root.eventBus.$emit('reloadRouter')
                        }
                    }else{
                        //未登录
                        that.$router.replace(`/login`);
                        that.$root.eventBus.$emit('reloadRouter')
                    }
                } else {
                    //自登录
                    console.log('notifyStartupOption---2',that.user.fromLogin)
                    if(that.user.fromLogin){
                        that.initPage();
                    }else{
                        that.loginLoading=true
                        that.autoLoginManager.autoLogin(()=>{
                            that.initPage()
                        })
                    }
                }
            });
            //CWorkstationCommunicationMng.QueryStartupOption();
            this.$root.eventBus.$on('NotifyGetDeviceID',(param)=>{
                console.log("################### NotifyGetDeviceID index.vue ###################",param);
                this.device_id = param.device_id;
                if("GetDeviceID_only" != param.append_info){
                    window.CWorkstationCommunicationMng.QueryStartupOption();
                }
            })
            window.CWorkstationCommunicationMng.GetDeviceID({});
            this.$root.eventBus.$off('updateProgressOSS').$on('updateProgressOSS',function(data){
                if (data.error) {
                    that.$store.commit('conversationList/updateUploadFail',{
                        cid:data.cid,
                        file_id:data.file_id
                    })
                }else{
                    that.$store.commit('conversationList/updateFileProgress',{
                        msg:{
                            file_id:data.file_id,
                            group_id:data.cid
                        },
                        percent:data.progress
                    })
                    if (data.progress==100) {
                        that.updateUploadProgress(data)
                    }else{
                    }
                }
            });
            //istation转发图片
            this.$root.eventBus.$off('initMachineTransfer').$on('initMachineTransfer',that.initMachineTransfer);
            this.$root.eventBus.$off('updateMachineTransfer').$on('updateMachineTransfer',that.updateMachineTransfer);
            this.$root.eventBus.$off('finishMachineTransfer').$on('finishMachineTransfer',that.finishMachineTransfer);
            this.$root.eventBus.$off('notifyNewExamImages').$on('notifyNewExamImages',that.notifyNewExamImages);
            // 保存keyup事件处理器引用
            this.keyupHandler = (event) => {
                if (this.$route.name=='gallery') {
                    //给画廊添加的按钮事件
                    if (event.keyCode==27) {
                        this.$root.eventBus.$emit('closeGallery')
                    }
                    if (event.keyCode==37||event.keyCode==38) {
                        this.$root.eventBus.$emit('prevImage')
                    }
                    if (event.keyCode==39||event.keyCode==40) {
                        this.$root.eventBus.$emit('nextImage')
                    }
                }
            }
            document.addEventListener('keyup', this.keyupHandler)

            // 血站-专家系统 文件交互
            this.$root.eventBus.$off('DealNotifySendFileToConversation').$on('DealNotifySendFileToConversation',this.DealNotifySendFileToConversation);
            this.$root.eventBus.$off('UpdateSendFileToConversation').$on('UpdateSendFileToConversation',this.UpdateSendFileToConversation);
            //检测音频设备的插拔事件

            this.$root.eventBus.$off('getCameraDevice').$on('getCameraDevice', (jsonStr) => {

                let json = jsonStr.replace(/\\/g, '/')
                const datas = JSON.parse(json).camera
                that.$store.commit('device/updateCameraDeviceListByApp', datas)
            })
            // 保存message事件处理器引用
            this.messageHandler = (event) => {
                var option = {};
                try{
                    option = JSON.parse( event.data);
                } catch (e) {
                    option = {};
                }

                if ("iworks_statistics_loaded" == option.message) {
                    this.$root.eventBus.$emit('iworks_statistics_loaded')
                }
            }
            window.addEventListener('message', this.messageHandler, false);
            this.$root.eventBus.$off('NotifySwitchNativeRtcSettingStatus').$on('NotifySwitchNativeRtcSettingStatus',that.NotifySwitchNativeRtcSettingStatus);
            this.$root.eventBus.$off('leaveSilence').$on('leaveSilence',that.leaveSilence);
            this.$root.eventBus.$off('openConversationFromIndexByUserId').$on('openConversationFromIndexByUserId',that.openConversationFromIndexByUserId);
            this.$root.eventBus.$off('updateLiveCount').$on('updateLiveCount',this.debounceUpdateLiveCount);
            this.$root.eventBus.$off('addExamToAnalyze').$on('addExamToAnalyze',that.addExamToAnalyze);
            this.$root.eventBus.$off('clearAndDirectToLogin').$on('clearAndDirectToLogin',that.clearAndDirectToLogin);
            this.$root.eventBus.$off('unBindControllerEvent').$on('unBindControllerEvent',that.unBindControllerEvent)
            // 保存beforeunload事件处理器引用
            this.beforeunloadHandler = function(event) {
                destroyAllConference()
            }
            window.addEventListener('beforeunload', this.beforeunloadHandler);
        })
    },
    destroyed() {
        // 清理所有eventBus事件监听器
        this.$root.eventBus.$off('NotifyGetDeviceID');
        this.$root.eventBus.$off('NotifyShowConfirmDialog');
        this.$root.eventBus.$off('notifyStartupOption');
        this.$root.eventBus.$off('updateProgressOSS');
        this.$root.eventBus.$off('initMachineTransfer');
        this.$root.eventBus.$off('updateMachineTransfer');
        this.$root.eventBus.$off('finishMachineTransfer');
        this.$root.eventBus.$off('notifyNewExamImages');
        this.$root.eventBus.$off('DealNotifySendFileToConversation');
        this.$root.eventBus.$off('UpdateSendFileToConversation');
        this.$root.eventBus.$off('getCameraDevice');
        this.$root.eventBus.$off('NotifySwitchNativeRtcSettingStatus');
        this.$root.eventBus.$off('leaveSilence');
        this.$root.eventBus.$off('openConversationFromIndexByUserId');
        this.$root.eventBus.$off('updateLiveCount');
        this.$root.eventBus.$off('addExamToAnalyze');
        this.$root.eventBus.$off('clearAndDirectToLogin');
        this.$root.eventBus.$off('unBindControllerEvent');

        // 清理DOM事件监听器
        if (this.keyupHandler) {
            document.removeEventListener('keyup', this.keyupHandler);
            this.keyupHandler = null;
        }
        if (this.messageHandler) {
            window.removeEventListener('message', this.messageHandler, false);
            this.messageHandler = null;
        }
        if (this.beforeunloadHandler) {
            window.removeEventListener('beforeunload', this.beforeunloadHandler);
            this.beforeunloadHandler = null;
        }

        // 清理管理器资源
        if (this.eventListenerManager) {
            this.eventListenerManager.destroy()
        }
        if (this.autoLoginManager) {
            this.autoLoginManager.destroy()
        }
        if (this.mainScreenManager) {
            this.mainScreenManager.destroy()
        }
        if (this.conversationManager) {
            this.conversationManager.destroy()
        }
    },
    methods: {
        NotifyShowConfirmDialog(json){
            console.log("-----------------NotifyConfirmDialogResult--------------------------", json);
            if(json && "live_audio_exception" == json.des){
                if(1 == json.no){//选择退出会诊
                    var realtimeVideo=this.$store.state.realtimeVideo||{}
                    if(2 == realtimeVideo.real_time_video_state){
                        this.$root.eventBus.$emit('StopConference')
                    }
                    if(this.systemConfig.gallery_image_mode.RealTimeVideo == realtimeVideo.gallery_image_mode){
                        this.$root.eventBus.$emit('closeGallery')
                    }
                }
            }
        },
        clearLiveStatus(){
            this.$store.commit('liveConference/clearLiveConferenceStatus');
            this.showLivingNotifyDialog = false
        },
        handleDialogClose(){
            this.$set(this,'livingGroupInfo',{})
        },
        // 检查用户的职业身份设置
        checkProfessionalIdentity(userInfo) {
            // 根据functionsStatus.professionalIdentityForce来判断是否需要强制设置职业身份
            if (!this.functionsStatus.professionalIdentityForce) {
                return;
            }

            // 检查用户信息中是否包含professional_identity字段并且有值
            if (!userInfo.professional_identity) {
                // 没有设置职业身份，显示弹窗
                this.showProfessionalIdentityDialog = true;
            }
        },

        // 职业身份设置成功回调
        handleProfessionalIdentitySuccess() {
            this.showProfessionalIdentityDialog = false;
        },
        initPage(){
            //自动下载
            var auto_download = localStorage.getItem('auto_download');
            if(auto_download) {
                this.$store.commit('globalParams/updateGlobalAutoDownload', JSON.parse(auto_download));
            }

            //自动推流
            var auto_push_stream = localStorage.getItem('auto_push_stream_' + this.user.id);
            if(auto_push_stream) {
                this.$store.commit('globalParams/updateGlobalAutoPushStream', JSON.parse(auto_push_stream));
            }

            //推流参数初始化
            var catch_option = localStorage.getItem('catch_option');
            if (!catch_option) {
                catch_option = JSON.stringify({image_mode:1});
                localStorage.setItem('catch_option', catch_option);
            }
            catch_option = JSON.parse(catch_option);
            window.catch_option = catch_option;
            window.CWorkstationCommunicationMng.setCatchOption(catch_option);
            //初始化MainScreen
            const controller = this.mainScreenManager.initMainScreen()
            //初始化事件监听器
            this.eventListenerManager.initMainScreenControllerEvent(controller)
            // if(this.user.enable_monitor_wall && this.user.is_into_tv_wall == 1 && Tool.ifAppConsultationClientType(window.clientType)){
            //     //客户端下，电视墙可用且设置自动进入电视墙，用户角色在主任以上
            //     if (this.user.role>1) {
            //         this.$root.eventBus.$emit('enterTVmode')
            //     }
            // }
        },
        initNetworkData(){
            this.$store.commit('loadingConfig/updateLoaded',{
                key:'networkUnavailable',
                loaded:false
            });
            // this.$store.commit('conversationList/clearConversation');
            window.main_screen.initGateway(this.$store.state.user.client_uuid);
            var controller = window.main_screen.controller;
            controller.init(this);
            this.eventListenerManager.initMainScreenControllerEvent(window.main_screen.controller);

        },
        openConversationFromIndexByUserId(id,callback){
            this.openConversationByUserId(id,callback)
        },

        updateChatMessage(messageList,cid){
            for(let message of messageList){
                parseImageListToLocal([message],'url')
                this.$store.commit('conversationList/updateChatMessage',message)
                this.$store.commit('conversationList/updateConversationImage',message)
                this.$store.commit('consultationImageList/updateConsultationImage',message)
                this.$root.eventBus.$emit('updateExamImageListIfNeed',message)
            }
        },
        socketConnectSuccess(){
            this.autoLoginManager.onSocketConnectSuccess()
            this.$store.commit('loadingConfig/updateLoaded',{
                key:'networkUnavailable',
                loaded:false
            });
        },

        notifyException(data) {
            this.$MessageBox.alert(this.lang.exception_to_login_again,
                "",
                {
                    confirmButtonText:this.lang.confirm_button_text,
                    type:'warning'
                }
            ).then(()=>{
                this.autoLoginManager.onNotifyException();
            }).catch(()=>{

            })
        },
        unBindControllerEvent(){
            this.mainScreenManager.unBindControllerEvent()
        },
        resetApp(){
            try {
                destroyAllConference()
                if (Tool.ifAppWorkstationClientType(window.clientType)) {
                    //退出Istation
                    if (true == this.$store.state.globalParams.realtime_ultrasound_mode) {
                        this.$store.commit('globalParams/updateGlobalParams', {
                            realtime_ultrasound_mode:false
                        })
                        window.CWorkstationCommunicationMng.exitRealtimeUltrasoundMode({});
                    }
                }
                window.CWorkstationCommunicationMng.resetApp();
                window.CWorkstationCommunicationMng.CloseNewWindow();
            } catch (e) {
                console.log("[error] resetApp in index",e);
            }
        },
        clearAndDirectToLogin(msg, to_auto_login){
            window.localStorage.setItem('password','');
            window.localStorage.setItem('loginToken','');
            this.$store.commit('user/updateUser',{
                new_token:''
            });
            this.unBindControllerEvent()
            this.$router.replace(`/login`)
            if (window.main_screen&&window.main_screen.CScanRoom&&window.main_screen.CScanRoom.StorageConsultationFile) {
                //在本地实时页面由App弹框
                setTimeout(()=>{
                    window.CWorkstationCommunicationMng.notifyStopStorageConsultationFile({
                        error:1,
                        error_info:msg
                    });
                    // location.reload();
                    this.$root.eventBus.$emit('reloadRouter')
                },100)
            } else {
                if(msg){
                    setTimeout(()=>{
                        this.$MessageBox.alert(msg).then(()=>{
                            window.localStorage.setItem('loginToken','');
                            this.$root.eventBus.$emit('reloadRouter')
                        // location.reload();
                        })
                    },100)
                }
            }

            window.localStorage.removeItem('local_store_device_token');


        },
        startConversationWithStartupOption() {
            var that = this;
            if (window.g_extend_info) {
                var option = Tool.parseStartupOption(window.g_extend_info);
                if (option.cid) {
                    this.joinAndStartConversation(option.cid)
                }
            }
        },
        joinAndStartConversation(cid){
            console.log('joinAndStartConversation',cid)
            window.main_screen.controller.emit("group_contains_attendee", {
                group_id:cid
            }, (is_succ) => {
                if(is_succ){
                    setTimeout(() => {
                        this.openConversation(cid,2);
                    }, 1000);
                }else{
                    window.main_screen.applyJoinGroup({
                        mark:'',
                        gid:cid,
                        inviterID:0,
                        source:1,
                    },(res)=>{
                        if (res.error_code==0) {
                            setTimeout(() => {
                                this.openConversation(cid,2);
                            }, 1000);
                        }
                    })
                }
            });
        },
        NotifySwitchNativeRtcSettingStatus(json){
            if(!json.error_code){
                this.$store.commit('device/updateDeviceInfo',{isOpenRtcSettingDialog:json.data.status==='open'?true:false})
            }
        },
        leaveSilence(callback){
            window.main_screen.CMonitorWallPush.controller.emit('leaveSilence',(is_suc)=>{
                callback&&callback(is_suc)
            })
        },
        updateLiveCount(){
            this.eventListenerManager.updateLiveCount()
        }
    }
}
</script>

<style scoped lang="scss">
.main-page{
    width: 100%;
    height: 100%;
    background: #f0f2f5;
    display: flex;
    flex: auto;
    flex-direction: column;
    color: rgba(0, 0, 0, 0.88);
    min-height: 0;
    .network_unavailable{
        position: absolute;
        left: 50%;
        top: 100px;
        z-index: 9001;
        transform: translate(-50%);
        background: #f92d2d;
        color: #fff;
        padding: 8px 20px;
        border-radius: 10px;
        box-shadow: 4px 4px 4px rgba(0,0,0,.3);
    }
    .main-container{
        font-size: 14px;
        flex-direction: row;
        background: #f0f2f5;
        height: 100%;
        display: flex;
        overflow: hidden;
        .left-content{
            flex: 0 0 96px;
            max-width: 96px;
            width: 96px;
            height: 100%;
            display: flex;
            position: relative;
            min-width: 0;
            background: #DDE3F0;
            transition: all 0.2s, background 0s;
        }
        .right-content{
            flex: auto;
            min-height: 0;
            overflow: hidden;
            background-color: #fff;
            width: 0;
            position: relative;
        }
    }
}
</style>
