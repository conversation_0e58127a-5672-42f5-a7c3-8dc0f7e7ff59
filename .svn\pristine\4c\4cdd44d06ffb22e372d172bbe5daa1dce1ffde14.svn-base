<template>
    <div class="completed_exam_list" v-loading="loadingPage">
        <div class="search_container">
            <el-input
            :placeholder="lang.homework_search_key"
            prefix-icon="el-icon-search"
            @input="debounceSearch"
            v-model="searchKey"
            ></el-input>
        </div>
        <template v-if="examList.length">
            <div class="cloud_exam_item" v-for="exam of examList" :key="exam._id">
                <div class="cloud_exam_item_card">
                    <span v-if="exam.status === 3" class="unread"></span>
                    <p class="exam_title">{{exam.assignmentInfo.paperInfo.title}}</p>
                    <p class="exam_author">{{exam.assignmentInfo.paperInfo.author}}</p>
                    <!-- <div>{{ exam }}</div> -->
                    <div class="exam_detail" >
                        <div class="exam_type_icon">
                            <template v-if="exam.assignmentInfo.paperInfo.contentType">
                                <img class="title_icon" :src="`static/resource_pc/images/homework_type${exam.assignmentInfo.paperInfo.contentType}.png`">
                                <p>{{lang['homework_type'+exam.assignmentInfo.paperInfo.contentType]}}</p>
                            </template>
                            <template v-else>
                                <img class="title_icon" src="static/resource_pc/images/homework_type5.png">
                                <p>{{lang.homework_type5}}</p>
                            </template>
                            
                        </div>
                        <div class="exam_description">
                            <p v-if="exam.status === 3 || exam.status === 4">{{lang.paper_results}}：<span>{{exam.score}}{{lang.point_tip}}</span>/{{exam.assignmentInfo.paperInfo.score}}{{lang.point_tip}}</p>
                            <p v-else>{{lang.paper_results}}：<span>{{lang.correcting}}</span></p>
                            <p>{{lang.paper_duration}}：{{exam.useTime | useTime}}</p>
                            <p>{{lang.submission_time}}：{{exam.submitAt | showData}}</p>
                        </div>
                    </div>
                    <div class="operation_btns">
                        <el-button class="gradient_btn" size="small" :loading="isButtonDisabled" @click="openStatistics(exam)">{{lang.paper_statistics}}</el-button>
                        <el-button :disabled="!exam.assignmentInfo.resultCanChecked || (exam.status!==3 && exam.status!==4)" :loading="isButtonDisabled" class="primary_btn" size="small" @click="ExamDetail(exam._id)">{{lang.view_details}}</el-button>
                    </div>
                </div>
            </div>
        </template>
        <no-data v-else :text="lang.no_data_txt"></no-data>
        <el-pagination
          v-show="examList.length"
          background
          :current-page="queryForm.pageNo"
          :layout="layout"
          :page-size="queryForm.pageSize"
          :total="examListTotal"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          style="margin-top:20px"
        />
    </div>
</template>
<script>
import base from '../../../lib/base';
import {formatDurationTime} from '../../../lib/common_base';
import service from '../../../service/service.js'
import moment from 'moment';
import NoData from "../../../MRComponents/noData.vue";
import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    name: 'cloudExamList',
    components: {
        NoData,
    },
    data(){
        return {
            cid:0,
            layout: 'total, sizes, prev, pager, next, jumper',
            queryForm:{
                pageNo:1,
                pageSize:30,
            },
            examListTotal:20,
            examList:[],
            loadingPage:false,
            searchKey:'',
            debounceSearch:null,
            isButtonDisabled: false,
        }
    },
    computed:{
    },
    filters:{
        useTime(duration){
            return formatDurationTime(duration)
        },
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm")
        }
    },
    created(){
        this.cid = parseInt(this.$route.params.cid);
        this.debounceSearch = Tool.debounce(this.fetchData,600);
    },
    mounted(){
        this.$root.eventBus.$off("refreshCompletePaper").$on("refreshCompletePaper",this.fetchData);
        this.fetchData()
    },
    watch:{
        
    },
    methods:{
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.queryForm.pageNo = val
            this.fetchData()
        },
        fetchData(){
            this.loadingPage = true;
            service.getFinishedList({
                gid:this.cid,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                searchKey:this.searchKey,
            }).then(res=>{
                this.loadingPage = false;
                if (res.data.error_code === 0) {
                    this.examListTotal = res.data.data.total;
                    this.examList = res.data.data.data;
                    
                    // 检查是否有status=3的作业，如果有则更新全局状态
                    const status3Exams = this.examList.filter(item => item.status === 3);
                    const correctedCount = status3Exams.length;
                    
                    if (this.cid === 0) {
                        this.$store.commit("homework/updateHomework", {
                            globalCorrected: correctedCount > 0 ? correctedCount : 0
                        });
                    } else {
                        let obj = {};
                        if (correctedCount > 0) {
                            obj[this.cid] = status3Exams;
                        } else {
                            obj[this.cid] = undefined;
                        }
                        this.$store.commit("homework/updateCorrected", obj);
                    }
                }
            })
        },
        enterExam(exam){
            // this.$store.commit('homework/setCurrentAssignment',exam)
            this.$router.push(this.$route.fullPath+'/exam/2/'+exam._id)
        },
        ExamDetail(id){
            // 防止连续点击导致路由重复
            const targetPath = '/exam/4/' + id;
            if (this.$route.fullPath.includes(targetPath) || this.isButtonDisabled) {
                return;
            }
            this.isButtonDisabled = true;
            
            // 查找当前作业
            const exam = this.examList.find(item => item._id === id);
            if (exam && exam.status === 3) {
                // 手动更新本地状态
                exam.status = 4;
                if(this.cid === 0){
                    // 更新全局状态
                    const remainingCorrectedCount = this.examList.reduce((count, item) => count + (item.status === 3 ? 1 : 0), 0);
                    this.$store.commit("homework/updateHomework", {
                        globalCorrected: remainingCorrectedCount
                    });
                    //从conversationCorrected中删除当前作业
                    for (const conversationId in this.$store.state.homework.conversationCorrected) {
                        const conversationExams = this.$store.state.homework.conversationCorrected[conversationId];
                        if (conversationExams && Array.isArray(conversationExams)) {
                            // 检查当前会话是否包含此作业
                            const examIndex = conversationExams.findIndex(item => item._id === id);
                            if (examIndex !== -1) {
                                // 排除当前作业
                                const updatedExams = [...conversationExams];
                                updatedExams.splice(examIndex, 1);
                                
                                // 更新状态
                                let obj = {};
                                if (updatedExams.length > 0) {
                                    obj[conversationId] = updatedExams;
                                } else {
                                    obj[conversationId] = undefined;
                                }
                                this.$store.commit("homework/updateCorrected", obj);
                            }
                        }
                    }
                    
                }else if(this.cid !== 0){
                    // 更新会话状态
                    let obj = {};
                    const status3Exams = this.examList.filter(item => item.status === 3); //群里的未读已批改数量
                    if (status3Exams.length > 0) {
                        obj[this.cid] = status3Exams;
                    } else {
                        obj[this.cid] = undefined;
                    }
                    this.$store.commit("homework/updateCorrected", obj);
                    
                    // 将globalCorrected减1但不小于0
                    const currentGlobalCorrected = this.$store.state.homework.globalCorrected;
                    if (currentGlobalCorrected !== undefined && currentGlobalCorrected > 0) {
                        this.$store.commit("homework/updateHomework", {
                            globalCorrected: currentGlobalCorrected - 1
                        });
                    }
                }
            }
            this.$router.push(this.$route.fullPath+'/exam/4/'+id);
            
            setTimeout(() => {
                this.isButtonDisabled = false;
            }, 1000);
        },
        openStatistics(exam){
            // 防止连续点击导致路由重复
            const targetPath = '/exam_statistics/' + exam.assignmentID;
            if (this.$route.fullPath.includes(targetPath) || this.isButtonDisabled) {
                return;
            }
            this.isButtonDisabled = true;
            
            this.$store.commit('homework/setCurrentPaper',exam.assignmentInfo);
            this.$router.push(this.$route.fullPath+'/exam_statistics/'+exam.assignmentID);
            
            setTimeout(() => {
                this.isButtonDisabled = false;
            }, 1000);
        },
    },
    destroyed(){
    },

}

</script>
<style lang="scss">
.completed_exam_list{

}
</style>
