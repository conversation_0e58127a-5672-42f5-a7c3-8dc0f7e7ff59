<template>
    <div :name="transition" v-show="show">
        <template v-if="visible">
            <div :class="className" :style="dropDownstyle" :data-popover="this.name" @click.stop ref="dropdown">
                <slot />
            </div>
            <span ref="dropdown-pop" :class="[positionClass, 'dropdown-pop']" :style="dropDownPopstyle"></span>
        </template>
    </div>
</template>

<script>
import { subscription } from "./subscription";
import { getFixedPositionParents, getLayer, getMaxZIndex } from "./utils";

const pointerSize = 6;

const anchors = {
    leftTop: "",
    leftCenter: "",
    leftBottom: "",
    topLeft: "",
    topCenter: "",
    topRight: "",
    rightTop: "",
    rightCenter: "",
    rightBottom: "",
    bottomLeft: "",
    bottomCenter: "",
    bottomRight: "",
};

const directions = {
    left: [-1, 0],
    right: [1, 0],
    top: [0, 1],
    bottom: [0, -1],
};

export default {
    name: "Popover",
    props: {
        name: {
            type: String,
            required: true,
        },
        show: {
            type: Boolean,
            default: false,
        },
        delay: {
            type: Number,
            default: 0,
        },
        transition: {
            type: String,
        },
        pointer: {
            type: Boolean,
            default: true,
        },
        event: {
            type: String,
            default: "click",
        },
        anchor: {
            type: Number,
            default: 0.5,
            validator: (v) => v >= 0 && v <= 1,
        },
    },
    data() {
        return {
            zIndex: 1,
            positionClass: "",
            fixedParents: [],
            position: {
                left: 0,
                top: 0,
            },
            popPosition: {
                left: 0,
                top: 0,
            },
            chat_direction: "left",
            visible:false
        };
    },
    mounted() {
        subscription.$on(this.showEventName, this.showEventListener);
        subscription.$on(this.hideEventName, this.hideEventListener);
    },
    beforeDestroy() {
        subscription.$off(this.showEventName, this.showEventListener);
        subscription.$off(this.hideEventName, this.hideEventListener);
    },
    computed: {
        showEventName() {
            return `show:${this.event}`;
        },
        hideEventName() {
            return `hide:${this.event}`;
        },
        className() {
            return ["vue-popover", `chat-${this.chat_direction}`];
        },
        dropDownstyle() {
            const { zIndex, fixedParents } = this;
            const hasFixedParents = fixedParents.length > 0;

            const styles = {
                zIndex,
                ...this.position,
            };

            if (hasFixedParents) {
                styles.position = "fixed";
            }

            return styles;
        },
        dropDownPopstyle() {
            const { zIndex, fixedParents } = this;
            const hasFixedParents = fixedParents.length > 0;

            const styles = {
                zIndex,
                ...this.popPosition,
            };

            if (hasFixedParents) {
                styles.position = "fixed";
            }

            return styles;
        },
    },
    methods: {
        showEventListener(event) {
            if (this.visible) {
                subscription.$emit(this.hideEventName);
                return;
            }

            let { name, position, chatDirection } = event;
            this.chat_direction = chatDirection;
            if (name !== this.name) {
                return;
            }

            this.timeout = setTimeout(() => {
                this.visible = true;

                this.$nextTick(() => {
                    // this.$emit('show', event)

                    this.$nextTick(() => {
                        const dropdown = this.$refs.dropdown;
                        const ddRect = dropdown.getBoundingClientRect();
                        let position = this.getDropdownPosition(event);
                        if (position.top < 20) {
                            event.position = "bottom";
                            position = this.getDropdownPosition(event);
                        }

                        this.positionClass = `dropdown-position-${event.position}`;
                        if (chatDirection === "left") {
                            //左侧激活的聊天
                            if (position.left < 20) {
                                position.left = 20;
                            }
                            this.position = {
                                left: `${position.left}px`,
                                top: `${position.top}px`,
                            };
                        } else {
                            if (window.innerWidth - ddRect.width - position.left < 20) {
                                this.position = {
                                    right: `${20}px`,
                                    top: `${position.top}px`,
                                };
                            } else {
                                if (position.left < 20) {
                                    position.left = 20;
                                }
                                this.position = {
                                    left: `${position.left}px`,
                                    top: `${position.top}px`,
                                };
                            }
                        }
                        this.$nextTick(() => {
                            let popPosition = this.getDropdownPopPosition(event);
                            if (event.position === "bottom") {
                                this.popPosition = {
                                    left: `${popPosition.left}px`,
                                    top: `${position.top - pointerSize}px`,
                                };
                            } else {
                                this.popPosition = {
                                    left: `${popPosition.left}px`,
                                    top: `${position.top + ddRect.height}px`,
                                };
                            }
                        });
                    });
                });
            }, Math.max(this.delay, 0));
        },

        hideEventListener(event) {
            if (this.timeout) {
                clearTimeout(this.timeout);
            }

            if (this.visible) {
                this.visible = false;
                this.$emit("hide", event);
            }
        },

        getDropdownPosition(event) {
            const { target, position } = event;
            const direction = directions[position];
            const dropdown = this.$refs.dropdown;

            const trRect = target.getBoundingClientRect();
            const ddRect = dropdown.getBoundingClientRect();

            this.fixedParents = getFixedPositionParents(target);

            const zIndex = event.zIndex ? event.zIndex : getMaxZIndex([target, ...this.fixedParents]) + 1;

            this.zIndex = zIndex;

            let offsetLeft = trRect.left;
            let offsetTop = trRect.top;

            if (this.fixedParents.length === 0) {
                // Scroll offset of the current document
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;

                const scrollLeft =
                    window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft;

                // Position within the parent
                offsetLeft = trRect.left + scrollLeft;
                offsetTop = trRect.top + scrollTop;
            }

            // let shiftX = ddRect.width - trRect.width
            let shiftY = 0.5 * (ddRect.height + trRect.height);
            // Center of the target element
            let centerX = offsetLeft - 0.5 * (ddRect.width - trRect.width);
            let centerY = offsetTop + trRect.height - shiftY;

            // let anchorX = direction[0] * this.anchor
            // let anchorY = direction[0] * this.anchor

            // Position of the dropdown relatively to target
            let x = direction[0] * 0.5 * (ddRect.width + trRect.width);
            let y = direction[1] * shiftY;

            // Pointer size correction
            if (this.pointer) {
                x += direction[0] * pointerSize;
                y += direction[1] * pointerSize;
            }
            return {
                left: Math.round(centerX + x),
                top: Math.round(centerY - y),
            };
        },
        getDropdownPopPosition(event) {
            const { target, position } = event;
            const direction = directions[position];
            const dropdownPop = this.$refs["dropdown-pop"];

            const trRect = target.getBoundingClientRect();
            const ddRect = dropdownPop.getBoundingClientRect();

            this.fixedParents = getFixedPositionParents(target);

            const zIndex = event.zIndex ? event.zIndex : getMaxZIndex([target, ...this.fixedParents]) + 1;

            this.zIndex = zIndex;

            let offsetLeft = trRect.left;
            let offsetTop = trRect.top;

            if (this.fixedParents.length === 0) {
                // Scroll offset of the current document
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop;

                const scrollLeft =
                    window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft;

                // Position within the parent
                offsetLeft = trRect.left + scrollLeft;
                offsetTop = trRect.top + scrollTop;
            }

            // let shiftX = ddRect.width - trRect.width
            let shiftY = 0.5 * (ddRect.height + trRect.height);
            // Center of the target element
            let centerX = offsetLeft - 0.5 * (ddRect.width - trRect.width);
            let centerY = offsetTop + trRect.height - shiftY;
            // let anchorX = direction[0] * this.anchor
            // let anchorY = direction[0] * this.anchor

            // Position of the dropdown relatively to target
            let x = direction[0] * 0.5 * (ddRect.width + trRect.width);
            let y = direction[1] * shiftY;

            // Pointer size correction
            if (this.pointer) {
                x += direction[0] * pointerSize;
                y += direction[1] * pointerSize;
            }
            if (event.position === "bottom") {
                return {
                    left: Math.round(centerX + x),
                    top: Math.round(centerY - y - 50),
                };
            } else {
                return {
                    left: Math.round(centerX + x),
                    top: Math.round(centerY - y - 38),
                };
            }
        },
    },
};
</script>

<style lang="scss">
$pointer-size: 6px;

.vue-popover {
    display: block;
    position: fixed;
    // background: #000;
    color: #fff;
    // box-shadow: 0px 4px 20px 0px rgba(52, 73, 94, 0.2);

    // padding: 5px;
    border-radius: 5px;

    z-index: 998;

    // &:before {
    //   display: block;
    //   position: absolute;
    //   width: 0;
    //   height: 0;
    //   content: '';
    // }
}
.dropdown-pop {
    display: block;
    position: fixed;
}
.dropdown-position-bottom {
    border-left: $pointer-size solid transparent;
    border-right: $pointer-size solid transparent;
    border-bottom: $pointer-size solid #000;
    // top: -$pointer-size;
    // left: calc(50% - #{$pointer-size});
    // filter: drop-shadow(0px -2px 2px rgba(52, 73, 94, 0.1));
}

.dropdown-position-top {
    border-left: $pointer-size solid transparent;
    border-right: $pointer-size solid transparent;
    border-top: $pointer-size solid #000;
    // bottom: -$pointer-size;
    // left: calc(50% - #{$pointer-size});
    // filter: drop-shadow(0px 2px 2px rgba(52, 73, 94, 0.1));
}
// &.chat-left:before{
//   left: 76px;
// }
// &.chat-right:before{
//   right: 76px;
// }
.dropdown-position-left {
    border-top: $pointer-size solid transparent;
    border-bottom: $pointer-size solid transparent;
    border-left: $pointer-size solid #000;
    // right: -$pointer-size;
    // top: calc(50% - #{$pointer-size});
    // filter: drop-shadow(2px 0px 2px rgba(52, 73, 94, 0.1));
}

.dropdown-position-right {
    border-top: $pointer-size solid transparent;
    border-bottom: $pointer-size solid transparent;
    border-right: $pointer-size solid #000;
    // left: -$pointer-size;
    // top: calc(50% - #{$pointer-size});
    // filter: drop-shadow(-2px 0px 2px rgba(52, 73, 94, 0.1));
}
</style>
