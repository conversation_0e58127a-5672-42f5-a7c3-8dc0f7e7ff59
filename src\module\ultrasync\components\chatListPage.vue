<template>
    <div class="chat_page">
        <div class="network_unavailable" v-if="loadingConfig.networkUnavailable">
            <i class="icon iconfont icon-warning-o"></i>
            {{ lang.network_unavailable }}
        </div>
        <div class="search_btn" @click="icloudSearch">
            <i class="iconfont svg_icon_search icon-magnifier"></i>
            <span>{{ lang.search }}</span>
        </div>
        <!-- <div v-if="!loadingConfig.loadedChatList" class="full_loading_spinner van_loading_spinner">
            <van-loading color="#00c59d" />
        </div> -->
        <div class="list_container">
            <recycle-scroller
                class="chatlist_page"
                :buffer="6000"
                :prerender="100"
                :item-size="itemSize"
                key-field="cid"
                :items="chatList"
                @scroll-start="scrollToTop"
                ref="chatListScroller"
            >
                <template #before>
                    <div @click="openAiChat" class="chat_list_item" v-if="functionsStatus.ai">
                        <img src="static/resource/images/ai_logo_small_6.png" />
                        <div class="chat_item_right">
                            <div class="chat_item_title">
                                <p class="subject">{{lang.MICO_AI_TITLE}}</p>
                            </div>
                        </div>
                    </div>
                    <div @click="openLive" class="chat_list_item" v-if="functionsStatus.live">
                        <img src="static/resource/images/live.png" />
                        <span class="item_unread font_middle" v-if="liveCount">
                            <i class="iconfont svg_ellipsis icon-shenglve" v-if="liveCount > 99"></i>
                            <template v-else>
                                {{ liveCount }}
                            </template>
                        </span>
                        <div class="chat_item_right">
                            <div class="chat_item_title">
                                <p class="subject">{{ lang.live }}</p>
                            </div>
                        </div>
                    </div>
                    <div
                        v-if="
                            !loadingConfig.networkUnavailable &&
                            (ultrasoundMachine.isConnecting || deviceCtrlList.length > 0)
                        "
                        @click="openMachine"
                        class="chat_list_item"
                    >
                        <img src="static/resource/images/new_logo.png" />
                        <span v-show="ultrasoundMachine.unread > 0" class="item_unread font_middle">
                            <i class="iconfont svg_ellipsis icon-shenglve" v-if="ultrasoundMachine.unread > 99"></i>
                            <template v-else>
                                {{ ultrasoundMachine.unread }}
                            </template>
                        </span>
                        <div class="chat_item_right">
                            <div class="chat_item_title">
                                <p class="subject">{{ lang.authorized_devices }}</p>
                            </div>
                            <div>
                                <p class="content">
                                    {{ ultrasoundMachine.ultrasound_machine_name || deviceCtrlList[0].name ||deviceCtrlList[0].device_id }}
                                </p>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-slot="{ item }">
                    <v-touch class="chat_list_item" :key="'chatItem_' + item.cid" @click.native.stop="clickChatItem(item)">
                        <mr-avatar
                            :url="getLocalAvatar(item)"
                            :origin_url="item.avatar"
                            :radius="2.6"
                            :onlineState="item.state"
                            :showOnlineState="checkIsShowOnlineStatus(item)"
                            :key="item.avatar"
                            :is_public="item.is_public"
                        ></mr-avatar>
                        <span
                            v-if="item.is_mute && unreadMap[item.cid] && unreadMap[item.cid] > 0"
                            class="item_mute"
                        ></span>
                        <span
                            v-else-if="unreadMap[item.cid] && unreadMap[item.cid] > 0"
                            class="item_unread font_middle"
                        >
                            <i
                                class="iconfont svg_ellipsis icon-shenglve"
                                v-if="unreadMap[item.cid] && unreadMap[item.cid] > 99"
                            ></i>
                            <template v-else-if="unreadMap[item.cid]">
                                {{ unreadMap[item.cid] }}
                            </template>
                        </span>
                        <span v-if="item.examConsultationUnread.total > 0" class="item_exam_unread"></span>
                        <div class="chat_item_right">
                            <div class="chat_item_title">
                                <p class="subject">
                                    <template v-if="!item.service_type">
                                        <template v-if="checkIsSingleChat(item)">{{
                                            remarkMap[item.fid] || item.nickname || item.subject
                                        }}</template>
                                        <template v-else>{{ item.subject }}</template>
                                    </template>
                                    <template
                                        v-else-if="item.service_type == systemConfig.ServiceConfig.type.AiAnalyze"
                                        >{{ lang.ai_analyze }}</template
                                    >
                                    <template
                                        v-else-if="
                                            item.service_type == systemConfig.ServiceConfig.type.FileTransferAssistant
                                        "
                                        >{{ lang.file_transfer_assistant }}</template
                                    >
                                    <template
                                        v-else-if="item.service_type == systemConfig.ServiceConfig.type.DrAiAnalyze">{{ lang.dr_ai_analyze }}
                                    </template>
                                </p>
                                <div class="operate_panel clearfix" @click.stop>
                                    <i
                                        class="iconfont svg_camera icon-camera"
                                        v-if="
                                            functionsStatus.live &&
                                            liveConference[item.cid] &&
                                            liveConference[item.cid].conferenceState
                                        "
                                    ></i>
                                    <span class="send_ts">{{ lastMessage[item.cid].showTime }}</span>
                                </div>
                            </div>
                            <div class="chat_item_right_text">
                                <!-- <i class="iconfont icon-video fr" ></i>
                                    <i class="iconfont icon-phone- fr" ></i> -->

                                <p v-if="item.mentionStatus" class="fl mention_tip font_light">
                                    {{ lang.someone_mention }}
                                </p>
                                <p v-if="item.is_mute && unreadMap[item.cid] > 0" class="fl mute_number font_light">
                                    [{{ unreadMap[item.cid] }}{{ lang.mute_message_number }}]
                                </p>
                                <p
                                    class="fl attendee_name font_light"
                                    v-show="showAttendeeName(item)"
                                >
                                    {{ remarkMap[lastMessage[item.cid].sender_id] || lastMessage[item.cid].nickname }}：
                                </p>
                                <p
                                    class="content font_light"
                                    v-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.Text"
                                    v-html="lastMessage[item.cid].msg_body"
                                ></p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.EXPIRATION_RES"
                                >
                                    {{ lang.ref_res_expired }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.Image"
                                >
                                    {{ lang.msg_type_image }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.Sound"
                                >
                                    {{ lang.msg_type_sound }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.File"
                                >
                                    {{ lang.msg_type_file }}{{ lastMessage[item.cid].file_name }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.IWORKS_PROTOCOL"
                                >
                                    {{ lang.msg_type_iworks_protocol }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.Video"
                                >
                                    {{ lang.msg_type_video }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.Frame ||
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.OBAI ||
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.Cine
                                    "
                                >
                                    {{ lang.msg_type_consultation_file }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.RealTimeVideoReview
                                    "
                                >
                                    {{ lang.realtime_review_msg }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.VIDEO_CLIP"
                                >
                                    {{ lang.video_clip_msg }} {{ formatTime(lastMessage[item.cid].start_ts) }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.SYS_START_RT_VOICE
                                    "
                                >
                                    {{ lang.request_voice_chat }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.SYS_STOP_RT_VOICE
                                    "
                                >
                                    {{ lang.close_voice_chat }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type ==
                                        systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION
                                    "
                                >
                                    {{ lang.request_realtime_video }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type ==
                                        systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION
                                    "
                                >
                                    {{ lang.close_realtime_video }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.SYS_JOIN_ATTENDEE
                                    "
                                >
                                    {{
                                        lastMessage[item.cid].attendee_changed_info &&
                                        lastMessage[item.cid].attendee_changed_info.nickname
                                    }}
                                    {{ lang.join_group_tip }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.SYS_KICKOUT_ATTENDEE
                                    "
                                >
                                    {{
                                        lastMessage[item.cid].attendee_changed_info &&
                                        lastMessage[item.cid].attendee_changed_info.nickname
                                    }}
                                    {{ lang.exit_group_tip }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.COMMENT"
                                >
                                    {{ lang.add_comment_msg_text }}：{{ lastMessage[item.cid].comment }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.TAG &&
                                        lastMessage[item.cid].action == 1
                                    "
                                >
                                    {{ lang.add_tag_msg_text }}：{{ lastMessage[item.cid].tags }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.TAG &&
                                        lastMessage[item.cid].action == 2
                                    "
                                >
                                    {{ lang.delete_tag_msg_text }}：{{ lastMessage[item.cid].tags }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="
                                        lastMessage[item.cid].msg_type == systemConfig.msg_type.SYS_CONFERENCE_PLAN
                                    "
                                >
                                    <template
                                        v-if="
                                            lastMessage[item.cid].tip_type == systemConfig.conference_plan_tip_type.New
                                        "
                                    >
                                        {{ lang.reserved_conference_tip
                                        }}{{
                                            lastMessage[item.cid].conference_plan &&
                                            lastMessage[item.cid].conference_plan.subject
                                        }}
                                    </template>
                                    <template
                                        v-if="
                                            lastMessage[item.cid].tip_type ==
                                            systemConfig.conference_plan_tip_type.Prepare
                                        "
                                    >
                                        {{ lang.conference_begin_tip
                                        }}{{
                                            lastMessage[item.cid].conference_plan &&
                                            lastMessage[item.cid].conference_plan.subject
                                        }}
                                    </template>
                                    <template
                                        v-if="
                                            lastMessage[item.cid].tip_type ==
                                            systemConfig.conference_plan_tip_type.Cancel
                                        "
                                    >
                                        {{ lang.delete_conference_tip
                                        }}{{
                                            lastMessage[item.cid].conference_plan &&
                                            lastMessage[item.cid].conference_plan.subject
                                        }}
                                    </template>
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.AI_ANALYZE"
                                >
                                    {{ lang.analyze_result_tip }}...
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.IWORKS_SCORE"
                                >
                                    {{ lang.iworks_score_label }}...
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.WITHDRAW"
                                >
                                    <template v-if="lastMessage[item.cid].sender_id === user.uid">{{
                                        lang.revocation_message_by_self
                                    }}</template>
                                    <template v-else
                                        ><span>{{ lastMessage[item.cid].nickname }}</span
                                        >{{ lang.revocation_message_by_other }}</template
                                    >
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.ResourceDelete"
                                >
                                    <template v-if="lastMessage[item.cid].sender_id === user.uid">{{
                                        lang.delete_message_by_self
                                    }}</template>
                                    <template v-else
                                        ><span>{{ lastMessage[item.cid].nickname }}</span
                                        >{{ lang.rdelete_message_by_other }}</template
                                    >
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.LIVE_INVITE"
                                >
                                    <template>{{ getLiveInviteStatusStr(lastMessage[item.cid]) }}</template>
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.EXAM_IMAGES"
                                >
                                    {{ lang.exam_images_title }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.MULTICENTER_REJECT"
                                >
                                    {{ lang.multicenter_reject_tip }}
                                </p>
                                <p
                                    class="content font_light"
                                    v-else-if="lastMessage[item.cid].msg_type == systemConfig.msg_type.HOMEWORK_DETAIL || lastMessage[item.cid].msg_type == systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL"
                                >
                                    [{{ lang.cloud_exam }}]
                                </p>
                                <p class="content font_light" v-else-if="lastMessage[item.cid].msg_type">
                                    {{ lang.unsupport_msg_type }}：{{ lastMessage[item.cid].msg_type }}
                                </p>
                                <p v-else></p>
                            </div>
                        </div>
                    </v-touch>
                </template>
            </recycle-scroller>
        </div>
        <!-- <groupset-exam-view v-show="isGroupset&&currentGroupset.view_mode==1"></groupset-exam-view> -->
    </div>
</template>
<script>
import base from "../lib/base";
import { Toast, Loading } from 'vant';
import { getLocalAvatar } from "../lib/common_base";
import Tool from "../../../common/tool";
export default {
    mixins: [base],
    components: {
        // VanLoading: Loading
    },
    data() {
        return {
            getLocalAvatar,
            loadingConfig: this.$store.state.loadingConfig,
            ultrasoundMachine: this.$store.state.ultrasoundMachine,
            itemSize: 0,
            testList: false,
            // currentGroupset:null
            windowResizeHandler: null,
        };
    },
    computed: {
        // currentGroupset(){
        //     return this.$store.state.chatList.currentGroupset
        // },
        deviceCtrlList() {
            return this.parseObjToArr(this.$store.state.deviceCtrl);
        },
        // resourceUnreview(){
        //     return this.$store.state.resourceUnreview
        // },
        chatList() {
            let chatList = [];
            this.$store.state.chatList.list.forEach((item) => {
                if (item.service_type !== this.systemConfig.ServiceConfig.type.LiveBroadCast) {
                    //
                    chatList.push(item);
                }
            });
            return chatList;
        },
        lastMessage() {
            return this.$store.state.chatList.lastMessage;
        },
        isShowMachineTransfer() {
            return true;
        },
        liveCount() {
            if (Object.keys(this.$store.state.notifications.liveCount).length > 0) {
                return (
                    this.$store.state.notifications.liveCount.startCount +
                    this.$store.state.notifications.liveCount.waittingCount
                );
            } else {
                return 0;
            }
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        liveConference() {
            return this.$store.state.liveConference;
        },
        unreadMap() {
            return this.$store.state.chatList.unreadMap;
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.itemSize = Tool.transferRemToHeight(3.5) || 75;
            this.windowResizeHandler = Tool.debounce(this.handleResize, 1000);  //等待1秒后执行
            window.addEventListener('resize', this.windowResizeHandler);
        });
    },
    beforeDestroy() {
        if (this.windowResizeHandler) {
            window.removeEventListener('resize', this.windowResizeHandler);
        }
    },
    methods: {
        handleResize() {
            this.itemSize = Tool.transferRemToHeight(3.5) || 75;

            this.$nextTick(() => {
                // vue-virtual-scroller的RecycleScroller组件有forceUpdate和updateSizes方法
                if (this.$refs.chatListScroller) {
                    if (typeof this.$refs.chatListScroller.forceUpdate === 'function') {
                        this.$refs.chatListScroller.forceUpdate();
                    }

                    if (typeof this.$refs.chatListScroller.updateSizes === 'function') {
                        this.$refs.chatListScroller.updateSizes();
                    }

                    // 如果上面两个方法都不存在，尝试通用的刷新方法
                    if (typeof this.$refs.chatListScroller.resize === 'function') {
                        this.$refs.chatListScroller.resize();
                    } else if (typeof this.$refs.chatListScroller.refresh === 'function') {
                        this.$refs.chatListScroller.refresh();
                    }
                }
            });

            console.log('Window resize detected, updating chatlist layout');
        },
        openMachine() {
            window.vm.$store.commit("ultrasoundMachine/updateMachine", {
                unread: 0,
            });
            this.$router.push(`/index/ultrasound_machine`);
        },
        clickChatItem(chatItem) {
            if(Tool.checkMainScreenConnected()){
                this.openConversation(chatItem.cid, 1);
            }else{
                Toast(this.lang.network_error_tip)
            }

            // this.$router.push(`/index/chat_window/${chatItem.cid}`)
        },
        icloudSearch() {
            this.$router.push("index/icloud_search");
        },
        async openLive() {
            Tool.loadModuleRouter("/index/live_management");
        },
        async openAiChat() {
            Tool.loadModuleRouter("/index/ai_main/ai_chat");
        },
        getLiveInviteStatusStr(msg) {
            return this.lang[`live_invite_status${msg.liveInfo.status}`].replace("{1}", msg.liveInfo.creator_name);
        },
        scrollToTop() {
            console.log("scrollToTop", 111);
        },
        checkIsSingleChat(item){
            const Single=this.systemConfig.ConversationConfig.type.Single
            return item.type===Single
        },
        checkIsShowOnlineStatus(item){
            if(this.checkIsSingleChat(item)&&item.service_type===0){
                return true
            }
            return false
        },
        showAttendeeName(chatItem){
            return !this.checkIsSingleChat(chatItem)&&
            this.lastMessage[chatItem.cid].msg_type!=undefined
            &&this.lastMessage[chatItem.cid].msg_type!=this.systemConfig.msg_type.SYS_JOIN_ATTENDEE
            &&this.lastMessage[chatItem.cid].msg_type!=this.systemConfig.msg_type.SYS_KICKOUT_ATTENDEE
            &&this.lastMessage[chatItem.cid].msg_type!=this.systemConfig.msg_type.WITHDRAW
            &&this.lastMessage[chatItem.cid].msg_type!=this.systemConfig.msg_type.ResourceDelete
        }
    },
};
</script>
<style lang="scss">
.content a {
    color: #b3b3b3 !important;
    text-decoration: none !important;
    cursor: default !important;
}
.chat_page {
    height: 100%;
    background: #fff;
    position: relative;
    display: flex;
    flex-direction: column;
    transform: translate3d(0, 0, 0);
    .search_btn {
        height: 1.7rem;
        line-height: 1.7rem;
        text-align: center;
        width: 100%;
        background-color: rgba(0, 197, 157, 0.16);
        color: rgb(179, 179, 179);
        font-size: 0.7rem;
        span,
        i {
            vertical-align: middle;
        }
        .svg_icon_search {
            width: 0.7rem;
            height: 0.7rem;
            top: 1rem;
            fill: rgb(0, 197, 157);
            color: rgb(0, 197, 157);
            margin-right: 0.1rem;
        }
    }

    .van_loading_spinner{
        display: flex;
        justify-content: center;
        align-items: center;

        .van-loading__spinner{
            width: 2.8rem;
            height: 2.8rem;
        }
    }

    .list_container {
        flex: 1;
        overflow: auto;
        position: relative;
    }
    .chatlist_page {
        background-color: #fff;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        overflow: auto;
    }
    .chat_list_item {
        position: relative;
        padding-left: 0.8rem;
        background-color: #fff;
        height: 3.5rem;
        display: flex;
        align-items: center;
        width: 100%;
        box-sizing: border-box;
        &:first-child {
            padding-top: 0.5rem;
            .item_unread {
                top: 2.3rem;
            }
        }
        & > img {
            float: left;
            border-radius: 50%;
            width: 2.6rem;
            height: 2.6rem;
            box-sizing: border-box;
            object-fit: contain;
            &.is_group {
                border: 0.1rem solid #ededee;
            }

            &.groupset_icon {
                width: 1rem;
                height: 1rem;
                margin-left: -0.7rem;
            }
        }
        .item_unread {
            position: absolute;
            right: 1rem;
            top: 1.8rem;
            background-color: #ff675c;
            color: #fff;
            border-radius: 50%;
            font-size: 0.45rem;
            width: 0.8rem;
            height: 0.8rem;
            display: inline-block;
            line-height: 0.8rem;
            text-align: center;
            .svg_ellipsis {
                font-size: 0.8rem;
                // transform: translateX(-1rem);
                width: 100%;
                height: 100%;
                color: #fff;
            }
        }
        .item_mute,
        .item_exam_unread {
            position: absolute;
            right: 1rem;
            background-color: #ff675c;
            color: #fff;
            border-radius: 50%;
            padding: 0.2rem;
            top: 2rem;
        }
        .chat_item_right {
            margin-left: 0.85rem;
            height: 100%;
            margin-right: 0.85rem;
            position: relative;
            flex: 1;
            min-width: 0;
            .chat_item_title {
                display: flex;
                .subject {
                    font-size: 0.8rem;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    color: #666;
                    padding-top: 0.55rem;
                    flex: 1;
                }
                .operate_panel {
                    .svg_camera {
                        width: 1.05rem;
                        height: 1.05rem;
                        fill: #a6ebdd;
                        color: #a6ebdd;
                        float: left;
                        margin-top: 0.5rem;
                        margin-right: 0.4rem;
                    }
                    .svg_mobile {
                        width: 1.05rem;
                        height: 1.05rem;
                        fill: #00c59d;
                        margin-top: 0.5rem;
                        float: left;
                        margin-right: 0.4rem;
                    }
                    .icon-remote-control-2-line {
                        font-size: 1.1rem;
                        padding-top: 0.5rem;
                        padding-right: 0.4rem;
                        display: inline-block;
                        line-height: 1;
                        color: #00c59d;
                    }
                    .send_ts {
                        float: right;
                        font-size: 0.55rem;
                        color: #b3b3b3;
                        margin-top: 0.7rem;
                        line-height: 1;
                    }
                }
            }
            .chat_item_right_text {
                overflow: hidden;
                white-space: nowrap;
                display: flex;
                height: 1.2rem;
                .mention_tip {
                    font-size: 0.65rem;
                    color: #ff6759;
                    margin-right: 0.2rem;
                    margin-top: 0.15rem;
                }
            }
            .icon-phone-,
            .icon-video {
                color: #04be02;
                font-size: 1.2rem;
                line-height: 1;
                margin: 0 0.1rem;
            }
            .content {
                font-size: 0.65rem;
                color: #b3b3b3;
                margin-right: 1rem;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                margin-top: 0.2rem;
                flex: 1;
                img {
                    height: 1rem;
                    vertical-align: middle;
                }
            }
            .attendee_name {
                font-size: 0.65rem;
                color: #b3b3b3;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                margin-top: 0.2rem;
            }
            .mute_number {
                font-size: 0.65rem;
                color: #b3b3b3;
                margin-top: 0.2rem;
                margin-right: 0.1rem;
            }
            ::after {
                content: "";
                height: 1px;
                background-color: rgb(230, 230, 230);
                position: absolute;
                width: 100%;
                bottom: 0;
                left: 0;
                transform: scaleY(0.55);
            }
        }
    }
}
</style>
