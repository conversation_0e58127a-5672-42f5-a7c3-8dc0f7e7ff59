<template>
    <div>
        <el-dialog
          class="live_addr_page"
          :title="lang.live_address"
          :visible="liveAddressVisible"
          :close-on-click-modal="true"
          width="450px"
          :modal="false"
          :before-close="closeLiveAddrPage">
            <vue-scroll class="live_addr_page_panel">
                <div>
                    <p>{{lang.share_live_addr_tip1}}</p>
                    <p>{{lang.share_live_addr_tip2}}</p>
                    <p>
                        {{lang.share_live_addr_tip4}}
                        <span @click="copyLiveAddr()" class="copy_here">{{lang.click_here}}</span>
                        {{lang.share_live_addr_tip3}}
                    </p>
                    <div class="live_addr">
                        <input id="live_addr_input"  type="text" v-model="live_addr" >
                    </div>
                    <div id="live_qr_addr_connent"></div>
                </div>
            </vue-scroll>
        </el-dialog>
    </div>

    </template>
<script>
import base from '../../lib/base'
import Tool from '@/common/tool.js'
import {getLiveRoomObj} from '../../lib/common_base'
export default {
    mixins: [base],
    name: 'LiveAddressDialog',
    components: {},
    props:{
        address:{
            type:String,
            default:''//组件类型，1在chatwindow页面，2在画廊
        },
        cid:{
            type:[String,Number],
            default:0
        }
    },
    data(){
        return {
            live_addr:'',
            liveAddressVisible:false
        }
    },
    computed:{

    },
    mounted(){
        this.$nextTick(()=>{


        })
    },
    methods:{
        GetChannelId(){
            let liveRoom = getLiveRoomObj(this.cid)
            if(!liveRoom){
                return ''
            }
            let channelId  = liveRoom.data.channelId
            return channelId
        },
        copyLiveAddr(){
            var url=document.getElementById("live_addr_input");
            url.select(); // 选择对象
            document.execCommand("Copy"); // 执行浏览器复制命令
            console.log("copy " + this.live_addr);
            this.$message.success(this.lang.copy_text_success);
        },
        closeLiveAddrPage(){
            this.liveAddressVisible = false
        },
        loadAddressDialog(){
            const LiveConferenceData = this.$store.state.liveConference[this.cid]&&this.$store.state.liveConference[this.cid].LiveConferenceData || {}
            let address = LiveConferenceData.liveAddress||''
            this.liveAddressVisible = true
            var channelId = this.GetChannelId();
            if (!channelId) {
                this.$notify.error(this.lang.get_live_addr_error)
                return;
            }
            let channelInfo = `channel_id=${channelId}`
            let groupInfo = `group_id=${this.cid}`
            let str = address||window.btoa(`${channelInfo}#####${groupInfo}`)

            let serverInfo=this.systemConfig.serverInfo;
            // this.live_addr = `http://${serverInfo.server_addr}:${serverInfo.listen_http_live_port}/activity/activity.html#/webLive/${str}`

            let url = this.systemConfig.server_type.protocol + this.systemConfig.server_type.host + this.systemConfig.server_type.port
            this.live_addr = Tool.transferLocationToCe(`${url}/activity/activity.html#/webLive/${str}`)
            console.log(`${channelInfo}#####${groupInfo}`)
            // let serverInfo=this.systemConfig.serverInfo;
            // var live_addr = "http://" + serverInfo.server_addr + ":" + serverInfo.listen_http_live_port + "/enterLivePlayPage?live_id=" + live_id;
            // console.log("live_addr: " + live_addr);

            this.$nextTick(()=>{
                document.getElementById("live_qr_addr_connent").innerHTML=''
                var qrcode = new window.QRCode(document.getElementById("live_qr_addr_connent"), {
                    text: this.live_addr,
                    width : 200,
                    height : 200
                });
            })


        }
    }
}
</script>
    <style lang="scss">
    .live_addr_page{
        background:none !important;
        &.el-dialog__wrapper .el-dialog .el-dialog__body{
            padding:4px 0;
        }
        .live_addr_page_panel{
            color:#333;
            height:100%;
            p{
                font-size:16px;
                color:red;
                line-height:2;
                .copy_here{
                    color:#06f;
                    text-decoration:underline;
                    cursor:pointer;
                }
            }
            .live_addr{
                #live_addr_input{
                    width:1px;
                    height:1px
                }
            }
            #live_qr_addr_connent{
                width:200px;
                height:200px;
                margin:30px auto;
            }
        }
    }
    </style>
