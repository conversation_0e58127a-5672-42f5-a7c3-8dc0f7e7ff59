<template>
    <van-dialog v-model="show" :show-cancel-button="false" :show-confirm-button="false" class="review-detail-dialog" @open="handleAfterOpen" @close="handleAfterClose">
        <div class="wrapper" @click.stop>
                <div class="wrapper-header">
                    <van-icon name="close" @click="closeDialog" />
                </div>
                <div class="wrapper-title">
                    <p>{{lang.live_ended}}</p>
                </div>
                <div class="wrapper-content">

                    <template v-if="isLoading">
                        <van-loading size="24" />
                    </template>
                    <template v-if="!creator_name&&!isLoading">
                        <p class="no-data">{{lang.no_data_txt}}</p>
                    </template>
                    <template v-if="creator_name&&!isLoading">
                        <p class="wrapper-content-info">
                            <span class="content-info-label">{{lang.live_broadcast_initiator}}:</span>
                            <span
                                class="longwrap content-info-text"
                                v-if="creator_name"
                                >{{ creator_name }}</span
                            >
                        </p>
                        <p class="wrapper-content-info">
                            <span class="content-info-label">{{lang.live_broadcast_initiation_time}}:</span>
                            <span class="longwrap content-info-text">{{ formatTime(start_ts) }}</span>
                        </p>
                        <div class="wrapper-content-line"></div>
                        <div class="wrapper-bottom">
                            <p class="wrapper-bottom-title">{{lang.live_broadcast_information}}</p>
                            <div class="wrapper-bottom-info">
                                <div class="info-item">
                                    <span class="content-info-label">{{lang.live_broadcast_duration}}:</span>
                                    <span class="longwrap content-info-text" v-if="start_ts">{{
                                        getDateDiff(start_ts,stop_ts)
                                    }}</span>
                                </div>
                                <div class="info-item">
                                    <span class="content-info-label">{{lang.live_broadcast_viewers}}:</span>
                                    <span class="longwrap content-info-text">{{count}}</span>
                                </div>
                            </div>
                            <div class="wrapper-bottom-more" v-if="hasMoreDetail">
                                <div v-show="isShowMore" class="more-list">
                                    <div class="tabs">
                                        <div class="tab_item" :class="{active:detailMode=='live'}" @click="detailMode='live'">{{`${lang.live_detail_title}(${moreLiveList.length})`}}</div>
                                        <div class="tab_item" :class="{active:detailMode=='review'}" @click="detailMode='review'">{{`${lang.review_detail_title}(${moreReviewList.length})`}}</div>
                                    </div>
                                    <div v-show="detailMode=='live'">
                                        <div class="more-item" v-for="member of moreLiveList" :key="member.userId">
                                            <mr-avatar :url="getLocalAvatar(member)" :origin_url="member.avatar" :radius="1.5" :showOnlineState="false" :key="member.avatar"></mr-avatar>
                                            <p class="more-item-name">{{member.nickname}}</p>
                                            <p>{{member.durationStr}}</p>
                                        </div>
                                    </div>
                                    <div v-show="detailMode=='review'">
                                        <div class="more-item" v-for="member of moreReviewList" :key="member.userId">
                                            <mr-avatar :url="getLocalAvatar(member)" :origin_url="member.avatar" :radius="1.5" :showOnlineState="false" :key="member.avatar"></mr-avatar>
                                            <p class="more-item-name">{{member.nickname}}</p>
                                            <p>{{member.durationStr}}</p>
                                        </div>
                                        <div v-if="moreReviewList.length==0">{{lang.no_data_txt}}</div>
                                    </div>
                                </div>
                                <span class="more-toggle-btn" v-show="!isShowMore" @click="toggleShowMore">{{lang.expand_more_details}}</span>
                                <span class="more-toggle-btn" v-show="isShowMore" @click="toggleShowMore">{{lang.retract_more_details}}</span>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
    </van-dialog>

</template>
<script>
import base from "../../lib/base";
import {  Icon, Loading,Dialog  } from "vant";
import { getDateDiff,getLocalAvatar } from "../../lib/common_base";
import moment from "moment";
import DialogManager from "../../lib/dialogManager";
export default {
    mixins: [base],
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
        hasMoreDetail: {
            type: Boolean,
            default: false,
        },
    },
    computed:{
    },
    watch: {
        value: {
            handler(val) {
                if (val) {
                    this.openDialog();
                } else {
                    this.closeDialog();
                }
            },
            immediate: true,
        },
        show: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    filters: {},
    components: {
        VanIcon: Icon,
        VanLoading: Loading,
        [Dialog.Component.name]: Dialog.Component,
    },
    data() {
        return {
            getDateDiff,
            getLocalAvatar,
            that: this,
            show: false,
            count:0,
            start_ts:0,
            stop_ts:0,
            creator_name:'',
            isLoading:false,
            isShowMore:false,
            moreLiveList:[],
            moreReviewList:[],
            detailMode:'live',
            // 弹窗管理相关
            dialogId: null
        };
    },

    created() {
        // 组件创建时不需要做任何事情
        // 弹窗打开时才注册到DialogManager
    },

    beforeDestroy() {
        // 组件销毁前，如果弹窗还在管理器中，则注销
        this.unregisterFromDialogManager();
    },
    methods: {
        /**
         * 打开弹窗 - 注册到DialogManager（注册即打开）
         */
        openDialog() {
            this.show = true;

            // 弹窗打开时注册到DialogManager（注册即打开）
            if (!this.dialogId) {
                this.dialogId = DialogManager.register(this, {
                    open: () => {
                        this.show = true;
                    },
                    close: () => {
                        this.show = false;
                    }
                });

                console.log('ReviewDetail弹窗已注册到DialogManager:', this.dialogId);
            }
        },

        /**
         * 关闭弹窗 - 从DialogManager注销（注销即关闭）
         */
        closeDialog() {
            this.show = false;

            // 弹窗关闭时从DialogManager注销（注销即关闭）
            this.unregisterFromDialogManager();
        },

        /**
         * 从DialogManager注销弹窗（注销即关闭）
         */
        unregisterFromDialogManager() {
            if (this.dialogId) {
                DialogManager.unregister(this.dialogId);
                console.log('ReviewDetail弹窗已从DialogManager注销:', this.dialogId);
                this.dialogId = null;
            }
        },

        /**
         * 更新弹窗属性（如果需要动态改变弹窗行为）
         */
        updateDialogProperties() {
            if (this.dialogId) {
                DialogManager.setDialogProperties(this.dialogId, {
                    canClose: this.checkCanClose(),
                    canCloseOnPopstate: this.checkCanCloseOnPopstate()
                });
            }
        },

        getLiveRecordInfo(){
            this.isLoading = true;
            this.count = 0;
            this.start_ts = 0;
            this.stop_ts = 0;
            this.creator_name = '';
            this.detailMode='live';
            this.isShowMore=false;

            return new Promise((resolve,reject)=>{
                window.main_screen.getReviewDetail({gmsg_id:this.message.gmsg_id},(res)=>{
                    console.log(res);
                    this.isLoading = false;

                    if(!res.error_code){
                        this.count = res.data.count;
                        this.start_ts = res.data.start_ts;
                        this.stop_ts = res.data.stop_ts;
                        this.creator_name = res.data.creator_name;
                        this.setMoreList(res.data.list,res.data.audience_list);
                        resolve(res.data);
                    } else {
                        reject(res);
                    }
                });
            });
        },
        toggleShowMore(){
            this.isShowMore=!this.isShowMore;
        },
        setMoreList(list,audience_list){
            this.moreLiveList=[];
            this.moreReviewList=[];
            for(let member of list){
                member.duration=0;
                for(let item of member.dataList){
                    member.duration+=item.duration;
                }
                member.durationStr=getDateDiff(0,member.duration)
                this.moreLiveList.push(member);
            }
            for(let member of audience_list){
                member.durationStr=getDateDiff(0,member.duration)
                this.moreReviewList.push(member);
            }
        },
        handleAfterOpen(){
            this.getLiveRecordInfo();
        },
        handleAfterClose(){
            this.isLoading = false;
        }
    },
};
</script>
<style lang="scss" scoped>
.review-detail-dialog {
    background-color: rgba(0, 0, 0, 0.7);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
    right: 0;
    bottom: 0;
    border-radius: 0;
    top: 0;
    width: auto;
    transform: translate3d(0px, 0px, 0px);
    .wrapper {
        width: 100%;
        height: 100%;
        color: #fff;
        .wrapper-header {
            height: 3rem;

            position: relative;
            .van-icon {
                position: absolute;
                bottom: 0;
                right: 0.5rem;
                font-size: 1.2rem;
            }
        }
        .wrapper-title {
            height: 10rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 1.3rem;
        }
        .wrapper-content {
            background: #3d3e40;
            width: 18rem;
            // height: 10rem;
            border-radius: 0.5rem;
            font-size: 0.8rem;
            margin: 0 auto;
            padding: 1rem;
            box-sizing: border-box;
            position: relative;
            .wrapper-content-info {
                display: flex;
                margin-bottom: 0.8rem;
                .content-info-label {
                    margin-right: 1rem;
                    flex-shrink: 0;
                }
                .content-info-text {
                    flex: 1;
                }
            }
            .wrapper-content-line {
                width: 100%;
                height: 1px;
                background: #9da8ae;
            }
            .van-loading{
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate3d(-50%,-50%,0);
            }
            .no-data{
                width: 100%;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
        .wrapper-bottom {
            margin-top: 0.8rem;
            .wrapper-bottom-info {
                margin-top: 0.8rem;
                display: flex;
                font-size: 0.7rem;
                color: #9da8ae;
                justify-content: space-between;
                .info-item {
                    // flex: 1;
                    .content-info-label {
                        margin-right: .3rem;
                        flex-shrink: 0;
                    }
                }
            }
            .wrapper-bottom-more{
                margin-top: .6rem;
                font-size: .7rem;
                color: #fff;
                .more-toggle-btn{
                    text-align: right;
                    color: #00c59d;
                    cursor: pointer;
                    user-select:none;
                }
                .more-list{
                    max-height: 10rem;
                    overflow: auto;
                    .tabs{
                        display: flex;
                        flex-direction: row;
                        text-align: center;
                        font-size: 0.8rem;
                        color: #fff;
                        background: #3d3e40;
                        line-height: 1.5rem;
                        margin-bottom: .5rem;
                        .tab_item{
                            flex: 1;
                            border-bottom: 0.1rem solid #fff;
                            &.active{
                                border-bottom:0.1rem solid #00c59d;
                                color:#00c59d;
                            }
                        }
                    }
                    .more-item{
                        display: flex;
                        align-items: center;
                        margin-bottom: .5rem;
                        .more-item-name{
                            padding: 0 .5rem;
                            flex:1;
                        }
                    }
                }
            }
        }
    }
}
</style>
