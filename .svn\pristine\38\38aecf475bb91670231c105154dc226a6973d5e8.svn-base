<template>
    <div class="exam_list_page" v-loading="supplying">
        <exam-search-bar
            ref="exam_search_bar"
            :searchCallback="searchCallback"
            :changeCondition="changeCondition"
        ></exam-search-bar>
        <div class="exam_list" :key="'exam_list' + examListStamp">
            <div class="exam_loading" v-if="loadingMore">
                <van-loading color="#00c59d" />
            </div>
            <van-list
                ref="loadmoreExam"
                v-model="groupLoading"
                @load="loadmore"
                :finished="index <= 0"
                :loading-text="lang.bottom_loading_text"
                offset="0"
                :immediate-check="false"
            >
                <div class="group_container" v-for="(group, index) of examGroup" :key="index">
                    <p class="group_year_title group_title" v-if="group.year">{{ group.year }}</p>
                    <p class="group_title">{{ group.title }}</p>
                    <div
                        v-for="item of group.list"
                        class="exam_item"
                        :class="'color_index_' + (item.colorIndex % 4)"
                        :key="item.exam_id"
                        v-popover.top="{
                            name: 'menu',
                            chat_direction: 'top',
                            callback: () => {
                                showExamItemToolTips(item);
                            },
                        }"
                    >
                        <div @click="toggleImages(item)" class="exam_title">
                            <div class="exam_uploader">
                                <mr-avatar
                                    :url="getLocalAvatar(item.sender_nickname[0])"
                                    :origin_url="item.sender_nickname[0].avatar"
                                    :showOnlineState="false"
                                    :key="item.sender_nickname[0].avatar"
                                ></mr-avatar>
                                <div class="uploader_info">
                                    <p class="sender">
                                        <span v-for="(sender, index) of item.sender_nickname" :key="index">
                                            {{
                                                sender.hospital_name
                                                    ? sender.hospital_name + "-" + sender.nickname
                                                    : sender.nickname
                                            }}
                                        </span>
                                    </p>
                                    <p class="date">{{ formatTime(item.upload_ts) }}</p>
                                </div>
                            </div>
                            <div class="exam_patient">
                                <table border="1" cellspacing="0">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <span>{{ item.patientInfo.patient_name_str }}</span>
                                            </td>
                                            <td>
                                                <span>{{ item.patientInfo.patient_age_str }}</span>
                                            </td>
                                            <td>
                                                <span>{{ item.patientInfo.patient_sex_str }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <span>{{
                                                    item.exam_type == 9
                                                        ? lang.exam_mode_label + lang.exam_types[item.exam_type]
                                                        : lang.exam_types[item.exam_type]
                                                }}</span>
                                            </td>
                                            <td>
                                                <span>{{ formatTime(item.patient_series_datetime) }}</span>
                                            </td>
                                            <td>
                                                <span>{{ lang.image_count }}: {{ item.count }}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <!-- <div class="patient_info">
                                    <p></p>
                                    <p></p>
                                    <p></p>
                                </div>
                                <div class="patient_info">
                                    <p></p>
                                    <p class="date"></p>
                                    <p></p>
                                </div>
                                <div class="patient_info">

                                </div> -->
                            </div>
                            <div class="exam_iworks" v-if="item.iworks_protocol_execution">
                                <div class="icon_title">
                                    <i class="iconfont icon-quanbuwenjian-weixuanzhong"></i>
                                </div>
                                <p>
                                    iWorks：{{ item.iworks_protocol_execution.protocol_name }}
                                    <span v-show="item.all_view_number">
                                        ({{ item.uploaded_view_number }}/{{ item.all_view_number }})
                                    </span>

                                    <!-- <i @click="showProtocol(item)" class="iconfont icon-warning-o"></i> -->
                                </p>
                            </div>
                            <i v-show="!item.openState" class="iconfont exam_down icon-exam_dowm"></i>
                            <i v-show="item.openState" class="iconfont exam_up icon-exam_up"></i>
                        </div>
                        <div class="exam_image_list clearfix" v-show="item.openState">
                            <div v-show="item.imageState == 1" class="loading_image">
                                <van-loading color="#00c59d" />
                            </div>
                            <div class="clearfix">
                                <template v-for="img of item.showImageList">
                                    <div
                                        class="file_item"
                                        :class="{ is_create: img.isCreate, is_delete: img.isDelete }"
                                        :key="img.resource_id"
                                        v-if="getResourceTempState(img.resource_id) === 1"
                                    >
                                        <v-touch
                                            v-popover.top="{
                                                name: 'menu',
                                                chat_direction: 'top',
                                                callback: () => {
                                                    showTooltipsMenu(img, 'examImageItem');
                                                },
                                            }"
                                            class="file_container"
                                            @click.native="openGallery(item, img)"
                                        >
                                            <template v-if="img.url">
                                                <p class="view_name longwrap" v-if="img.protocol_view_name">
                                                    {{ img.protocol_view_name }}
                                                </p>
                                                <common-image :fileItem="img" size="mini"></common-image>
                                                <span v-if="img.count > 1" class="view_count"
                                                    >{{ img.count }}{{ lang.piece_tip }}</span
                                                >
                                            </template>
                                            <template v-else>
                                                <p class="view_name longwrap">{{ img.protocol_view_name }}</p>
                                                <img :src="default_image" class="file_image needsclick" />
                                            </template>
                                        </v-touch>
                                        <van-checkbox-group
                                            v-show="item.isShowCheck && img.url_local"
                                            v-model="item.selected"
                                        >
                                            <van-checkbox
                                                :name="img.resource_id"
                                                checked-color="#00c59d"
                                            ></van-checkbox>
                                        </van-checkbox-group>
                                        <template v-if="showAiAnalyzeIcon(img)">
                                            <template v-if="item.isIworksTest">
                                                <span>
                                                    <span v-if="!img.url">
                                                        <span
                                                            class="icon iconfont ai_result_deletion_icon icon-wenhao-yuankuang-copy"
                                                            :title="lang.view_deletion"
                                                        ></span>
                                                    </span>
                                                    <template v-else>
                                                        <span
                                                            v-for="(iconObj, index) in imageStandardIcon(img)"
                                                            :key="index"
                                                            :class="[iconObj.css]"
                                                            :title="iconObj.tips"
                                                        >
                                                            {{ iconObj.label }}
                                                        </span>
                                                    </template>
                                                </span>
                                            </template>
                                            <template v-else>
                                                <span
                                                    v-for="(iconObj, index) in imageStandardIcon(img)"
                                                    :key="index"
                                                    :class="[iconObj.css]"
                                                    :title="iconObj.tips"
                                                >
                                                    {{ iconObj.label }}
                                                </span>
                                            </template>
                                        </template>
                                    </div>
                                </template>
                                <div
                                    v-if="isShowSupplyExamImageBtn(item)"
                                    class="file_item needsclick"
                                    @click="selectImageToSupply(item)"
                                >
                                    <div class="file_container needsclick">
                                        <div class="supuly_image needsclick">+</div>
                                    </div>
                                </div>
                                <div v-if="item.more" class="file_item" @click="showMoreImage(item)">
                                    <div class="file_container more_container">
                                        <div class="more_text">
                                            {{ item.moreText }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="consultation_btn clearfix">
                                <span @click.stop="transferExamImage(item)" class="apply_btn">
                                    <i class="iconfont transmit_icon icon-share"></i>
                                </span>
                                <span @click.stop="openExamComment(item)" class="apply_btn">
                                    <i class="iconfont comment_icon icon-comment"></i>
                                </span>
                            </div>
                            <div class="exam_comments">
                                <p>
                                    {{ lang.exam_opinion_text
                                    }}<span v-if="item.opinionList.length > 0">{{ item.opinionList.length }}</span>
                                </p>
                                <div
                                    v-for="comment in item.opinionList"
                                    class="comment_item clearfix"
                                    :key="comment.id"
                                >
                                    <div class="time_and_author">
                                        <span class="author">{{
                                            attendeeList && attendeeList["attendee_" + comment.sender_id].nickname
                                        }}</span>
                                        <span>{{ formatTime(comment.send_ts) }}</span>
                                    </div>
                                    <p>
                                        {{ comment.content
                                        }}<span v-if="comment.type == 1">{{ lang.exam_conclusion_text }}</span>
                                    </p>
                                </div>
                            </div>
                            <!-- <div class="conclusions" v-if="item.exam_consultation&&item.exam_consultation.status==2">
                                <p>{{lang.exam_conclusion_text}}</p>
                                <template v-for="history in item.exam_consultation.historyList">
                                    <p v-for="conclusion in history.conclusionList">
                                        {{conversationList[cid].attendeeList['attendee_'+conclusion.sender_id].nickname}}：{{conclusion.content}}
                                    </p>
                                </template>
                            </div> -->
                        </div>
                    </div>
                </div>
            </van-list>

            <!-- <input
                :key="supply_exam_image_input_file_tag"
                type="file"
                id="supply_exam_image_input_file"
                class="supply_exam_image_input_file"
                @change="supplyImage"
                accept=".jpg,.jpeg,.png,.pdf,.mp4,.dcm"
            /> -->
             <div v-show="false">
             <van-uploader
                    v-model="fileList"
                    ref="upload"
                    :after-read="handleFilesSelected"
                    :before-read="beforeFilesSelected"
                    accept=".jpg,.jpeg,.png,.pdf,.mp4,.dcm,.ai,.json"
                    multiple
                    max-count="9"
                    />
             </div>
            <div class="no_more_data" v-show="index == -1">{{ lang.no_more_data }}</div>
            <div class="no_more_data" v-show="loadFail" @click="search">{{ lang.loading_fail }}</div>
        </div>
        <div class="edit_comment_modal needsclick" @touchmove.prevent :class="{ showTagModal: showTagModal }">
            <div class="edit_comment_panel needsclick">
                <div class="edit_comment_container clearfix needsclick">
                    <div
                        class="comment_editer needsclick"
                        :contenteditable="showTagModal"
                        ref="tag_text"
                        @input="changeTextTag"
                        @click.stop="enterEditMode($event)"
                        tabindex="2"
                    ></div>
                    <span class="send_comment primary_bg fr" @click.stop="addCustomTag">{{ lang.add_btn }}</span>
                    <span class="colse_comment primary_bg fl" @click.stop="closeAddCustomTag">{{
                        lang.cancel_btn
                    }}</span>
                </div>
                <div class="rainbow_row clearfix">
                    <div class="block1 fl"></div>
                    <div class="block2 fl"></div>
                    <div class="block3 fl"></div>
                    <div class="block4 fl"></div>
                    <div class="block5 fl"></div>
                </div>
            </div>
        </div>
        <div
            class="edit_comment_modal needsclick"
            @touchmove.prevent
            :class="{ showExamCommentModal: showExamCommentModal }"
        >
            <div class="edit_comment_panel needsclick">
                <div class="edit_comment_container clearfix needsclick">
                    <!-- <div class="comment_editer needsclick" :contenteditable="showExamCommentModal" ref="exam_comment_text" @input="changeTextExamComment" @click="enterEditMode($event)" tabindex="2"></div> -->
                    <textarea v-model="examCommentText" ref="exam_comment_text" class="comment_editer"></textarea>
                    <span class="send_comment primary_bg fr" @click="addExamComment">{{ lang.add_btn }}</span>
                    <span class="colse_comment primary_bg fl" @click="closeExamComment">{{ lang.cancel_btn }}</span>
                </div>
                <div class="rainbow_row clearfix">
                    <div class="block1 fl"></div>
                    <div class="block2 fl"></div>
                    <div class="block3 fl"></div>
                    <div class="block4 fl"></div>
                    <div class="block5 fl"></div>
                </div>
            </div>
        </div>
        <MenusAction :currentFile="currentFile" ref="menusAction"></MenusAction>
    </div>
</template>
<script>
import base from "../lib/base";
import send_message from "../lib/send_message.js";
import examListCommon from "../lib/examListCommon";
import iworksTool from "../lib/iworksTool";
import { Toast, Loading, List, Checkbox, CheckboxGroup, Uploader } from "vant";
import { cloneDeep } from "lodash";
import examSearchBar from "./examSearchBar";
import Tool from "@/common/tool.js";
import MenusAction from "./menusAction.vue";
import uploadConsultImage from "../lib/uploadConsultImage";

import {
    setProtocolTree,
    parseImageListToLocal,
    enterEditMode,
    changeTextTag,
    transferPatientInfo,
    findProtocolViewNode,
    patientDesensitization,
    getLocalAvatar,
    isIworksTest,
    imageStandardIcon,
    getResourceTempState,
    setIworksInfoToMsg,
} from "../lib/common_base";
export default {
    mixins: [base, send_message, iworksTool, examListCommon, uploadConsultImage],
    components: {
        examSearchBar,
        MenusAction,
        VanLoading: Loading,
        VanList: List,
        VanCheckbox: Checkbox,
        VanCheckboxGroup: CheckboxGroup,
        VanUploader: Uploader,

    },
    data() {
        return {
            setIworksInfoToMsg,
            getResourceTempState,
            isIworksTest,
            imageStandardIcon,
            getLocalAvatar,
            controller: null,
            loadingMore: false,
            showTagModal: false,
            tagText: "",
            cid: this.$route.params.cid,
            purposeText: "",
            applyExamTem: "",
            showExamCommentModal: false,
            examCommentText: "",
            commentExamTem: "",
            examListStamp: 0,
            loadFail: false,
            debounceType: 4,
            condition: {},
            groupLoading: false,
            examGroup: [],
            menuDatas: [],
            editMode: true,
            supply_exam_image_input_file_tag: 0,
            examInfo: null,
            currentFile: null,
            default_image: "static/resource/images/default.png",
        };
    },
    computed: {
        examObj() {
            return this.$store.state.examList[this.cid] || {};
        },
        examList() {
            return this.examObj.list || [];
        },
        // examGroup(){
        //     let type=this.condition.groupSelected||1;
        //     return this.parseExamListToGroup(this.examList);
        // },
        index() {
            const index = this.examObj.index || 0;
            return index;
        },
        attendeeList() {
            return this.conversationList[this.cid].attendeeList;
        },
        unreadObj() {
            let temp = {};
            for (let chat of this.$store.state.chatList.list) {
                if (chat.cid == this.cid) {
                    temp = chat.examConsultationUnread;
                    break;
                }
            }
            return temp;
        },
        isAiAnalyze() {
            let conversation = this.conversationList[this.cid];
            return (
                conversation &&
                (conversation.service_type == this.systemConfig.ServiceConfig.type.AiAnalyze ||
                    conversation.service_type == this.systemConfig.ServiceConfig.type.DrAiAnalyze)
            );
        },
    },
    activated() {
        this.$nextTick(() => {});
    },
    mounted() {
        this.$nextTick(() => {
            this.$root.eventBus
                .$off("updateExamImageListIfNeed")
                .$on("updateExamImageListIfNeed", this.updateExamImageListIfNeed);
            this.$root.eventBus.$off("updateExamPageImage").$on("updateExamPageImage", this.updateExamPageImage);
            this.$root.eventBus.$off("deleteFileToExamList").$on("deleteFileToExamList", this.deleteFileToExamList);
            this.$root.eventBus.$off("deleteExamItem").$on("deleteExamItem", this.deleteExamItem);
        });
    },
    created() {},
    methods: {
        init() {
            this.cid = this.$route.params.cid;
            this.$refs.exam_search_bar.init();
            if (!this.examObj.init) {
                this.search();
            } else {
                //检查列表初始化过再次进入只渲染上次搜索条件
                this.$refs.exam_search_bar.renderSearchCondition(this.examObj);
                // this.parseExamListToGroup();
            }
        },
        getExamList(callback) {
            if (this.loadingMore) {
                return;
            }
            this.loadingMore = true;
            this.groupLoading = true;
            this.loadFail = false;
            var that = this;
            let index = this.examObj.index;
            this.controller = this.conversationList[this.cid].socket;
            let sort_by = this.condition.sortSelected == 1 ? "exam_ts" : "upload_ts";
            let keys = this.condition;
            const count = 20;
            this.$store.commit("examList/updateExamObj", {
                cid: this.cid,
                keys: keys,
            });
            this.controller.emit(
                "get_exam_list",
                {
                    key: keys,
                    // start: index,
                    count,
                    sort_by: sort_by,
                    skip: this.examList.length,
                },
                function (is_succ, data) {
                    that.loadingMore = false;
                    if (is_succ) {
                        if (data.exam_list.length == 0 && data.is_local) {
                            //加载本地为空时不更新UI，等待网络数据
                            return;
                        }
                        patientDesensitization(data.exam_list);
                        for (let item of data.exam_list) {
                            item.patientInfo = transferPatientInfo(item);
                            parseImageListToLocal(item.sender_nickname, "avatar");
                            that.setDefaultImg(item.sender_nickname);
                            item.imageState = 0;
                            item.openState = false;
                            item.isShowCheck = false;
                            item.imageList = [];
                            item.selected = [];
                            item.iworksImages = [];
                            item.noneIworksImages = [];
                            if (item.iworks_protocol) {
                                that.setIworksProtocol(item.iworks_protocol);
                            }
                            if (item.iworks_protocol_execution) {
                                let protocol = item.iworks_protocol_execution
                                protocol.protocolTree=[setProtocolTree(protocol)]
                                that.setIworksProtocol(item.iworks_protocol_execution);
                            }
                        }
                        let index = data.index;
                        if (data.exam_list.length < count) {
                            index = -1;
                        }
                        that.$store.commit("examList/setExamList", {
                            cid: that.cid,
                            exam_list: data.exam_list,
                            index: index,
                            start: index,
                        });

                        that.parseExamListToGroup();
                        if (index !== 0 || index !== -1) {
                            that.groupLoading = false;
                            // Toast(that.lang.loading_complete)
                        }

                        //不知道为什么加了个列表时间戳导致列表重渲，每次加载更多都滚动回顶部
                        // that.examListStamp=new Date().valueOf();
                        callback && callback();
                    } else {
                        that.loadFail = true;
                        that.groupLoading = false;
                        Toast(that.lang[data]);
                    }
                }
            );
        },
        loadmore() {
            this.getExamList();
        },
        updateExamImageListIfNeed(msg) {
            //收到消息后实时刷新检查列表
            if (msg.msg_type == this.systemConfig.msg_type.EXAM_IMAGES) {
                msg = cloneDeep(msg);
                msg.msg_type = msg.cover_msg_type;
            }
            if (
                msg.msg_type == this.systemConfig.msg_type.OBAI ||
                msg.msg_type == this.systemConfig.msg_type.Frame ||
                msg.msg_type == this.systemConfig.msg_type.Cine
            ) {
                //收到的会诊文件exam_id已在列表内
                let hasExam = false;
                let cid = msg.group_id;
                let examObj = this.$store.state.examList[cid];
                if (!examObj) {
                    //检查列表没获取过，直接忽略
                    return;
                }
                let examList = examObj.list;
                for (let index = 0; index < examList.length; index++) {
                    let exam = examList[index];
                    if (msg.exam_id == exam.exam_id) {
                        hasExam = true;
                        if (this.cid == cid) {
                            if (exam.imageState == 2) {
                                let obj = {};
                                exam.imageList.unshift(msg);
                                if (msg.iworks_protocol) {
                                    exam.iworks_protocol = msg.iworks_protocol;
                                }
                                if (msg.iworks_protocol_execution) {
                                    exam.iworks_protocol_execution = msg.iworks_protocol_execution;
                                }
                                this.setShowImageList(exam, exam.imageList, msg.sender_nickname.upload_ts);
                                this.addUploaderIfneed(exam, msg, index);
                                this.updatePatientInfo(exam, msg);
                            } else if (exam.imageState === 0) {
                                // 没加载过，新增图片自动加载，以对齐count字段
                                this.updatePatientInfo(exam, msg);
                                this.loadImageList(exam);
                            }
                        } else {
                            this.$store.commit("examList/initExamObj", cid);
                        }
                    }
                }
                if (!hasExam) {
                    //收到的会诊文件exam_id不在列表内,新增一个检查
                    if (examObj.init) {
                        if (this.cid == cid) {
                            this.search();
                        } else {
                            this.$store.commit("examList/initExamObj", cid);
                        }
                    }
                }
            }
        },
        extendIworksSameNodeImage(list) {
            let new_list = list.reduce((h, v) => {
                h = [...h, ...(v.same_node_image || []), v];
                return h;
            }, []);
            return new_list || [];
        },
        openGallery(item, img) {
            if (!img.url_local) {
                // return;
                img.url_local = this.default_image;
                img.is_default_image = true;
            }
            let arr = [];
            for (let temp of this.extendIworksSameNodeImage(item.iworksImages)) {
                if (!temp.url_local) {
                    temp.url_local = this.default_image;
                    temp.is_default_image = true;
                }
                arr.push(temp);
            }
            let galleryList = arr.concat(item.noneIworksImages);
            let index = 0;
            for (let i = 0; i < galleryList.length; i++) {
                if (img.resource_id == galleryList[i].resource_id) {
                    index = i;
                }
            }
            this.$store.commit("gallery/setGallery", {
                list: galleryList,
                index: index,
            });
            this.$nextTick(() => {
                this.$router.push(`/index/chat_window/${this.$route.params.cid}/gallery`);
            });
        },
        search() {
            this.$refs.exam_search_bar.search();
        },
        searchCallback(condition) {
            this.examGroup = [];
            this.condition = condition;
            this.$store.commit("examList/initExamObj", this.cid);
            this.$nextTick(() => {
                this.getExamList();
            });
        },
        changeCondition(condition) {
            this.condition = condition;
            this.$store.commit("examList/updateExamObj", {
                cid: this.cid,
                keys: condition,
            });
            this.parseExamListToGroup();
        },
        openEditModal_tag() {
            var that = this;
            this.showTagModal = true;
            this.$nextTick(() => {
                this.$refs.tag_text.focus();
                this.$refs.tag_text.scrollIntoViewIfNeeded();
            });
        },
        addCustomTag() {
            let str = this.tagText;
            if (str.length == 0) {
                Toast(this.lang.tag_text_null);
                return;
            } else {
                this.selectTags.push({
                    name: str,
                    f_id: "temp_" + new Date().valueOf(),
                });
                this.tagText = "";
                this.$refs.tag_text.innerHTML = "";
                this.closeAddCustomTag();
            }
        },
        closeAddCustomTag() {
            this.showTagModal = false;
            this.tagText = "";
            this.$refs.tag_text.blur();
            this.$refs.tag_text.innerHTML = "";
            window.getSelection().removeAllRanges();
        },
        clickSelectedWrap() {
            if (this.selectTags.length == 0) {
                this.openEditModal_tag();
            }
        },
        addUploaderIfneed(exam, msg, index) {
            let senderList = exam.sender_nickname;
            let sender = msg.sender_nickname;
            let hasSender = false;
            for (let temp of senderList) {
                if (temp.sender_id == sender.sender_id) {
                    hasSender = true;
                }
            }
            if (!hasSender) {
                this.$store.commit("examList/updateExamByIndex", {
                    cid: this.cid,
                    index: index,
                    key: senderList.concat([sender]),
                });
            }
        },
        changeTextPurpose() {
            this.purposeText = this.$refs.purpose_text.innerHTML;
        },
        changeTextExamComment() {
            this.examCommentText = this.$refs.exam_comment_text.innerHTML;
        },
        openExamComment(exam) {
            this.commentExamTem = exam;
            this.showExamCommentModal = true;

            this.$nextTick(() => {
                this.$refs.exam_comment_text.focus();
            });
        },
        closeExamComment() {
            // this.$refs.exam_comment_text.innerHTML='';
            this.examCommentText = "";
            this.$refs.exam_comment_text.blur();
            this.showExamCommentModal = false;
        },
        addExamComment() {
            let content = this.examCommentText;
            if (content == "") {
                return;
            }
            let exam_id = this.commentExamTem.exam_id;
            this.controller = this.conversationList[this.cid].socket;
            this.controller.emit(
                "submit_exam_consultation_opinion",
                {
                    exam_id: exam_id,
                    content: content,
                },
                (is_succ, data) => {
                    if (is_succ) {
                        this.$store.commit("examList/addExamComment", {
                            cid: this.cid,
                            exam_id: exam_id,
                            comment: data.opinionList[0],
                        });
                        this.closeExamComment();
                    } else {
                        Toast(this.lang[data]);
                    }
                }
            );
        },
        transferExamImage(exam) {
            if (!exam.isShowCheck) {
                //初次点击展示复选框
                this.$store.commit("examList/updateExamById", {
                    cid: this.cid,
                    exam_id: exam.exam_id,
                    key: {
                        isShowCheck: true,
                    },
                });
                return;
            }
            if (exam.selected.length == 0) {
                //二次点击无选中，隐藏复选框
                this.$store.commit("examList/updateExamById", {
                    cid: this.cid,
                    exam_id: exam.exam_id,
                    key: {
                        isShowCheck: false,
                    },
                });
                return;
            }
            let arr = [];
            for (let item of exam.selected) {
                for (let img of exam.imageList) {
                    if (img.resource_id == item) {
                        arr.push(img);
                        break;
                    }
                }
            }
            this.$root.transmitTempList = arr;
            let path = this.$route.path;
            path += "/transmit";
            this.$router.push(path);
            this.$store.commit("examList/updateExamById", {
                cid: this.cid,
                exam_id: exam.exam_id,
                key: {
                    isShowCheck: false,
                    selected: [],
                },
            });
        },
        showProtocol(item) {
            let protocolInfo = this.gallery.iworks_protocol_list[item.iworks_protocol.attributes.GUID];
            let checked = [];
            for (let image of item.iworksImages) {
                let node = findProtocolViewNode(protocolInfo.protocolTree[0], image.protocol_view_guid);
                if (node) {
                    checked.push(node.id);
                }
            }
            this.$router.push(
                this.$route.fullPath + "/protocol_tree/" + item.iworks_protocol.attributes.GUID + "/" + checked.join()
            );
        },

        deleteFileToExamList(data) {
            let cid = data.cid;
            let examObj = this.$store.state.examList[cid];
            if (!examObj) {
                return;
            }
            let examList = examObj.list;
            let done = false;
            for (let exam of examList) {
                for (let index = 0; index < exam.imageList.length; index++) {
                    if (exam.imageList[index].resource_id == data.resource_id) {
                        let obj = {};
                        exam.imageList.splice(index, 1);
                        obj.imageList = exam.imageList;
                        this.parseIworksImage(exam);
                        obj.count = exam.count - 1;
                        exam.showImageList = exam.iworksImages.concat(exam.noneIworksImages);
                        this.$store.commit("examList/updateExamById", {
                            cid: cid,
                            exam_id: exam.exam_id,
                            key: obj,
                        });
                        done = true;
                        break;
                    }
                }
                if (done) {
                    break;
                }
            }
        },
        showMoreImage(exam) {
            let key = {};
            key["more"] = false;
            key["showImageList"] = exam.iworksImages.concat(exam.noneIworksImages);
            this.$store.commit("examList/updateExamById", {
                cid: this.cid,
                exam_id: exam.exam_id,
                key: key,
            });
            // this.$forceUpdate()
        },
        toggleImages(exam) {
            var that = this;
            var item = exam;
            this.$store.commit("examList/updateExamById", {
                cid: this.cid,
                exam_id: exam.exam_id,
                key: {
                    openState: !item.openState,
                },
            });
            //切换展示状态
            if (item.imageState == 0) {
                //未下载过则下载图像列表
                this.loadImageList(exam);
            }
        },
        loadImageList(exam) {
            const that = this;
            this.$store.commit("examList/updateExamById", {
                cid: this.cid,
                exam_id: exam.exam_id,
                key: {
                    imageState: 1,
                },
            });
            this.controller = this.conversationList[this.cid].socket;
            var tag = this.condition.tag || [];
            this.controller.emit(
                "get_exam_image_list",
                {
                    exam_id: exam.exam_id,
                    key: {
                        tag: tag,
                    },
                },
                function (is_succ, data) {
                    if (is_succ) {
                        that.setShowImageList(exam, data.image_list);
                    } else {
                        let key = {
                            imageState: 0,
                            openState: false,
                        };
                        Toast(that.lang.operate_err);
                        that.$store.commit("examList/updateExamById", {
                            cid: that.cid,
                            exam_id: exam.exam_id,
                            key: key,
                        });
                    }
                }
            );
        },
        setShowImageList(exam, imageList, upload_ts) {
            for (let i in imageList) {
                let image = imageList[i];
                let commentObj = {};
                commentObj = {
                    ai_analyze_report: image.ai_analyze_report || {},
                    tag_names: image.tag_names,
                    comment_list: image.comment_list || [],
                    tags_list: image.tags_list || [],
                    showAISearchSuggest: image.showAISearchSuggest || false,
                };
                this.$store.commit("gallery/updateCommentToGallery", {
                    obj: commentObj,
                    resource_id: image.resource_id,
                });
            }
            let key = {};
            key.imageState = 2;
            patientDesensitization(imageList);
            parseImageListToLocal(imageList, "url");
            if (exam.iworks_protocol) {
                key.iworks_protocol = exam.iworks_protocol;
            }
            if (exam.iworks_protocol_execution) {
                key.iworks_protocol_execution = exam.iworks_protocol_execution;
            }
            //对检查图像进行去重
            for (let item of imageList ) {
                this.setIworksInfoToMsg(item, exam.protocol_execution_guid);
            }
            key.imageList = this.deDuplicatingImg(imageList);
            this.parseIworksImage(key, exam.protocol_execution_guid);
            key.showImageList = key.iworksImages.concat(key.noneIworksImages);
            key.count = key.imageList.length;
            if (key.showImageList.length > 10) {
                key.more = true;
                key.moreText = "+" + (key.showImageList.length - 10);
                key.showImageList = key.showImageList.splice(0, 10);
            } else {
                key.more = false;
            }
            if (upload_ts) {
                key.upload_ts = upload_ts;
            }
            this.$store.commit("examList/updateExamById", {
                cid: this.cid,
                exam_id: exam.exam_id,
                key: key,
            });
        },
        enterEditMode(e) {
            enterEditMode(e);
        },
        changeTextTag() {
            let innerHTML = this.$refs.tag_text.innerHTML;
            innerHTML = changeTextTag(innerHTML);
            this.tagText = innerHTML;
        },
        isShowSupplyExamImageBtn(item) {
            let show = false;
            if (item && item.showImageList && 0 < item.showImageList.length && !item.more) {
                show = true;
            }
            return show;
        },
        selectImageToSupply(exam) {
            this.examInfo = exam;
            const uploadInput = this.$refs.upload.$el.querySelector('input[type=file]');
            uploadInput.click();
        },
        generateImageName() {
            var date = new Date();
            var image_name_str =
                "" +
                date.getFullYear() +
                (date.getMonth() < 9 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) +
                (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) +
                (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) +
                (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) +
                (date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds());

            for (var i = 0; i < 4; i++) {
                image_name_str += Math.floor(Math.random() * 9).toString();
            }

            return image_name_str;
        },
        beforeFilesSelected(file){
            if (!this.examInfo || !this.examInfo.sender_nickname) {
                Toast(this.lang.supply_exam_image.err_tip.not_exam_sender);
                return;
            } else {
                let is_sender = false;
                for (let i in this.examInfo.sender_nickname) {
                    var sender = this.examInfo.sender_nickname[i];
                    if (sender.sender_id == this.user.uid) {
                        is_sender = true;
                        break;
                    }
                }
                if (!is_sender) {
                    Toast(this.lang.supply_exam_image.err_tip.not_exam_sender);
                    return;
                }
            }
            this.supplying=true;
            let files = file
            if (!Array.isArray(file)){
                files = [file]
            }
            if(!Array.isArray(files) || files.length<1){
                this.supplying=false;
                return false
            }
            if (files.length > 9) {
                this.supplying=false;
                Toast(this.lang.supply_exam_image.err_tip.select_up_to_number_files.replace("{number}", 9));
                return false
            }

            let isIllegal =false
            let isLt300M =false
            this.supply_exam_image_input_file_tag = new Date().valueOf();
            const uploadFiles = (Object.values(files)||[]).reduce((h,file)=>{
                console.error(file)
                isLt300M = file.size / 1024 / 1024 <= 300;
                if(this.beforeAvatarUpload(file,files)){
                    h.push(file)
                }else{
                    isIllegal = true;
                }
                return h;
            },[])
            if(!isLt300M){
                this.supplying=false;
                Toast(`${this.lang.upload_max_text} 300M`);
                return
            }
            if(isIllegal){
                this.supplying=false;
                Toast(this.lang.upload_forbidden_file_type_text);
                return
            }
            // 判断上传文件列表是否全为json类型
            // const isAllJson = uploadFiles.every(item => Tool.getFileType(item.name) === 'json');

            // if (isAllJson) {
            //     this.supplying=false;
            //     Toast(this.lang.transmit_less_tip);
            //     return false;
            // }

            if(uploadFiles.length < 1){
                this.supplying=false;
                Toast(this.lang.transmit_less_tip);
                return false;
            }

            return true;

        },
        //创建批量任务
        async handleFilesSelected(file) {
            if (!this.examInfo) {
                return;
            }
            let uploadFiles = this.fileList
            if (Object.keys(this.uploadTask).length === 0) {
                let uploadFns = [];
                uploadFiles.forEach((item) => {
                    this.$set(this.uploadTask, item.file.name, {
                        ...item,
                        isUploaded: false,
                        percentage: 0,
                        progressShow: false,
                        ossImageUrl: "",
                        isError: false,
                    });
                    uploadFns.push(async () => {
                        return await this.uploadFiles(item.file.name);
                    });
                });
                const taskGroups = [];
                for (let i = 0; i < uploadFns.length; i += 10) {
                    taskGroups.push(uploadFns.slice(i, i + 10));
                }

                async function runTaskGroup(taskGroup) {
                    return await Promise.all(taskGroup.map((task) => task()));
                }
                async function runTasks() {
                    let resultArr = [];
                    for (const taskGroup of taskGroups) {
                        const result = await runTaskGroup(taskGroup);
                        resultArr.push(result);
                    }
                    return resultArr;
                }
                let ping = true
                await Tool.handleAfterMainScreenCreated(ping)
                runTasks()
                    .then((result) => {
                        console.log("All tasks have been completed.", result);
                        const flatArr = result.flat();
                        this.alertUploadResult(flatArr);
                        this.clearUploadTask();
                        return flatArr;
                    })
                    .catch((error) => {
                        if (error !== "cancel") {
                            Toast(this.lang.supply_exam_image.err_tip.upload_file_failed)
                        }
                        this.clearUploadTask();
                    })
            } else {
                return [];
            }
        },
        showTooltipsMenu(msg, from) {
            if (msg.is_default_image) {
                return false;
            }
            this.$set(this, "currentFile", msg);
            this.$refs.menusAction.showTooltipsMenu(msg, from);
        },
        showExamItemToolTips(msg) {
            this.$set(this, "currentFile", msg);
            this.$refs.menusAction.showExamItemToolTips(msg);
        },
        deleteExamItem() {
            this.parseExamListToGroup();
        },
        getImageListLength(oImageList, oCount) {
            if (oImageList.length > 0) {
                let imageList = oImageList.filter((img) => {
                    return getResourceTempState(img.resource_id) === 1;
                });
                return imageList.length;
            } else {
                return oCount;
            }
        },
        showAiAnalyzeIcon(img) {
            return this.functionsStatus.breastAI || this.functionsStatus.drAIAssistant; //||this.functionsStatus.obstetricalAI
        },
        updatePatientInfo(exam, msg) {
            if (msg.patient_id) {
                let patientInfo = transferPatientInfo(msg);
                this.$store.commit("examList/updateExamById", {
                    cid: msg.group_id,
                    exam_id: exam.exam_id,
                    key: {
                        patientInfo,
                    },
                });
            }
        },
    },
};
</script>
<style lang="scss">
.exam_list_page {
    flex-grow: 1;
    overflow: auto;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch;
    position: relative;
    .search_bar {
    }
    table {
        width: 100%;
        td {
            vertical-align: middle;
            text-align: left;
            padding: 10px;
            font-size: 12px;
            color: #858c96;
            word-break: break-all;
            min-width: 50px;
        }
    }
    .exam_list {
        box-sizing: border-box;
        height: calc(100% - 1.6rem);
        overflow: auto;
        position: absolute;
        width: 100%;
        top: 1.6rem;
        background-color: #fff;
        padding-bottom: 1.6rem;
        padding-top: 0.25rem;
        .exam_loading {
            display: inline-block;
            margin-left: 50%;
            transform: translateX(-50%);
            margin-top: 1rem;

            .van-loading__spinner {
                width: 1.8rem;
                height: 1.8rem;

                .van-loading__circular circle {
                    stroke-width: 0.2rem;
                }
            }
        }
        .group_container {
            padding: 0.65rem 0.8rem 0;
        }
        .group_title {
            padding: 0 0.2rem;
            color: rgba(101, 109, 112, 1);
            font-size: 0.65rem;
            line-height: 1;
            margin-bottom: 0.6rem;
        }
        .group_year_title {
            font-size: 0.7rem;
            color: #000;
        }
        .exam_item {
            padding: 0.7rem 1.1rem;
            margin-bottom: 0.6rem;
            background-color: #f2f6f9;
            border-radius: 0.2rem;
            box-shadow: 0.1rem 0.1rem 0.2rem rgba(140, 152, 155, 0.7);
            position: relative;
            overflow: hidden;
            transform: translate3d(0, 0, 0);
            &::before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 0.1rem;
            }
            .qc_standard_icon {
                bottom: 0.5rem;
                right: 0.2rem;
                position: absolute;
                color: rgb(0, 255, 102);
                font-size: 10px;
                z-index: 10;
            }
            .qc_non_standard_icon {
                bottom: 0.5rem;
                right: 0.2rem;
                position: absolute;
                color: rgb(255, 153, 51);
                font-size: 10px;
                z-index: 10;
            }
            .ai_result_deletion_icon {
                bottom: 0.5rem;
                right: 0.2rem;
                position: absolute;
                color: rgb(255, 0, 0);
                font-size: 10px;
                z-index: 10;
            }
            &.color_index_0 {
                &::before {
                    background-color: rgb(255, 103, 92);
                }
                .file_item {
                    &.is_create {
                        .file_container::before {
                            content: "";
                            position: absolute;
                            left: 0;
                            top: 0;
                            bottom: 0;
                            width: 0.1rem;
                            background-color: rgb(255, 103, 92);
                            z-index: 2;
                        }
                        .view_name {
                            color: rgb(255, 103, 92);
                        }
                    }
                }
            }
            &.color_index_1 {
                &::before {
                    background-color: rgb(255, 177, 68);
                }
                .file_item {
                    &.is_create {
                        .file_container::before {
                            content: "";
                            position: absolute;
                            left: 0;
                            top: 0;
                            bottom: 0;
                            width: 0.1rem;
                            background-color: rgb(255, 177, 68);
                            z-index: 2;
                        }
                        .view_name {
                            color: rgb(255, 177, 68);
                        }
                    }
                }
            }
            &.color_index_2 {
                &::before {
                    background-color: rgb(86, 200, 255);
                }
                .file_item {
                    &.is_create {
                        .file_container::before {
                            content: "";
                            position: absolute;
                            left: 0;
                            top: 0;
                            bottom: 0;
                            width: 0.1rem;
                            background-color: rgb(86, 200, 255);
                            z-index: 2;
                        }
                        .view_name {
                            color: rgb(86, 200, 255);
                        }
                    }
                }
            }
            &.color_index_3 {
                &::before {
                    background-color: rgb(116, 128, 234);
                }
                .file_item {
                    &.is_create {
                        .file_container::before {
                            content: "";
                            position: absolute;
                            left: 0;
                            top: 0;
                            bottom: 0;
                            width: 0.1rem;
                            background-color: rgb(116, 128, 234);
                            z-index: 2;
                        }
                        .view_name {
                            color: rgb(116, 128, 234);
                        }
                    }
                }
            }
            .exam_title {
                font-size: 0.8rem;
                color: #333;
                line-height: 1.6;
                position: relative;
                .exam_uploader {
                    display: flex;
                    padding-bottom: 0.6rem;
                    position: relative;
                    .uploader_info {
                        flex: 1;
                        padding-left: 0.6rem;
                        .date {
                            font-size: 0.4rem;
                            color: #999;
                            margin-top: 0.1rem;
                        }
                    }
                    &::after {
                        content: "";
                        position: absolute;
                        height: 1px;
                        background-color: rgb(171, 181, 186);
                        transform: scaleY(0.25);
                        left: 0;
                        right: 0;
                        bottom: 0;
                    }
                }
                .exam_patient {
                    font-size: 0.55rem;
                    padding-top: 0.55rem;
                    color: rgb(101, 109, 112);
                    line-height: 1;
                    padding-bottom: 0.55rem;
                    .patient_info {
                        display: flex;
                        margin-bottom: 0.5rem;
                        p {
                            flex: 1;
                            word-break: break-all;
                            padding-right: 0.2rem;
                        }
                        .date {
                            flex: 2;
                        }
                    }
                }
                .exam_iworks {
                    display: flex;
                    align-items: center;
                    color: rgb(101, 109, 112);
                    font-size: 0.55rem;
                    margin-bottom: 0.5rem;
                    .icon_title {
                        background-color: #666;
                        border-radius: 50%;
                        width: 0.85rem;
                        height: 0.85rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        .icon-quanbuwenjian-weixuanzhong {
                            color: #fff;
                            font-size: 0.6rem;
                            line-height: 1;
                        }
                    }
                    p {
                        line-height: 1rem;
                        padding-left: 0.4rem;
                        font-size: 0.55rem;
                    }
                }
                .exam_down,
                .exam_up {
                    position: absolute;
                    fill: gray;
                    color: gray;
                    right: 0;
                    top: 0;
                    width: 0.7rem;
                    height: 0.7rem;
                }
                .icon-warning-o {
                    position: static;
                    vertical-align: sub;
                    margin-left: 0.2rem;
                }
                .sender {
                    padding-right: 2rem;
                    word-break: break-all;
                    font-size: 0.6rem;
                    color: rgb(101, 109, 112);
                    span:after {
                        content: ",";
                        display: inline-block;
                    }
                    span:last-child {
                        &:after {
                            content: "";
                        }
                    }
                }
                .unread {
                    position: absolute;
                    right: 0;
                    top: 0;
                    background: #f00;
                    color: #fff;
                    display: inline-block;
                    width: 0.7rem;
                    height: 0.7rem;
                    line-height: 0.7rem;
                    font-size: 0.7rem;
                    text-align: center;
                    border-radius: 50%;
                    padding: 0.1rem;
                }
                .comment_num {
                    position: absolute;
                    right: 1.5rem;
                    top: -0.3rem;
                    font-size: 1rem;
                    color: #333;
                    span {
                        position: absolute;
                        right: -0.3rem;
                        top: 0rem;
                        background-color: #00c59d;
                        color: #fff;
                        border-radius: 50%;
                        font-size: 0.5rem;
                        min-width: 0.7rem;
                        min-height: 0.7rem;
                        display: inline-block;
                        line-height: 0.7rem;
                        text-align: center;
                    }
                }
            }
        }
        .no_more_data {
            text-align: center;
            color: #666;
            font-size: 0.8rem;
            height: 50px;
            line-height: 50px;
        }
        .exam_image_list {
            padding-top: 0.55rem;
            position: relative;
            &::before {
                content: "";
                position: absolute;
                height: 1px;
                background-color: rgb(171, 181, 186);
                transform: scaleY(0.25);
                left: 0;
                right: 0;
                top: 0;
            }
            .loading_image {
                width: 36px;
                margin: 10px auto;
            }
            .file_item {
                float: left;
                width: 25%;
                box-sizing: border-box;
                position: relative;
                background: #f2f6f9;
                padding-right: 0.15rem;
                padding-bottom: 0.5rem;
                overflow: hidden;
                .file_container {
                    position: relative;
                    padding-top: 75%;
                    height: 0;
                    background-color: #000;
                    overflow: hidden;
                    border-radius: 0.2rem;
                    &.more_container {
                        background: transparent;
                        .more_text {
                            position: absolute;
                            width: 50%;
                            height: 66%;
                            top: 16%;
                            left: 25%;
                            border: 1px solid #aaa;
                            border-radius: 50%;
                            font-size: 0.6rem;
                            color: #666;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                    .review_text {
                        position: absolute;
                        color: yellow;
                        z-index: 1;
                        top: 0;
                        font-size: 0.5rem;
                        width: 100%;
                        text-align: center;
                        top: 5%;
                        transform: scale(0.8);
                    }
                    .file_image {
                        max-width: 100%;
                        max-height: 100%;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                    }
                    .video_icon {
                        width: 0.5rem;
                        height: 0.5rem;
                        position: absolute;
                        bottom: 0.2rem;
                        left: 0.3rem;
                    }
                    .review_time {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%) scale(0.4);
                        font-size: 1rem;
                        color: #fff;
                        text-align: center;
                        white-space: nowrap;
                    }
                    .view_name {
                        color: #fff;
                        position: absolute;
                        top: 4px;
                        z-index: 9;
                        left: 4px;
                        transform: none;
                        font-size: 0.4rem;
                        right: 4px;
                    }
                    .view_count {
                        color: #fff;
                        position: absolute;
                        bottom: 4px;
                        z-index: 9;
                        right: 4px;
                        font-size: 0.4rem;
                    }
                    .supuly_image {
                        position: absolute;
                        width: 50%;
                        height: 66%;
                        top: 16%;
                        left: 25%;
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);

                        font-size: 1.2rem;
                        color: #fff;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
                &.is_delete {
                    .view_name {
                        text-decoration: line-through;
                    }
                }
            }
            .consultation_btn {
                margin-top: 0.1rem;
                padding-top: 0.65rem;
                position: relative;
                padding-bottom: 0.65rem;
                &::before {
                    content: "";
                    position: absolute;
                    height: 1px;
                    background-color: rgb(171, 181, 186);
                    transform: scaleY(0.25);
                    left: 0;
                    right: 0;
                    top: 0;
                }
                .apply_btn {
                    float: left;
                    font-size: 0.6rem;
                    padding: 0.2rem 0.4rem;
                    background-color: #01bd97;
                    margin: 0rem 1.75rem 0 0;
                    border-radius: 0.2rem;
                    box-sizing: border-box;
                    color: #fff;
                    border: none;
                    line-height: 0.65rem;
                }
                .transmit_icon,
                .comment_icon,
                .apply_icon {
                    width: 0.65rem;
                    height: 0.65rem;
                    fill: #fff;
                    float: left;
                }
            }
            .exam_comments {
                padding-top: 0.65rem;
                position: relative;
                padding-bottom: 0.2rem;
                &::before {
                    content: "";
                    position: absolute;
                    height: 1px;
                    background-color: rgb(171, 181, 186);
                    transform: scaleY(0.25);
                    left: 0;
                    right: 0;
                    top: 0;
                }
                & > p {
                    font-size: 0.6rem;
                    line-height: 1;
                    padding-bottom: 0.4rem;
                    color: rgb(101, 109, 112);
                    span {
                        margin-left: 0.3rem;
                    }
                }
                .comment_item {
                    color: #333;
                    font-size: 0.5rem;
                    margin-bottom: 0.3rem;
                    .time_and_author {
                        color: #00c59d;
                        line-height: 0.95rem;
                        .author {
                            margin-right: 0.3rem;
                        }
                    }
                    & > p {
                        line-height: 0.85rem;
                        color: rgb(101, 109, 112);
                        span {
                            font-size: 0.5rem;
                            background-color: #01c59d;
                            color: #fff;
                            line-height: 1;
                            padding: 0.2rem 0.3rem;
                            border-radius: 0.2rem;
                            margin-top: 0;
                            margin-left: 0.2rem;
                            display: inline-block;
                        }
                    }
                }
            }
            .conclusions {
                font-size: 0.8rem;
                color: #333;
                line-height: 1.6;
            }
        }
        .supply_exam_image_input_file {
            display: none;
        }
    }
    .edit_comment_modal {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.8);
        z-index: -1;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        &.showTagModal,
        &.showPurposeModal,
        &.showExamCommentModal {
            z-index: 99;
        }
        .edit_comment_panel {
            background-color: #eee;
            width: calc(100% - 0.4rem);
            margin-top: 30%;
            .title {
                color: #333;
                padding: 0.2rem 0.4rem;
            }
            .edit_comment_container {
                padding: 0.4rem;
                background-color: #eee;
                .comment_editer {
                    line-height: 1.6rem;
                    background-color: #fff;
                    color: #333;
                    border-radius: 0.2rem;
                    border: 1px solid #ccc;
                    box-sizing: border-box;
                    max-height: 3.2rem;
                    overflow: auto;
                    user-select: text;
                    -webkit-user-select: text;
                    padding: 0 0.4rem;
                    width: 100%;
                    resize: none;
                    font-size: 1rem;
                }
                .send_comment,
                .colse_comment {
                    font-size: 0.7rem;
                    line-height: 1.2rem;
                    padding: 0.2rem 0.5rem;
                    border-radius: 0.2rem;
                    margin-top: 0.4rem;
                }
            }
        }
    }
}
.exam_list_page {
    .van-checkbox-group {
        position: absolute;
        width: 100%;
        height: 88%;
        max-height: 200px;
        overflow: auto;
        border-radius: 0.75rem;
        top: 0px;
        right: 0px;
        z-index: 1000;
        .van-checkbox {
            width: 100%;
            height: 100%;
            box-sizing: border-box;
            justify-content: flex-end;
            align-items: flex-end;
            padding: 5px;
        }
    }
}
</style>
