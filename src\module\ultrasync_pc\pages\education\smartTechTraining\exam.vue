<template>
    <div class="smart_tech_exam_container">
        <div class="custom_header">
            <div class="back_btn" @click="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body">
            <div class="correct_exam_detail">
                <div class="exam_detail_left">
                    <!-- 答题模式，可编辑学生信息 -->
                    <div class="student_info">
                        <div class="student_info_row">
                            <div class="student_info_label">{{ lang.student_name }}:&nbsp;</div>
                            <span>{{ studentName }}</span>
                        </div>
                        <div class="student_info_row student_info_row_org">
                            <div class="student_info_label">{{ lang.admin_hospital_name }}:&nbsp;</div>
                            <span>{{ hospital }}</span>
                        </div>
                    </div>
                </div>
                <div class="exam_detail_right">
                    <template v-if="type === CLOUD_TEST_TYPE.VIEW">
                        <el-button class="primary_btn" @click="shareExam" size="large" type="default">{{
                            lang.share_paper
                        }}</el-button>
                        <el-button class="error_btn" @click="deletePaper" size="large" type="default">{{
                            lang.homework.delete_paper
                        }}</el-button>
                        <el-button class="gradient_btn" @click="arrangeExam">{{ lang.assign_homework }}</el-button>
                    </template>
                    <template v-else-if="type === CLOUD_TEST_TYPE.EDIT">
                        <el-button class="error_btn" @click="back" size="large" type="default">{{
                            lang.cancel_btn
                        }}</el-button>
                        <el-button class="primary_btn" @click="saveExam" size="large" type="default">{{
                            lang.save_txt
                        }}</el-button>
                    </template>
                </div>
            </div>
            <div ref="topicContent" class="topic_content" v-if="examContents.length>0">
                <div class="exam-title-wrapper" v-if="type === CLOUD_TEST_TYPE.EDIT">
                    <span class="title-tip-label">{{ lang.homework.exam_title_tip }}：</span>
                    <!-- 编辑模式，可编辑试卷标题 -->
                    <text-field
                        :value="examTitle"
                        :isEdit="true"
                        :placeholder="lang.input_enter_tips"
                        @change="(val) => handleFieldChange(examTitle, 'title', val)"
                    />
                </div>
                <div class="content-container">
                    <div class="topic_list">
                        <div v-for="(topicType, index) of examContents" :key="index" class="topic_list_item">
                            <div class="topic_summary">
                                {{ lang.topic_type[topicType.type] }}（{{ topicType.summaryText }}）
                            </div>
                            <pre
                                v-if="type !== CLOUD_TEST_TYPE.EDIT && topicType.type === 'operation'"
                                class="topic_tip"
                                v-html="lang.homework_operation_step"
                            ></pre>
                            <div
                                class="topic_detail"
                                v-for="(topic, j_index) of topicType.list"
                                :key="j_index"
                                :class="{ 'is_locked': isTopicLocked(topic, topicType.type)&& topicType.type !== 'operation' }"
                                @click="setCurrentQuestionByTypeIndex(index, j_index)"
                            >
                                <div v-if="isTopicLocked(topic, topicType.type) && topicType.type !== 'operation'" class="locked_overlay">
                                    <span class="locked_text status-tag-passed-new">{{ lang.passed }}</span>
                                </div>
                                <short-answer-topic
                                    v-if="topicType.type === 'shortAnswer'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    @delete-image="deleteImage"
                                    @upload-click="() => uploadStart(index, j_index, '-1')"
                                    @image-order-changed="handleImageOrderChange"
                                    @update-value="handleUpdateValue"
                                    @update-pass-status="handleUpdatePassStatus"
                                    @update-comment="handleUpdateComment"
                                    @set-current-question="setCurrentQuestionByTypeIndex"
                                    :isPassMode="true"
                                />
                                <operation-topic
                                    v-else-if="topicType.type === 'operation'"
                                    :topic="topic"
                                    :type="type"
                                    :topicTypeIndex="index"
                                    :topicIndex="j_index"
                                    :disableModify="disableModify"
                                    :isUploading="topic.isUploading"
                                    :subTopicLocked="getSubTopicLockedArray(topic)"
                                    :isCollecting="isCollecting"
                                    :formattedCountdown="formattedCountdown"
                                    :antiCheatEnabled="currentTestData && currentTestData.antiCheatEnabled === 1"
                                    @delete-image="deleteImage"
                                    @upload-click="(subIndex) => uploadStart(index, j_index, subIndex)"
                                    @image-order-changed="handleImageOrderChange"
                                    @update-pass-status="handleUpdatePassStatus"
                                    @update-comment="handleUpdateComment"
                                    @update-subtopic-pass-status="handleUpdateSubtopicPassStatus"
                                    @update-subtopic-comment="handleUpdateSubtopicComment"
                                    @open-collect="openCollect"
                                    @close-collect="closeCollect"
                                    @subtopic-click="jumpToSubTopic"
                                    @set-current-question="setCurrentQuestionByTypeIndex"
                                    :isPassMode="true"
                                />

                                <input
                                    v-show="false"
                                    ref="uploadComponent"
                                    type="file"
                                    :accept="acceptFileTypes"
                                    multiple
                                    @change="handleFileChange($event)"
                                    style="position: fixed; top: -1000px; left: -1000px; pointer-events: none;"
                                />
                            </div>
                        </div>
                    </div>
                    <!-- 答题、批改模式，显示进度条 -->
                    <progress-indicator
                        v-if="(type === CLOUD_TEST_TYPE.ANSWER || type === CLOUD_TEST_TYPE.CORRECT) && examContents.length>0"
                        :progressList="progressList"
                        @submit="handleSubmitTest"
                        :topicType="type"
                        :totalScore="currentTotalScore"
                        @jump="jumpToQuestion"
                        :isPassMode="true"
                        :showSaveBtn="false"
                    />
                </div>
            </div>
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../../lib/base";
import service from "../../../service/service.js";
import { formatDurationTime, findServiceId } from "../../../lib/common_base";
import moment from "moment";
import { uploadFile } from "@/common/oss/index";
import Tool from "@/common/tool.js";
import { cloneDeep } from "lodash";
import { CLOUD_TEST_TYPE } from "../../../lib/constants";
import onlineTestOverviewMixins from '../../../lib/onlineTestOverviewMixins';

// 导入自定义组件'
import ProgressIndicator from "../../cloudExam/components/progressIndicator.vue";
import ShortAnswerTopic from "../../cloudExam/components/ShortAnswerTopic.vue";
import OperationTopic from "../../cloudExam/components/OperationTopic.vue";

export default {
    mixins: [base, onlineTestOverviewMixins],
    name: "smartTechExam",
    components: {
        ProgressIndicator,
        ShortAnswerTopic,
        OperationTopic,
    },
    data() {
        return {
            paperId: 0,
            type: null, // 查看类型 2：答卷 3：批卷 4：查看结果
            cid: 0, // 群id ，0为全局进入
            examContents: [],
            currentTestData: {}, //原始考试信息
            currentGradingData: {},
            useTime: 0,
            interval: null,
            uploadIndex: [0, 0, 0],
            collectIndex: [0, 0, 0],
            fileTransferAssistant: null,
            studentName: "",
            hospital: "",
            currentQuestionIndex: 0,
            CLOUD_TEST_TYPE: CLOUD_TEST_TYPE,
            userRole: "",
            trainingID: "",
            examTitle: "",
            testID: "",
            videoTimer: null, // 视频定时器ID
            liveReadPromiseResolve: null,
            liveReadPromiseReject: null,
            isSubmitted: false, // 标识考试是否已提交成功
            studentInfo: null,
            originGradingData:null, //原始批改数据
            isCollecting: false, // 标识是否正在采集
            collectingInfo: null, // 当前采集的题目信息
            lockTimer: null, // 加锁定时器
            lockInterval: 15000, // 加锁间隔时间（15秒）
            isForceBack: false, // 标识是否强制退出

            fileTransferAssistantCid:0,//文件传输助手cid
            // 心跳轮询相关
            heartbeatTimer: null,
            lastHeartbeatConfirmTime: null,
            missedHeartbeatCount: 0,

            // 采集时间限制相关
            collectTimeLimit: 30 * 60, // 30分钟，单位：秒
            collectCountdownTimer: null, // 倒计时定时器
            collectRemainingTime: 0, // 剩余时间，单位：秒
        };
    },
    filters: {
        showData(ts) {
            return moment(ts).format("YYYY-MM-DD HH:mm:ss z");
        },
        useTime(duration) {
            return formatDurationTime(duration);
        },
    },
    computed: {
        title() {
            return this.examTitle || this.lang.homework.exam_detail;
        },
        disableModify() {
            return this.type !== CLOUD_TEST_TYPE.ANSWER;
        },
        acceptFileTypes() {
            const imageTypes = this.getSupportImageType().join(",.").toLowerCase();
            const videoTypes = this.getSupportVideoType().join(",.").toLowerCase();
            return `.${imageTypes},.${videoTypes}`;
        },
        progressList() {
            if (!this.examContents) {
                return [];
            }
            let progress = [];
            let currentIndex = 0;

            // 直接遍历examContents中的所有题目，确保包含已通过的题目
            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    let isCompleted = false;
                    let baseStatus = "none";
                    let isPassed = null;

                    if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                        // 答题模式：检查题目是否已完成（无论是否被锁定）
                        if (topicType.type === "operation") {
                            // 实操题：检测子题中是否有上传图片
                            isCompleted = topic.subTopic && topic.subTopic.some((sub) => sub.value && sub.value.length > 0);
                        } else {
                            // 简答题：检查是否有答案内容
                            isCompleted = topic.value && typeof topic.value === "string" && topic.value.trim() !== "";
                        }

                        // 检查题目是否已通过（显示特殊状态）
                        if (this.isTopicLocked(topic, topicType.type)) {
                            isPassed = true;
                            isCompleted = true; // 已通过的题目视为已完成
                        }
                    } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                        // 批改模式：只要还可以批改且当前会话未批改，就显示为未批改
                        if (this.isTopicLocked(topic, topicType.type)) {
                            // 题目已被锁定（历史记录中已通过），不可再批改，显示为已完成
                            isCompleted = true;
                            isPassed = true;
                        } else {
                            // 题目还可以批改，只检查当前会话的批改状态
                            if (topicType.type === "shortAnswer") {
                                // 简答题：只检查当前会话的批改状态
                                isCompleted = topic.isPassed !== null && topic.isPassed !== undefined;
                                isPassed = topic.isPassed;
                            } else if (topicType.type === "operation") {
                                // 实操题：只检查当前会话的批改状态
                                if (topic.subTopic && Array.isArray(topic.subTopic)) {
                                    // 检查是否有任何子题在当前会话中被批改
                                    isCompleted = topic.subTopic.some(subTopic =>
                                        subTopic.isPassed !== null && subTopic.isPassed !== undefined
                                    );
                                    // 对于实操题，isPassed基于所有子题的当前会话状态计算
                                    if (isCompleted) {
                                        const allGraded = topic.subTopic.every(subTopic =>
                                            subTopic.isPassed !== null && subTopic.isPassed !== undefined
                                        );
                                        const allPassed = topic.subTopic.every(subTopic =>
                                            subTopic.isPassed === true
                                        );
                                        isPassed = allGraded && allPassed;
                                    }
                                } else {
                                    isCompleted = false;
                                    isPassed = null;
                                }
                            }
                        }
                    }

                    baseStatus = isCompleted ? "done" : "none";
                    // 如果该题为当前题，则显示 current 样式
                    let status = currentIndex === this.currentQuestionIndex ? "current" : baseStatus;

                    progress.push({
                        status: status,
                        completed: isCompleted,
                        isPassed: isPassed,
                    });

                    currentIndex++;
                });
            });

            return progress;
        },
        currentTotalScore() {
            if (this.type !== CLOUD_TEST_TYPE.CORRECT || this.examContents.length === 0) {
                return 0;
            }
            let totalScore = 0;
            this.examContents.forEach((topicType) => {
                topicType.list.forEach((item) => {
                    // 只累加已经有correctScore的题目分数
                    if (item.correctScore !== null && item.correctScore !== undefined && item.correctScore !== "") {
                        totalScore += Number(item.correctScore);
                    }
                });
            });
            return totalScore;
        },
        // 格式化倒计时显示
        formattedCountdown() {
            if (this.collectRemainingTime <= 0) {
                return '00:00';
            }
            const minutes = Math.floor(this.collectRemainingTime / 60);
            const seconds = this.collectRemainingTime % 60;
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    },
    async created() {
        this.userRole = this.$route.params.role;
        this.cid = parseInt(this.$route.params.cid) || 0;
        this.paperId = this.$route.params.testId;
        this.type = parseInt(this.$route.params.pager_type);
        this.trainingID = this.$route.params.trainingId;
        this.testID = this.$route.params.testId;
        this.currentTestData = cloneDeep(this.$route.params.testData);
        this.currentGradingData = cloneDeep(this.$route.params.gradingData);
        this.originGradingData = cloneDeep(this.$route.params.gradingData);
        // 在批改模式下，直接从currentGradingData获取学生信息
        if ((this.type === CLOUD_TEST_TYPE.CORRECT || this.type === CLOUD_TEST_TYPE.VIEW_RESULT)) {
            this.studentInfo = this.$route.params.studentInfo;
            this.studentName = this.studentInfo.name || "";
            this.hospital = this.studentInfo.hospital || "";
        } else {
            await this.getTrainingStudentInfo();
        }
        if (this.type === CLOUD_TEST_TYPE.ANSWER) {
            // 考生获取答题卡
            this.getanswerSheetDetail();
            // 学生答题时启动加锁
            this.startAnswerLock();
        } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
            // 老师批卷获取答题卡
            this.getCorrectDetail();
            // 老师批改时启动加锁
            this.startAnswerLock();
        } else if (this.type === CLOUD_TEST_TYPE.VIEW_RESULT) {
            // 查看已批改的详情
            this.getCorrectDetail();
        }
        console.log(this.currentTestData, "currentTestData");
        console.log(this.currentGradingData, "currentGradingData");
        console.log(this.studentInfo, "studentInfo");
    },
    mounted() {
        // 监听页面关闭事件，确保在用户直接关闭浏览器时也能清理加锁定时器
        this.handleBeforeUnload = () => {
            // 浏览器关闭时只清理定时器，不调用API（可能无效）
            this.clearLockTimer();
        };
        window.addEventListener('beforeunload', this.handleBeforeUnload);
    },
    beforeRouteLeave(to, from, next) {
        // 在离开组件前提示保存
        if (this.type !== CLOUD_TEST_TYPE.ANSWER) {
            // 非答题模式，直接停止加锁并离开
            this.stopAnswerLock();
            next(true);
            return;
        }
        // 如果考试已经提交成功，直接允许离开
        if (this.isSubmitted) {
            // 考试已提交，停止加锁并离开
            this.stopAnswerLock();
            next(true);
            return;
        }
        if(this.isForceBack){
            // 强制退出，停止加锁并离开
            this.stopAnswerLock();
            next(true);
            this.isForceBack = false;
            return;
        }
        setTimeout(() => {
            this.$alert(this.lang.answer_not_submitted_leave_tips, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_btn,
                type: "warning",
            })
                .then(() => {
                    // 用户确认离开，停止加锁定时器并解锁
                    this.stopAnswerLock();
                    next(true)
                })
                .catch(() => {
                    // 用户取消离开，不停止加锁，继续保持答题状态
                    next(false)
                });
        }, 100);
    },
    destroyed() {
        clearInterval(this.interval);
        this.closeFileTransferListener();
        // 强制清理采集状态，确保下次进入时状态正确
        this.forceCleanupCollectionState();
        // 清除加锁定时器（作为最后的安全网，不调用解锁API）
        this.clearLockTimer();
        // 确保组件销毁时清除定时器
        if (this.videoTimer) {
            clearInterval(this.videoTimer);
            this.videoTimer = null;
        }
        // 清除采集状态
        if (this.isCollecting) {
            this.sendMsgToOwnerByPC('stopLiveToFileTransferAssistant', this.collectingInfo?.collectInfo || {});
            this.destroyMsgFromOwnerByPC();
        }
        // 停止心跳轮询
        this.stopCollectingHeartbeat();
        // 清除倒计时定时器
        this.clearCollectCountdown();
        this.isCollecting = false;
        this.collectingInfo = null;

        // 移除beforeunload事件监听器
        if (this.handleBeforeUnload) {
            window.removeEventListener('beforeunload', this.handleBeforeUnload);
        }
    },
    methods: {
        isTopicLocked(topic, topicType) {
            if (this.type !== CLOUD_TEST_TYPE.ANSWER && this.type !== CLOUD_TEST_TYPE.CORRECT) {
                return false;
            }

            if (topicType === 'shortAnswer') {
                // 简答题的锁定状态取决于其持久化的批改记录，而不是当前会话的临时状态
                return topic.answerRecords && topic.answerRecords.some(r => r.isPassed === true);
            }

            if (topicType === 'operation') {
                if (!topic.subTopic || topic.subTopic.length === 0) {
                    return false;
                }
                // 实操题的锁定状态取决于是否所有子题都已在持久化记录中通过
                return topic.subTopic.every(st => st.answerRecords && st.answerRecords.some(r => r.isPassed === true));
            }

            return false;
        },
        isSubTopicLocked(subTopic) {
            if (this.type !== CLOUD_TEST_TYPE.ANSWER && this.type !== CLOUD_TEST_TYPE.CORRECT) {
                return false;
            }

            // 检查子题的answerRecords中是否有isPassed为true的记录
            if (subTopic.answerRecords && subTopic.answerRecords.some(r => r.isPassed === true)) {
                return true;
            }

            return false;
        },
        getSubTopicLockedArray(topic) {
            if (this.type !== CLOUD_TEST_TYPE.ANSWER && this.type !== CLOUD_TEST_TYPE.CORRECT || !topic.subTopic) {
                return [];
            }
            const lockedArray = topic.subTopic.map(subTopic => ({
                subID: subTopic.subID,
                isLocked: this.isSubTopicLocked(subTopic)
            }));
            console.log('lockedArray',lockedArray);
            return lockedArray;
        },
        // 添加辅助方法来根据索引查找题目
        findTopicByIndex(index) {
            let currentIndex = 0;
            for (const topicType of this.examContents) {
                for (const topic of topicType.list) {
                    if (currentIndex === index) {
                        return {
                            ...topic,
                            type: topicType.type,
                        };
                    }
                    currentIndex++;
                }
            }
            return null;
        },
        getSupportImageType() {
            return Tool.getSupportImageType();
        },
        getSupportVideoType() {
            return Tool.getSupportVideoType();
        },
        getTopicTypeSummary(topicType) {
            console.log('topicType',topicType);
            let summary = this.lang.topic_summary_no_score;
            summary = summary.replace("{a}", topicType.count);
            return summary;
        },
        deleteImage(imageList, index) {
            imageList.splice(index, 1);

            // 触发计算属性更新
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                this.$forceUpdate();
            }
        },
        getCorrectDetail() {
            this.examContents = this.currentGradingData?.paperInfo.content || this.currentTestData?.paperInfo.content;
            this.examTitle = this.currentGradingData?.title;
            console.log(this.examContents, "examContents");

            if (this.examContents && this.examContents.length > 0) {
                // 重新处理题目索引，确保按顺序递增
                this.reindexTopics();

                this.examContents.forEach(topicType => {
                    this.$set(topicType, 'summaryText', this.getTopicTypeSummary(topicType));
                    // 为每个topic初始化isUploading状态
                    topicType.list.forEach(topic => {
                        this.$set(topic, 'isUploading', false);
                        // 为实操题的子题初始化采集相关状态
                        if (topicType.type === "operation" && topic.subTopic && Array.isArray(topic.subTopic)) {
                            topic.subTopic.forEach(subTopic => {
                                this.$set(subTopic, "collecting", false);
                                this.$set(subTopic, "loadingStartCollecting", false);
                            });
                        }
                    });
                });
            }
            this.initAnswer(this.currentGradingData?.answer);
        },
        getanswerSheetDetail() {
            this.examContents = this.currentGradingData?.paperInfo.content|| this.currentTestData?.paperInfo.content;
            this.examTitle = this.currentTestData.title;
            console.log(this.examContents, "examContents");

            if (this.examContents && this.examContents.length > 0) {
                // 重新处理题目索引，确保按顺序递增
                this.reindexTopics();

                this.examContents.forEach(topicType => {
                    this.$set(topicType, 'summaryText', this.getTopicTypeSummary(topicType));
                    // 为每个topic初始化isUploading状态
                    topicType.list.forEach(topic => {
                        this.$set(topic, 'isUploading', false);
                        // 为实操题的子题初始化采集相关状态
                        if (topicType.type === "operation" && topic.subTopic && Array.isArray(topic.subTopic)) {
                            topic.subTopic.forEach(subTopic => {
                                this.$set(subTopic, "collecting", false);
                                this.$set(subTopic, "loadingStartCollecting", false);
                            });
                        }
                    });
                });
            }

            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                this.useTimeInterval();
            } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                console.log(this.currentGradingData, "currentGradingData");

            } else if (this.type === CLOUD_TEST_TYPE.VIEW_RESULT) {
            }

            // 初始化答案数据，确保答案区域正确显示历史作答内容
            this.initAnswer(this.currentGradingData?.answer || this.currentTestData?.answer);
        },
        save() {
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                let { answer } = this.getAnswer();
                this.submitAnswer(answer);
            } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                this.submitCorrect();
            }
        },
        handleSubmitTest() {
            if (this.type === CLOUD_TEST_TYPE.ANSWER) {
                let { answer } = this.getAnswer();
                console.log(answer, "answer");
                // 如果没有可提交的答案，直接提示并终止提交
                if (answer.length === 0) {
                    this.$message.warning(this.lang.no_answer_content_submission_not_allowed);
                    return;
                }
                console.log(answer, "answer");
                let message = this.lang.sure_to_submit_answer_tips
                this.$confirm(message, this.lang.tip_title, {
                    confirmButtonText: this.lang.submit_btn,
                    cancelButtonText: this.lang.cancel_button_text,
                })
                    .then(() => {
                        // 添加学生信息到answer对象
                        this.submitAnswer(answer);
                    })
                    .catch(() => {});
            } else if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                this.submitCorrect();
            }
        },
        async submitAnswer(answer) {
            console.log('submitAnswer', answer);
            service
                .submitTrainingTestAnswer({
                    trainingID: this.trainingID,
                    testID: this.testID,
                    useTime: this.useTime,
                    paperInfo: this.currentTestData?.paperInfo,
                    answer,
                })
                .then((res) => {
                    console.log(res,'res')
                    if (res.data.error_code === 0) {
                        this.isSubmitted = true; // 标记考试已提交成功
                        this.$message.success(this.lang.operate_success);
                        this.back();
                    } else {
                        // this.$message.error(this.lang.submission_failed);
                    }
                })
                .catch((error) => {
                    console.error("submit exam error", error);
                    this.$message.error(this.lang.submission_failed);
                });
        },
        async submitCorrect() {
            // 验证所有学生已作答的题目是否都已批改
            const uncorrectedTopics = [];
            let topicNumber = 0;

            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    topicNumber++;

                    // 跳过已锁定通过的题目
                    if (this.isTopicLocked(topic, topicType.type)) {
                        return;
                    }

                    let hasStudentAnswer = false;
                    let isCorrected = false;

                    if (topicType.type === "shortAnswer") {
                        // 简答题：检查是否有学生作答
                        hasStudentAnswer = topic.value && typeof topic.value === "string" && topic.value.trim() !== "";
                        // 检查是否已批改
                        isCorrected = topic.isPassed !== null && topic.isPassed !== undefined;

                        if (hasStudentAnswer && !isCorrected) {
                            uncorrectedTopics.push(`第${topicNumber}题（简答题）`);
                        }
                    } else if (topicType.type === "operation") {
                        // 实操题：检查子题级别的作答和批改情况
                        if (topic.subTopic && Array.isArray(topic.subTopic)) {
                            topic.subTopic.forEach((subTopic, subIndex) => {
                                // 跳过已锁定通过的子题
                                if (this.isSubTopicLocked(subTopic)) {
                                    return;
                                }

                                // 检查子题是否有学生作答
                                const hasSubAnswer = subTopic.value && Array.isArray(subTopic.value) && subTopic.value.length > 0;
                                // 检查子题是否已批改
                                const isSubCorrected = subTopic.isPassed !== null && subTopic.isPassed !== undefined;

                                if (hasSubAnswer && !isSubCorrected) {
                                    uncorrectedTopics.push(`第${topicNumber}题第${subIndex + 1}小题（实操题）`);
                                }
                            });
                        }
                    }
                });
            });

            // 如果有未批改的已作答题目，阻止提交
            if (uncorrectedTopics.length > 0) {
                console.error(uncorrectedTopics.join('\n'));
                const message = this.lang.please_make_complete_corrections_tips;
                this.$message.warning(message);
                return;
            }

            // 构建智教培批改数据
            let scoreDetail = [];
            let passCount = 0;
            let lockedPassedCount = 0; // 统计已锁定且通过的题目数量
            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    if (this.isTopicLocked(topic, topicType.type)) {
                        lockedPassedCount++;
                    }
                });
            });
            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    if (topicType.type === "shortAnswer") {
                        if (topic.isPassed !== null && topic.isPassed !== undefined) {
                            scoreDetail.push({
                                id: topic.id,
                                isPassed: topic.isPassed,
                                comment: topic.comment || ""
                            });
                            if (topic.isPassed === true && !this.isTopicLocked(topic, topicType.type)) {
                                passCount++;
                            }
                        }
                    } else if (topicType.type === "operation") {
                        if (topic.subTopic && Array.isArray(topic.subTopic)) {
                            let totalSubTopics = topic.subTopic.length;
                            let newlyGradedSubTopics = [];
                            let newlyPassedCount = 0;
                            let alreadyLockedPassedCount = 0;
                            topic.subTopic.forEach((subTopic) => {
                                if (this.isSubTopicLocked(subTopic)) {
                                    alreadyLockedPassedCount++;
                                } else if (subTopic.isPassed !== null && subTopic.isPassed !== undefined) {
                                    newlyGradedSubTopics.push({
                                        subID: subTopic.subID,
                                        isPassed: subTopic.isPassed,
                                        comment: subTopic.comment || "",
                                    });
                                    if (subTopic.isPassed === true) {
                                        newlyPassedCount++;
                                    }
                                }
                            });
                            const allSubTopicsPassed = (alreadyLockedPassedCount + newlyPassedCount) === totalSubTopics;
                            if (newlyGradedSubTopics.length > 0) {
                                scoreDetail.push({
                                    id: topic.id,
                                    subTopicDetails: newlyGradedSubTopics,
                                    isPassed: allSubTopicsPassed,
                                    comment: topic.comment || "",
                                });
                                if (allSubTopicsPassed && !this.isTopicLocked(topic, topicType.type)) {
                                    passCount++;
                                }
                            }
                        }
                    }
                });
            });
            if (scoreDetail.length === 0) {
                this.$message.warning(this.lang.please_complete_grading_at_least_one_question);
                return;
            }
            const totalPassCount = lockedPassedCount + passCount;
            console.log(`批改统计 - 已锁定通过题目: ${lockedPassedCount}, 本次批改通过: ${passCount}, 总通过数: ${totalPassCount}`);
            console.log(scoreDetail, "scoreDetail");
            service
                .submitTrainingOnlineTestResult({
                    answerID: this.currentGradingData._id,
                    scoreDetail: scoreDetail,
                    passCount: totalPassCount,
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        this.$message.success(this.lang.operate_success);
                        this.back();
                    } else {
                        // this.$message.error(this.lang.submission_failed);
                    }
                })
                .catch((error) => {
                    console.error("submit correction error", error);
                    this.$message.error(this.lang.submission_failed);
                });
        },
        unlockAnswerSheet() {
            service.unlockAnswerSheet({
                answerSheetID: this.paperId,
            });
        },
        // 启动答题加锁
        startAnswerLock() {
            // 不再立即执行加锁，因为在进入页面前已经加锁了
            // 直接设置定时器，每15秒执行一次加锁以保持锁定状态
            this.lockTimer = setInterval(() => {
                this.keepAnswerLock();
            }, this.lockInterval);
        },
        // 停止答题加锁并解锁
        stopAnswerLock() {
            // 清除定时器
            this.clearLockTimer();
            // 执行解锁
            this.unlockAnswer();
        },
        // 仅清除加锁定时器（不调用解锁API）
        clearLockTimer() {
            if (this.lockTimer) {
                clearInterval(this.lockTimer);
                this.lockTimer = null;
            }
        },
        // 保持答题加锁
        keepAnswerLock() {
            const params = {
                testID: this.testID,
                studentID: this.studentInfo.uid,
            };
            service.keepAnswerLock(params)
                .then(result => {
                    if(result.data.error_code === 0){
                        console.log('答题加锁成功:', result);
                    }else{
                        console.error('答题加锁失败:', result);
                        this.handleLockError('答题加锁失败，系统将自动退出试卷');
                    }
                })
                .catch(error => {
                    console.error('答题加锁失败:', error);
                    this.handleLockError('答题加锁网络异常，系统将自动退出试卷');
                });
        },
        // 处理加锁错误，强制退出试卷
        handleLockError(message) {
            // 显示错误提示
            this.$message.error(message);

            // 设置强制退出标志，避免离开确认弹窗
            // 注意：不在这里停止加锁，让beforeRouteLeave统一处理
            this.isForceBack = true;

            // 延迟退出，确保用户能看到错误提示
            setTimeout(() => {
                this.back();
            }, 2000);
        },
        // 解锁答题
        unlockAnswer() {
            const params = {
                testID: this.testID,
                studentID: this.studentInfo.uid,
            };
            service.unlockAnswer(params)
                .then(result => {
                    if(result.data.error_code === 0){
                        console.log('答题解锁成功:', result);
                    }else{
                        console.error('答题解锁失败:', result);
                    }
                })
                .catch(error => {
                    console.error('答题解锁失败:', error);
                });
        },
        teacherCorrectLock() {
            this.correctLockInterval = setInterval(() => {
                service.lockAnswerSheet({
                    answerSheetID: this.paperId,
                });
            }, 30000);
        },
        useTimeInterval() {
            this.interval = setInterval(() => {
                this.useTime += 1;
            }, 1000);
        },
        reindexTopics() {
            // 重新处理题目索引，确保按顺序递增
            if (!this.examContents || !Array.isArray(this.examContents)) {
                return;
            }

            let globalIndex = 1; // 从1开始计数
            this.examContents.forEach(topicType => {
                if (topicType.list && Array.isArray(topicType.list)) {
                    topicType.list.forEach(topic => {
                        // 重新设置题目的index为连续递增的数字
                        this.$set(topic, 'index', globalIndex);
                        globalIndex++;
                    });
                }
            });

            console.log('题目索引重排完成，总题数:', globalIndex - 1);
        },
        initAnswer(answer) {
            console.log('initAnswer', answer,this.examContents);
            if (!this.examContents || !answer) {
                return;
            }

            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    // 使用id匹配题目答案
                    const answerItem = answer.find((item) => item.id === topic.id);

                    if (topicType.type === "shortAnswer") {
                        // 简答题
                        let answerValue = answerItem ? answerItem.value : "";

                        // 如果当前答案为空，从历史记录中获取最近的学生答题记录
                        // 对于学生答题模式：只有题目已通过时才从历史记录获取
                        // 对于批改模式和查看结果模式：无论题目是否通过都从历史记录获取，以便老师查看学生答题内容
                        if (!answerValue && (this.type === CLOUD_TEST_TYPE.CORRECT || this.type === CLOUD_TEST_TYPE.VIEW_RESULT || this.isTopicLocked(topic, topicType.type))) {
                            const latestStudentRecord = topic.answerRecords?.find(record =>
                                record.type === "student_answer" && record.content
                            );
                            if (latestStudentRecord) {
                                answerValue = latestStudentRecord.content;
                                console.log(`简答题 ${topic.id} 从历史记录获取答案:`, answerValue);
                            }
                        }

                        console.log(`设置简答题 ${topic.id} 的答案:`, answerValue);
                        this.$set(topic, "value", answerValue);

                        // 在批改模式下，清除当前会话的批改状态，确保只显示当前会话的批改结果
                        if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                            // 清除可能存在的isPassed字段，确保初始状态为未批改
                            this.$delete(topic, "isPassed");
                            this.$delete(topic, "comment");
                            this.$delete(topic, "correctScore");
                        }

                        // 如果没有作答记录数组，初始化一个空数组
                        if (!topic.answerRecords) {
                            this.$set(topic, "answerRecords", []);
                        }

                    } else if (topicType.type === "operation") {
                        // 实操题
                        if (answerItem && answerItem.subTopic && Array.isArray(answerItem.subTopic)) {
                            // 使用subID匹配子题答案
                            topic.subTopic.forEach((subTopic) => {
                                const subAnswerItem = answerItem.subTopic.find(subAnswer => subAnswer.subID === subTopic.subID);
                                let subAnswerValue = (subAnswerItem && subAnswerItem.value) ? subAnswerItem.value : [];

                                // 如果当前子题答案为空，从历史记录中获取最近的学生答题记录
                                // 对于学生答题模式：只有子题已通过时才从历史记录获取
                                // 对于批改模式和查看结果模式：无论子题是否通过都从历史记录获取，以便老师查看学生答题内容
                                if ((!subAnswerValue || subAnswerValue.length === 0) && (this.type === CLOUD_TEST_TYPE.CORRECT || this.type === CLOUD_TEST_TYPE.VIEW_RESULT || this.isSubTopicLocked(subTopic))) {
                                    const latestStudentRecord = subTopic.answerRecords?.find(record =>
                                        record.type === "student_answer" && record.images && record.images.length > 0
                                    );
                                    if (latestStudentRecord) {
                                        subAnswerValue = latestStudentRecord.images;
                                        console.log(`实操子题 ${subTopic.subID} 从历史记录获取答案:`, subAnswerValue);

                                        // 同时从历史记录中恢复现场录像
                                        if (latestStudentRecord.videoRecords && Array.isArray(latestStudentRecord.videoRecords)) {
                                            this.$set(subTopic, "videoRecords", latestStudentRecord.videoRecords);
                                            console.log(`实操子题 ${subTopic.subID} 从历史记录获取现场录像:`, latestStudentRecord.videoRecords);
                                        }
                                    }
                                }

                                console.log(`设置实操子题 ${subTopic.subID} 的答案:`, subAnswerValue);
                                this.$set(subTopic, "value", subAnswerValue);

                                // 在批改模式下，清除当前会话的批改状态，确保只显示当前会话的批改结果
                                if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                                    // 清除可能存在的isPassed字段，确保初始状态为未批改
                                    this.$delete(subTopic, "isPassed");
                                    this.$delete(subTopic, "comment");
                                }

                                // 重置采集相关的UI状态，确保每次重新进入时都是初始状态
                                this.$set(subTopic, "collecting", false);
                                this.$set(subTopic, "loadingStartCollecting", false);

                                // 恢复视频记录 - 优先使用当前答题会话的视频记录
                                if (subAnswerItem && subAnswerItem.videoRecords && Array.isArray(subAnswerItem.videoRecords)) {
                                    // 确保历史记录中的视频记录不被标记为当前会话
                                    const historicalVideoRecords = subAnswerItem.videoRecords
                                    this.$set(subTopic, "videoRecords", historicalVideoRecords);
                                } else if (!subTopic.videoRecords) {
                                    // 如果当前答题会话没有视频记录，且之前也没有从历史记录中设置，则初始化为空数组
                                    this.$set(subTopic, "videoRecords", []);
                                }
                            });
                        } else {
                            // 没有找到该实操题的答案，初始化所有子题
                            topic.subTopic.forEach(subTopic => {
                                let subAnswerValue = [];

                                // 从历史记录中获取最近的学生答题记录
                                // 对于学生答题模式：只有子题已通过时才从历史记录获取
                                // 对于批改模式和查看结果模式：无论子题是否通过都从历史记录获取，以便老师查看学生答题内容
                                if (this.type === CLOUD_TEST_TYPE.CORRECT || this.type === CLOUD_TEST_TYPE.VIEW_RESULT || this.isSubTopicLocked(subTopic)) {
                                    const latestStudentRecord = subTopic.answerRecords?.find(record =>
                                        record.type === "student_answer" && record.images && record.images.length > 0
                                    );
                                    if (latestStudentRecord) {
                                        subAnswerValue = latestStudentRecord.images;
                                        console.log(`实操子题 ${subTopic.subID} 从历史记录获取答案:`, subAnswerValue);

                                        // 同时从历史记录中恢复现场录像
                                        if (latestStudentRecord.videoRecords && Array.isArray(latestStudentRecord.videoRecords)) {
                                            this.$set(subTopic, "videoRecords", latestStudentRecord.videoRecords);
                                            console.log(`实操子题 ${subTopic.subID} 从历史记录获取现场录像:`, latestStudentRecord.videoRecords);
                                        }
                                    }
                                }

                                console.log(`设置实操子题 ${subTopic.subID} 的答案:`, subAnswerValue);
                                this.$set(subTopic, 'value', subAnswerValue);

                                // 在批改模式下，确保初始状态为未批改
                                if (this.type === CLOUD_TEST_TYPE.CORRECT) {
                                    this.$delete(subTopic, "isPassed");
                                    this.$delete(subTopic, "comment");
                                }

                                // 重置采集相关的UI状态，确保每次重新进入时都是初始状态
                                this.$set(subTopic, "collecting", false);
                                this.$set(subTopic, "loadingStartCollecting", false);

                                // 确保videoRecords字段被初始化
                                if (!subTopic.videoRecords) {
                                    this.$set(subTopic, "videoRecords", []);
                                }
                            });
                        }
                    }
                });
            });
        },
        initScoreDetail(scoreDetail) {
            if (!scoreDetail || !this.examContents) {
                return;
            }

            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    const scoreItem = scoreDetail.find((item) => item.index === topic.index);
                    if (scoreItem) {
                        topic.correctScore = scoreItem.score;
                    }
                });
            });
        },

        jumpToQuestion(index) {
            // 更新当前题目索引
            this.currentQuestionIndex = index;

            // 找到对应的题目并滚动到视图
            this.$nextTick(() => {
                const topicElements = this.$el.querySelectorAll(".topic_detail");
                if (topicElements[index]) {
                    topicElements[index].scrollIntoView({ behavior: "smooth" });
                }
            });
        },
        jumpToSubTopic(topicTypeIndex, topicIndex, subIndex) {
            // 计算并更新当前题目索引
            let globalIndex = 0;
            for (let i = 0; i < topicTypeIndex; i++) {
                globalIndex += this.examContents[i].list.length;
            }
            globalIndex += topicIndex;
            this.currentQuestionIndex = globalIndex;

            // 找到对应的子题目并滚动到视图
            this.$nextTick(() => {
                const topic = this.examContents[topicTypeIndex].list[topicIndex];
                const subtopic = topic.subTopic[subIndex];
                const subtopicId = `${topic.id}-${subtopic.subID}`;
                const subtopicElement = this.$el.querySelector(`[data-subtopic-id="${subtopicId}"]`);

                if (subtopicElement) {
                    subtopicElement.scrollIntoView({ behavior: "smooth", block: "center" });
                } else {
                    // 如果找不到子题目元素，回退到大题目滚动
                    const topicElements = this.$el.querySelectorAll(".topic_detail");
                    if (topicElements[globalIndex]) {
                        topicElements[globalIndex].scrollIntoView({ behavior: "smooth" });
                    }
                }
            });
        },
        setCurrentQuestionByTypeIndex(typeIndex, topicIndex) {
            let index = 0;
            for (let i = 0; i < typeIndex; i++) {
                index += this.examContents[i].list.length;
            }
            index += topicIndex;
            this.currentQuestionIndex = index;
        },
        handleUpdateValue(topic, value) {
            topic.value = value;
        },
        handleUpdatePassStatus(topic, value) {
            // 使用Vue.set确保响应式更新
            this.$set(topic, "isPassed", value);
            // 强制更新视图
            this.$nextTick(() => {
                this.$forceUpdate();
            });
        },
        handleUpdateComment(topic, value) {
            // 使用Vue.set确保响应式更新评论
            this.$set(topic, "comment", value);
        },
        handleUpdateSubtopicPassStatus(topic, subID, isPassed) {
            // 更新子题的通过状态 - 通过subID精确匹配
            if (topic.subTopic && Array.isArray(topic.subTopic)) {
                const subTopic = topic.subTopic.find(sub => sub.subID === subID);
                if (subTopic) {
                    this.$set(subTopic, "isPassed", isPassed);
                }
            }
        },
        handleUpdateSubtopicComment(topic, subID, comment) {
            // 更新子题的评论 - 通过subID精确匹配
            if (topic.subTopic && Array.isArray(topic.subTopic)) {
                const subTopic = topic.subTopic.find(sub => sub.subID === subID);
                if (subTopic) {
                    this.$set(subTopic, "comment", comment);
                }
            }
        },
        getAnswer() {
            let answer = [];

            if (!this.examContents) {
                return { answer };
            }

            this.examContents.forEach((topicType) => {
                topicType.list.forEach((topic) => {
                    if (this.isTopicLocked(topic, topicType.type)) {
                        return; // 如果题目被锁定，则不包含在提交的答案中
                    }
                    if (topicType.type === "shortAnswer") {
                        // 简答题
                        if(topic.value && topic.value.trim()){
                            answer.push({
                                value: topic.value,
                                id:topic.id,
                            });
                        }

                    } else if (topicType.type === "operation") {
                        // 实操题
                        let subTopicAnswer = [];
                        topic.subTopic.forEach((subTopic) => {
                            // 跳过已通过的子题
                            if (this.isSubTopicLocked(subTopic)) {
                                return;
                            }
                            if (subTopic.value && subTopic.value.length > 0) {
                                const subTopicData = {
                                    value: subTopic.value || [],
                                    subID: subTopic.subID,
                                };

                                // 如果存在视频记录，一起包含在答案中
                                if (subTopic.videoRecords && subTopic.videoRecords.length > 0) {
                                    subTopicData.videoRecords = subTopic.videoRecords;
                                }

                                subTopicAnswer.push(subTopicData);
                            }
                        });

                        // 只有存在未通过子题的答案时，才加入answer
                        if (subTopicAnswer.length > 0) {
                            answer.push({
                                subTopic: subTopicAnswer,
                                id: topic.id,
                            });
                        }
                    }
                });
            });

            return { answer };
        },
        back() {
            this.$router.back();
        },
        openFileTransfer() {
            return new Promise(async(resolve, reject) => {
                try {
                    const service_type = this.systemConfig.ServiceConfig.type.FileTransferAssistant;
                    let fileTransferAssistant = await findServiceId(service_type);
                    if (fileTransferAssistant.cid) {
                        this.fileTransferAssistantCid = fileTransferAssistant.cid;
                        this.openConversation(fileTransferAssistant.cid, 10, 0, (is_succ, conversation) => {
                            this.openFileTransferListener(conversation.id);
                        });
                    } else {
                        this.$root.socket.emit(
                            "request_start_single_chat_conversation",
                            {
                                list: [fileTransferAssistant.id, this.user.uid],
                                start_type: undefined,
                                mode: this.systemConfig.ConversationConfig.mode.Single,
                                type: this.systemConfig.ConversationConfig.type.Single,
                            },
                            async (is_succ, data) => {
                                if (is_succ) {
                                    this.$store.commit("conversationList/initConversation", data);
                                    this.$store.commit("examList/initExamObj", data);
                                    await Tool.handleAfterConversationCreated(data, "openConversation");
                                    this.openFileTransferListener(data);
                                    this.fileTransferAssistantCid = data
                                } else {
                                    this.$message.error(this.lang.start_conversation_error);
                                }
                            }
                        );
                    }
                    resolve();
                } catch (error) {
                    reject(error);
                }
            });
        },
        openFileTransferListener(cid) {
            // 监听传输助手消息通知
            const controler = this.conversationList[cid].socket;
            if (!controler) {
                setTimeout(() => {
                    this.openFileTransferListener(cid);
                }, 1000);
            } else {
                this.closeFileTransferListener();
                controler.on("other_say", this.handleFileTransferOtherSay);
                controler.on("receive_group_message", this.handleFileTransferReceiveGroupMessage);
                this.fileTransferAssistant = controler;
            }
        },
        closeFileTransferListener() {
            if (this.fileTransferAssistant) {
                this.fileTransferAssistant.off("other_say", this.handleFileTransferOtherSay);
                this.fileTransferAssistant.off("receive_group_message", this.handleFileTransferReceiveGroupMessage);
                this.fileTransferAssistant = null;
            }
        },
        /**
         * 处理文件传输助手的other_say
         * 这个方法会一直监听，直到组件销毁
         * @param {Array} messageList - 消息列表
         */
        handleFileTransferOtherSay(messageList) {
            console.error("handleFileTransferOtherSay - 收到文件传输助手消息", messageList);

            // 检查是否正在采集状态，只有在采集时才处理图像采集
            if (this.isCollecting) {
                // 如果开启了防作弊，需要额外检查是否已经与u-Linker建立连接
                if (this.collectingInfo && this.collectingInfo.needAntiCheat) {
                    // 防作弊模式下，只有在u-Linker连接成功后才处理消息
                    // 通过检查心跳定时器是否存在来判断是否已连接成功
                    if (this.heartbeatTimer) {
                        this.collectImage(messageList);
                    } else {
                        console.log("handleFileTransferOtherSay - 防作弊模式下u-Linker未连接，忽略消息");
                    }
                } else {
                    // 非防作弊模式，直接处理消息
                    this.collectImage(messageList);
                }
            } else {
                console.log("handleFileTransferOtherSay - 当前未在采集状态，忽略消息");
            }

        },
        /**
         * 处理文件传输助手的receive_group_message
         * 这个方法会一直监听，直到组件销毁
         * @param {Array} messageList - 消息列表
         */
        /**
         * 检查视频封面是否生成成功
         * @param {string} coverUrl - 封面URL
         * @returns {Promise<boolean>} - 封面是否可用
         */
        async checkVideoCoverAvailable(coverUrl) {
            if (!coverUrl) {
                return false;
            }

            return new Promise((resolve) => {
                const img = new Image();

                // 设置超时，避免无限等待
                const timeout = setTimeout(() => {
                    img.onload = null;
                    img.onerror = null;
                    resolve(false);
                }, 3000); // 3秒超时

                img.onload = () => {
                    clearTimeout(timeout);
                    resolve(true);
                };

                img.onerror = () => {
                    clearTimeout(timeout);
                    resolve(false);
                };

                img.src = coverUrl;
            });
        },

        /**
         * 等待视频封面生成，最多等待8秒
         * @param {string} coverUrl - 封面URL
         * @returns {Promise<boolean>} - 封面是否最终可用
         */
        async waitForVideoCover(coverUrl) {
            if (!coverUrl) {
                return false;
            }

            const maxWaitTime = 8000; // 最多等待8秒
            const checkInterval = 1000; // 每1秒检查一次
            const maxAttempts = Math.floor(maxWaitTime / checkInterval);

            console.log(`开始等待视频封面生成: ${coverUrl}`);

            for (let attempt = 1; attempt <= maxAttempts; attempt++) {
                console.log(`第${attempt}次检查封面是否可用...`);

                const isAvailable = await this.checkVideoCoverAvailable(coverUrl);

                if (isAvailable) {
                    console.log(`封面生成成功，第${attempt}次尝试后可用`);
                    return true;
                }

                // 如果不是最后一次尝试，等待1秒后再试
                if (attempt < maxAttempts) {
                    console.log(`封面暂不可用，1秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, checkInterval));
                }
            }

            console.log(`等待${maxWaitTime/1000}秒后，封面仍不可用，放弃使用封面`);
            return false;
        },

        async handleFileTransferReceiveGroupMessage(message) {
            console.error("handleFileTransferReceiveGroupMessage - 收到文件传输助手消息", message);
            if(message.msg_type === this.systemConfig.msg_type.RealTimeVideoReview){ //直播回放
                // 如果开启了防作弊，需要检查是否已经与u-Linker建立连接
                if (this.collectingInfo && this.collectingInfo.needAntiCheat) {
                    // 防作弊模式下，只有在u-Linker连接成功后才处理现场录像消息
                    if (!this.heartbeatTimer) {
                        console.log("handleFileTransferReceiveGroupMessage - 防作弊模式下u-Linker未连接，忽略现场录像消息");
                        return;
                    }
                }

                let videoUrl = message.ultrasound_url || message.mp4FileUrl
                let videoCoverUrl = message.coverUrl

                // 根据回调消息中的topicInfo来决定视频插入位置
                const topicInfo = message.live_record_data?.topicInfo;
                console.log("收到现场录像，题目信息:", topicInfo);

                if (topicInfo && videoUrl) {
                    // 根据topicInfo查找对应的题目位置
                    const targetLocation = this.findTopicLocation(topicInfo);

                    if (targetLocation) {
                        const { topicTypeIndex, topicIndex, subIndex } = targetLocation;
                        const topicType = this.examContents[topicTypeIndex];
                        const topic = topicType.list[topicIndex];

                        // 根据题目类型获取目标对象
                        let targetObject = null;
                        if (topicType.type === 'operation' && topic.subTopic && topic.subTopic[subIndex]) {
                            targetObject = topic.subTopic[subIndex];
                        } else if (topicType.type === 'shortAnswer') {
                            targetObject = topic;
                        }

                        console.log("找到目标题目位置:", {
                            topicTypeIndex,
                            topicIndex,
                            subIndex,
                            topicType: topicType.type,
                            topicInfo
                        });

                        if (targetObject) {
                            // 确保 videoRecords 字段存在
                            if (!targetObject.videoRecords) {
                                this.$set(targetObject, "videoRecords", []);
                            }

                            // 如果有封面URL，等待封面生成
                            let finalCoverUrl = null;
                            if (videoCoverUrl) {
                                console.log("检测到视频封面URL，开始验证封面是否可用...");
                                const coverAvailable = await this.waitForVideoCover(videoCoverUrl);
                                if (coverAvailable) {
                                    finalCoverUrl = videoCoverUrl;
                                    console.log("封面验证成功，将使用封面");
                                } else {
                                    console.log("封面验证失败，将不使用封面");
                                }
                            }

                            // 创建视频记录对象
                            const videoRecord = {
                                url: videoUrl,
                                coverUrl: finalCoverUrl,
                                timestamp: new Date().toISOString(),
                                type: 'video/mp4',
                                isVideo: true
                            };

                            // 添加到videoRecords数组
                            targetObject.videoRecords.push(videoRecord);

                            console.log("防作弊录制回放已添加到题目", {
                                topicTypeIndex,
                                topicIndex,
                                subIndex,
                                topicType: topicType.type,
                                videoRecord,
                                totalVideoRecords: targetObject.videoRecords.length
                            });

                            // 强制更新视图
                            this.$forceUpdate();
                        }
                    } else {
                        console.warn("未找到对应的题目位置，无法插入现场录像", topicInfo);
                    }
                } else {
                    console.warn("现场录像消息缺少题目信息或视频URL", { topicInfo, videoUrl });
                }
            }
        },

        /**
         * 根据题目信息查找题目在examContents中的位置
         * @param {Object} topicInfo - 题目信息 {subID, testID, topicID, trainingID}
         * @returns {Object|null} - 返回位置信息 {topicTypeIndex, topicIndex, subIndex} 或 null
         */
        findTopicLocation(topicInfo) {
            const { subID, topicID, testID, trainingID } = topicInfo;

            // 验证基本信息是否匹配
            if (testID !== this.testID || trainingID !== this.trainingID) {
                console.warn("题目信息中的testID或trainingID不匹配", {
                    received: { testID, trainingID },
                    current: { testID: this.testID, trainingID: this.trainingID }
                });
                return null;
            }

            // 遍历examContents查找匹配的题目
            for (let topicTypeIndex = 0; topicTypeIndex < this.examContents.length; topicTypeIndex++) {
                const topicType = this.examContents[topicTypeIndex];

                for (let topicIndex = 0; topicIndex < topicType.list.length; topicIndex++) {
                    const topic = topicType.list[topicIndex];

                    // 匹配topicID
                    if (topic.id === topicID) {
                        // 如果是实操题，需要进一步匹配subID
                        if (topicType.type === 'operation' && topic.subTopic && Array.isArray(topic.subTopic)) {
                            for (let subIndex = 0; subIndex < topic.subTopic.length; subIndex++) {
                                const subTopic = topic.subTopic[subIndex];
                                if (subTopic.subID === subID) {
                                    return { topicTypeIndex, topicIndex, subIndex };
                                }
                            }
                        } else if (topicType.type === 'shortAnswer') {
                            // 简答题没有subTopic，直接返回
                            return { topicTypeIndex, topicIndex, subIndex: 0 };
                        }
                    }
                }
            }

            console.warn("未找到匹配的题目位置", topicInfo);
            return null;
        },

        /**
         * 采集图像处理方法
         * 只有在采集状态下才会被调用
         * @param {Array} messageList - 消息列表
         */
        collectImage(messageList) {
            console.log("collectImage - 开始处理图像采集", messageList);
            const msg_type = this.systemConfig.msg_type;
            messageList.forEach(async (msg) => {
                if (
                    msg.msg_type === msg_type.Frame ||
                    msg.msg_type === msg_type.Cine ||
                    msg.msg_type === msg_type.EXAM_IMAGES ||
                    msg.msg_type === msg_type.OBAI
                ) {
                    //只采集单帧，多帧和聚合消息,和产科质控
                    let type = msg.msg_type;
                    if (msg.msg_type === msg_type.EXAM_IMAGES) {
                        type = msg.cover_msg_type;
                    }
                    let downloadUrl = "";
                    if (type === msg_type.Frame) {
                        downloadUrl = msg.url.replace("thumbnail.jpg", `SingleFrame.${msg.img_encode_type}`);
                    } else if (type === msg_type.Cine) {
                        downloadUrl = msg.url.replace("thumbnail.jpg", `DeviceVideo.${msg.img_encode_type}`);
                    } else if (type === msg_type.OBAI) {
                        downloadUrl = msg.url.replace("thumbnail.jpg", `ScreenShot.${msg.img_encode_type}`);
                    }
                    if (downloadUrl) {
                        const response = await fetch(downloadUrl);
                        const blob = await response.blob();
                        const file = new File([blob], `${msg.file_id}.${msg.img_encode_type}`, {
                            type: blob.type, // 你可以从blob中获取MIME类型
                        });
                        this.uploadCollectImage(file, this.collectIndex);
                        console.log("collectImage - 处理完成", file);
                    }
                }
            });
        },
        async openCollect(topicTypeIndex, topicIndex, subIndex, subtopic) {
            console.log('openCollect', topicTypeIndex, topicIndex, subIndex, subtopic);
            console.log('antiCheatEnabled:', this.currentTestData.antiCheatEnabled);

            try {
                // 检查是否已经有题目在采集
                if (this.isCollecting) {
                    this.$message.warning('已有题目正在采集中，请先停止当前采集再开始新的采集');
                    return;
                }

                // 立即设置采集状态，防止并发点击
                this.isCollecting = true;

                // 根据antiCheatEnabled字段决定是否需要防作弊
                const needAntiCheat = this.currentTestData.antiCheatEnabled === 1;

                if (needAntiCheat) {
                    // 需要防作弊：显示完整的确认弹窗，要求与灵柯通信
                    try {
                        await this.$confirm(
                            '',
                            this.lang.confirmation_before_collection,
                            {
                                confirmButtonText: this.lang.start_collecting,
                                cancelButtonText: this.lang.cancel_btn,
                                type: 'info',
                                showClose: false,
                                closeOnClickModal: false,
                                closeOnPressEscape: false,
                                dangerouslyUseHTMLString: true,
                                message: `
                                    <div style="text-align: left; line-height: 1.6;">
                                        <p style="margin-bottom: 12px;">${this.lang.confirmation_before_collection_tips1}</p>
                                        <p style="margin: 8px 0;">${this.lang.confirmation_before_collection_tips2}</p>
                                        <p style="margin: 8px 0;">${this.lang.confirmation_before_collection_tips3}</p>
                                        <p style="margin: 8px 0;">${this.lang.confirmation_before_collection_tips4}</p>
                                        <p style="margin: 12px 0; color: #E6A23C; font-weight: bold;">${this.lang.confirmation_before_collection_tips5}</p>
                                    </div>
                                `
                            }
                        );
                    } catch (error) {
                        // 用户取消了操作
                        console.log('用户取消了采集操作');
                        this.isCollecting = false; // 重置采集状态
                        this.collectingInfo = null; // 清除采集信息
                        return;
                    }
                } else {
                    // 不需要防作弊：简化确认弹窗
                    try {
                        await this.$confirm(
                            '',
                            this.lang.start_collecting,
                            {
                                confirmButtonText: this.lang.start_collecting,
                                cancelButtonText: this.lang.cancel_btn,
                                type: 'info',
                                dangerouslyUseHTMLString: true,
                                message: `
                                    <div style="text-align: left; line-height: 1.6;">
                                        <p style="margin-bottom: 12px;">${this.lang.sure_start_collecting_tips}</p>
                                        <p style="margin: 12px 0; color: #E6A23C; font-weight: bold;">${this.lang.confirmation_before_collection_tips5}</p>
                                    </div>
                                `
                            }
                        );
                    } catch (error) {
                        // 用户取消了操作
                        console.log('用户取消了采集操作');
                        this.isCollecting = false; // 重置采集状态
                        this.collectingInfo = null; // 清除采集信息
                        return;
                    }
                }

                // 防作弊模式下，需要优先检查设备连接状态
                if (needAntiCheat) {
                    const isLiveReady = await this.checkConferenceExamIn();
                    if (!isLiveReady) {
                        this.isCollecting = false; // 重置采集状态
                        this.collectingInfo = null; // 清除采集信息
                        return;
                    }
                }

                // 用户确认开始采集后，立即设置采集信息，确保消息处理逻辑能正确识别防作弊模式
                this.collectIndex = [topicTypeIndex, topicIndex, subIndex];

                // 获取当前题目信息
                const currentTopic = this.examContents[topicTypeIndex].list[topicIndex];
                const collectInfo = {
                    trainingID: this.trainingID,
                    testID: this.testID,
                    topicID: currentTopic.id,
                    subID: subtopic.subID
                };

                // **关键修复：在开启文件传输助手监听之前，先设置完整的采集状态信息**
                // 这样确保handleFileTransferOtherSay能正确识别防作弊模式并在u-Linker连接前忽略消息
                this.collectingInfo = {
                    topicTypeIndex,
                    topicIndex,
                    subIndex,
                    collectInfo,
                    needAntiCheat // 记录是否需要防作弊
                };

                this.safeUpdateSubtopicState(subtopic, "loadingStartCollecting", true);

                // 确保 videoRecords 字段被初始化
                if (!subtopic.videoRecords) {
                    this.$set(subtopic, "videoRecords", []);
                }

                // 打开文件传输
                await this.openFileTransfer();
                console.log('openCollect - 文件传输已打开');

                if (needAntiCheat) {
                    // 需要防作弊：与灵柯通信并监听现场录像结果
                    console.log('开启防作弊模式，等待ULinker响应');
                    await this.waitingULinkerLiveReady(collectInfo);

                    // 启动心跳轮询，定时确认对端采集状态
                    this.startCollectingHeartbeat(collectInfo);
                } else {
                    // 不需要防作弊：直接开始采集，不需要与灵柯通信
                    console.log('非防作弊模式，直接开始采集');
                }

                // 设置UI状态（采集状态信息已经在上面设置过了）
                this.safeUpdateSubtopicState(subtopic, "loadingStartCollecting", false);
                this.safeUpdateSubtopicState(subtopic, "collecting", true);

                // 启动采集倒计时
                this.startCollectCountdown();



            } catch (e) {
                console.error(e);
                // 根据错误类型给出不同的提示
                if (e.code === 'TIMEOUT') {
                    this.$message.error(this.lang.start_collecting_timeout_tips);
                } else {
                    this.$message.error(this.lang.failed_to_start_collection);
                }

                // 清除采集状态和UI状态
                this.isCollecting = false;
                this.collectingInfo = null;
                this.safeUpdateSubtopicState(subtopic, "collecting", false);
                this.safeUpdateSubtopicState(subtopic, "loadingStartCollecting", false);
                // 不关闭文件传输助手监听，保持监听直到组件销毁
                // this.closeFileTransferListener();
                this.destroyMsgFromOwnerByPC();
            } finally {
                this.safeUpdateSubtopicState(subtopic, "loadingStartCollecting", false);


                // 启动视频定时推送
                // if (!this.videoTimer) {
                //     this.videoTimer = setInterval(() => {
                //         const [typeIdx, topicIdx, subIdx] = this.collectIndex;
                //         const currentSubtopic = this.examContents[typeIdx].list[topicIdx].subTopic[subIdx];

                //         // Add video to videoRecords instead of value
                //         if (!currentSubtopic.videoRecords) {
                //             this.$set(currentSubtopic, "videoRecords", []);
                //         }
                //         currentSubtopic.videoRecords.push(this.generateMockVideo());
                //         console.log("currentSubtopic.videoRecords", currentSubtopic.videoRecords);
                //     }, 1000);
                // }
            }
        },
        closeCollect(topicTypeIndex, topicIndex, subIndex, subtopic) {
            // 检查是否当前正在采集状态
            if (!this.isCollecting) {
                return;
            }

            console.log('closeCollect - 停止采集');

            // 检查当前采集是否需要防作弊
            const needAntiCheat = this.collectingInfo && this.collectingInfo.needAntiCheat;

            if (needAntiCheat) {
                // 防作弊模式：停止心跳轮询和灵柯通信
                this.stopCollectingHeartbeat();

                // 获取当前题目信息
                const currentTopic = this.examContents[topicTypeIndex].list[topicIndex];
                const collectInfo = {
                    trainingID: this.trainingID,
                    testID: this.testID,
                    topicID: currentTopic.id,
                    subID: subtopic.subID
                };

                this.sendMsgToOwnerByPC('stopLiveToFileTransferAssistant', collectInfo);
                this.destroyMsgFromOwnerByPC();

                console.log('防作弊模式 - 已停止与灵柯的通信');
            } else {
                // 非防作弊模式：直接停止采集
                console.log('非防作弊模式 - 直接停止采集');
            }

            this.hasClickedStopCollect = true;
            this.collectIndex = [0, 0, 0];
            this.safeUpdateSubtopicState(subtopic, "collecting", false);

            // 注意：不再关闭文件传输助手监听，只是停止采集状态
            // this.closeFileTransferListener(); // 移除这行，保持监听直到组件销毁

            // 清除全局采集状态
            this.isCollecting = false;
            this.collectingInfo = null;

            // 清除采集倒计时
            this.clearCollectCountdown();

            // 清除视频定时器
            if (this.videoTimer) {
                clearInterval(this.videoTimer);
                this.videoTimer = null;
            }

        },
        uploadStart(index, j_index, subIndex) {
            if (this.type === CLOUD_TEST_TYPE.EDIT && subIndex === "-1") {
                this.uploadIndex = [index, j_index];
            } else {
                this.uploadIndex = [index, j_index, subIndex];
            }
            // this.uploadIndex = [index, j_index, subIndex];

            // 保存当前滚动位置
            const scrollTopDoc = document.documentElement.scrollTop || document.body.scrollTop;
            const container = this.$refs.topicContent;
            const scrollTopContainer = container ? container.scrollTop : 0;

            this.$refs.uploadComponent[0].value = ""; // 处理无法连续上传相同文件
            this.$refs.uploadComponent[0].click();

            // 立即恢复滚动位置
            this.$nextTick(() => {
                if (container) {
                    container.scrollTop = scrollTopContainer;
                }
                document.documentElement.scrollTop = scrollTopDoc;
                document.body.scrollTop = scrollTopDoc;
            });
        },
        handleFileChange(e) {
            let files = e.target.files;

            // 如果用户没有选择文件（取消了文件选择），无需设置loading状态
            if (!files || files.length === 0) {
                return;
            }

            // 文件选择后，开始上传前，设置对应题目的loading状态
            const index = this.uploadIndex[0];
            const j_index = this.uploadIndex[1];
            const currentTopic = this.examContents[index].list[j_index];
            this.$set(currentTopic, 'isUploading', true);

            let key,
                uploadList = [];
            for (const file of files) {
                if (file.size > 20 * 1024 * 1024) {
                    this.$message.error(`${this.lang.upload_max_text}20M`);
                    currentTopic.isUploading = false; // 文件大小错误，清除loading状态
                    return;
                }
                if (file.size == 0) {
                    this.$message.error(`${this.lang.upload_min_text}0M`);
                    currentTopic.isUploading = false; // 文件大小错误，清除loading状态
                    return;
                }

                // 检查文件格式
                const fileExt = file.name.split(".").pop().toLowerCase();

                // 验证文件类型是否在支持的图片或视频类型列表中
                const supportedImageTypes = this.getSupportImageType();
                const supportedVideoTypes = this.getSupportVideoType();
                const isValidFileType = supportedImageTypes.includes(fileExt) || supportedVideoTypes.includes(fileExt);

                if (!isValidFileType) {
                    const supportedTypes = [...supportedImageTypes, ...supportedVideoTypes].join(', ');
                    this.$message.error(this.lang.supply_exam_image.err_tip.unsupported_file_format)
                    currentTopic.isUploading = false; // 文件格式错误，清除loading状态
                    return;
                }
                this.uploadCollectImage(file, this.uploadIndex);
            }
        },
        uploadCollectImage(file, uploadIndex) {
            let dir = new Date().getTime() + parseInt(Math.random() * 1000 + 1000, 10); // 目录
            var date = new Date();
            var time =
                date.getFullYear() +
                "-" +
                (date.getMonth() < 9 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1) +
                "-" +
                (date.getDate() < 10 ? "0" + date.getDate() : date.getDate());
            let filePath = `homework/operationImage/${time}/${dir}/${file.name}`;
            let uploadConfig = this.systemConfig.serverInfo.file_upload_config;
            //OSS
            uploadFile({
                bucket: uploadConfig.ossInfo.bucket,
                filePath: filePath,
                file,
                callback: (event, data) => {
                    console.log("uploadFile", event, data);
                    if ("complete" == event) {
                        let url = uploadConfig.ossInfo.playback_https_addr + "/" + filePath;
                        const index = uploadIndex[0];
                        const j_index = uploadIndex[1];
                        const subIndex = uploadIndex[2];
                        const msg_type = Tool.getMsgType(filePath);

                        if (this.type === CLOUD_TEST_TYPE.EDIT) {
                            if (uploadIndex.length === 2) {
                                // 题目主体的图片
                                if (!this.examContents[index].list[j_index].imageList) {
                                    this.examContents[index].list[j_index].imageList = [];
                                }
                                this.examContents[index].list[j_index].imageList.push({
                                    msg_type: msg_type,
                                    url: url,
                                });
                            } else {
                                // 子题目的图片
                                if (!this.examContents[index].list[j_index].subTopic[subIndex].imageList) {
                                    this.examContents[index].list[j_index].subTopic[subIndex].imageList = [];
                                }
                                this.examContents[index].list[j_index].subTopic[subIndex].imageList.push({
                                    msg_type: msg_type,
                                    url: url,
                                });
                            }
                        } else {
                            // 原有上传逻辑
                            this.examContents[index].list[j_index].subTopic[subIndex].value.push({
                                msg_type: msg_type,
                                url: url,
                            });
                        }
                        // 上传完成，清除对应题目的loading状态
                        const currentTopic = this.examContents[uploadIndex[0]].list[uploadIndex[1]];
                        currentTopic.isUploading = false;
                    } else if ("error" == event) {
                        this.$message.error(this.lang.upload_file_error_text);
                        // 上传失败，清除对应题目的loading状态
                        const currentTopic = this.examContents[uploadIndex[0]].list[uploadIndex[1]];
                        currentTopic.isUploading = false;
                    }
                },
            });
        },
        generateMockVideo() {
            return {
                msg_type: 4, // 视频类型标识
                url: `mock/video_${Date.now()}.mp4`,
                timestamp: new Date().toISOString()
            };
        },
        uploadAndAddToList(file, topic, subIndex) {
            const isImage = this.getSupportImageType().some((type) =>
                file.type.toLowerCase().includes(type.toLowerCase())
            );
            const isVideo = this.getSupportVideoType().some((type) =>
                file.type.toLowerCase().includes(type.toLowerCase())
            );

            if (!isImage && !isVideo) {
                this.$message.error(this.lang.file_type_error);
                return;
            }

            // 上传文件
            uploadFile(file)
                .then((res) => {
                    if (res.url) {
                        const fileObj = {
                            msg_type: isImage ? 3 : 4,
                            url: res.url,
                            size: file.size,
                            name: file.name,
                        };

                        if (subIndex === "-1") {
                            // 添加到题目图片
                            if (!topic.imageList) {
                                topic.imageList = [];
                            }
                            topic.imageList.push(fileObj);
                        } else {
                            // 添加到子题图片
                            if (!topic.subTopic[subIndex].value) {
                                topic.subTopic[subIndex].value = [];
                            }
                            topic.subTopic[subIndex].value.push(fileObj);
                        }

                        this.$forceUpdate();
                    }
                })
                .catch((err) => {
                    this.$message.error(this.lang.upload_failed);
                });
        },
        handleImageOrderChange(imageList, evt) {
            const { oldIndex, newIndex } = evt;
            if (oldIndex !== newIndex) {
                // 手动执行数组元素的移动操作
                const itemToMove = imageList[oldIndex];
                // 从数组中移除
                imageList.splice(oldIndex, 1);
                // 在新位置插入
                imageList.splice(newIndex, 0, itemToMove);
            } else {
                console.log("NO CHANGE");
            }
        },
        getTrainingStudentInfo() {
            return new Promise((resolve,reject)=>{
                service
                    .getTrainingStudentInfo({
                        trainingID: this.trainingID,
                    })
                    .then((res) => {
                        const data = res.data;
                        if (data.error_code === 0) {
                            this.studentName = data.data.name;
                            this.hospital = data.data.hospital;
                            this.studentInfo = data.data;
                            resolve(data.data);
                        }else{
                            reject(data.error_msg);
                        }
                    });
            });
        },
        sendMsgToOwnerByPC(action,data = {}) {
            window.main_screen.sendMsgOwner(
                {
                    action: action,
                    body: {
                        deviceType: 'PC',
                        targetDeviceType:'ULinker',
                        ...data
                    },
                }
            )
        },
        receiveMsgFromOwnerByPC() {
            window.main_screen.controller.on('notify_msg_from_owner',this.handleMsgFromOwnerByPC);
        },
        destroyMsgFromOwnerByPC() {
            if (this.handleMsgFromOwnerByPC) { // 确保方法存在
                window.main_screen.controller.off('notify_msg_from_owner', this.handleMsgFromOwnerByPC);
            }
            // Clean up promise callbacks if they haven't been settled
            this.liveReadPromiseResolve = null;
            this.liveReadPromiseReject = null;
        },
        handleMsgFromOwnerByPC(data) {
            if (data.action === 'NotifyStartLiveToFileTransferAssistant' && data.body.targetDeviceType === 'PC') {
                console.error('notify_msg_from_owner in handleMsgFromOwnerByPC', data); // Updated log for clarity
                if (data.body.error_code === 0) {
                    if (this.liveReadPromiseResolve) {
                        this.liveReadPromiseResolve();
                    }
                } else {
                    if (this.liveReadPromiseReject) {
                        this.liveReadPromiseReject(data.body);
                    }
                }
                // Once the promise is settled, clear the callbacks
                this.liveReadPromiseResolve = null;
                this.liveReadPromiseReject = null;
            }else if(data.action === 'NotifyStopLiveToFileTransferAssistant' && data.body.targetDeviceType === 'PC'){
                // 直播结束，需要结束当前这个正在进行的采集
                this.$message.error(this.lang.live_recording_ended_collection_stopped_tips);
                if (this.isCollecting && this.collectingInfo) {
                    const { topicTypeIndex, topicIndex, subIndex } = this.collectingInfo;
                    const currentSubtopic = this.examContents[topicTypeIndex]?.list[topicIndex]?.subTopic[subIndex];
                    this.stopCurrentCollection(currentSubtopic);
                }
                this.stopCollectingHeartbeat();
            }else if(data.action === 'NotifyAntiCheatLiveStatus' && data.body.targetDeviceType === 'PC'){
                console.error('收到对端防作弊直播状态心跳确认 NotifyAntiCheatLiveStatus', data); // Updated log for clarity
                // 收到对端防作弊直播状态心跳确认
                if(data.body.antiCheatLive){
                    // 重置心跳丢失计数
                    this.missedHeartbeatCount = 0;
                    this.lastHeartbeatConfirmTime = Date.now();
                }else{
                    // 对端已停止采集，停止本地采集，并清理相关状态
                    this.$message.error(this.lang.live_recording_lost_connection_collection_stopped_tips);
                    if(this.collectingInfo){
                        const { topicTypeIndex, topicIndex, subIndex } = this.collectingInfo;
                        const currentSubtopic = this.examContents[topicTypeIndex]?.list[topicIndex]?.subTopic[subIndex];
                        this.stopCurrentCollection(currentSubtopic);
                    }
                    this.stopCollectingHeartbeat();
                }
            }
        },
        waitingULinkerLiveReady(collectInfo) {
            return new Promise((resolve, reject) => {
                // 先清除旧的监听和Promise回调，确保幂等性
                console.log('waitingULinkerLiveRead');
                this.destroyMsgFromOwnerByPC();
                this.liveReadPromiseResolve = resolve;
                this.liveReadPromiseReject = reject;
                this.receiveMsgFromOwnerByPC();
                this.sendMsgToOwnerByPC('startLiveToFileTransferAssistant', collectInfo);

                // 设置30秒超时定时器
                const timeoutId = setTimeout(() => {
                    // 超时处理
                    this.destroyMsgFromOwnerByPC();
                    this.liveReadPromiseResolve = null;
                    this.liveReadPromiseReject = null;

                    const timeoutError = new Error('等待ULinker响应超时（30秒）');
                    timeoutError.code = 'TIMEOUT';
                    reject(timeoutError);
                }, 30000); // 30秒超时

                // 保存原始的resolve和reject，用包装函数替代
                const originalResolve = this.liveReadPromiseResolve;
                const originalReject = this.liveReadPromiseReject;

                this.liveReadPromiseResolve = () => {
                    clearTimeout(timeoutId);
                    if (originalResolve) {
                        originalResolve();
                    }
                };

                this.liveReadPromiseReject = (error) => {
                    clearTimeout(timeoutId);
                    if (originalReject) {
                        originalReject(error);
                    }
                };
            });
        },
        forceBack(){
            // 停止加锁定时器并解锁
            this.stopAnswerLock();
            this.isForceBack = true;
            this.back();
        },

        /**
         * 停止当前采集
         * @param {Object} subtopic - 当前采集的子题目
         */
        stopCurrentCollection(subtopic) {
            // 更新UI状态
            if (subtopic) {
                this.safeUpdateSubtopicState(subtopic, "collecting", false);
                this.safeUpdateSubtopicState(subtopic, "loadingStartCollecting", false);
            }

            // 不关闭文件传输监听，保持监听直到组件销毁
            // this.closeFileTransferListener();

            // 清除全局采集状态
            this.isCollecting = false;
            this.collectingInfo = null;

            // 清除采集倒计时
            this.clearCollectCountdown();

            // 清除视频定时器
            if (this.videoTimer) {
                clearInterval(this.videoTimer);
                this.videoTimer = null;
            }

            // 清理消息监听
            this.destroyMsgFromOwnerByPC();

            console.log('当前采集已停止');
            // 确保心跳定时器被清理
            this.stopCollectingHeartbeat();
        },

        /**
         * 强制停止所有采集（错误恢复机制）
         */
        forceStopAllCollection() {
            console.warn('执行强制停止所有采集');

            // 清除所有采集相关的状态
            this.isCollecting = false;
            this.collectingInfo = null;

            // 清除所有UI状态
            if (this.examContents) {
                this.examContents.forEach(topicType => {
                    if (topicType.list) {
                        topicType.list.forEach(topic => {
                            if (topic.subTopic) {
                                topic.subTopic.forEach(subtopic => {
                                    this.safeUpdateSubtopicState(subtopic, "collecting", false);
                                    this.safeUpdateSubtopicState(subtopic, "loadingStartCollecting", false);
                                });
                            }
                        });
                    }
                });
            }

            // 清理所有相关资源
            // 不关闭文件传输监听，保持监听直到组件销毁
            // this.closeFileTransferListener();
            this.destroyMsgFromOwnerByPC();

            if (this.videoTimer) {
                clearInterval(this.videoTimer);
                this.videoTimer = null;
            }

            console.log('强制停止所有采集完成');
        },
        /**
         * 检查是否有正在进行的采集
         * @returns {boolean} 是否有正在进行的采集
         */
        hasActiveCollection() {
            return this.isCollecting && this.collectingInfo;
        },
        /**
         * 获取当前采集的题目信息
         * @returns {Object|null} 当前采集的题目信息
         */
        getCurrentCollectionInfo() {
            if (!this.hasActiveCollection()) {
                return null;
            }

            try {
                const { topicTypeIndex, topicIndex, subIndex } = this.collectingInfo;
                const currentTopic = this.examContents[topicTypeIndex].list[topicIndex];
                const currentSubtopic = currentTopic.subTopic[subIndex];

                return {
                    topic: currentTopic,
                    subtopic: currentSubtopic,
                    indices: { topicTypeIndex, topicIndex, subIndex }
                };
            } catch (error) {
                console.error('获取当前采集信息时发生错误:', error);
                return null;
            }
        },
        /**
         * 安全地更新子题目状态
         * @param {Object} subtopic - 子题目对象
         * @param {string} property - 要更新的属性名
         * @param {*} value - 新值
         */
        safeUpdateSubtopicState(subtopic, property, value) {
            try {
                if (subtopic) {
                    this.$set(subtopic, property, value);
                } else {
                    console.warn('无法安全更新子题目状态:', { subtopic, property, value });
                }
            } catch (error) {
                console.error('更新子题目状态时发生错误:', error);
            }
        },


        // 心跳轮询：启动
        startCollectingHeartbeat(collectInfo) {
            // 清理旧的心跳定时器
            this.stopCollectingHeartbeat();
            this.lastHeartbeatConfirmTime = Date.now();
            this.missedHeartbeatCount = 0;
            // 每 5 秒向 ULiner 发送一次心跳包，查询采集状态
            this.heartbeatTimer = setInterval(() => {
                // 发送心跳
                this.sendMsgToOwnerByPC('checkAntiCheatLiveStatus', collectInfo || {});
                // 记录丢失次数，如果收到回应会被重置
                this.missedHeartbeatCount += 1;
                if (this.missedHeartbeatCount >= 2) {
                    this.$message.error(this.lang.live_recording_lost_connection_collection_stopped_tips);
                    if (this.collectingInfo) {
                        const { topicTypeIndex, topicIndex, subIndex } = this.collectingInfo;
                        const currentSubtopic = this.examContents[topicTypeIndex]?.list[topicIndex]?.subTopic[subIndex];
                        this.stopCurrentCollection(currentSubtopic);
                    }
                    this.stopCollectingHeartbeat();
                }
            }, 5000);
        },
        // 心跳轮询：停止
        stopCollectingHeartbeat() {
            if (this.heartbeatTimer) {
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }
            this.missedHeartbeatCount = 0;
        },
        checkConferenceExamIn(){
            console.log('checkConferenceExamIn')
            return new Promise((resolve,reject)=>{
                try{
                    let timer = null
                    window.main_screen.checkConferenceExamIn({},(res)=>{
                        console.log(res)
                        if(res.error_code === 0){
                            const isULinkerLogin = res.data.includes(this.systemConfig.client_type.UltraSoundMobile)
                            const isDopplerLogin = res.data.includes(this.systemConfig.client_type.Doppler)
                            if(!isULinkerLogin){
                                this.$message.error(this.lang.uLinker_not_logged_in_tips);
                                resolve(false)
                                return
                            }
                            if(!isDopplerLogin){
                                this.$message.error(this.lang.doppler_device_not_logged_in_tips);
                                resolve(false)
                                return
                            }
                            resolve(true)
                        }else{
                            resolve(false)
                        }
                        clearTimeout(timer)
                    })
                    timer = setTimeout(()=>{
                        resolve(false)
                        clearTimeout(timer)
                    },6000)
                }catch (error) {
                    resolve(false)
                }

            })
        },

        // 开始采集倒计时
        startCollectCountdown() {
            this.collectRemainingTime = this.collectTimeLimit;
            this.collectCountdownTimer = setInterval(() => {
                this.collectRemainingTime--;

                if (this.collectRemainingTime <= 0) {
                    // 时间到，自动结束采集
                    this.handleCollectTimeout();
                }
            }, 1000);
        },

        // 清除采集倒计时
        clearCollectCountdown() {
            if (this.collectCountdownTimer) {
                clearInterval(this.collectCountdownTimer);
                this.collectCountdownTimer = null;
            }
            this.collectRemainingTime = 0;
        },

        // 处理采集超时
        handleCollectTimeout() {
            console.log('采集时间到，自动结束采集');

            // 清除倒计时
            this.clearCollectCountdown();

            // 获取当前采集的子题信息
            if (this.collectingInfo) {
                const { topicTypeIndex, topicIndex, subIndex } = this.collectingInfo;
                const subtopic = this.examContents[topicTypeIndex].list[topicIndex].subTopic[subIndex];

                // 调用结束采集方法
                this.closeCollect(topicTypeIndex, topicIndex, subIndex, subtopic);

                // 显示超时提示
                this.$message.warning(this.lang.collect_time_reached_limit_tips);
            }
        },
        forceCleanupCollectionState() {
            // 清除所有采集相关的状态
            this.isCollecting = false;
            this.collectingInfo = null;

            // 清除所有UI状态
            if (this.examContents) {
                this.examContents.forEach(topicType => {
                    if (topicType.list) {
                        topicType.list.forEach(topic => {
                            if (topic.subTopic) {
                                topic.subTopic.forEach(subtopic => {
                                    this.safeUpdateSubtopicState(subtopic, "collecting", false);
                                    this.safeUpdateSubtopicState(subtopic, "loadingStartCollecting", false);
                                });
                            }
                        });
                    }
                });
            }

            // 清理所有相关资源
            // 关闭文件传输监听
            this.closeFileTransferListener();
            this.destroyMsgFromOwnerByPC();

            if (this.videoTimer) {
                clearInterval(this.videoTimer);
                this.videoTimer = null;
            }

            console.log('强制清理采集状态完成');
        }
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";
.smart_tech_exam_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;
    z-index: 10;
    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: hidden;
        background-color: #f7f9fc;
        position: relative;
        display: flex;
        flex-direction: column;
        .correct_exam_detail {
            display: flex;
            color: #333;
            padding-bottom: 20px;
            .exam_detail_left {
                flex-wrap: wrap;
                flex: 4;
                display: flex;
                justify-content: space-between;
                padding: 0 20px;
                font-size: 16px;
                align-items: center;
                .exam_detail_author {
                    .el-input {
                        width: 150px;
                    }

                    .text-editor {
                        width: 150px;
                    }
                }

                .student_info {
                    width: 100%;
                    margin-top: 10px;
                    display: flex;
                    align-items: center;
                    .student_info_row {
                        display: flex;
                        align-items: center;
                    }

                    .student_info_row_org {
                        margin-left: 10%;
                    }

                    .student_info_label {
                        flex-shrink: 0;
                    }
                    .el-input {
                        margin-left: 10px;
                        width: 200px;
                        font-size: 15px;
                        input {
                            padding: 0 10px;
                            text-align: center;
                        }
                    }
                }
            }
            .exam_detail_right {
                flex: 6;
                display: flex;
                justify-content: flex-end;
                font-size: 16px;
                align-items: center;
                .exam_detail_item {
                    margin-right: 30px;
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    span {
                        margin: 0 8px;
                    }
                    .assignment_score {
                        color: #00c59d;
                        font-size: 40px;
                        font-weight: 100;
                    }
                }
                button {
                    width: 180px;
                }
            }
        }
        .topic_content {
            flex: 1;
            overflow: hidden; /* 隐藏滚动条 */
            background: #ebeff2;
            padding: 20px;
            border-radius: 6px;
            overflow-y: auto;

            .exam-title-wrapper {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 20px;
                background-color: #fff;
                border-radius: 6px;
                padding: 10px 120px 10px 20px;
                display: flex;
                align-items: center;

                .title-tip-label {
                    margin-right: 4px;
                    white-space: nowrap;
                }

                .text-editor {
                    font-size: 18px;
                    font-weight: bold;
                    flex: 1;
                }

                .editable-content {
                    input {
                        font-size: 18px;
                        font-weight: bold;
                    }
                }
            }
        }
        .topic_summary {
            margin-bottom: 10px;
            line-height: 2.5;
            font-size: 16px;
            font-weight: bold;
        }
        .topic_tip {
            color: #000;
            margin-bottom: 10px;
            background: #fff7ec;
            padding: 14px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 15px;
        }
        .topic_detail {
            position: relative;
            font-size: 16px;
            background-color: #fff;
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
        }
        .topic_detail.is_locked {
            opacity: 0.7;
        }
        .locked_overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(245, 247, 250, 0.3);
            z-index: 10;
            border-radius: 6px;
            cursor: not-allowed;
        }
        .locked_text {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 11;

            &.status-tag-passed-new {
                border-radius: 4px;
                font-size: 15px;
                color: #202226;
                text-align: center;
                line-height: 24px;
                font-weight: 400;
                padding: 1px 8px;
                display: inline-block;
                min-width: 70px;
                box-sizing: border-box;
                background: rgba(3, 203, 0, 0.3);
                border: 1px solid #00a124;
            }
        }
        div.topic_detail:last-of-type {
            border-bottom: none;
        }

        .content-container {
            display: flex;
            gap: 20px;
            align-items: flex-start; /* 防止子项在交叉轴上拉伸 */
            position: relative;
            min-height: calc(100vh - 200px); /* 确保容器有足够高度 */
            overflow: hidden; /* 确保内容不会溢出容器 */
        }

        .topic_list {
            flex: 1;
            overflow-x: hidden; /* 隐藏水平滚动条 */
            max-width: calc(100% - 300px); /* 确保为答题卡预留足够空间，避免内容被遮挡 */
        }
        .progress-indicator{
            right: 64px;
            position: fixed;
            top: calc(50% + 180px);

        }

    }
}
.topic_footer_pagination {
    margin-top: 20px;
    text-align: center;
}
</style>
