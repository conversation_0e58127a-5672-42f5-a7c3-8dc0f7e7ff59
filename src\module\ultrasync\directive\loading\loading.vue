<template>
    <transition name="fade">
        <div v-show="visible" class="loading-mask van_loading_spinner" @click.stop>
            <van-loading color="#00c59d" />
        </div>
    </transition>
</template>
<script>
import { Loading } from 'vant';

export default {
    name: 'LoadingComponent',
    components: {
        VanLoading: Loading
    },
    data(){
        return {
            visible:false
        }
    }
}
</script>
<style lang="scss">
.loading-mask{
    position: absolute;
    left: 0;
    top: 0;
    z-index:2;
    width: 100%;
    height: 100%;
    background: #fffafabb;

    &.van_loading_spinner{
        margin-top: 0 !important;

        .van-loading{
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .van-loading__spinner{
            width: 1.5rem !important;
            height: 1.5rem !important;

            .van-loading__circular circle {
                stroke-width: 0.2rem;
            }
        }
    }

}
.loading-parent--relative{
    position:relative !important;
}
</style>
