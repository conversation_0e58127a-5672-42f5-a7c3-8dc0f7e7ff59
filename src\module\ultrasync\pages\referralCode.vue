<template>
	<div class="register_welcome_page second_level_page">
        <mrHeader :leftArrow="isShowBack">
            <template #title>
                {{lang.referral_code}}
            </template>
        </mrHeader>
        <div class="container">
            <input type="tel" v-model="referralCode" maxlength="6" class="commont_input" ref="referral_code" :placeholder="lang.referral_code">
            <p class="referral_tip">{{lang.no_referral_code}}<span @click="openIntroduce">{{lang.how_to_get_referral}}</span></p>
            <div class="referral_btn_wraper">
                <button class="primary_bg common_btn" :class="{primary_bg:isCanSubmit}" v-loading="binding" @click="bindReferralCode">{{lang.submit_btn}}</button>
                <button v-if="isShowLogin" class="login_btn common_btn" v-loading="loging" @click="login">{{lang.login_directly}}</button>
            </div>
        </div>
        <router-view></router-view>
	</div>
</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import { Toast } from 'vant';
import {
    parseImageListToLocal,
    handleAfterLogin,
    jumpRoute,
} from '../lib/common_base'
export default {
    mixins: [base],
    name:'referral_code',
    data(){
        return {
            loging:false,
            binding:false,
            token:'',
            referralCode:'',
            isShowLogin:false,
            isShowBack:false,
            isAutoLogin:false,
        }
    },
    computed:{
        isCanSubmit(){
            return this.referralCode.length>0
        },
        clipBoardInfo(){
            return this.$store.state.clipBoard.data
        },
        deviceInfo(){
            return this.$store.state.device
        },
    },
    mounted(){
        this.token=this.$route.query.token;
        this.isShowLogin=this.$route.query.isShowLogin;
        this.isShowBack=this.$route.query.isShowBack;
        this.isAutoLogin=this.$route.query.isAutoLogin;
        this.emitFun=this.$route.query.emitFun;
        if(this.clipBoardInfo&&this.clipBoardInfo.referral_code){
            this.referralCode = this.clipBoardInfo.referral_code
        }
    },
    methods:{
        bindReferralCode(){
            if (this.isCanSubmit) {
                this.binding = true;
                service.bindReferralCode({
                    token:this.token,
                    referralCode:this.referralCode
                }).then((res)=> {
                    this.binding=false;
                    if (res.data.error_code===0) {
                        if (this.isAutoLogin) {
                            this.login();
                        } else if (this.emitFun) {
                            this.back();
                            Toast(this.lang.referral_success_tip)
                            setTimeout(()=>{
                                this.$root.eventBus.$emit(this.emitFun);
                            },200)
                        } else{
                            this.$store.commit('user/updateUser',{
                                probationary_expiry:'',
                            });
                            Toast(this.lang.referral_success_tip)
                            this.back();
                        }
                    }
                })
            }
        },
        login(){
            this.loging = true;
            service.loginByToken({
                token:this.token,
                deviceInfo:{
                    device_id:this.deviceInfo.device_id,
                    client_type:this.systemConfig.clientType
                }
            }).then((res)=>{
                this.loging = false;
                if (res.data.error_code===0) {
                    const user=res.data.data;
                    user.fromLogin=true
                    parseImageListToLocal([user],'avatar')
                    this.setDefaultImg([user])
                    handleAfterLogin(user);
                    jumpRoute(1,'index');
                }else{
                    this.back()
                    // const msg=this.lang[res.data.key]||this.lang.unknown_error
                    // Toast(msg)
                }
            })
        },
        openIntroduce(){
            this.$router.push(this.$route.path+'/referral_introduce')
        }
    }
}
</script>
<style lang="scss">
.register_welcome_page{
    & > .container{
        margin:1rem;
        .referral_tip{
            text-align:right;
            color:#999;
            font-size: .6rem;
            &>span{
                color:#333;
            }
        }
        .login_btn{
            background:#ddd;
        }
    }
}
</style>
