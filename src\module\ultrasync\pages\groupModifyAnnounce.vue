<template>
	<transition name="slide">
		<div class="announce_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.group_setting_announce}}
                </template>
            </mrHeader>
			<div class="container">
				<input type="hidden" name="" v-model="conversation.subject">
				<div v-if="announcement.sender_id" class="announce_info clearfix">
                    <mr-avatar :url="getLocalAvatar(sender)" :origin_url="sender.avatar" :showOnlineState="false" :key="sender.avatar"></mr-avatar>
					<div class="announce_info_wrap">
						<p class="name">{{sender.nickname}}</p>
						<p class="time">{{formatTime(announcement.send_ts)}}</p>
					</div>
				</div>
				<textarea :disabled="disabledEdit" class="commont_input" @click="scrollInput" v-model="content" maxlength="300" ref="subject"></textarea>
				<button v-if="isCreator||isManager" class="primary_bg modify_subject_btn" @click="submit">{{lang.save_txt}}</button>
				<div v-else class="announce_tip">
					{{lang.only_creator_can_edit}}
				</div>
			</div>
		</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import {getLocalAvatar} from '../lib/common_base'
export default {
    mixins: [base],
    name: 'GroupModifyAnnounce',
    components: {},
    data(){
    	return {
            getLocalAvatar,
    		cid:this.$route.params.cid,
    		announcement:{},
    		content:''
    	}
    },
    activated(){
        this.cid=this.$route.params.cid
    },
    mounted(){
        this.$nextTick(()=>{

        })
    },
    computed:{
        conversation(){
        	let conversation=this.conversationList[this.cid]||{}
        	this.announcement=conversation.announcement||{};
            this.content=this.announcement.content||this.lang.no_announcement_tip;
            return conversation
        },
       	attendeeList(){
       		return this.conversation.attendeeList||{}
       	},
        sender(){
        	if (this.announcement.sender_id) {
        		return this.attendeeList['attendee_'+this.announcement.sender_id]
        	}else{
        		return {}
        	}
        },
        isCreator(){
            return this.user.uid==this.conversation.creator_id&&this.conversation.is_single_chat==0
        },
        isManager(){
            let result = false;
            let list=this.parseObjToArr(this.conversation.attendeeList);
            for(let item of list){
                if (item.role == this.systemConfig.groupRole.manager) {
                    if (item.userid === this.user.uid) {
                        result = true;
                        break;
                    }
                }
            }
            return result;
        },
        disabledEdit(){
            return !this.isCreator&&!this.isManager
        }
    },
    methods:{
    	submit(){
    		if (this.content=='') {
    			return
    		}
    		this.conversation.socket.emit('edit_announcement',{content:this.content},(is_succ,data)=>{
    			if (is_succ) {
    				data.cid=this.cid;
    				this.$store.commit('conversationList/updateAnnounce',data);
    			}
    		})
    		this.back();
    	},
    	scrollInput(){
            if (this.disabledEdit) {
                return ;
            }
    		if (this.content==this.lang.no_announcement_tip) {
    			this.content=''
    		}
    		setTimeout(()=>{
    			this.$refs.subject.scrollIntoViewIfNeeded(true);
    		},300)
    	}
    }
}

</script>
<style lang="scss">
	.announce_page{
		.container{
			margin:.8rem;
			.announce_info{
				display: flex;
				padding-bottom: .5rem;
    			border-bottom: 1px solid #ddd;
				.announce_info_wrap{
					padding-left: .4rem;
				    line-height: 1.4;
				    float: left;
				    .name{
				    	font-size: .8rem;
				    }
				    .time{
				    	font-size: .7rem;
				    	color:#666;
				    }
				}
			}
			.modify_tip{
				font-size:.8rem;
				color:#707070;
				margin:.1rem 0;
			}
			textarea{
				background-color:transparent;
				margin:0;
				color:#333;
				height:10rem;
				font-size:0.9rem;
				border:none;
			}
			.modify_subject_btn{
				display: block;
			    width: 100%;
			    border: none;
			    font-size: 1rem;
			    line-height: 2rem;
			    margin: 1rem 0 .6rem;
			    border-radius: .2rem;
			}
			.announce_tip{
				text-align:center;
				font-size:.8rem;
				margin-top:1rem;
				color:#333;
			}
		}
	}
</style>
