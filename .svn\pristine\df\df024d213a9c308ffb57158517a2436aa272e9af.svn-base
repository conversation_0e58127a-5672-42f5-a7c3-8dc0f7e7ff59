<template>
    <transition name="slide">
    	<div class="my_live_page second_level_page">
            <mrHeader>
                <template #title>
                    {{lang.my_live}}
                </template>
            </mrHeader>
            <div class="live_container">
                <van-tabs v-model="actived">
                    <van-tab :title="lang.my_booking_live" name="1" class="live_list">
                        <van-pull-refresh
                            ref="loadMoreLiveCreator"
                            :pulling-text="loadLiveCreatorViewInfo.topPullText"
                            :loosing-text="loadLiveCreatorViewInfo.topDropText"
                            :loading-text="loadLiveCreatorViewInfo.topLoadingText"
                            v-model="loadLiveCreatorViewInfo.topLoading"
                            @refresh="refreshLiveCreatorList"
                        >
                            <van-list
                                v-model="loadLiveCreatorViewInfo.bottomLoading"
                                :finished="loadLiveCreatorViewInfo.loadAll"
                                @load="loadMoreLiveCreatorList"
                                :loading-text="loadLiveCreatorViewInfo.bottomLoadingText"
                                :immediate-check="false"
                                offset="1"
                            >
                                <no-Data v-if="liveCreatorList.length==0" :text="lang.no_data_txt"></no-Data>
                                <div v-for="item in liveCreatorList" :key="item.id" class="live_item">
                                    <div class="live_item_image">
                                        <img :src="limitImageSize(item.cover_image,300)" v-if="item.cover_image">
                                        <img src="static/resource/images/live_cover.png" v-else>
                                    </div>
                                    <div class="live_item_container">
                                        <div class="card-content">
                                            <div class="card-top-text">
                                                <p class="card-title">{{ item.topic }}</p>
                                                <span class="card-tips-status0" v-if="item.status===systemConfig.liveManagement.waiting">{{lang.waiting}}</span>
                                                <span class="card-tips-status1" v-if="item.status===systemConfig.liveManagement.waiting&&item.is_will_start">{{lang.begin_in_minute}}</span>
                                                <span class="card-tips-status2" v-if="item.status===systemConfig.liveManagement.starting">{{lang.live_broadcasting}}</span>
                                                <span class="card-tips-status3" v-if="item.status===systemConfig.liveManagement.end">{{lang.live_broadcast_end}}</span>
                                                <span class="card-tips-status3" v-if="item.status===systemConfig.liveManagement.cancel">{{lang.live_broadcast_cancel}}</span>
                                            </div>
                                            <div class="card-desc">{{ item.description }}</div>
                                        </div>
                                        <div class="card-date">
                                            <div class="card-date-text">{{formatTime(item.start_ts)}}</div>
                                            <div class="card-date-text">{{formatTime(item.end_ts)}}</div>
                                        </div>
                                        <div class="card-info">
                                            <span class="textEllipsis">{{lang.moderator}}:{{item.creatorInfo.nickname}}</span>
                                        </div>
                                        <div class="card-tools" v-if="canHandleLive(item.status)">
                                            <i class="icon iconfont icon-chaoshengbo" @click.stop="startConference(item)"></i>
                                            <i class="icon iconfont icon-xiugai" @click.stop="gotoEditBookingPage(item)"></i>
                                            <i class="icon iconfont icon-share-1-copy" @click.stop="gotoLiveDetailPage(item)"></i>
                                            <i class="icon iconfont icon-delete" @click.stop="deleteLiveInvite(item)"></i>

                                        </div>
                                    </div>
                                </div>
                                <div v-if="loadLiveCreatorViewInfo.loadAll&&liveCreatorList.length>0" class="van-loadmore-bottom" :style="{'margin-bottom':loadLiveCreatorBottomStatus==='loadingEnd'?'0px':'-50px'}">
                                    <span class="van-loadmore-text">{{loadLiveCreatorViewInfo.bottomText}}</span>
                                </div>
                            </van-list>
                        </van-pull-refresh>
                    </van-tab>
                    <van-tab :title="lang.available_live" name="2" class="live_list">
                        <van-pull-refresh
                            ref="loadMoreLiveAttendee"
                            :pulling-text="loadLiveAttendeeViewInfo.topPullText"
                            :loosing-text="loadLiveAttendeeViewInfo.topDropText"
                            :loading-text="loadLiveAttendeeViewInfo.topLoadingText"
                            v-model="loadLiveAttendeeViewInfo.topLoading"
                            @refresh="refreshLiveAttendeeList"
                        >
                            <van-list
                                v-model="loadLiveAttendeeViewInfo.bottomLoading"
                                :finished="loadLiveAttendeeViewInfo.loadAll"
                                @load="loadMoreLiveAttendeeList"
                                :loading-text="loadLiveAttendeeViewInfo.bottomLoadingText"
                                :immediate-check="false"
                                offset="1"
                            >
                                <no-Data v-if="liveAttendeeList.length==0" :text="lang.no_data_txt"></no-Data>
                                <div v-for="item in liveAttendeeList" :key="item.id" class="live_item" @click="enterRealTimeVideo(item)">
                                    <div class="live_item_image">
                                        <i class="icon iconfont icon-videoplay"></i>
                                        <img :src="limitImageSize(item.cover_image,300)" v-if="item.cover_image">
                                        <img src="static/resource/images/live_cover.png" v-else>
                                    </div>
                                    <div class="live_item_container">
                                        <div class="card-content">
                                            <div class="card-top-text">
                                                <p class="card-title textEllipsis">{{ item.topic }}</p>
                                                <span class="card-tips-status0" v-if="item.status===systemConfig.liveManagement.waiting">{{lang.waiting}}</span>
                                                <span class="card-tips-status1" v-if="item.status===systemConfig.liveManagement.waiting&&item.is_will_start">{{lang.begin_in_minute}}</span>
                                                <span class="card-tips-status2" v-if="item.status===systemConfig.liveManagement.starting">{{lang.live_broadcasting}}</span>
                                                <span class="card-tips-status3" v-if="item.status===systemConfig.liveManagement.end">{{lang.live_broadcast_end}}</span>
                                                <span class="card-tips-status3" v-if="item.status===systemConfig.liveManagement.cancel">{{lang.live_broadcast_cancel}}</span>
                                            </div>
                                            <div class="card-desc textEllipsis">{{ item.description }}</div>
                                        </div>
                                        <div class="card-date">
                                            <div class="card-date-text"><i class="el-icon-date"></i>{{formatTime(item.start_ts)}}</div>
                                            <div class="card-date-text"><i class="el-icon-date"></i>{{formatTime(item.end_ts)}}</div>
                                        </div>
                                        <div class="card-info">
                                            <span class="textEllipsis">{{lang.moderator}}:{{item.creatorInfo.nickname}}</span>
                                        </div>
                                        <div class="card-tools">
                                            <i class="icon iconfont icon-share-1-copy" @click.stop="gotoLiveDetailPage(item)"></i>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="loadLiveAttendeeViewInfo.loadAll&&liveAttendeeList.length>0" class="van-loadmore-bottom" :style="{'margin-bottom':loadLiveAttendeeBottomStatus==='loadingEnd'?'0px':'-50px'}">
                                    <span class="van-loadmore-text">{{loadLiveAttendeeViewInfo.bottomText}}</span>
                                </div>
                            </van-list>
                        </van-pull-refresh>
                    </van-tab>
                </van-tabs>
            </div>
            <keep-alive :include="/chat/">
                <router-view></router-view>
            </keep-alive>

    	</div>
	</transition>
</template>
<script>
import Tool from '@/common/tool.js'
import base from '../../lib/base'
import noData from '../../MRComponents/noData.vue'
import { Dialog, Toast, PullRefresh, List, Tab, Tabs } from 'vant';
export default {
    mixins: [base],
    name: 'LiveManagementMyLive',
    components: {
        noData,
        [Dialog.Component.name]: Dialog.Component,
        VanList: List,
        VanPullRefresh: PullRefresh,
        VanTabs: Tabs,
        VanTab: Tab
    },
    computed:{
    },
    data(){
        return {
            actived:'1',
            liveCreatorList:[],
            liveAttendeeList:[],
            currentAction:'',
            liveCreatorForm:{
                page:1,
                pageSize:10
            },
            liveAttendeeForm:{
                page:1,
                pageSize:10
            },
            loadLiveCreatorViewInfo:{
                loadAll:false,
                topPullText:'',
                topDropText:'',
                topLoadingText:'',
                bottomText:'',
                topLoading: false,
                bottomLoading: false
            },
            loadLiveAttendeeViewInfo:{
                loadAll:false,
                topPullText:'',
                topDropText:'',
                topLoadingText:'',
                bottomText:'',
                topLoading: false,
                bottomLoading: false
            },
            loadLiveCreatorBottomStatus:'',
            loadLiveAttendeeBottomStatus:''

        }
    },
    computed:{
        ConversationConfig(){
            return this.$store.state.systemConfig.ConversationConfig
        },
        groupList() {
            return this.$store.state.groupList
        },
        groupsetList(){
            return this.$store.state.groupset.list
        },
        groupPublicState(){
            return this.$store.state.systemConfig.groupPublicState
        },
    },
    watch:{
        loadLiveCreatorBottomStatus(val){
            switch (val) {
            case 'pull':
                this.loadLiveCreatorViewInfo.bottomText = this.lang.bottom_pull_text
                break;
            case 'drop':
                this.loadLiveCreatorViewInfo.bottomText = this.lang.bottom_drop_text
                break;
            case 'loading':
                this.loadLiveCreatorViewInfo.bottomText = this.lang.bottom_loading_text
                break;
            case 'loadingEnd':
                this.loadLiveCreatorViewInfo.bottomText = this.lang.no_more_text
                break;
            }
        },
        loadLiveAttendeeBottomStatus(val){
            switch (val) {
            case 'pull':
                this.loadLiveAttendeeViewInfo.bottomText = this.lang.bottom_pull_text
                break;
            case 'drop':
                this.loadLiveAttendeeViewInfo.bottomText = this.lang.bottom_drop_text
                break;
            case 'loading':
                this.loadLiveAttendeeViewInfo.bottomText = this.lang.bottom_loading_text
                break;
            case 'loadingEnd':
                this.loadLiveAttendeeViewInfo.bottomText = this.lang.no_more_text
                break;
            }
        },
    },
    created(){
        this.loadLiveCreatorViewInfo.topPullText=this.lang.top_pull_text,
        this.loadLiveCreatorViewInfo.topDropText=this.lang.bottom_drop_text,
        this.loadLiveCreatorViewInfo.topLoadingText=this.lang.bottom_loading_text

        this.loadLiveAttendeeViewInfo.topPullText=this.lang.top_pull_text,
        this.loadLiveAttendeeViewInfo.topDropText=this.lang.bottom_drop_text,
        this.loadLiveAttendeeViewInfo.topLoadingText=this.lang.bottom_loading_text

        this.refreshLiveCreatorList()
        this.refreshLiveAttendeeList()
    },
    mounted(){
        this.$root.eventBus
            .$off("refreshLiveManagementList")
            .$on("refreshLiveManagementList", this.refreshLiveManagementList);
    },
    beforeDestroy(){
        this.$root.eventBus.$off("refreshLiveManagementList")
    },
    methods:{
        getLiveCreatorList(){
            return new Promise((resolve,reject)=>{
                window.main_screen.getLiveCreatorList(this.liveCreatorForm,(res)=>{
                    if(!res.error_code){
                        resolve(res.data.data)

                    }else{
                        Toast(res.error_msg)
                        reject(false)
                    }
                })
            })

        },
        getLiveAttendeeList(){
            return new Promise((resolve,reject)=>{
                window.main_screen.getLiveAttendeeList(this.liveAttendeeForm,(res)=>{
                    if(!res.error_code){
                        resolve(res.data.data)
                    }else{
                        Toast(res.error_msg)
                        reject(false)
                    }
                })
            })

        },
        async refreshLiveCreatorList(){
            this.liveCreatorForm.page = 1
            const list = await this.getLiveCreatorList()
            this.liveCreatorList = list
            if(this.$refs.loadMoreLiveCreator){
                this.loadLiveCreatorViewInfo.topLoading = false;
            }
            if(list.length<this.liveCreatorForm.pageSize){
                this.loadLiveCreatorViewInfo.loadAll = true

                setTimeout(()=>{
                    this.loadLiveCreatorBottomStatus = 'loadingEnd'
                },0)
            }else{
                this.loadLiveCreatorViewInfo.loadAll = false
            }


        },
        async refreshLiveAttendeeList(){
            this.liveAttendeeForm.page = 1
            const list = await this.getLiveAttendeeList()
            this.liveAttendeeList = list;
            if(this.$refs.loadMoreLiveAttendee){
                this.loadLiveAttendeeViewInfo.topLoading = false;
            }
            if(list.length<this.liveAttendeeForm.pageSize){
                this.loadLiveAttendeeViewInfo.loadAll = true
                setTimeout(()=>{
                    this.loadLiveAttendeeBottomStatus = 'loadingEnd'
                },0)
            }else{
                this.loadLiveAttendeeViewInfo.loadAll = false
            }

        },
        async loadMoreLiveCreatorList(){
            setTimeout(async () => {
                this.liveCreatorForm.page= this.liveCreatorForm.page+1
                const list = await this.getLiveCreatorList()
                this.liveCreatorList = [...this.liveCreatorList,...list]
                this.loadLiveCreatorViewInfo.bottomLoading = false;
                if(list.length<this.liveCreatorForm.pageSize){
                    this.loadLiveCreatorViewInfo.loadAll = true

                    setTimeout(()=>{
                        this.loadLiveCreatorBottomStatus = 'loadingEnd'
                    },0)
                }
            }, 0);
        },
        async loadMoreLiveAttendeeList(){
            setTimeout(async () => {
                this.liveAttendeeForm.page=this.liveAttendeeForm.page+1
                const list = await this.getLiveAttendeeList()
                this.liveAttendeeList =this.liveAttendeeList = [...this.liveAttendeeList,...list]
                console.log(this.liveAttendeeList)
                this.loadLiveAttendeeViewInfo.bottomLoading = false;
                if(list.length<this.liveAttendeeForm.pageSize){
                    this.loadLiveAttendeeViewInfo.loadAll = true
                    setTimeout(()=>{
                        this.loadLiveAttendeeBottomStatus = 'loadingEnd'
                    },0)

                }
            }, 0);
        },
        handleLoadLiveCreatorBottomChange(status){
            this.loadLiveCreatorBottomStatus = status
        },
        handleLoadLiveAttendeeBottomChange(status){
            this.loadLiveAttendeeBottomStatus = status
        },
        requestAddLiveGroup(item){
            return new Promise((resolve,reject)=>{
                let gid = item.group_id
                window.main_screen.applyJoinGroup({
                    mark:'',
                    gid:gid,
                    inviterID:0,
                    source:1,
                },(res)=>{
                    if (res.error_code==0) {
                        resolve(true)
                    }else{
                        reject(false)
                    }
                })
            })
        },
        async enterRealTimeVideo(item){
            window.main_screen.checkJoinLiveStatus({live_id:item.id},async(res)=>{
                if(!res.error_code){
                    if(!res.data.enterAuth){
                        Toast(this.lang.no_permission_to_open_room)
                        return
                    }
                    if(!res.data.groupEnterStatus){//不在群内
                        await this.requestAddLiveGroup(item)  // 先加入直播间，再进入会话
                    }
                    // this.currentAction = 'enterRealTimeVideo'
                    this.openConversation(item.group_id,13,()=>{
                        this.$router.push({
                            path:`/index/live_management/my_live/chat_window/${item.group_id}`,
                            query:{
                                live_id:item.id
                            }
                        })
                    })
                }else{
                    Toast(res.error_msg)
                }
            })
        },
        gotoLiveDetailPage(item){
            this.$router.push(`/index/live_management/my_live/live_detail/${item.id}`)
        },
        gotoEditBookingPage(item){
            this.$router.push({
                name:'/index/live_management/my_live/booking_live',
                params:{
                    editMode:'edit',
                    liveInfo:item
                }
            })
        },
        canHandleLive(status) {
            return (
                status === this.systemConfig.liveManagement.waiting ||
                status === this.systemConfig.liveManagement.starting
            );
        },
        refreshLiveManagementList() {
            this.refreshLiveCreatorList();
            this.refreshLiveAttendeeList();
            // this.$root.eventBus.$emit('updateLiveCount')
        },
        startConference(item){
            window.main_screen.getLiveInfoById({ live_id: item.id }, (liveRes) => {
                if (
                    liveRes.data.status === this.systemConfig.liveManagement.cancel &&
                    this.user.uid !== liveRes.data.creator_id
                ) {
                    Toast(this.lang.live_invite_status3.replace('{1}',''));
                    return;
                }
                window.main_screen.checkJoinLiveStatus({ live_id: item.id }, async (res) => {
                    if (!res.error_code) {
                        if (!res.data.enterAuth) {
                            Toast(this.lang.no_permission_to_open_room);
                            return;
                        }
                        if (!res.data.groupEnterStatus) {
                            //不在群内
                            await this.requestAddLiveGroup(item); // 先加入直播间，再进入会话
                        }
                        this.openConversation(item.group_id, 13, () => {
                            this.$router.push(`/index/live_management/my_live/chat_window/${item.group_id}?live_id=${item.id}`);
                            setTimeout(()=>{
                                this.$root.eventBus.$emit('callLiveConference')
                            },1500)
                        });
                    } else {
                        Toast(res.error_msg);
                    }
                });
            });
        },
        deleteLiveInvite(item){
            Tool.openMobileDialog(
                {
                    message: this.lang.dissolve_live,
                    showRejectButton:true,
                    confirm:()=>{
                        window.main_screen.cancelLiveBroadcast({ live_id: item.id }, (res) => {
                            if (!res.error_code) {
                                this.$root.eventBus.$emit('updateLiveCount')
                            } else {
                                // this.$message.error(res.error_msg)
                                console.error(res);
                            }
                        });
                    },
                }
            )
        }
    }
}

</script>
<style lang="scss">
	.my_live_page{
        .live_container{
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .van-tabs {
                display: flex;
                flex-direction: column;
                overflow: auto;
            }
            .van-tabs__content{
                overflow: auto;
                flex: 1;

                 .live_list {
                    width: 100%;
                    height: 100%;
                }

                .live_list{
                    flex: 1;
                    overflow: auto;
                    .live_item{
                        display: flex;
                        justify-content: space-around;
                        padding: 10px;
                        box-sizing: border-box;
                        border-bottom: 1px solid #e5e5e5;
                        min-height: 130px;
                        .live_item_image{
                            width: 40%;
                            display: flex;
                            -ms-flex-pack: center;
                            justify-content: center;
                            position: relative;
                            height: 130px;
                            overflow: hidden;
                            .icon-videoplay{
                                // display: none;
                                position: absolute;
                                color: #fff;
                                font-size: 1.8rem;
                                left: 50%;
                                top: 50%;
                                transform: translate3d(-50%,-50%,0);
                            }
                            img {
                                height: 100%;
                            }
                        }
                        .live_item_container{
                            width: 60%;
                            padding-left: 20px;
                            box-sizing: border-box;
                            .card-date{
                                font-size: 14px;
                                color: #9c9c9c;
                                line-height: 1.8;
                            }
                            .card-info{
                                display: flex;
                                justify-content: space-between;
                                font-size: 14px;
                                color: #9c9c9c;
                            }
                            .card-content{
                                padding: 5px 0;
                                .card-top-text{
                                    display: flex;
                                    justify-content: space-between;
                                    .card-title {
                                        font-size: .7rem;
                                        font-weight: bold;
                                        flex: 1;
                                        width: 0;
                                        overflow-wrap: break-word;
                                    }
                                    span{
                                        font-size: 12px;
                                        text-align: right;
                                        flex-shrink: 0;
                                        padding-left: 1rem;
                                    }
                                    .card-tips-status0{
                                        color: #0c7bea;
                                    }
                                    .card-tips-status1{
                                        color: #c4a20a;
                                    }
                                    .card-tips-status2{
                                        color: #3dc40a;
                                    }
                                    .card-tips-status3{
                                        color: #fb1212;
                                    }
                                }

                                .card-desc {
                                    font-size: 14px;
                                    color: rgba(0,0,0, 0.6);
                                    overflow-wrap: break-word;
                                    margin-top: 0.2rem;
                                }
                            }
                            .card-tools{
                                display: flex;
                                justify-content: flex-end;
                                color: #0c7bea;
                                margin-top: 0.8rem;
                               .iconfont{
                                    font-size: .8rem;
                                    margin-left: 0.6rem;
                                    border: 1px solid;
                                    border-radius: 50%;
                                    width: 1.4rem;
                                    height: 1.4rem;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                               }
                            }
                        }
                    }

                }
            }
            .van-tab--active{
                color: #26A2FF;
            }
            .van-tabs__line{
                width: 50%;
                background-color: #26A2FF;
            }
            .nodata_page{
                font-size: 0.8rem;
            }

            .van-loadmore-bottom {
                text-align: center;
                height: 50px;
                line-height: 50px;
                .van-loadmore-text {
                    vertical-align: middle;
                    font-size: 0.8rem;
                }
            }
        }

	}
</style>
