<template>
    <transition name="slide">
        <div class="group_add_attendee_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.group_add_attendee_title}}({{attendeeArray.length}})
                </template>
                <template #right>
                    <span class="add_attendee_btn" @click="submit" :class="{enable:enable}">{{lang.confirm_txt}}({{groupUser.length}})</span>
                </template>
            </mrHeader>
            <div class="group_setting_container">
                <ContactSelectList :optionList="checkOption" v-model="groupUser" v-if="activatedComponent"></ContactSelectList>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import { checkIsCreator, checkIsManager} from "../lib/common_base";
import ContactSelectList from "../components/contactSelectList.vue";
export default {
    mixins: [base],
    name: 'group_setting_add_attendee',
    components: {
        ContactSelectList
    },
    data(){
        return {
            cid:this.$route.params.cid,
            groupUser:[],
            activatedComponent:true
        }
    },
    activated(){
        this.groupUser=[];
        this.cid=this.$route.params.cid
        this.activatedComponent = true
    },
    deactivated(){
        this.activatedComponent = false
    },
    mounted(){
        this.$nextTick(()=>{
        })
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{}
        },
        attendeeList(){
            return this.conversation.attendeeList
        },
        attendeeArray(){
            let arr=[];
            for(let key in this.attendeeList){
                if (this.attendeeList[key].attendeeState!=0) {
                    arr.push(this.attendeeList[key])
                }
            }
            return arr
        },
        checkOption(){
            var arr=[]
            for(let friend of this.$store.state.friendList.list){
                let item=Object.assign({},friend);
                if (item.alias) {
                    item.nickname=item.alias;
                }
                if (item.service_type==0&&item.user_status!=this.systemConfig.userStatus.Destroy) {
                    let option={}
                    option.label=item.nickname
                    option.value=item.id
                    option.avatar=item.avatar
                    //已在群里不可选
                    if (this.attendeeList['attendee_'+item.id] && (this.attendeeList['attendee_'+item.id].attendeeState != 0)) {
                        option.disabled=true
                    }

                    arr.push(option)
                }

            }

            return arr;
        },
        enable(){
            return this.groupUser.length>0
        },
    },
    methods:{
        submit(){
            if (this.enable) {
                let list=[];
                let that=this;
                let currentGroupUser = []
                console.log("###########")
                Object.keys(this.attendeeList).map(item=>{
                    if(this.attendeeList[item].attendeeState===1){
                        currentGroupUser.push({
                            uid:this.attendeeList[item].userid,
                            avatar:this.attendeeList[item].avatar
                        })
                    }
                })
                for(let user of this.checkOption) {
                    for(let checkedUser of this.groupUser) {
                        if(user.value === checkedUser) {
                            currentGroupUser.push({
                                uid: user.value,
                                avatar: user.avatar
                            })
                        }
                    }
                }
                if (this.conversation.is_single_chat) {
                    //单聊变群聊
                    for(let user in this.attendeeList){
                        list.push(this.attendeeList[user].userid)
                    }
                    list=list.concat(this.groupUser)
                    var data={
                        subject:this.lang.group_chat_text,
                        group_user_list:list.join(),
                        is_single_chat:0,
                        type:this.systemConfig.ConversationConfig.type.Group,
                        is_public:0
                    }
                    this.$root.socket.emit("request_create_conversation",data,function(is_succ,data){
                        Toast(that.lang.create_group_text)
                        that.openConversation(data,6)
                    })
                }else{
                    for(let user of this.groupUser){
                        list.push(user);
                    }
                    window.main_screen.conversation_list[this.cid].groupInviteJoin({
                        uidList:list,
                    },(res)=>{
                        if (res.error_code==0) {
                            const isCreator=checkIsCreator(this.cid);
                            const isManager=checkIsManager(this.cid);
                            if (!isCreator&&!isManager&&this.conversation.join_check) {
                                Toast(this.lang.group_apply_success)
                            }else{
                                setTimeout(()=>{
                                    this.$root.eventBus.$emit('createGroupAvatar',{
                                        conversation:this.conversation,
                                        userList:currentGroupUser
                                    })
                                },2000)
                            }
                        }
                    })
                    this.back();
                }
            }
        },
    }
}

</script>
<style lang="scss">
.group_add_attendee_page{
    .choose_list{
        background:#fff;
        .group_user_item{
            padding:0.5rem;
        }
    }
    .group_setting_container{
        flex:1;
        overflow: hidden;
    }
    .add_attendee_btn{
        width: 4rem;
        color: #fff;
        opacity: 0.6;
        font-size: 0.7rem;
        text-align: right;
        white-space: nowrap;
        &.enable{
            opacity:1;
        }
    }
}
</style>
