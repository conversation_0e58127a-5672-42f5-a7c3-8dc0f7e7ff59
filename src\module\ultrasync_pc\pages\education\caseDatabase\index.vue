<template>
    <div class="case_database_page_container">
        <canvas style="display:none" id="ai_canvas"></canvas>
        <div class="case_database_page">
            <div class="container">
                <div class="search_box">
                    <i class="icon iconfont iconsearch" @click="startTextSearch()"></i>
                    <i class="icon iconfont iconzhaoxiangji left-margin" @click="selectPicture"></i>
                    <input type="file" class="select_picture" ref="selectPictureRef"
                        accept="image/png,image/jpg,image/jpeg,image/bmp" @change="uploadPicture()">
                    <input type="text" :placeholder="lang.search" v-model="searchInfo.searchInputKey"
                        @input="debounceSearch" ref="inputVal" @focus="clickInput" @keyup.enter="blurInput"
                        maxlength="12">
                    <i class="icon iconfont iconclose" @click="clearKey"
                        v-show="searchInfo.searchInputKey.length > 0"></i>
                    <!-- <el-button type="primary" size="mini" @click="startSearch()">{{lang.search}}</el-button> -->
                </div>
                <div class="search_key" v-show="history.list.length > 0 && isShowHot">
                    <el-button v-for="(tag, index) of history.list" type="primary" size="mini" @click="clickSearch(tag)"
                        :key="index">{{ tag }}
                        <i class="icon iconfont iconclose" @click.stop="deleteTag(tag)"></i>
                    </el-button>
                    <i v-show="history.list.length > 0" class="iconfont iconel-icon-delete2" @click="clearTag"></i>
                </div>
                <caseFliter ref="case_fliter" @handleback="handleback" :condition="filters.condition"
                    v-show="!isShowHot"></caseFliter>
                <div v-show="searchInfo.imageSrc" class="examlist_image_tip_container">
                    <div class="examlist_image_button_wrap">
                        <img class="examlist_image_button" :src="imageDraw || searchInfo.imageSrc"
                            @click="showUploadImage" />
                    </div>
                    <div class="examlist_image_close">
                        <div class='left'>
                            <i class="f3 icon iconfont iconshuaxin" @click="refreshlImageSearch"></i>
                        </div>
                        <div class='right'>
                            <i class=" fl icon iconfont iconsearchclose" @click="clearImageSearch"></i>
                        </div>
                    </div>
                </div>
                <p v-show="isShowHot" class="search_result">{{ lang.hot_search_tip }}</p>
                <div class="case_list_container">
                    <p v-show="searchInfo.searchInputKey != '' && !isShowHot" class="examlist_tip">{{ fliterTip }}</p>
                    <div class="no_data" v-show="resultTip != ''">{{ resultTip }}</div>
                    <div class="search_loading"></div>
                    <div class="case_item" v-for="(item, index) of exams.currentExamList.slice(0, exams.endCase)"
                        @click="showGallery(item)" :key="index">
                        <div class="image_list_first  clearfix">
                            <div class="image_item" v-for="(image_src, image_index) of item.image_list.slice(0, 1)"
                                :key="image_index">
                                <div class="image_item_container">
                                    <img class="image" :src="prefUrl + image_src">
                                </div>
                                <div class="picture_format">{{ item.image_list.length }}{{ lang.picture_unit }}</div>
                            </div>
                            <div class="patient_info_item">
                                <p class="patient_info_item_name">
                                    {{ item.postoperative_pathology }}:{{ item.benign_malignant }}</p>
                                <p>{{ lang.patient_id }}: {{ item.collection_id }}</p>
                                <div class="patient_info_item_people">
                                    <p>{{ lang.exam_patient_sex }}: {{ item.patient_sex }}</p>
                                    <p>{{ lang.exam_patient_age }}: {{ item.patient_age }}</p>
                                </div>
                                <!-- <div class="patient_info_item">
                                    <p >similarity: {{item.similarity? (item.similarity+'').substr(0,5):'unknow'}}</p>
                                </div> -->
                            </div>
                        </div>
                    </div>
                    <div class="load-more" @click='loadMore' v-if="exams.endCase < exams.ids.length">{{ lang.load_more
                        }}
                    </div>
                    <div class="load-more" v-if='exams.ids.length > 0 && exams.ids.length <= exams.endCase'>
                        {{ lang.no_more_text }}
                    </div>
                </div>
            </div>
        </div>
        <previewPhoto :pathologyInfo="exams.pathologyInfo" v-if="galleryState == 1" @showImageEvent="showImageEvent">
        </previewPhoto>
        <previewPhoto :imageObj="uploadImageObj" v-if="galleryState == 2" @showSearchImageEvent="showSearchImageEvent"
            :isCaseImage="false"></previewPhoto>

        <div class="case_database_zhezhao" v-loading.lock="isSearchLoading">
        </div>
    </div>
</template>
<script>
import base from '../../../lib/base';
import Tool from '@/common/tool.js'
import { getRealUrl } from '../../../lib/common_base'
import previewPhoto from './components/previewPhoto.vue'
import caseFliter from './components/caseFliter.vue'
import { cloneDeep, trim } from 'lodash'
import service from '../../../service/service.js'
import { uploadFile, cancelUpload } from '@/common/oss/index'

export default {
    mixins: [base],
    name: 'EducationCaseDatabase',
    components: {
        previewPhoto,
        caseFliter,
    },
    data() {
        return {
            filters: {
                isShowCondition: false,//是否显示过滤条件
                condition: {},
            },                //相机或者照片弹出
            exams: {
                endCase: 20, //默认20条
                // isNoData: false,//无数据
                sliceCount: 20, //每次增20条
                currentExamList: [], //当前显示列表
                ids: [], //ids
                oldIds: [], //筛选使用
                pathologyInfo: {}, //当前病例
            },
            isSearchLoading: false, //是否加载中
            defaultSearchInfo: {
                defaltSearchInputKey: '回声',
                searchInputKey: '', //搜索文本内容
                imageSrc: '',//用于显示到前端
                file: '',//文件
                name: '',//文件名字
                msg: '',//文件名字
                selectRoi: 0,//选中的roi，默认第一个
                roi: [], //本次搜索roi
                // oldRoi:[], //上次搜索roi
                hotcount: 20, //热搜获取的数据
                isUrl: false, //是否未网络资源
                url: '',//图片云端地址
                isShowHot: false,
            },
            imageDraw: '',
            uploadImageObj: {},//用于画廊显示
            searchInfo: {},
            galleryState: 0,//是否显示画廊0默认，1病例，2搜索图片
            history: {
                list: [],  //展示的搜索历史
            },
            start_time: 0,
            end_time: 0,
            colors: ['#FF0000', '#00FF00', '#0000FF', '#8000FF', '#FFFFFF', '#FFFF00'],
            case_database_fliter: {
                "benign_or_malignant": "良恶性",
                "benign": "良性",
                "malignant": "恶性",
                "bi_rads_feature": "BI - RADS特征",
                "shape": "形状",
                "oval": "卵圆形",
                "circular": "圆形",
                "irregular": "不规则形",
                "direction": "方向",
                "parallel": "与皮肤平行",
                "unparallel": "不平行",
                "edge": "边缘",
                "vague": "模糊",
                "finishing": "光整",
                "angled": "成角",
                "microphylation": "微分叶",
                "hairpin_like": "毛刺状",
                "echo_type": "回声类型",
                "anechoic": "无回声",
                "hypoechoic": "低回声",
                "hyperechoic": "高回声",
                "isoechoic": "等回声",
                "mixed_echo": "混合回声",
                "uneven_echo": "不均回声",
                "posterior_echo": "后方回声",
                "no_change": "无改变",
                "echo_enhancement": "回声增强",
                "acoustic_shadow": "声影",
                "mixed_change": "混合性改变",
                "calcification": "钙化",
                "no_calcification": "无钙化",
                "calcification_in_mass": "肿块内钙化",
                "calcification_out_mass": "肿块外钙化",
                "intraductal_calcification": "导管内钙化",
                "microcalcification": "微钙化",
                "coarse_calcification": "粗大钙化",
                "bi_rads_type": "BI - RADS分类",
                "bi_rads_1": "1类",
                "bi_rads_2": "2类",
                "bi_rads_3": "3类",
                "bi_rads_4a": "4a类",
                "bi_rads_4b": "4b类",
                "bi_rads_4c": "4c类",
                "bi_rads_5": "5类",
                "bi_rads_6": "6类",
                "pathological_classification_breast_cancer": "乳腺癌病理分类",
                "noninvasive_breast_cancer": "非浸润性乳腺癌",
                "ductal_carcinoma_in_situ": "导管原位癌",
                "lobular_carcinoma_in_situ": "小叶原位癌",
                "invasive_breast_cancer": "浸润性乳腺癌",
                "infiltrating_ductal_carcinoma": "浸润性导管癌",
                "infiltrating_lobular_carcinoma": "浸润性小叶癌",
                "cephaloma": "髓样癌",
                "mucinous_carcinoma": "粘液癌",
                "papillary_carcinoma": "乳头状癌"
            },
        }
    },
    computed: {
        // https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/AiSearch/standardImgLib.mini/001202100014--病灶横切面（不带测量标记）.JPG
        prefUrl() {
            return this.systemConfig.serverInfo.oss_attachment_server.playback_https_addr + '/' + 'AiSearch' + '/'
        },
        condition() {
            return this.$store.state.caseDatabase.defaultCondition || {}
        },

        fliterTip() {
            return this.searchInfo.searchInputKey ? this.lang.search_result_tip.replace('{1}', this.searchInfo.searchInputKey) : ''
        },
        resultTip() {
            if (this.isSearchLoading) {
                return this.lang.searching
            }
            if (this.searchInfo.msg != '') {
                return this.searchInfo.msg
            }
            if (this.exams.ids.length < 1) {
                return this.lang.is_no_case_text
            }

            return ''
        },
        searchParams() {
            return this.$store.state.caseDatabase.searchParams || { type: 'text', content: '' }
        },
        isSetCondition() {
            for (let key in this.filters.condition) {
                let item = this.filters.condition[key]
                if (this.isArrary(item)) {
                    let list = item
                    if (list.length > 0) {
                        return true
                    }
                } else {
                    for (let key_ in item) {
                        let list = item[key_]
                        if (list.length > 0) {
                            return true
                        }
                    }
                }
            }
            return false
        }
    },

    mounted() {
        this.$nextTick(() => {
            this.visible = true;
        });
    },
    created() {
        this.searchInfo = cloneDeep(this.defaultSearchInfo)
        this.imageDraw = ''
        this.start_time = (new Date()).getTime()
        this.clearFilterCondition()
        this.debounceSearch = Tool.debounce(() => { }, 400);
        this.setCurrentList();
        this.initTagHistory();

    },
    watch: {
        searchInfo: {
            handler(newVal, oldVal) {
                if (newVal && newVal.imageSrc && newVal.roi.length > 0) {
                    this.LoadImage()
                }
            },
            deep: true,
        },
    },
    methods: {
        serverUrl(method) {
            let ai_searcher_server = this.$store.state.systemConfig.serverInfo.ai_searcher_server;
            let ajaxServer = ai_searcher_server.protocol + ai_searcher_server.addr + ':' + ai_searcher_server.port + '/' + trim(trim(ai_searcher_server.api, '/'), '\\')
            return trim(trim(ajaxServer, '/'), '\\') + '/' + trim(trim(method, '/'), '\\')
        },
        drawCanvasToImage(realImage) {
            let that = this
            //左右[x1,y1,x2,y2]
            var canvas = document.getElementById('ai_canvas')
            var context = canvas.getContext('2d');
            canvas.width = realImage.width;
            canvas.height = realImage.height;
            context.drawImage(realImage, 0, 0);
            context.strokeStyle = "#f00"
            context.lineWidth = 1
            let roi = that.searchInfo.roi[that.searchInfo.selectRoi]
            if (roi) {
                context.beginPath();
                context.moveTo(roi[0], roi[1]); //把画笔移动到指定的坐标
                context.lineTo(roi[2], roi[1]);  //绘制一条从当前位置到指定坐标(200, 50)的直线.
                context.lineTo(roi[2], roi[3]);
                context.lineTo(roi[0], roi[3]);
                //闭合路径。会拉一条从当前点到path起始点的直线。如果当前点与起始点重合，则什么都不做
                context.closePath();
                context.strokeStyle = that.colors[that.searchInfo.selectRoi];
                context.lineWidth = 8;
                // context.fillStyle = this.colors[i];
                // context.fill();
                context.stroke(); //绘制路径。
            }
            let base64 = canvas.toDataURL("image/jpeg");
            return base64;
        },
        LoadImage() {
            let that = this
            // console.error('that.searchInfo.isUrl:',that.searchInfo.isUrl)
            if (!that.searchInfo.isUrl) {//base64
                let reader = new FileReader()
                reader.onload = function (e) {
                    let data = e.target.result
                    let image = new Image()
                    image.src = data
                    image.onload = function () {
                        that.imageDraw = that.drawCanvasToImage(image)
                    }
                };
                // console.error('that.searchInfo.file:',that.searchInfo.file)
                reader.readAsDataURL(that.searchInfo.file);
            } else {//url
                let image = new Image()
                image.onerror = function (e) {
                    console.log('preloadError', e)
                }
                image.onload = function () {
                    that.imageDraw = that.drawCanvasToImage(image)
                }
                image.setAttribute("crossOrigin", 'anonymous');
                image.src = `${that.searchInfo.file}?temp=${Date.now()}`;
            }

        },
        //清空过滤条件
        clearFilterCondition() {
            this.filters.condition = cloneDeep(this.condition)
        },
        //点击搜索按钮
        startSearch() {
            this.startTextSearch()
        },
        //触发文本搜索
        startTextSearch() {
            let params = {
                type: 'text',//text,url,file
                content: this.searchInfo.searchInputKey
            }
            this.sortAndSaveTag(this.searchInfo.searchInputKey)
            this.$store.commit('caseDatabase/updateSearchParams', params)
            this.setCurrentList(params)
        },
        //设置当前列表
        async setCurrentList(params = null) {
            this.clearFilterCondition()
            this.isShowHot = false
            this.isSearchLoading = true
            this.exams.endCase = 20
            this.exams.currentExamList = []
            this.exams.ids = []
            this.exams.oldIds = []
            this.searchInfo = cloneDeep(this.defaultSearchInfo)
            this.imageDraw = ''
            let { type, content } = params == null ? this.searchParams : params
            console.log('setCurrentList:', type)
            switch (type) {
            case 'text':
                let op = { type: 'text', text: content, pageNo: 1, pageSize: this.exams.sliceCount }
                this.searchInfo.searchInputKey = content
                let datas = await this.searchAction('find_by_keyword', op)
                if (datas && !datas.error) {
                    this.exams.currentExamList = datas.data.list
                    this.exams.ids = datas.data.ids
                    this.exams.oldIds = cloneDeep(datas.data.ids)

                }
                // if(this.isSetCondition){
                //     this.handleback(this.filters.condition)
                // }
                break;
            case 'file':
                await this.uploadPicture(content)
                break;
            case 'url':
                await this.pictureUrlDeal(content)
                break;
            default:
                break;
            }
            this.isSearchLoading = false
        },
        //初始化，拿到热搜、全部数据

        //执行搜索
        async searchAction(name, options, numb = 30000) {
            let timer = null
            let that = this
            return new Promise(async (resolve, reject) => {
                let result = { error: false, msg: '', data: { list: [], roi: [], ids: [] } }
                timer = setTimeout(() => {
                    result.msg = 'timeout'
                    result.error = true
                    that.$message.error(that.lang.requestTimeout)
                    that.isSearchLoading = false
                    reject(result)
                    return
                }, numb)
                that.$once('hook:beforeDestroy', function () {
                    timer && clearTimeout(timer)
                })
                let start_req_time = new Date().getTime()
                let op = {
                    "pageNo": options.pageNo || 1,
                    "pageSize": options.pageSize || that.exams.sliceCount,
                    type: 'BREASTSEARCH',
                    sender_id: this.user.id,
                }

                if (name == 'find_by_keyword') {
                    op.keyword = options.text
                }
                if (name == 'find_by_image') {
                    op.url = options.FILE.url

                }
                if (name == 'find_by_ids') {
                    op.ids = options.ids
                }
                if (name == 'fliter_by_condition') {
                    if (options.ids && options.ids.length > 0) {
                        op.ids = options.ids
                    }
                }
                if (options && options.condition) {
                    op.fliterCondition = options.condition
                }
                if (options && options.FILE && options.FILE.roi && options.FILE.roi.length > 0) {
                    op.roi = options.roi
                }
                if (options.resource_id) {
                    op.resource_id = options.resource_id
                }
                if (options.group_id) {
                    op.group_id = options.group_id
                }

                service.requestAiAnalyzeWithUrl({ method: name, condition: op }).then(async (res) => {
                    // console.log('res:',res)
                    let data = {}
                    if (res.data.error_code) {
                        data = res.data
                        data.error = true
                    } else {
                        data = res.data.data || {}
                        data.error = false
                    }
                    that.end_time = (new Date()).getTime()
                    console.log('time:', that.end_time - that.start_time)
                    timer && clearTimeout(timer)
                    let end_req_time = new Date().getTime()
                    if (!data.error) {
                        if (data && data.roi) {
                            result.data.roi = []
                            for (let key in data.roi) {
                                if (key && data.roi[key].length > 0) {
                                    result.data.roi.push(data.roi[key])
                                }
                                if (options && options.FILE && options.FILE.roi) {
                                    result.data.roi = cloneDeep(that.searchInfo.roi)
                                } else {
                                    that.searchInfo.roi = cloneDeep(result.data.roi)
                                }
                            }
                        }
                        // if(result.data.roi.length<1&&result.data.list.length<1&&options.type=='image'){
                        //     that.searchInfo.msg = that.lang.picture_is_no_roi
                        // }
                        result.data.ids = data.ids || []
                        result.data.list = data.list || []
                        that.isSearchLoading = false
                        resolve(result)
                        return
                    } else if (data.error == 1) {
                        let msg = that.transErrorMsg(data.key)
                        this.$message.error(msg)
                        result.error = true
                        result.msg = msg
                        resolve(result)
                        return
                    }
                })
            }).catch(() => { })
        },
        //提示错误
        transErrorMsg(error_key) {
            let msg = this.lang.unknown_error
            if (this.lang.error[error_key] || this.lang[error_key]) {
                msg = this.lang.error[error_key] || this.lang[error_key]
            }
            this.$message.error(msg)
            return msg
        },
        //点击选中文件
        selectPicture() {
            this.searchInfo.searchInputKey = ''
            this.$store.commit('caseDatabase/updateSearchParams', {
                type: 'text',//text,url,file
                content: ''
            })
            this.isShowHot = false
            //模拟本地
            this.$refs.selectPictureRef.click();
        },
        //显示搜索图片
        showUploadImage() {
            this.uploadImageObj = {
                imageUrl: this.searchInfo.imageSrc,
                file: this.searchInfo.file,
                roi: this.searchInfo.roi,
                selectRoi: this.searchInfo.selectRoi,
                isHttpResoucel: this.searchInfo.isUrl,
            }
            this.galleryState = 2
        },
        //刷新图片搜索
        async refreshlImageSearch() {
            let that = this
            that.clearFilterCondition()
            that.isShowHot = false
            that.isSearchLoading = true
            that.exams.endCase = 20
            that.exams.currentExamList = []
            that.exams.ids = []
            that.exams.oldIds = []

            if (that.searchInfo.msg == ''
                || (that.searchInfo.msg == this.lang.is_no_case_text)) {
                // || (that.searchInfo.msg == this.lang.picture_is_no_roi)
                that.searchInfo.msg = ''
                //
                if (that.searchInfo.isUrl) {
                    let op = {
                        realUrl: that.searchInfo.url,
                    }
                    if (that.searchInfo.roi.length > 0) {
                        op.roi = that.searchInfo.roi[that.searchInfo.selectRoi]
                    }
                    await this.pictureUrlDeal(op)
                } else {
                    let op = {
                        file: that.searchInfo.file,
                    }
                    if (that.searchInfo.roi.length > 0) {
                        op.roi = that.searchInfo.roi[that.searchInfo.selectRoi]
                    }
                    await this.uploadPicture(op)
                }

                that.isSearchLoading = false
            } else {
                that.isSearchLoading = false
                that.$message.error(that.searchInfo.msg);
            }
        },
        //清空搜索
        clearImageSearch() {
            this.isShowHot = false
            this.searchInfo = cloneDeep(this.defaultSearchInfo)
            this.imageDraw = ''
            this.exams.endCase = 20
            this.clearFilterCondition()
            this.exams.currentExamList = []
            this.exams.ids = []
            this.exams.oldIds = []

            this.startTextSearch();

        },
        //上传文件
        async uploadPicture(options = {}) {
            console.log('uploadPicture')
            let roiObj = {
                roi: cloneDeep(this.searchInfo.roi),
                selectRoi: this.searchInfo.selectRoi
            }
            this.searchInfo = cloneDeep(this.defaultSearchInfo)
            this.imageDraw = ''
            this.clearFilterCondition()
            this.exams.endCase = 20
            this.exams.currentExamList = []
            this.exams.ids = []
            this.exams.oldIds = []
            this.isSearchLoading = true
            let that = this;
            let timer = null
            let client = null
            let time_start = new Date().getTime()
            return new Promise((resolve, reject) => {
                let result = { error: false, msg: '', data: {} }
                timer = setTimeout(() => {
                    result.msg = 'timeout'
                    that.$message.error(that.lang.requestTimeout)
                    that.isSearchLoading = false
                    client && cancelUpload(client)
                    reject(result)
                    return
                }, 30 * 1000)
                that.$once('hook:beforeDestroy', function () {
                    timer && clearTimeout(timer)
                })
                let roi = options.roi || []
                let files = []
                let URL = window.URL || window.webkitURL;
                if (options && options.file && options.file != null) {
                    files = that.isArrary(options.file) ? options.file : [options.file]

                } else {
                    files = that.$refs.selectPictureRef.files
                }
                let base64_obj = options.base64_obj
                // that.user = that.$store.state.user

                if (files && files.length > 0) {
                    let file = files[0];
                    if (that.$refs.selectPictureRef && that.$refs.selectPictureRef.value) {
                        that.$refs.selectPictureRef.value = []
                    }
                    let file_type = file.name.replace(/.+\./, "")
                    that.searchInfo.name = file.name
                    if (that.searchInfo.name.length > 20) {
                        that.searchInfo.name = that.searchInfo.name.substr(0, 15) + '...'
                    }
                    let is_legal_format = ['png', 'jpg', 'jpeg', 'bmp'].indexOf(file_type.toLowerCase()) > -1
                    if (!is_legal_format) {
                        that.$message.error(that.lang.picture_is_only_jpg_jpeg_bmp_png);
                        timer && clearTimeout(timer)
                        result = { error: true, msg: that.lang.picture_is_only_jpg_jpeg_bmp_png, data: {} }
                        that.isSearchLoading = false
                        that.searchInfo.msg = that.lang.picture_is_only_jpg_jpeg_bmp_png
                        reject(result)
                        return
                    }
                    let is_legal_size = file.size <= 1024 * 1024 * 10 ///不能大于10M
                    if (options.size && options.size != null) {
                        is_legal_size = parseFloat(options.size) <= 1024 * 1024 * 10  ///不能大于10M
                    }
                    let reader = new FileReader()
                    reader.onload = function (e) {
                        let data = e.target.result
                        let image = new Image()
                        image.onload = async function () {
                            console.log('onload:', 1)
                            let width = image.width
                            let height = image.height
                            let is_legal_resolution_ratio = image.width > 0 && image.height > 0
                            let tempUrl = URL.createObjectURL(file)
                            that.searchInfo.imageSrc = tempUrl
                            that.searchInfo.file = base64_obj || file
                            if (is_legal_format && is_legal_size && is_legal_resolution_ratio) {
                                let file_id = that.user.uid + that.user.client_uuid + new Date().getTime()
                                let destination = that.systemConfig.serverInfo.oss_attachment_server.sub_dir + "/" + 'AiSearch' + '/' + that.user.uid + '/' + file_id + '.' + file_type
                                that.searchInfo.url = that.systemConfig.serverInfo.oss_attachment_server.playback_https_addr + '/' + destination
                                client = await uploadFile({
                                    bucket: that.systemConfig.serverInfo.oss_attachment_server.bucket,
                                    filePath: destination,
                                    file,
                                    callback: async (event, data) => {

                                        if ("error" == event) {
                                            //上传失败文件记录到本地缓存
                                            timer && clearTimeout(timer)
                                            console.error('FileReader error:', e);
                                            result = { error: true, msg: that.lang.file_upload_exception, data: {} }
                                            that.$message.error(that.lang.file_upload_exception);
                                            that.isSearchLoading = false
                                            reject(result)
                                            return
                                        } else {
                                            if (event && event == "complete") {
                                                timer && clearTimeout(timer)
                                                let time_end = new Date().getTime()
                                                let num = 30000 - time_end + time_start
                                                let param = {
                                                    type: 'image',
                                                    CLASSIFICATION: 'BREAST',
                                                    FILE: {
                                                        filename: that.searchInfo.name,
                                                        url: that.searchInfo.url
                                                    },
                                                    splitCount: ''
                                                }
                                                if (roi && roi.length > 0) {
                                                    param.FILE.roi = roi
                                                    that.searchInfo.roi = roiObj.roi
                                                    that.searchInfo.selectRoi = roiObj.selectRoi
                                                }
                                                let datas = await that.searchAction('find_by_image', param, num)
                                                // console.error('datas:',datas)
                                                if (datas && !datas.error) {
                                                    that.exams.currentExamList = datas.data.list
                                                    that.exams.ids = datas.data.ids
                                                    that.exams.oldIds = cloneDeep(datas.data.ids)

                                                    if (that.exams.ids < 1) {
                                                        that.searchInfo.msg = that.lang.is_no_case_text
                                                        if (datas.data.roi.length < 1) {
                                                            // that.searchInfo.msg = that.lang.picture_is_no_roi
                                                        }
                                                    }
                                                }
                                                that.isSearchLoading = false
                                                resolve(datas)
                                                return
                                            }
                                        }
                                    }
                                })
                            } else {
                                timer && clearTimeout(timer)
                                console.log('Please choose an leagal image file.');
                                let msg = ''
                                result = { error: true, msg: msg, data: {} }
                                if (!is_legal_size) {
                                    msg = that.lang.upload_max_text + '10M'
                                }
                                if (!is_legal_resolution_ratio) {
                                    msg = that.lang.picture_is_too_blurred
                                }
                                if (!is_legal_format) {
                                    msg = that.lang.picture_is_only_jpg_jpeg_bmp_png
                                }
                                that.searchInfo.msg = msg
                                result.msg = msg
                                that.isSearchLoading = false
                                reject(result)
                                return
                            }
                        }
                        image.onerror = function (e) {
                            timer && clearTimeout(timer)
                            result = { error: true, msg: this.lang.get_picture_info_fail, data: {} }
                            console.error('new Image error:', e);
                            that.isSearchLoading = false
                            that.$message.error(that.lang.get_picture_info_fail);
                            reject(result)
                            return
                        }
                        image.src = data
                    };
                    reader.onerror = function (e) {
                        timer && clearTimeout(timer)
                        result = { error: true, msg: this.lang.get_picture_info_fail, data: {} }
                        console.error('FileReader error:', e);
                        that.$message.error(that.lang.get_picture_info_fail);
                        that.isSearchLoading = false
                        reject(result)
                        return
                    }
                    reader.readAsDataURL(file);
                } else {
                    timer && clearTimeout(timer)
                    console.log('Please choose an image file.');
                    // that.isLatestExamlist = true
                    result = { error: true, msg: 'Please choose an image file.', data: {} }
                    that.$message.error(that.lang.reselect_upload_file);
                    that.isSearchLoading = false
                    reject(result)
                    return
                }

            }).catch(() => { })
        },
        //网络资源
        async pictureUrlDeal(options) {
            console.log('pictureUrlDeal')
            let roiObj = {
                roi: cloneDeep(this.searchInfo.roi),
                selectRoi: this.searchInfo.selectRoi
            }
            this.searchInfo = cloneDeep(this.defaultSearchInfo)
            this.imageDraw = ''
            this.exams.endCase = 20
            this.exams.currentExamList = []
            this.exams.ids = []
            this.exams.oldIds = []

            this.clearFilterCondition()
            let that = this;
            let timer = null
            let time_start = new Date().getTime()

            return new Promise((resolve, reject) => {
                let result = { error: false, msg: '', data: {} }
                timer = setTimeout(() => {
                    result.msg = 'timeout'
                    that.$message.error(that.lang.requestTimeout)
                    that.isSearchLoading = false
                    reject(result)
                    return
                }, 30 * 1000)
                that.$once('hook:beforeDestroy', function () {
                    timer && clearTimeout(timer)
                })
                let roi = options.roi || []
                let image_url = options.realUrl || getRealUrl(options)
                if (image_url == '' || image_url == undefined || image_url == null) {
                    image_url = options.url
                }
                let temp_arr = image_url.split('_thumb')
                if (temp_arr && temp_arr.length > 1) {
                    temp_arr.pop()
                    image_url = temp_arr.join('_thumb')
                }
                that.searchInfo.url = image_url
                that.searchInfo.imageSrc = image_url
                that.searchInfo.file = that.searchInfo.url
                that.searchInfo.isUrl = true
                let URL = window.URL || window.webkitURL;
                let file_type = that.searchInfo.url.replace(/.+\./, "")
                that.searchInfo.name = that.searchInfo.url.split('/').pop()
                if (that.searchInfo.name.length > 20) {
                    that.searchInfo.name = that.searchInfo.name.substr(0, 15) + '...'
                }
                if (that.searchInfo.url && that.searchInfo.name) {
                    let is_legal_format = ['png', 'jpg', 'jpeg', 'bmp'].indexOf(file_type.toLowerCase()) > -1
                    if (!is_legal_format) {
                        that.$message.error(that.lang.picture_is_only_jpg_jpeg_bmp_png);
                        result = { error: true, msg: that.lang.picture_is_only_jpg_jpeg_bmp_png, data: {} }
                        that.isSearchLoading = false
                        that.searchInfo.msg = that.lang.picture_is_only_jpg_jpeg_bmp_png
                        timer && clearTimeout(timer)
                        reject(result)
                        return
                    }

                    console.log('load image')
                    let realImage = new Image()
                    realImage.onerror = function (e) {
                        timer && clearTimeout(timer)
                        result = { error: true, msg: this.lang.get_picture_info_fail, data: {} }
                        console.error('new Image error:', e);
                        that.isSearchLoading = false
                        that.$message.error(that.lang.get_picture_info_fail);
                        reject(result)
                        return
                    }
                    realImage.onload = function () {
                        var xhr = new XMLHttpRequest();
                        xhr.open('HEAD', that.searchInfo.url, true);
                        xhr.onreadystatechange = async function () {
                            if (xhr.readyState == 4) {
                                if (xhr.status == 200) {
                                    timer && clearTimeout(timer)
                                    let size = xhr.getResponseHeader('Content-Length')
                                    let is_legal_size = size <= 1024 * 1024 * 10 ///不能大于10M
                                    let is_legal_resolution_ratio = realImage.width > 0 && realImage.height > 0
                                    if (is_legal_format && is_legal_size && is_legal_resolution_ratio) {
                                        timer && clearTimeout(timer)
                                        let time_end = new Date().getTime()
                                        let num = 30000 - time_end + time_start
                                        let param = {
                                            type: 'image',
                                            CLASSIFICATION: 'BREAST',
                                            FILE: {
                                                filename: that.searchInfo.name,
                                                url: that.searchInfo.url
                                            },
                                            splitCount: '',
                                            group_id: options.group_id,
                                            resource_id: options.resource_id,
                                        }
                                        if (roi && roi.length > 0) {
                                            param.FILE.roi = roi
                                            that.searchInfo.roi = roiObj.roi
                                            that.searchInfo.selectRoi = roiObj.selectRoi
                                        }

                                        console.log('iimage searchAction')
                                        let datas = await that.searchAction('find_by_image', param, num)
                                        if (datas && !datas.error) {
                                            that.exams.currentExamList = datas.data.list
                                            that.exams.ids = datas.data.ids
                                            that.exams.oldIds = cloneDeep(datas.data.ids)
                                            if (that.exams.ids < 1) {
                                                that.searchInfo.msg = that.lang.is_no_case_text
                                                if (datas.data.roi.length < 1) {
                                                    // that.searchInfo.msg = that.lang.picture_is_no_roi
                                                }
                                            }
                                        }
                                        that.isSearchLoading = false
                                        resolve(datas)
                                        return
                                    } else {
                                        timer && clearTimeout(timer)
                                        console.log('Please choose an leagal image file.');
                                        let msg = ''
                                        if (!is_legal_size) {
                                            msg = that.lang.upload_max_text + '10M'
                                        }
                                        if (!is_legal_resolution_ratio) {
                                            msg = that.lang.picture_is_too_blurred
                                        }
                                        if (!is_legal_format) {
                                            msg = that.lang.picture_is_only_jpg_jpeg_bmp_png
                                        }
                                        that.searchInfo.msg = msg
                                        result.msg = msg
                                        that.isSearchLoading = false
                                        reject(result)
                                        return
                                    }
                                } else {
                                    timer && clearTimeout(timer)
                                    result = { error: true, msg: this.lang.get_picture_info_fail, data: {} }
                                    that.$message.error(that.lang.get_picture_info_fail);
                                    that.isSearchLoading = false
                                    reject(result)
                                    return
                                }
                            }
                        }
                        xhr.send(null);
                    }
                    if (this.systemConfig.serverInfo.network_environment === 1) {
                        that.searchInfo.url = Tool.replaceInternalNetworkEnvImageHost(that.searchInfo.url)
                    }
                    realImage.setAttribute("crossOrigin", 'anonymous');
                    realImage.src = `${that.searchInfo.url}?temp=${Date.now()}`;
                } else {
                    timer && clearTimeout(timer)
                    console.log('Please choose an image file.');
                    // that.isLatestExamlist = true
                    result = { error: true, msg: 'Please choose an image file.', data: {} }
                    that.$message.error(that.lang.reselect_upload_file);
                    that.isSearchLoading = false
                    reject(result)
                    return
                }
            })
        },
        //隐藏病例图片
        showImageEvent(flag) {
            this.exams.pathologyInfo = {};
            this.galleryState = 0;
        },
        //隐藏上传图片
        async showSearchImageEvent(param) {
            let that = this
            that.clearFilterCondition()
            that.galleryState = 0
            if (param.flag) {//用户改变了选中roi
                that.isSearchLoading = true
                let roi = that.searchInfo && that.searchInfo.roi && that.searchInfo.roi[param.roiIndex] ? that.searchInfo.roi[param.roiIndex] : ''
                that.searchInfo.selectRoi = param.roiIndex
                that.searchInfo.endCase = 0
                that.exams.currentExamList = []
                that.exams.ids = []
                that.exams.oldIds = []

                that.exams.endCase = 20
                let op = {
                    "FILE": {
                        "url": that.searchInfo.url,
                        "roi": roi,
                    },
                }
                console.error('**123')
                let datas = await that.searchAction('find_by_image', op)
                if (datas && !datas.error) {
                    that.exams.currentExamList = datas.data.list
                    that.exams.ids = datas.data.ids
                    that.exams.oldIds = cloneDeep(datas.data.ids)

                }
                that.isSearchLoading = false
            } else {
            }
        },
        // 加载更多
        async loadMore() {
            let pageNo = Math.ceil(this.exams.endCase / 20) + 1;
            let ids = this.exams.ids.slice(this.exams.endCase, this.exams.endCase + 20);
            let op = {
                ids: ids,
                pageNo: pageNo,
                pageSize: this.exams.sliceCount
            }
            this.isSearchLoading = true
            let datas = await this.searchAction('find_by_ids', op)
            if (datas && !datas.error) {
                this.exams.currentExamList = this.exams.currentExamList.concat(datas.data.list)
                // that.exams.ids = datas.data.ids
                this.exams.endCase += 20;
            }
            this.isSearchLoading = false
        },
        blurInput() {
            this.isShowHot = false;
            this.$refs.inputVal.blur()
            this.startTextSearch()
            // if(this.searchInfo.searchInputKey.length!=0){
            // this.sortAndSaveTag(this.searchInfo.searchInputKey)
            // }
            // this.$nextTick(()=>this.$refs.inputVal.blur())
        },
        // focus事件
        async clickInput() {
            this.isShowHot = true
            let oldText = this.searchInfo.searchInputKey
            this.searchInfo = cloneDeep(this.defaultSearchInfo)
            this.imageDraw = ''
            this.searchInfo.searchInputKey = oldText
            this.exams.endCase = 20
            this.exams.currentExamList = []
            this.exams.ids = []
            this.exams.oldIds = []

            let op = {
                "text": '回声',
            }
            this.isSearchLoading = true
            let datas = await this.searchAction('find_by_keyword', op)
            if (datas && !datas.error) {
                this.exams.currentExamList = datas.data.list
                this.exams.ids = datas.data.list.map(v => {
                    return v.collection_id
                })
                this.exams.oldIds = cloneDeep(this.exams.ids)
            }
            this.isSearchLoading = false
        },
        //显示画廊
        showGallery(items) {
            this.showImage = true;
            this.galleryState = 1
            const collectionID = items.collection_id;
            // window.main_screen.sendAnalyzeClick({
            //     collectionID
            // })
            service.requestAiAnalyzeWithUrl({ method: 'update.click.' + collectionID, condition: { type: 'BREASTSEARCH' } }).then(async (res) => {
                console.log(res)
            })
            this.exams.pathologyInfo = items;
        },
        // 点击搜索历史
        clickSearch(keyword) {
            this.searchInfo.searchInputKey = keyword;
            this.startTextSearch()
        },
        //搜索历史
        initTagHistory() {
            const localTag = window.localStorage.getItem('caseTagHistoryList') || ''
            if (localTag == '') {
                return;
            }
            this.history.list = localTag.split(',')
        },
        sortAndSaveTag(keyword) {
            //重排和保存tag
            if (keyword && keyword.length > 0) {
                const index = this.history.list.indexOf(keyword)
                if (index > -1) {
                    this.history.list.splice(index, 1);
                    this.history.list.unshift(keyword)
                } else {
                    if (this.history.list.length >= this.maxNum) {
                        this.history.list.pop();
                    }
                    this.history.list.unshift(keyword);
                }
                //  console.error(this.history.list.join(','))
                window.localStorage.setItem('caseTagHistoryList', this.history.list.join(','))
            }
        },
        clearKey() {
            this.searchInfo.searchInputKey = '';
            this.startTextSearch()
        },
        deleteTag(tag) {
            const index = this.history.list.indexOf(tag);
            this.history.list.splice(index, 1);
            window.localStorage.setItem('caseTagHistoryList', this.history.list.join(','))
        },
        clearTag() {
            this.$confirm(this.lang.delete_all_history_tip, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_button_text,
                cancelButtonText: this.lang.cancel_button_text,
                type: 'warning'
            }).then(() => {
                this.history.list = [];
                window.localStorage.setItem('caseTagHistoryList', '')
            })
        },
        //筛选条件
        async handleback(newCondition = null) {
            // console.error('newCondition',newCondition)
            if (newCondition != null) {
                this.$store.commit('caseDatabase/updateCondition', newCondition)
                this.filters.condition = cloneDeep(newCondition)
            }
            this.isSearchLoading = true
            await this.startFliter()
            this.isSearchLoading = false
        },
        async startFliter() {
            // if(this.isSetCondition){
            this.exams.endCase = 20
            let content = this.searchInfo.searchInputKey
            if (this.exams.oldIds.length < 1 && (this.searchInfo.searchInputKey || this.searchInfo.imageSrc)) {
                return
            }
            let op = {
                ids: this.exams.oldIds,
                text: content, pageNo: 1,
                pageSize: this.exams.sliceCount,
                condition: this.filters.condition
            }
            let datas = await this.searchAction('fliter_by_condition', op)
            if (datas && !datas.error) {
                this.exams.currentExamList = datas.data.list
                this.exams.ids = datas.data.ids
            }
            // }
        },
        //检查查找
        isArrary(a) {
            return a instanceof Array
        },
    },
    destroyed() {
        this.$store.commit('caseDatabase/updateSearchParams', {
            type: 'text',//text,url,file
            content: ''
        })
    }
}
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/education.scss';

.case_database_zhezhao {
    .el-loading-mask {
        background-color: rgba(255, 255, 255, 0.5);
        position: fixed;
        z-index: 99999;
        margin: 0;
        top: calc(10% + 40px);
        right: 11%;
        bottom: 11%;
        left: 11%;
    }
}

.case_database_page_container {
    height: 100%;
    overflow: hidden;

    .case_database_page {
        height: 100%;
        overflow: auto;
        padding: 20px;
        box-sizing: border-box;

        .left-margin {
            margin-left: 8px;

        }

        .select_picture {
            display: none;
        }

        .popContainer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: '100%';
            width: '100%';
            bottom: 0;
            z-index: 999;
            background: rgba(0, 0, 0, 0.3);
        }

        .container {
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;

            .search_box {
                position: relative;
                width: 100%;
                height: 32px;
                padding: 0 10px;
                line-height: 32px;
                border-bottom: 1px solid #DBDBDB;

                .el-input {
                    position: absolute;
                    width: calc(100% - 26px);
                }

                i,
                input {
                    vertical-align: middle;
                    vertical-align: middle;
                    height: 100%;
                }

                input {
                    margin-left: 5px;
                    color: #666;
                    width: 90%;
                    border: none !important;
                }

                .iconfont.iconclose {
                    color: #bbcdce;
                    font-size: 20px;
                    position: absolute;
                    right: 8px;
                    cursor: pointer;
                }
            }

            .no_data {
                text-align: center;
                font-size: 18px;
            }

            .search_loading {
                text-align: center;
                margin-top: 1%;
                font-size: 18px;
            }

            .search_key {
                display: flex;
                flex-wrap: wrap;
                padding-right: 20px;
                position: relative;
                padding-bottom: 5px;

                &>button {
                    margin-right: 5px;
                    margin-top: 10px;
                    position: relative;
                    padding-right: 22px;
                }

                &>p {
                    text-align: center;
                    width: 100%;
                    font-size: 17px;
                    margin-top: 10px;
                }

                .iconclose {
                    position: absolute;
                    right: 4px;
                    top: 6px;
                    font-size: 14px;
                }

                .iconel-icon-delete2 {
                    color: #666;
                    position: absolute;
                    right: 4px;
                    top: 6px;
                    font-size: 20px;
                    cursor: pointer;
                }
            }

            .search_loading {
                z-index: 10;
            }

            .search_result {
                width: 100%;
                font-size: 17px;
                margin-top: 10px;
                margin-left: 5px;
            }

            .search_result {
                border-bottom: 1px solid #DBDBDB;
            }

            .examlist_tip {
                border-bottom: 1px solid #DBDBDB;
            }

            .case_list_container {
                padding: 0.65rem 0.8rem 0;
                z-index: 1;
                flex: 1;
                overflow-y: auto;
                overflow-x: hidden;

                .case_item {
                    width: 100%;
                    padding: 0.7rem 2.1rem 0.15rem;
                    margin-bottom: 0.6rem;
                    background-color: #f2f6f9;
                    border-radius: 0.2rem;
                    box-shadow: 0.1rem 0.1rem 0.2rem rgba(140, 152, 155, 0.7);
                    position: relative;
                    overflow: hidden;
                    border-top: 1px solid #a9bfbe;

                    .patient_info_item {
                        font-size: 0.7rem;
                        margin-left: 160px;

                        p {
                            padding-top: 0.4rem;
                            font-size: 0.7rem;
                        }

                        .patient_info_item_name {
                            font-size: 1rem;
                            margin-bottom: 15px;
                        }

                        .patient_info_item_people {
                            display: flex;

                            p {
                                padding-right: 10px;
                            }
                        }
                    }

                    .image_list_first {
                        user-select: none;

                        .image_item,
                        .folder_item {
                            float: left;
                            width: 140px;
                            height: 110px;
                            box-sizing: border-box;
                            position: relative;
                            background: #000;
                            margin-right: 6px;
                            margin-bottom: 6px;
                            overflow: hidden;
                            border-radius: 0.2rem;

                            .image_item_container {
                                position: relative;
                                padding-top: 75%;
                                height: 0;
                                background-color: #000;
                                overflow: hidden;

                                .image {
                                    max-width: 100%;
                                    max-height: 100%;
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                }
                            }

                            .picture_format {
                                position: absolute;
                                top: 10px;
                                right: 10px;
                                border: 1px solid gray;
                                width: 30px;
                                text-align: center;
                                background-color: gray;
                                border-radius: 20px;
                                font-size: 0.5rem;
                                color: white;
                                letter-spacing: 1px;
                            }
                        }

                        .image_item {
                            float: left;
                            width: 250px;
                            height: auto;
                            position: relative;
                            background: #000;
                            margin-right: 25px;
                            margin-bottom: 6px;
                            overflow: hidden;
                            border-radius: 0.2rem;
                            cursor: pointer;
                        }

                        .folder_item {
                            float: right;
                            width: calc(100% - 444px);
                            background: transparent;

                            .image_item_container {
                                cursor: pointer;
                                background-color: transparent;

                                .exam_down,
                                .exam_up {
                                    position: absolute;
                                    top: 50%;
                                    left: 50%;
                                    transform: translate(-50%, -50%);
                                    width: 1rem;
                                    height: 1rem;
                                }
                            }
                        }
                    }
                }

                .load-more {
                    font-size: 20px;
                    text-align: center;
                    cursor: pointer;
                }

            }
        }

        .examlist_image_tip_container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 100%;
            height: calc(40% + 50px);
            font-size: 15px;
            color: #6d7271;
            border-bottom-width: 1px;
            border-bottom-style: solid;
            border-bottom-color: #00c59d;
            vertical-align: middle;

            .examlist_image_button_wrap {
                height: calc(100%);
                text-align: center;

                .examlist_image_button {
                    color: #00c59d;
                    width: 30%;
                    height: 100%;
                    border-radius: 9px;
                    -webkit-border-radius: 9px;
                    -moz-border-radius: 9px;
                    padding: 5px;
                    object-fit: contain;
                }
            }
        }

        .examlist_image_close {
            display: flex;
            margin-top: -25px;
            color: #00c59d;
            width: calc(100%);
            line-height: 25px;
            color: black;
            color: #00c59d;
            height: 25px;
            margin-top: -25px;
            width: 100%;

            .left {
                width: 50%;

                &>i {
                    text-align: left;
                    float: left;
                    margin: auto;
                }
            }

            .right {
                width: 50%;

                &>i {
                    margin: auto;
                    text-align: right;
                    float: right;
                }
            }


        }
    }
}
</style>
