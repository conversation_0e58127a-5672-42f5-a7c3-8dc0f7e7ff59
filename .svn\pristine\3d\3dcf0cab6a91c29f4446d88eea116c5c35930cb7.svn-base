<template>
	<div class="mine_page">
		<div class="setting_list_group">
			<a class="setting_list_item personal_info" href="#/index/personal_setting" >
                <mr-avatar :url="avatar_url" :origin_url="user.avatar" :radius="2.6" :showOnlineState="false" :key="user.avatar"></mr-avatar>
				<div class="personal_info_right longwrap">
					<p class="nickname longwrap">{{user.nickname}}</p>
					<p class="account longwrap">{{lang.register_account}}：{{user.username}}</p>
				</div>
                <i class="iconfont svg_icon_entry icon-entry"></i>
                <span class="user_unread" v-show="isShowAccountImprove"></span>
			</a>
            <p v-if="functionsStatus.referralCode&&isShowInviteRegistration" class="setting_list_item" @click="inviteRegistration">{{lang.invite_registration}}
            </p>
            <p v-if="functionsStatus.referralCode&&!isShowInviteRegistration" class="setting_list_item" @click="fillReferral">{{lang.fill_in_referral_code}}
                <span class="user_unread"></span>
            </p>
		</div>
		<div class="setting_list_group">
            <p class="setting_list_item" @click="openCloudFavorites">{{lang.cloud_favorites}}</p>
			<p class="setting_list_item" @click="openCloudExam">{{lang.my_cloud_exam}}
                <span v-if="showHomeworkUnread" class="user_unread"></span>
            </p>
            <p class="setting_list_item" @click="gotoMyDevicePage" v-if="isUltraSoundMobile">{{lang.ultrasound_device_title}}</p>
		</div>
		<div class="setting_list_group">
            <a v-show="isShowAdmin"  class="setting_list_item" href="#/index/setting">{{lang.system_setting_text}}</a>
			<!-- <a v-show="isAdmin" class="setting_list_item" href="#/index/admin_login">{{lang.background_manage_title}}</a> -->

			<a class="setting_list_item" href="#/index/about_ultrasync">{{lang.about_ultrasync}}</a>
		</div>
		<div class="logout_btn_wrapper">
			<van-button v-loading="logouting" type='danger' size="large" class="logout_btn" @click="logout">{{lang.logout}}</van-button>
		</div>

	</div>

</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import {getLocalAvatar,resetLoginStorage} from '../lib/common_base'
import { destroyAllConference } from "../lib/common_base";
import { Button } from 'vant'
export default {
    mixins: [base],
    name: 'minePage',
    props:{
        currentTab:{
            type:String,
            default:'chat'
        }
    },
    components: {
        VanButton: Button,
    },
    data(){
        return {
            getLocalAvatar,
            logouting:false,
        }
    },

    mounted(){
        this.$root.eventBus.$off('logout').$on('logout',this.logout);
    },
    computed:{
        isShowAdmin(){
            if (this.isApp){
                return true
            }
            return true
        },
        isAdmin(){
            return this.user.role==2||this.user.role==3
        },
        isShowInviteRegistration(){
            if ('undefined' == typeof this.user.probationary_expiry) {
                return false;
            }

            return 0 < this.user.probationary_expiry.length ? false : true;
        },
        isShowAccountImprove(){
            return  !this.user.is_enhance_password || !this.user.is_password_privatized
        },
        isUltraSoundMobile(){
            return this.$store.state.device.isUltraSoundMobile
        },
        avatar_url(){
            return this.getLocalAvatar(this.user)
        },
        showHomeworkUnread(){
            return this.$store.state.homework.globalUnfinish > 0 || this.$store.state.homework.globalUnCorrect !==undefined ||
            (this.$store.state.homework.globalCorrected !== undefined && this.$store.state.homework.globalCorrected > 0)
        }
    },
    methods:{
        logout(){
            this.logouting=true;
            this.$root.eventBus.$emit('unBindControllerEvent')
            setTimeout(()=>{
                window.CWorkstationCommunicationMng.ULinkerConnectionResult({
                    errorcode: 0,
                    errormsg: '',
                    action: 'Logout',
                });
                this.resetApp()
                try{
                    service.logout({
                        user_id:this.$store.state.user.uid,
                        client_uuid:this.$store.state.user.client_uuid
                    }).then((res)=>{

                    }).catch(res=>{

                    })
                }catch(e){
                    console.error(e,'logout')
                }
                this.$root.eventBus.$emit('reloadRouter')
            },0)


        },
        openLocalPatients(){
            this.$router.push(`index/local_patients`)
        },
        openCloudFavorites(){
            this.$router.push(`index/user_favorites`)
        },
        resetApp(){
            window.CWorkstationCommunicationMng.notifyDisconnectFromDoppler();
            resetLoginStorage()
            this.$store.commit('user/updateUser',{
                new_token:''
            });
            window.CWorkstationCommunicationMng.CallTEAirActionResult({action:'logout',result:1})
            if (window.main_screen && window.main_screen.gateway) {

                window.main_screen.CloseSocket();
            }
            destroyAllConference();
            if(window.main_screen&&window.main_screen.CMonitorWallPush&&window.main_screen.CMonitorWallPush.joined){
                window.main_screen.CMonitorWallPush.LeaveChannelSilence()
            }
            setTimeout(()=>{
                this.$router.replace(`/login`);
            },500)
        },
        inviteRegistration(){
            let path=this.$route.path;
            path+='/invite_registration';
            this.$router.push(path);
        },

        gotoMyDevicePage(){
            this.$router.push('/index/my_device');
        },
        fillReferral(){
            const token=this.user.new_token;
            this.$router.push(`/index/referral_code?token=${token}&isShowBack=1`)
        },
        openCloudExam(){
            this.$router.push('/index/cloud_exam')
        }
    }
}

</script>
<style lang="scss">
	.mine_page{
        height: 100%;
        overflow: auto;
        position: relative;
        z-index: 1;
        transform: translate3d(0, 0, 0);
        padding-bottom: 50px;
        box-sizing: border-box;
        .setting_list_group {
            .setting_list_item{
                display: flex;
                font-size: 0.8rem;
                position: relative;
                .user_unread{
                    position: absolute;
                    background-color: #f00;
                    border-radius: 50%;
                    min-width: 0.5rem;
                    min-height: 0.5rem;
                    right: .8rem;
                    top: .7rem;
                }
            }
        }
		.personal_info{
            position:relative;
            display: flex;
            align-items: center;
			.personal_info_right{
				padding:.4rem .8rem;
				line-height:1.2rem;
                flex: 1;
				.nickname{
					font-size:1rem;
				}
				.account{
					font-size:0.8rem;
					color:#999;
				}
			}
            .svg_icon_entry{
                position:absolute;
                right:0;
                top:2em;
                fill:#aaa;
                color:#aaa;
                width: 0.4rem;
                height: 100%;
            }
            .user_unread{
                position: absolute;
                background-color: #f00;
                border-radius: 50%;
                min-width: 0.5rem;
                min-height: 0.5rem;
                left: 2.8rem;
                right: auto !important;
                top: 1rem !important;
            }
		}
		.logout_btn_wrapper{
			padding:0 .5rem;
			.logout_btn{
				background-color:#ff6759;
				height:2rem;
				font-size:.8rem;
                border-color: #ff6759;
			}
		}

	}
</style>
