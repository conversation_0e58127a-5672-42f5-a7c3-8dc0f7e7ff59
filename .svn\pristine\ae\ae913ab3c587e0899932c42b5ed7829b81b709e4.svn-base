<template>
    <div class="welcome-animation-container">
        <div class="welcome" >
            <div class="text-wrapper">
                <div class="text-container fade-animation">
                    {{ line2Text }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import base from '../../../lib/base'
export default {
    mixins: [base],
    name: 'WelcomeComponent',
    data() {
        return {
            line2Text: "欢迎来到医学影像智慧之境！",

        }
    },
    created() {
        this.line2Text = this.lang.welcome_to_realm_imaging_intelligence
    },
    mounted() {
        // 监听动画结束
        const textContainer = this.$el.querySelector('.text-container');
        textContainer.addEventListener('animationend', () => {
            this.$emit('animation-completed');
        });
    }
};
</script>

<style lang="scss" scoped>
.welcome-animation-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    background-color: #fff;

.welcome {
    text-align: center;
}

.text-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.text-container {
    font-size: 40px;
    letter-spacing: 4px;
    color: #303133;
    opacity: 0; /* 确保初始状态是不可见的 */
    transform: scale(0.8); /* 初始缩放比例为0.8 */
}

.fade-animation {
    animation: fadeInOut 2.5s ease-in-out forwards;
}

@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    70% {
        opacity: 1;
        transform: scale(1);
    }
    100% { /* 2.5s */
        opacity: 0;
        transform: scale(1);
    }
}
}

</style>
