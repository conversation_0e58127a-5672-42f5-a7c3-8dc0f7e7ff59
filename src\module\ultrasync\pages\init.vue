<template>
    <div>
        <loading-page v-click-n-times:7="callVConsole" v-if="loading && !timeout"></loading-page>
        <div v-else-if="timeout" class="timeout-ui">
            <div class="timeout-text">
            Loading failed, please try again later
            </div>
            <van-button class="refresh-btn" type="primary" @click="refreshPage">refresh</van-button>
        </div>
        <PrivacyPolicy :show.sync="showPrivacyPolicy" ref="PrivacyPolicy"></PrivacyPolicy>
    </div>

</template>
<script>
import LoadingPage from "../components/loadingPage.vue";
import languages from "@/common/language";
import vConsole from "vconsole";
import { Toast,Button } from 'vant';
import CWorkstationCommunicationMng from "@/common/CommunicationMng/index";
import Tool from "@/common/tool.js";
import service from "../service/service";
import { getAppVersion } from "@/common/version";
import {checkPrivacyPolicy} from "../lib/common_base"
import PrivacyPolicy from "../components/privacyPolicy.vue"
import { designatedClientType,clientTypeMap } from '../../../../config/clientType'
// 判断是否允许离开路由
let allowLeave = false; // 这里设置为 false，表示禁止离开
export default {
    mixins: [],
    name: "initPage",
    components: {
        LoadingPage,
        PrivacyPolicy,
        [Button.name]: Button
    },
    beforeRouteLeave(to, from, next) {
    // 在用户尝试离开当前路由时执行逻辑
        if(this.showPrivacyPolicy){
            this.$refs.PrivacyPolicy&&this.$refs.PrivacyPolicy.close()
        }
        if (allowLeave) {
            next(); // 允许离开，继续导航
        } else {
            next(false); // 禁止离开，取消导航
        }
    },
    data() {
        return {
            lang: this.$store.state.language,
            systemConfig: this.$store.state.systemConfig,
            globalParams: this.$store.state.globalParams,
            showPrivacyPolicy:false,
            loading: true,
            timeout: false,
            timeoutTimer: null,
            timeoutStartTime: null, // 记录倒计时开始时间
            timeoutPausedTime: null, // 记录暂停时的剩余时间
            timeoutDuration: 30000 // 倒计时总时长（30秒）
        };
    },
    async beforeCreate() {
        window.CWorkstationCommunicationMng = CWorkstationCommunicationMng;
    },
    created() {
        this.startTimeout();
        window.Logger.save({
            message:`buildTime:${process.env.VUE_APP_BUILD_TIME}`,
            eventType:'init'
        })
        this.checkImmediateOpenVConsole()
        let systemConfig = this.$store.state.systemConfig;
        let cloudConfig = systemConfig.cloudConfig;
        const isCE = process.env.VUE_APP_PROJECT_NOV === "CE";

        this.$store.commit("globalParams/updateGlobalParams", {
            isCE: isCE,
        });
        const osName = this.getOSName();
        this.$store.commit("globalParams/updateGlobalParams", {
            osName: osName,
        });
        let { server_type, appVersion } = this.getServerType();
        console.log(server_type, appVersion, "getServerType");
        this.$store.commit("systemConfig/updateSystemConfig", {
            server_type: server_type,
            appVersion: appVersion,
            // cloudInfo: cloudInfo,
        });
        this.$root.platformToast = Toast;
        if (isCE) {
            //CE版本处理
            // cloudInfo=systemConfig.regionConfig.serverList

            Tool.replaceAppNameToLanguages(languages);
        } else {
            // cloudInfo = cloudConfig[appVersion]
        }
        //全局变量，用与语言切换引起的token清空问题
        window.iSLanguageChanged = false;

    },
    async beforeMount() {},
    mounted() {

        setTimeout(async () => {
            try {
                const browse_type = await CWorkstationCommunicationMng.init();
                if(browse_type === 'UNKNOW' || !browse_type){
                    window.location.reload();
                    window.CWorkstationCommunicationMng.reloadWebview({ address: window.location.href });
                    return
                }
                CWorkstationCommunicationMng.hideStartupView();
                window.Logger.save({
                    message:`browse_type:${browse_type}`,
                    eventType:'init'
                })
                await this.getEnvConfig()
                await this.setInitInformation()
                await this.queryConfig()
                await this.handleAfterAgreePrivacyPolicy()
                if(browse_type.indexOf('BROWSER') === -1){
                    await this.getMobileInfo();
                }
                // this.setClientType()
            } catch(error){
                console.error(error)
            } finally {

                try {
                    this.setClientType();
                    this.setDefaultAutoPushStream();
                    this.checkIOSUpdate();
                    if (window.browse_type == "MUI_IOS" || window.browse_type == "MUI_ANROID") {
                    // window.clientType = this.systemConfig.client_type.AppMobile
                    // this.$store.commit('systemConfig/updateSystemConfig', { clientType: window.clientType })
                        try {
                            CWorkstationCommunicationMng.setStatusBarBackground({ color: "#01C59D" });

                            Tool.createCWorkstationCommunicationMng({
                                name: "isApplicationExist",
                                emitName: "NotifyIsApplicationExist",
                                params: { pname: "com.tencent.mm", action: "weixin://" },
                                timeout: null,
                            }).then((res) => {
                                console.log("NotifyIsApplicationExist:", res);
                                let weiXinInstalled = res.error_code == 0;
                                this.$store.commit("globalParams/updateGlobalParams", {
                                    weiXinInstalled: weiXinInstalled,
                                });
                            });
                            //访问类型切换为app
                            Tool.createCWorkstationCommunicationMng({
                                name: "shareGetService",
                                emitName: "NotifyShareGetService",
                                params: {},
                                timeout: null,
                            }).then((res) => {
                                console.log("NotifyShareGetService:", res);
                                if (res.error_code == "0") {
                                    window.shares = {};
                                    for (var i in res.list) {
                                        var t = res.list[i];
                                        window.shares[t.id] = t;
                                        window.shares[t.id].authenticated = JSON.parse(window.shares[t.id].authenticated);
                                        console.log("shares id: " + t.id);
                                    }
                                } else {
                                    Toast("获取分享服务列表失败：" + res.error_message);
                                }
                            });
                            //获得默认地址
                            Tool.createCWorkstationCommunicationMng({
                                name: "getDefaultServer",
                                emitName: "NotifyGetDefaultServer",
                                params: {},
                                timeout: null,
                            }).then((res) => {
                                console.log("NotifyGetDefaultServer:", res);
                                if (res.error_code == "0") {
                                    let server_type = this.systemConfig.server_type;
                                    server_type.default_server = res.default_server || window.location.href;
                                    this.$store.commit("systemConfig/updateSystemConfig", {
                                        server_type: server_type,
                                    });
                                } else {
                                }
                            });
                        } catch (error) {
                            Toast(error);
                        }
                    //禁用侧滑
                    // console.error(wv,'webview')
                    // wv.setStyle({'popGesture':'none'});
                    // return
                    }
                    await this.getRegionFunctions();
                    clearTimeout(this.timeoutTimer)
                    this.handleAfterInit();

                } catch (error) {
                    clearTimeout(this.timeoutTimer)
                    this.loading = false;
                    this.timeout = true;
                }



            }
        },0);
    },
    methods: {
        getEnvConfig(){
            return new Promise((resolve)=>{
                service.getEnvConfig().then((res)=>{
                    console.log("getEnvConfig:", res);
                    // 将getEnvConfig返回的数据存储到store中
                    if (res && res.data) {
                        // 统一存储envConfig数据
                        this.$store.commit("systemConfig/updateSystemConfig", {
                            envConfig: res.data.data
                        });
                    }
                    resolve(res)
                }).catch(e=>{
                    console.warn("getEnvConfig failed, but continuing with initialization:", e)
                    // 即使失败也resolve，确保不影响后续流程
                    resolve(null)
                })
            })
        },
        handleAfterInit() {
            allowLeave = true
            this.$store.commit("globalParams/updateGlobalParams", {
                init: true,
            });
            const isReadULinkerManual = window.localStorage.getItem('isReadULinkerManual') === '1'
            if(!isReadULinkerManual&&this.globalParams.isCE&&Tool.checkAppClient('UltraSoundMobile')){
                this.$router.replace("/uLinkerInstructionManual")
            }else{
                const redirectPath = this.$route.query.previousPath||'/index'
                this.$router.replace(redirectPath)
            }

        },
        getOSName() {
            let ua = navigator.userAgent.toLowerCase();
            let os_name = "unKnow";
            if (ua.match(/android|adr/i)) {
                os_name = "android";
            } else if (ua.match(/iphone|ipad|ipod/i)) {
                os_name = "ios";
            } else if (ua.match(/(Windows NT|Macintosh|Linux)/i)) {
                os_name = "windows";
            }
            return os_name;
        },
        getLoginState(ultraSync_state) {
            var result = ultraSync_state & this.systemConfig.UltraSync_State.UltraSync_Login;
            if (result == 0) {
                return 0;
            } else {
                return 1;
            }
        },
        getRegisterState(ultraSync_state) {
            var result = ultraSync_state & this.systemConfig.UltraSync_State.UltraSync_registered;
            if (result == 0) {
                return 0;
            } else {
                return 1;
            }
        },
        DEBUG_TO_SERVER(msg, data) {
            if (window.main_screen) {
                window.main_screen.gateway.emit("debug", msg, data);
            }
        },
        async getMobileInfo() {
            const notifyRes = await Tool.createCWorkstationCommunicationMng({
                name: "getMobileInfo",
                emitName: "notifySetMobileInfo",
                params: {},
                timeout: 1500,
                checkClientType:false
            });
        },
        setClientType() {
            window.clientType = this.systemConfig.client_type.MobileBrowser;
            if (window.browse_type == "MUI_IOS" || window.browse_type == "MUI_ANROID") {
                window.clientType = this.systemConfig.client_type.AppMobile;
            }
            if (this.$store.state.device.isUltraSoundMobile) {
                window.clientType = this.systemConfig.client_type.UltraSoundMobile;
            }
            this.$store.commit("systemConfig/updateSystemConfig", { clientType: window.clientType });
            if (window.browse_type == "MUI_IOS") {
                this.$store.commit("globalParams/updateGlobalParams", {
                    osName: "ios",
                });
            }
            if (window.browse_type == "MUI_ANROID") {
                this.$store.commit("globalParams/updateGlobalParams", {
                    osName: "android",
                });
            }
            this.setDesignatedClientType()
        },
        setDesignatedClientType(){
            if(designatedClientType){
                window.clientType = designatedClientType
                window.localStorage.setItem("clientType", designatedClientType);
                this.$store.commit("systemConfig/updateSystemConfig", {
                    clientType: window.clientType,
                });
                if(designatedClientType === clientTypeMap.UltraSoundMobile){
                    this.$store.commit("device/updateDeviceInfo", {
                        isUltraSoundMobile: true,
                    });
                }
            }
        },
        setDefaultAutoPushStream() {
            if (this.$store.state.device.isUltraSoundMobile) {
                let isAutoPushStream = window.localStorage.getItem("isAutoPushStream");
                if (isAutoPushStream === undefined) {
                    window.localStorage.setItem("isAutoPushStream", 1);
                }
            }
        },
        initMuiConfig() {},
        async checkIOSUpdate() {
            if (Tool.checkAppClient("App") && Tool.checkAppClient("IOS") && !Tool.checkAppClient("TEAir")) {
                const res = await getAppVersion();
                let versions = res.data.app_desc.version.split(".");
                // let versions=window.AppVersion.app_desc.version.split('.');
                // let versions="2.06.666".split('.')
                let MajorVersion = versions[0] + "." + versions[1];
                let MinorVersion = versions[2];
                service.upgradeIOSInfo({ MajorVersion, MinorVersion }).then((res) => {
                    if (res.data.error_code == 0) {
                        this.upgradeIOS(res.data.data, MinorVersion, MajorVersion);
                    }
                });
            }
        },
        upgradeIOS(data, MinorVersion, MajorVersion) {
            let force = false;
            let that = this;
            if (data.Action == 1) {
                force = true;
            } else if (data.Action == 2) {
                let ignoreUpgradeVersion = window.localStorage.getItem("ignoreUpgradeVersion");
                if(ignoreUpgradeVersion!==null){
                    if (ignoreUpgradeVersion === `${MajorVersion}${MinorVersion}`) {
                        return;
                    }
                }
            } else {
                return;
            }
            var info = `${that.lang.cur_version} ${MajorVersion}.${MinorVersion} <br/>${that.lang.new_version} ${data.MajorVersion}.${data.MinorVersion}<br/>${that.lang.confirm_update_to_new_version}`;

            Tool.openMobileDialog(
                {
                    message: info,
                    showRejectButton:!force,
                    showCancelButton:!force,
                    closeOnClickOverlay:false,
                    closeOnPopstate:false,
                    forbiddenClose:force,
                    confirm:()=>{
                        CWorkstationCommunicationMng.forceUpdateApp({
                            apk_download_url: data.DownloadUrl,
                        });
                    },
                    reject:()=>{
                        window.localStorage.setItem("ignoreUpgradeVersion", `${MajorVersion}${MinorVersion}`);
                    },
                    cancel:()=>{
                        window.localStorage.setItem("ignoreUpgradeVersion", `${MajorVersion}${MinorVersion}`);
                    }
                }
            )
        },
        getServerType() {
            if (window.location.protocol.indexOf("file:") < 0) {
                //在线包
                let port = window.location.port ? ':' + window.location.port : '';
                let server_type = {
                    hostname: window.location.hostname,
                    protocol: window.location.protocol + '//',
                    host: window.location.hostname,
                    port: port,
                    enable_sms_identification: true,
                    websocket_protocol: window.location.protocol === 'https:' ? 'wss://' : 'ws://',
                    websocket_prot: window.location.protocol === 'https:' ? ':443' : port,
                };
                if(window.location.protocol === 'sw:'){
                    server_type.protocol = 'https://'
                    server_type.port = ':443',
                    server_type.websocket_protocol = 'wss://'
                    server_type.websocket_prot = ':443'
                }
                let appVersion = "Prod";
                if (Tool.isDev() && process.env.NODE_ENV !== 'development') {
                    if(!this.$root.isInitVConsole){
                        this.$root.isInitVConsole = true;
                        window.vConsole = new vConsole();
                    }

                    appVersion = "Dev";
                } else if (Tool.isBeta()) {
                    appVersion = "Beta";
                } else if (window.location.href.indexOf("localhost") > -1) {
                    appVersion = "Dev";
                } else if (!!window.location.hostname.match(/^((25[0-5]|2[0-4]\d|[01]?\d\d?)($|(?!\.$)\.)){4}$/)) {

                }
                return { server_type, appVersion };
            } else {
                //本地包
                console.log("getServerType", window.location);
                let server_type = {
                    hostname: "consult.mindray.com",
                    protocol: "https://",
                    host: "consult.mindray.com",
                    port: ":443",
                    enable_sms_identification: true,
                    websocket_protocol: "wss://",
                    websocket_prot: ":443",
                };
                let appVersion = "Prod";
                if (window.urlHash.host) {
                    server_type.hostname = window.urlHash.host;
                    server_type.host = window.urlHash.host;
                }
                if (Tool.isDev()) {
                    appVersion = "Dev";
                    if(!this.$root.isInitVConsole){
                        this.$root.isInitVConsole = true;
                        window.vConsole = new vConsole();
                    }
                }
                if (!!window.location.hostname.match(/^((25[0-5]|2[0-4]\d|[01]?\d\d?)($|(?!\.$)\.)){4}$/)) {
                    server_type.port = ":" + window.location.port;
                    server_type.websocket_prot = ":" + window.location.port;
                    server_type.host = window.location.hostname;
                    server_type.protocol = window.location.protocol + "//" ;
                    if(!this.$root.isInitVConsole){
                        this.$root.isInitVConsole = true;
                        window.vConsole = new vConsole();
                    }
                }
                return { server_type, appVersion };
            }
        },
        getRegionFunctions() {
            return new Promise((resolve, reject) => {
                service
                    .getRegionFunctions({
                        region: this.globalParams.region,
                    })
                    .then(async (res) => {
                        if (res.data.error_code == 0) {
                            this.$store.commit("globalParams/updateFunctionsStatus", res.data.data);
                            resolve(true);
                        } else {
                            setTimeout(()=>{
                                this.getRegionFunctions()
                                resolve(true)
                            },3000)
                        }
                    })
            });
        },
        queryConfig(){
            return new Promise((resolve,reject)=>{
                const getConfig = (qt=0)=>{
                    let queryTimes = qt||0;
                    service.query_login_config().then((res)=>{
                        this.$store.commit('systemConfig/updateSystemConfig',{
                            serverInfo:res.data
                        })
                        resolve(res)
                    }).catch(e=>{
                        if (queryTimes<100) {
                            setTimeout(()=>{
                                getConfig(++queryTimes);
                            },3000)
                        }else{
                            reject(e)
                        }
                    })
                }
                getConfig()
            })
        },
        setInitInformation(){
            return new Promise((resolve,reject)=>{
                let localLanguage = window.localStorage.getItem("lang");
                let lang = localLanguage || "CN";
                if (this.isCE) {
                    lang = localLanguage || "EN";
                }
                if (window.browse_type == "MUI_IOS" || window.browse_type == "MUI_ANROID") {
                    let osInfo = Tool.createCWorkstationCommunicationMng({
                        name: "getOSInformation",
                        emitName: "NotifyOSInformation",
                        params: {},
                        timeout: 1500,
                        checkClientType:false
                    }).then(async (res) => {
                        console.log("NotifyOSInformation:", res);
                        // let localLanguage=window.localStorage.getItem('lang')
                        if (!localLanguage) {
                            lang = "EN";
                            let os_lang = res.data.language.toLowerCase();
                            if (/zh_|zh-|zh/.test(os_lang)) {
                                lang = "CN";
                            }
                            if (/es-ES/.test(os_lang)) {
                                lang = "ES";
                            }
                            if (/ru/.test(os_lang)) {
                                lang = "RU";
                            }
                            if (/pt/.test(os_lang)) {
                                lang = "PTBR";
                            }
                            if (/de/.test(os_lang)) {
                                lang = "DE";
                            }
                            if (/fr/.test(os_lang)) {
                                lang = "FR";
                            }
                            if (/it/.test(os_lang)) {
                                lang = "IT";
                            }
                        }
                        console.log("操作系统语言:", lang);
                        this.$store.commit("language/setLanguage", languages[lang]);
                        this.$store.commit("language/setLanguage", { currentLanguage: lang });
                        this.$store.commit("globalParams/updateGlobalParams", {
                            osVersion: res.data.version,
                        });
                        this.$store.commit("globalParams/updateGlobalParams", {
                            region: res.data.areaData && res.data.areaData.currentArea,
                        });
                        // await this.getRegionFunctions();
                        resolve()
                    });
                }else{
                    // let lang = 'EN'
                    this.$store.commit("language/setLanguage", languages[lang]);
                    this.$store.commit("language/setLanguage", { currentLanguage: lang });
                    resolve('not app')
                }

            })
        },
        // 开始倒计时
        startTimeout() {
            this.timeoutStartTime = Date.now();
            this.timeoutTimer = setTimeout(() => {
                this.loading = false;
                this.timeout = true;
            }, this.timeoutDuration);
        },
        // 暂停倒计时
        pauseTimeout() {
            if (this.timeoutTimer) {
                clearTimeout(this.timeoutTimer);
                this.timeoutTimer = null;
                // 计算剩余时间
                const elapsed = Date.now() - this.timeoutStartTime;
                this.timeoutPausedTime = Math.max(0, this.timeoutDuration - elapsed);
            }
        },
        // 恢复倒计时
        resumeTimeout() {
            if (this.timeoutPausedTime !== null && this.timeoutPausedTime > 0) {
                this.timeoutStartTime = Date.now();
                this.timeoutTimer = setTimeout(() => {
                    this.loading = false;
                    this.timeout = true;
                }, this.timeoutPausedTime);
                this.timeoutPausedTime = null;
            }
        },
        handleAfterAgreePrivacyPolicy(){
            return new Promise((resolve,reject)=>{
                const privacyCheck = checkPrivacyPolicy()

                // 检查是否需要显示隐私协议界面
                // 1. 从未同意过隐私协议 (!hasAgreed)
                // 2. 需要重新同意隐私协议 (needReagree)
                if(!privacyCheck.hasAgreed || privacyCheck.needReagree){
                    // 需要显示隐私协议弹窗，暂停倒计时
                    this.pauseTimeout();
                    this.showPrivacyPolicy = true
                    this.$root.eventBus.$once('handleAfterAgreePrivacyPolicy',(isAgree)=>{
                        if(isAgree){
                            this.showPrivacyPolicy = false
                            // 恢复倒计时
                            this.resumeTimeout();
                            resolve()
                        }
                    });
                }else{
                    // 已经同意过且版本号一致，直接放行
                    // 获取当前版本号并通知客户端
                    const privacy_version = this.$store.state.systemConfig.envConfig && this.$store.state.systemConfig.envConfig.privacy_agreement_version
                    CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                        status: 1,
                        version: privacy_version
                    })
                    resolve()
                }

            })
        },
        setHeaderClassToAndroid(){
            let body = document.getElementsByTagName("body")[0];
            console.error(body.classList)
            body.classList.remove("mui-ios");
            body.classList.add("mui-android");
        },
        callVConsole(){
            if (!this.$root.isInitVConsole) {
                this.$root.isInitVConsole = true
                window.vConsole = new vConsole()
            }else{
                window.vConsole.destroy();
                this.$root.isInitVConsole=false;
            }
        },
        checkImmediateOpenVConsole(){
            if (!this.$root.isInitVConsole) {
                const  immediateOpenVConsole = window.localStorage.getItem('immediateOpenVConsole')
                if(immediateOpenVConsole === '1'){
                    this.$root.isInitVConsole = true;
                    window.vConsole = new vConsole();
                }

            }
        },
        refreshPage() {
            window.location.reload();
            window.CWorkstationCommunicationMng.reloadWebview({ address: window.location.href }); // 安卓APP因为reload无效提供了一个重新加载方法
        }
    },
    beforeDestroy() {
        // 清理倒计时定时器
        if (this.timeoutTimer) {
            clearTimeout(this.timeoutTimer);
            this.timeoutTimer = null;
        }
    }
};
</script>
<style lang="scss" scoped>
    .timeout-ui{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100vh;
        .timeout-text{
            margin-bottom: 2rem ;
        }
    }
</style>
