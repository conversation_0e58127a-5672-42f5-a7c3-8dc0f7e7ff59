<template>
    <div>
        <div class="version_history_view">
            <div class="version_history_head">{{ lang.history_version }}</div>
            <div class="version_history_body">
                <div class="version_history_list">
                    <template v-if="historyList.length > 0">
                        <div class="version_history_item" v-for="(item, idx) in historyList" :key="item.version">
                            <div class="version_history_item_head">
                                <p class="version">{{ item.version + " " + lang.history_version_title }}</p>
                                <p class="time">{{ formattedTime(item.time) }}</p>
                                <div class="extend_icon">
                                    <img
                                        :id="'extend_' + idx"
                                        class="extend_img"
                                        :src="imageBase64.extend"
                                        @click="handleHistoryItemContent(idx, 'extend')"
                                    />
                                    <img
                                        :id="'shrink_' + idx"
                                        class="shrink_img"
                                        :src="imageBase64.shrink"
                                        style="display: none"
                                        @click="handleHistoryItemContent(idx, 'shrink')"
                                    />
                                </div>
                            </div>
                            <template v-if="item.content.length > 0">
                                <div :id="'item_content_' + idx" class="shrank">
                                    <hr />
                                    <div class="version_history_item_body" v-for="val in item.content" :key="val.text">
                                        <p class="version_history_item_body_content" v-for="el in val" :key="JSON.stringify(el)">
                                            <template v-if="el.type === 'text'">{{ el.text }}</template>
                                            <template v-if="el.type === 'image'">
                                                <img v-for="img in el.images" :key="img.url" v-lazy="img.url">
                                            </template>
                                        </p>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../lib/base";
import vConsole from "vconsole";
import service from "../service/service";
export default {
    mixins: [base],
    name: "versionHistory",
    data() {
        return {
            ajaxServer: "",
            historyList: [],
            imageBase64: {
                shrink: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAZCAYAAAC/zUevAAAA0klEQVRIS+3WvQ2DMBQE4PMsaa/MABkgmSmwUwbIAql4ZcbIAJGlWEKRjd8PBYVpkJCBj3sHIuEAWzqAAQNRpjCS2C0JkpOITJGCh8aRAQDuAOYIxI1YAUoIbogLUQGEIGZELYHfSNwQE6I1gsrxXNZZW1Y1oteBCESF6AHKE3shXYQWEIFsIqwAL6SJIHkC8F6Vy/QdqDzATUQetbJqESZALZGU0nlZlpcJkReTvOS9iDy1r9v/OpJXAJ+ta3SL6b255byB2O1/whJ7a+0YR0nmC7y4aBp7ccuqAAAAAElFTkSuQmCC",
                extend: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAZCAYAAAC/zUevAAAA8klEQVRIS+2VvQ3CMBCF7SYNomSGNHEaClZggTBJ1kjLEGQc2zWb+JARkSIrP+/OFCmc+vnpy7t3tlYH+PQBGFSBmKZQkoCSqOv6XFXV3Vo7SgtsjLmGEG7e++eax+Y4mqaheJCIRu/9gwtijOmI6PU71zvnhiWPVYi2bU8hhLdS6iIBSQCixeCc61kQUZwaoYlwz+1uB9eQq48/uwvBSUQCAEMgIFIAFsQWSA4AG2INRGvdTa1HyzvfEqgT6VotrN9XIgEQJTEB5Y4gO4kURJoA9HYg13RMJOdtyRoHAohqRMVEzVFdgfhbMdHIt3SHGMcHq97QGn2MsQAAAAAASUVORK5CYII=",
            },
        };
    },
    computed: {
        currentLanguage() {
            return localStorage.getItem("lang");
        },
        formattedTime() {
            return function (time) {
                const timeArr = time.toString().split("-");
                return timeArr.map((val) => {
                    if(val.length > 2) {
                        return val.padStart(4, "0")
                    }
                    return val.padStart(2, "0")
                }).join("-");
            };
        },
    },
    created() {
        this.$nextTick(async () => {
            this.ajaxServer =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.port;
            const data = {
                type: (() => {
                    const u = navigator.userAgent;
                    if (u.indexOf("iPhone") > -1 || u.indexOf("iPad") > -1) {
                        return "IOS";
                    } else if (u.indexOf("Android") > -1) {
                        return "Android";
                    } else {
                        return "PC";
                    }
                })(),
                language: this.currentLanguage || "CN",
            };
            const {
                data: { list: res },
            } = await service.getQueryHistoryVersion(this.ajaxServer, data);
            this.historyList = [...res];
            console.log(this.historyList);
        });
    },
    methods: {
        handleHistoryItemContent(idx, method) {
            let content = document.querySelector(`#item_content_${idx}`);
            let extend = document.querySelector(`#extend_${idx}`);
            let shrink = document.querySelector(`#shrink_${idx}`);
            if (method === "extend") {
                extend.style.display = "none";
                shrink.style.display = "block";
                content.classList.add("extended");
                content.classList.remove("shrank");
            } else {
                shrink.style.display = "none";
                extend.style.display = "block";
                content.classList.add("shrank");
                content.classList.remove("extended");
            }
        },
    },
};
</script>

<style>
body {
    background-color: #e7eff2;
}

hr {
    height: 1px;
    margin: 0.2em 0 0.2em 0;
    border: none;
    background-color: black;
}

.version {
    width: 15em;
    margin-right: 1em;
}

.time {
    width: 10em;
}

.extend_icon {
    cursor: pointer;
    height: min-content;
}

.shrank {
    height: 0;
}

.extended {
    height: auto;
}

.version_history_head {
    font-size: 1.875rem;
    width: 100%;
    border-bottom: 2px solid #000000;
    text-align: center;
    padding: 10px 0;
}

.version_history_item {
    overflow: hidden;
    background-color: #f5f8fa;
    padding: 0 2rem;
    margin-top: 0.8rem;
}

.version_history_item_head {
    width: 100%;
    height: 1.375rem;
    line-height: 1.375rem;
    font-weight: 500;
    font-size: 1.375rem;
    display: flex;
    justify-content: space-between;
}

.version_history_item_body {
    font-size: 1.125rem;
    margin-block-start: 1em;
    margin-block-end: 1em;
    margin-inline-start: 0;
    margin-inline-end: 0;
}

@media all and (max-width: 520px) {
    .version_history_item_head {
        font-size: 1.1rem;
    }
    .version_history_item_body {
        font-size: 1rem;
    }
}

@media all and (max-width: 400px) {
    .version_history_item_head {
        font-size: 1rem;
    }
    .version_history_item_body {
        font-size: 0.895rem;
    }
}
</style>
