<template>
    <transition name="slide">
    	<div class="groups_page second_level_page">
            <mrHeader>
                <template #title>
                    {{lang.my_groups}}
                </template>
            </mrHeader>
            <van-index-bar class="my_groups" :index-list="indexBarList" highlight-color="#00c59d" v-loading="!initList">
                <div v-for="(item,itemIndex) of indexedList" :key="itemIndex">
                    <van-index-anchor :index="item.title"  />
                    <div v-for="group of item.list" class="group_item clearfix" @click="openConversation(group.id,2)" :key="group.id">
                        <mr-avatar :url="getLocalAvatar(group)" :origin_url="group.avatar" :showOnlineState="false" :key="group.avatar" :is_public="group.is_public"></mr-avatar>
                        <p class="longwrap">{{group.subject}}</p>
                    </div>
                </div>
            </van-index-bar>
    	</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import { IndexBar, IndexAnchor } from 'vant'
import {getIndexedList,getLocalAvatar} from '../lib/common_base'
export default {
    mixins: [base],
    name: 'GroupsPage',
    components: {
        VanIndexBar: IndexBar,
        VanIndexAnchor: IndexAnchor
    },
    data(){
        return {
            getLocalAvatar,
            groupList:[],
            initList:false
        }
    },
    computed:{
        indexedList(){
            let indexedListTemp =  getIndexedList(this.groupList,'subject');
            let indexList =[]
            for (let i = 0; i < indexedListTemp.length; i++) {
                if(indexedListTemp[i].list.length > 0){

                    indexList.push(indexedListTemp[i]);
                }
            }
            return indexList
        },
        indexBarList(){
            let indexedListTemp =  getIndexedList(this.groupList,'subject');
            let barList = []
            for (let i = 0; i < indexedListTemp.length; i++) {
                if(indexedListTemp[i].list.length > 0){
                    barList.push(indexedListTemp[i].title)
                }
            }
            return barList;
        },
    },
    mounted(){
        setTimeout(()=>{
            this.groupList=this.$store.state.groupList
            this.initList=true;
        },300)
    },
    methods:{
    }
}

</script>
<style lang="scss">
	.groups_page{
        display: flex;
        flex-direction: column;
        height:100%;
        background-color:#fff;
        .my_groups{
            flex: 1;
            overflow: auto;
            position: relative;
            background-color: #F4F4F4;


            .van-index-bar__index {
                line-height: 22px;
                font-size: .6rem;
                flex: 1;
            }

            .van-index-anchor--sticky{
               transform: translate3d(0, 0, 0) !important;
               position: inherit;
            }
        }
        .group_item{
            background-color:#fff;
            border-bottom:1px solid #eee;
            padding:.4rem .6rem;
            // margin:0 1rem 0 .7rem;
            font-size:0.8rem;
            display: flex;
            align-items: center;
            & > p{
                line-height:2rem;
                padding-left:0.5rem;
                color:#333;
            }
        }
	}
</style>
