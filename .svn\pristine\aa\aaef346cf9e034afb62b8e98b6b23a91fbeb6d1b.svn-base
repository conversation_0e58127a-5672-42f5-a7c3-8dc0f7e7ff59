<template>
    <div class="uploadTestOverview_container">
        <div class="custom_header">
            <div class="back_btn" @click.stop="back">
                <i class="el-icon-arrow-left"></i>
                <span>{{ lang.back_button }}</span>
            </div>
        </div>
        <div class="custom_body" ref="customBody" v-loading="loading">
            <template v-if="!loading">
                <div class="top_info_section">
                    <div class="icon_area">
                        <div class="placeholder_icon">
                            <i class="el-icon-collection"></i>
                        </div>
                    </div>
                    <div class="details_area">
                        <div class="detail_top">
                            <div class="detail_title">
                                {{ title }}
                            </div>
                        </div>
                        <div class="detail_bottom" v-if="deadline">
                            <p class="deadline">{{ capitalizeFirstLetter(lang.deadline) }}: {{ deadline }}</p>
                        </div>
                        <div
                            v-if="!isExamPassed"
                            class="action_buttons_container"
                            style="display: flex; align-items: center; margin-top: 12px"
                        >
                            <div class="upload-button-container">
                                <el-upload
                                    ref="uploadComponent"
                                    action=""
                                    :auto-upload="false"
                                    :show-file-list="false"
                                    :multiple="false"
                                    :on-change="handleFilesSelectedAndImmediateUpload"
                                    :file-list="fileListForElUpload"
                                    :before-upload="beforeUploadCheck"
                                >
                                    <el-button
                                        slot="trigger"
                                        type="primary"
                                        class="upload_btn"
                                        :loading="validatingFile"
                                    >
                                        <i class="el-icon-upload"></i>
                                        {{
                                            Object.values(this.uploadTask).some(
                                                (task) => task.status === "waiting" && !task.isUploaded && !task.ossContent
                                            )
                                                ? this.lang.select_file_again
                                                : this.lang.select_file
                                        }}
                                    </el-button>
                                </el-upload>
                                <!-- 禁用遮罩层 -->
                                <div
                                    v-if="isUploadButtonDisabled"
                                    class="button-disabled-overlay"
                                    @click.stop.prevent
                                ></div>
                            </div>
                            <div class="submit-button-container" style="margin-left: 10px">
                                <el-button
                                    plain
                                    class="submit_btn upload_btn"
                                    @click="handleSubmitAnswer"
                                    :loading="isSubmitting"
                                >
                                    {{ lang.submitted_answers }}
                                </el-button>
                                <!-- 禁用遮罩层 -->
                                <div
                                    v-if="isSubmitButtonDisabled"
                                    class="button-disabled-overlay"
                                    @click.stop.prevent
                                ></div>
                            </div>
                        </div>
                        <div
                            v-if="!isExamPassed"
                            class="el-upload__tip"
                            style="margin-top: 10px; color: #909399; font-size: 14px; text-align: left"
                        >
                            {{lang.support_format}}:{{ fileTypesText }}
                        </div>
                        <div
                            v-if="warningMessage.show"
                            :class="{
                                'warning_text': warningMessage.type === 'warning',
                                'success_text': warningMessage.type === 'success'
                            }"
                        >
                            <i :class="{
                                'el-icon-warning': warningMessage.type === 'warning',
                                'el-icon-success': warningMessage.type === 'success'
                            }"></i> {{ warningMessage.text }}
                        </div>
                    </div>
                </div>
                <div class="content_section review_history_section">
                    <h3>{{ lang.grading_history }}</h3>
                    <el-table :data="reviewHistoryData" style="width: 100%">
                        <el-table-column :label="capitalizeFirstLetter(lang.upload_datetime)" width="160">
                            <template slot-scope="scope">
                                <span>{{ formatTime(scope.row.createdAt) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="fileName" :label="capitalizeFirstLetter(lang.fileName)"></el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.fileSize)" width="160">
                            <template slot-scope="scope">
                                <span>{{ formatFileSize(scope.row.fileSize) }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.correction_status)" width="160">
                            <template slot-scope="scope">
                                <span v-if="scope.row.status === 1">{{ lang.Uncorrected }}</span>
                                <span v-else-if="scope.row.status === 2">{{ lang.corrected }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.exam_result)" width="160">
                            <template slot-scope="scope" v-if="scope.row.status === 2">
                                <span
                                    :class="[
                                        {
                                            'status-tag-passed-new': scope.row.isPass,
                                            'status-tag-failed-new': !scope.row.isPass,
                                        },
                                    ]"
                                    >{{ formatResult(scope.row.isPass) }}</span
                                >
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.gallery_navbar_comment)" min-width="200">
                            <template slot-scope="scope">
                                <el-tooltip
                                    :content="scope.row.teacherRemark"
                                    placement="top"
                                    :disabled="!scope.row.teacherRemark || scope.row.teacherRemark.length <= 100"
                                    popper-class="teacher-remark-tooltip"
                                >
                                    <div class="teacher-remark-text">
                                        {{ scope.row.teacherRemark }}
                                    </div>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.operation)" width="120">
                            <template slot-scope="scope">
                                <div class="operation-buttons-container">
                                    <el-button @click="handleDownloadFile(scope.row)" type="text" class="option-btn">
                                        <i class="el-icon-download"></i> {{ lang.download_title }}
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
                <div v-if="!isExamPassed" class="content_section uploaded_files_section">
                    <h3>{{ lang.case_exam_status.unsubmit }}</h3>
                    <el-table :data="processedUploadedFilesData" style="width: 100%">
                        <el-table-column prop="name" :label="capitalizeFirstLetter(lang.fileName)"></el-table-column>
                        <el-table-column prop="size" :label="capitalizeFirstLetter(lang.fileSize)"></el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.status)">
                            <template slot-scope="scope">
                                <template v-if="!scope.row.showProgress">
                                    <span
                                        :class="[
                                            {
                                                'status-tag-pending-new': scope.row.rawTask.status === 'waiting',
                                                'status-tag-passed-new': scope.row.rawTask.status === 'success',
                                                'status-tag-failed-new':
                                                    scope.row.rawTask.status === 'error' ||
                                                    (scope.row.rawTask.isError &&
                                                        scope.row.rawTask.status !== 'cancelled'),
                                                'status-tag-unsubmitted-new': scope.row.rawTask.status === 'cancelled',
                                                'status-tag-default-new':
                                                    !['waiting', 'success', 'error', 'cancelled'].includes(
                                                        scope.row.rawTask.status
                                                    ) &&
                                                    !(
                                                        scope.row.rawTask.isError &&
                                                        scope.row.rawTask.status !== 'cancelled'
                                                    ),
                                            },
                                        ]"
                                        >{{ scope.row.statusText }}</span
                                    >
                                </template>
                                <el-progress
                                    v-if="scope.row.showProgress"
                                    :percentage="scope.row.percentage"
                                    :stroke-width="16"
                                    text-inside
                                ></el-progress>
                            </template>
                        </el-table-column>
                        <el-table-column :label="capitalizeFirstLetter(lang.operation)" width="200">
                            <template slot-scope="scope">
                                <div class="operation-buttons-container">
                                    <el-button
                                        @click="handleDownloadFile(scope.row.rawTask)"
                                        type="text"
                                        class="option-btn"
                                        v-if="
                                            scope.row.isUploaded && scope.row.rawTask && scope.row.rawTask.ossImageUrl
                                        "
                                    >
                                        <i class="el-icon-download"></i> {{ lang.download_title }}
                                    </el-button>
                                    <el-button
                                        @click="handleDeleteFile(scope.row.rawTask)"
                                        type="text"
                                        class="option-btn"
                                        :disabled="
                                            scope.row.rawTask &&
                                            scope.row.rawTask.status === 'uploading' &&
                                            !scope.row.rawTask.ossContent
                                        "
                                    >
                                        <i class="el-icon-delete"></i> {{ lang.action_delete_text }}
                                    </el-button>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import base from "../../../lib/base";
import { uploadFile, cancelUpload } from "@/common/oss/index";
import Tool from "@/common/tool.js";
import { v4 as uuidv4 } from "uuid";
import service from "../../../service/service";
import moment from "moment";
export default {
    mixins: [base],
    name: "UploadTestOverview",
    components: {},
    data() {
        return {
            capitalizeFirstLetter: Tool.capitalizeFirstLetter,
            title: "",
            deadline: 0,
            reviewHistoryData: [],
            uploadTask: {},
            fileListForElUpload: [],
            assessmentInfo: {
                allowFileTypes: [
                    "jpg",
                    "jpeg",
                    "png",
                    "mp4",
                    "pdf",
                    "doc",
                    "docx",
                    "xml",
                    "avi",
                    "mov",
                    "wmv",
                    "flv",
                    "mkv",
                ],
                maxFileSizeMB: 200,
            },
            currentlyUploadingCount: 0,
            uploadQueue: [],
            debouncedAlertUnSupportedFileTypes: null,
            debouncedAlertMaxFileSize: null,
            loading: false,
            userRole: "",
            testId: "",
            trainingId: "",
            testInfo: {},
            validatingFile: false,
            isSubmitting: false,
            isSubmittingAnswer: false,
            shouldSubmitAnswer: false,
        };
    },
    computed: {
        // 判断是否超过截止时间
        isDeadlineExceeded() {
            if (!this.testInfo || !this.testInfo.deadline) {
                return false;
            }
            const currentTimeUnix = moment().unix();
            return currentTimeUnix > this.testInfo.deadline;
        },
        // 统一管理警告信息，优先显示考试截止
        warningMessage() {
            if (this.isDeadlineExceeded) {
                return {
                    show: true,
                    text: this.lang.exam_ended_cannot_be_submitted,
                    type: "warning"
                };
            }

            // 如果有批改历史记录，根据最新记录状态显示相应信息
            if (this.reviewHistoryData.length > 0) {
                const latestRecord = this.reviewHistoryData[0];

                // 如果最新记录是未批改状态
                if (latestRecord.status === 1) {
                    return {
                        show: true,
                        text: this.lang.unable_submit_wait_teacher_correction_result_tips,
                        type: "warning"
                    };
                }

                // 如果最新记录是已批改且通过
                if (latestRecord.status === 2 && latestRecord.isPass) {
                    return {
                        show: true,
                        text: this.lang.exam_already_passed,
                        type: "success"
                    };
                }

                // 如果最新记录是已批改但不通过，允许重新提交，不显示警告
            }

            return {
                show: false,
                text: "",
                type: "warning"
            };
        },
        // 判断考试是否已通过
        isExamPassed() {
            return this.reviewHistoryData.length > 0 &&
                   this.reviewHistoryData[0].status === 2 &&
                   this.reviewHistoryData[0].isPass;
        },
        // 判断用户是否可以上传新文件
        canUploadNewFile() {
            // 如果超过截止时间，不允许上传新文件
            if (this.isDeadlineExceeded) {
                return false;
            }
            // 如果正在加载数据，不允许上传新文件
            if (this.loading) {
                return false;
            }
            // 如果没有批改历史记录，允许上传新文件
            if (this.reviewHistoryData.length === 0) {
                return true;
            }
            // 如果最近一次提交状态是未批改(status === 1)，不允许上传新文件
            if (this.reviewHistoryData[0].status === 1) {
                return false;
            }
            // 如果最近一次提交状态是已批改(status === 2)且结果是不通过，允许上传新文件
            if (this.reviewHistoryData[0].status === 2 && !this.reviewHistoryData[0].isPass) {
                return true;
            }
            // 其他情况不允许上传新文件
            return false;
        },
        processedUploadedFilesData() {
            return Object.values(this.uploadTask).map((task) => {
                let statusText = "";
                let showProgress = false;

                switch (task.status) {
                case "waiting":
                    statusText = this.lang.waiting_upload;
                    break;
                case "uploading":
                    statusText = "";
                    showProgress = true;
                    break;
                case "success":
                    statusText = this.lang.upload_success;
                    break;
                case "error":
                    statusText = this.lang.homework.upload.error.general;
                    break;
                case "cancelled":
                    statusText = this.lang.cancel_btn;
                    break;
                default:
                    statusText = "";
                }
                if (task.isError && task.status !== "cancelled") {
                    statusText = this.lang.failed;
                }

                return {
                    uid: task.uid,
                    name: this.truncateFileName(task.name || "", 30),
                    size: this.formatFileSize(task.size || 0),
                    statusText: statusText,
                    percentage: task.percentage || 0,
                    showProgress: showProgress,
                    isUploaded: task.isUploaded,
                    rawTask: task,
                };
            });
        },
        fileTypesText() {
            if (
                !this.assessmentInfo ||
                !this.assessmentInfo.allowFileTypes ||
                this.assessmentInfo.allowFileTypes.length === 0
            ) {
                return "";
            }

            return this.assessmentInfo.allowFileTypes.map((type) => `.${type}`).join("、");
        },
        // 计算提交按钮是否应该被禁用
        isSubmitButtonDisabled() {
            // 检查是否有待提交的文件
            const pendingFiles = Object.values(this.uploadTask).filter(
                (task) => task.status === 'waiting' && !task.isUploaded && !task.ossContent
            );

            // 如果没有待提交的文件，禁用按钮
            if (pendingFiles.length === 0) {
                return true;
            }

            // 如果有文件正在上传，禁用按钮
            if (this.currentlyUploadingCount > 0) {
                return true;
            }

            // 如果正在提交，禁用按钮
            if (this.isSubmitting) {
                return true;
            }

            // 如果不能上传新文件，禁用按钮
            if (!this.canUploadNewFile) {
                return true;
            }

            return false;
        },
        // 计算上传按钮是否应该被禁用
        isUploadButtonDisabled() {
            // 如果有文件正在上传，禁用按钮
            if (this.currentlyUploadingCount > 0) {
                return true;
            }

            // 如果正在提交，禁用按钮
            if (this.isSubmitting) {
                return true;
            }

            // 如果不能上传新文件，禁用按钮
            if (!this.canUploadNewFile) {
                return true;
            }

            return false;
        },
    },
    watch: {
        $route: {
            handler(to) {
                console.log("to", to);
                if (to.name === "SmartTechTrainingExamProject_UploadTestOverview") {
                    this.fetchPageData();
                }
            },
            immediate: true,
        },
    },
    async created() {
        this.debouncedAlertUnSupportedFileTypes = Tool.debounce(this.alertUnSupportedFileTypesMsg, 300);
        this.debouncedAlertMaxFileSize = Tool.debounce(this.alertMaxFileSizeMsg, 300);
    },
    beforeDestroy() {
        Object.values(this.uploadTask).forEach((task) => {
            if (task.status === "uploading" && task.ossContent && typeof cancelUpload === "function") {
                cancelUpload(task.uploadId)
                    .then(() => console.log(`Upload ${task.uid} cancelled.`))
                    .catch((err) => console.error(`Error cancelling upload ${task.uid}:`, err));
                task.status = "cancelled";
            }
        });
    },
    methods: {
        beforeUploadCheck() {
            // Prevent file selection when upload button is disabled
            if (this.currentlyUploadingCount > 0 || this.isSubmitting || !this.canUploadNewFile) {
                return false;
            }
            return true;
        },
        async fetchPageData() {
            this.loading = true;
            this.userRole = this.$route.params.role;
            this.testId = this.$route.params.testId;
            this.trainingId = this.$route.params.trainingId;

            try {
                await Promise.all([this.getTrainingTestInfoByTestId(), this.getTrainingTestAnswerHistory()]);
            } catch (error) {
                console.error("获取页面数据失败:", error);
                this.back();
            } finally {
                this.loading = false;
            }
        },
        alertUnSupportedFileTypesMsg() {
            this.$message.error(this.lang.upload_forbidden_file_type_text);
        },
        alertMaxFileSizeMsg() {
            this.$message.error(`${this.lang.upload_max_text}${this.assessmentInfo.maxFileSizeMB}M`);
        },
        back() {
            if (this.$router) {
                this.$router.go(-1);
            } else {
                console.warn("Vue Router not found for back() method.");
            }
        },
        formatResult(isPass) {
            return isPass ? this.lang.passed : this.lang.not_pass;
        },
        handleUploadFile() {
            console.log("Upload file clicked");
        },
        handleDownloadFile(task) {
            console.log("Download file:", task);
            if (task && (task.fileUrl || task.ossImageUrl)) {
                const fileUrl = task.fileUrl || task.ossImageUrl;
                const fileName = task.fileName || task.name || "download";

                if (Tool.checkAppClient('Cef')) {
                    // CEF环境使用下载管理器
                    let url = fileUrl;
                    let surl = window.location.origin;
                    if (url.indexOf("http") != 0) {
                        url = surl + "/" + url;
                    }
                    this.$root.eventBus.$emit("createDownLoadTask", {
                        downLoadUrlList: [{
                            src: url,
                            filename: fileName
                        }],
                        needsInputFileName: true,
                    });
                } else {
                    // 浏览器环境使用Tool方法
                    Tool.downloadFileByBrowser(fileUrl, fileName).catch(error => {
                        console.error("下载失败:", error);
                        this.$message.error("下载失败");
                    });
                }
            } else {
                console.error("file url missing");
                this.$message.error("文件链接缺失");
            }
        },
        handleDeleteFile(taskToDelete) {
            if (!taskToDelete || !taskToDelete.uid) {
                return;
            }
            const uid = taskToDelete.uid;
            const task = this.uploadTask[uid];
            console.error(task);
            if (!task) {
                const queueIndex = this.uploadQueue.indexOf(uid);
                if (queueIndex > -1) {
                    this.uploadQueue.splice(queueIndex, 1);
                }
                return;
            }

            // 如果文件只是在待提交列表 (status: waiting, 未上传, 也没有ossContent)
            if (task.status === "waiting" && !task.isUploaded && !task.ossContent) {
                this.$delete(this.uploadTask, uid);
                const queueIndex = this.uploadQueue.indexOf(uid); // 也可能在提交后但在上传前被加入队列
                if (queueIndex > -1) {
                    this.uploadQueue.splice(queueIndex, 1);
                }
                return;
            }

            this.$confirm(
                this.lang.are_you_sure_delete_file_tips,
                this.lang.tip_title,
                {
                    confirmButtonText: this.lang.confirm_txt,
                    cancelButtonText: this.lang.cancel_btn,
                    type: "warning",
                }
            )
                .then(async () => {
                    console.error(task.status, task.uploadId);
                    if (task.status === "uploading" && task.uploadId) {
                        try {
                            await cancelUpload(task.uploadId);
                            task.status = "cancelled";
                            task.progressShow = false;
                        } catch (error) {
                            console.error("Failed to cancel upload:", error);
                        }
                    }

                    if (task.isUploaded) {
                        console.log(`TODO: Call API to delete server file for ${task.name}, path: ${task.ossImageUrl}`);
                    }
                    console.error(uid, this.uploadQueue, this.uploadTask, this.currentlyUploadingCount);
                    const queueIndex = this.uploadQueue.indexOf(uid);
                    console.error(queueIndex);
                    if (queueIndex > -1) {
                        this.uploadQueue.splice(queueIndex, 1);
                    }

                    this.$delete(this.uploadTask, uid);
                })
                .catch(() => {});
        },
        triggerFileUploadProcess() {
            console.warn("triggerFileUploadProcess is now obsolete in the primary upload flow.");
        },
        async uploadSingleFile(uid) {
            const task = this.uploadTask[uid];
            if (!task || !task.rawFile) {
                console.error("Task or rawFile not found for UID:", uid);
                throw new Error("Task or rawFile not found");
            }

            task.status = "uploading";
            task.progressShow = true;
            task.percentage = 0;
            task.isError = false;

            const fileNameInOss = this.generateUniqueFileName(task.name);
            let filePath = `${this.systemConfig.serverInfo.oss_attachment_server.sub_dir}/smartTechTraining/${this.trainingId}/${this.testId}/${this.user.uid}/${fileNameInOss}`;
            let ossBucket = "";
            try {
                if (
                    !this.$store ||
                    !this.$store.state ||
                    !this.$store.state.systemConfig ||
                    !this.$store.state.systemConfig.serverInfo
                ) {
                    console.error("System config not available in Vuex store.");
                    throw new Error("System config not available");
                }
                ossBucket = this.systemConfig.serverInfo.oss_attachment_server.bucket;
                if (!ossBucket) {
                    throw new Error("OSS Bucket not configured");
                }
            } catch (e) {
                task.status = "error";
                task.isError = true;
                task.progressShow = false;
                console.error("Error getting OSS bucket:", e);
                throw e;
            }

            return new Promise(async (resolve, reject) => {
                try {
                    const { ossClient } = await uploadFile({
                        bucket: ossBucket,
                        file: task.rawFile,
                        filePath: filePath,
                        callback: (event, data, uploadId) => {
                            if (task.status === "cancelled") {
                                return;
                            }

                            if (event === "progress") {
                                task.percentage = data < 100 ? data : 99;
                                task.uploadId = uploadId;
                            } else if (event === "complete") {
                                task.percentage = 100;
                                task.status = "success";
                                task.isUploaded = true;
                                task.progressShow = false;
                                task.ossImageUrl = data;
                                task.uploadTime = new Date().toLocaleString();
                                console.log(`File ${task.name} uploaded successfully to ${data}`);
                                resolve({ uid: task.uid, success: true, url: data });
                            } else if (event === "error") {
                                console.error("Upload error event for:", task.name, data);
                                task.status = "error";
                                task.isError = true;
                                task.progressShow = false;
                                reject({ uid: task.uid, success: false, error: data });
                            } else if (event === "cancel") {
                                console.error("Upload cancel event for:", task.name, data);
                                task.status = "cancelled";
                                task.isError = true;
                                task.progressShow = false;
                                reject({ uid: task.uid, success: false, error: data });
                            }
                        },
                    });
                    task.ossContent = ossClient;
                } catch (uploadError) {
                    console.error("Failed to start upload for:", task.name, uploadError);
                    task.status = "error";
                    task.isError = true;
                    task.progressShow = false;
                    reject({ uid: task.uid, success: false, error: uploadError });
                }
            });
        },
        generateUniqueFileName(originalName) {
            const ext = originalName.includes(".") ? "." + originalName.split(".").pop() : "";
            const baseName = ext ? originalName.substring(0, originalName.lastIndexOf(".")) : originalName;
            const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9_.-]/g, "_");
            return `${uuidv4()}_${sanitizedBaseName}${ext}`;
        },
        formatFileSize(bytes) {
            if (bytes === 0) {
                return "0 Bytes";
            }
            const k = 1024;
            const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
        },
        truncateFileName(name, maxLength = 30) {
            if (!name) {
                return "";
            }
            if (name.length <= maxLength) {
                return name;
            }
            const extDotIndex = name.lastIndexOf(".");
            if (extDotIndex === -1 || name.length - extDotIndex > 10) {
                return name.substring(0, maxLength - 3) + "...";
            }
            const baseName = name.substring(0, extDotIndex);
            const extension = name.substring(extDotIndex);
            if (baseName.length > maxLength - extension.length - 3) {
                return baseName.substring(0, maxLength - extension.length - 3) + "..." + extension;
            }
            return name;
        },
        validateSingleFile(rawFile) {
            if (!rawFile) {
                return false;
            }
            const { allowFileTypes, maxFileSizeMB } = this.assessmentInfo;
            const fileName = rawFile.name;
            const fileSizeMB = rawFile.size / 1024 / 1024;

            let isValidType = true;
            if (allowFileTypes && allowFileTypes.length > 0) {
                const fileExt = Tool.getFileType(fileName);
                isValidType = allowFileTypes.includes(fileExt);
            }

            if (!isValidType) {
                this.debouncedAlertUnSupportedFileTypes();
                console.error("file type not allowed");
                return false;
            }

            if (fileSizeMB > maxFileSizeMB) {
                this.debouncedAlertMaxFileSize();
                console.error("file size exceeds limit");
                return false;
            }

            return isValidType;
        },
        handleFilesSelectedAndImmediateUpload(file, fileList) {
            // 由于 :multiple="false", fileList 参数通常不在此场景下使用，主要处理单个 file 对象
            // 计划：先清除所有"待提交"状态的文件
            Object.keys(this.uploadTask).forEach((uid) => {
                const task = this.uploadTask[uid];
                // 将 status === 'waiting' 并且尚未开始上传（没有ossContent）的任务视为待提交任务并清除
                if (task.status === "waiting" && !task.isUploaded && !task.ossContent) {
                    this.$delete(this.uploadTask, uid);
                }
            });

            if (file.status !== "ready") {
                // el-upload 触发时，文件状态可能非ready，例如手动调用clearFiles()
                return;
            }

            this.validatingFile = true;
            const rawFile = file.raw;

            if (!this.validateSingleFile(rawFile)) {
                this.validatingFile = false;
                // 清理el-upload内部可能存在的文件引用，以允许用户再次选择相同文件
                if (this.$refs.uploadComponent) {
                    this.$refs.uploadComponent.clearFiles();
                }
                return;
            }

            const uid = file.uid; // 使用el-upload生成的uid

            // 添加新文件到uploadTask，状态为'waiting'（等待提交）
            this.$set(this.uploadTask, uid, {
                uid: uid,
                name: rawFile.name,
                size: rawFile.size,
                rawFile: rawFile,
                percentage: 0,
                status: "waiting", // 'waiting' 现在表示等待用户点击"提交答案"
                isUploaded: false,
                isError: false,
                progressShow: false,
                ossContent: null,
                uploadTime: null,
            });
            this.validatingFile = false;
            // 不再将uid推入this.uploadQueue
            // 不再调用this.tryProcessUploadQueue()
        },
        handleSubmitAnswer() {
            const pendingFiles = Object.values(this.uploadTask).filter(
                (task) => task.status === "waiting" && !task.isUploaded && !task.ossContent
            );

            if (pendingFiles.length === 0) {
                console.error("no pending files");
                return;
            }

            // 检查是否已有文件在上传队列或正在上传中
            if (this.currentlyUploadingCount > 0 || this.uploadQueue.length > 0) {
                console.error("files are uploading or waiting to upload, please try again later");
                return;
            }

            this.isSubmitting = true; // 开始提交过程，设置按钮加载状态
            this.shouldSubmitAnswer = true; // 设置标志，表示上传完成后需要提交答案

            pendingFiles.forEach((task) => {
                if (!this.uploadQueue.includes(task.uid)) {
                    // 避免重复添加
                    this.uploadQueue.push(task.uid);
                }
            });

            // 如果向队列中添加了文件，则开始处理队列
            if (pendingFiles.length > 0) {
                this.tryProcessUploadQueue();
            } else {
                // 如果没有文件实际被加入队列（理论上pendingFiles检查后不会到这里，除非逻辑错误），则重置状态
                this.isSubmitting = false;
                this.shouldSubmitAnswer = false;
            }
            // isSubmitting 将在 tryProcessUploadQueue 中所有任务完成后被重置为 false
        },
        tryProcessUploadQueue() {
            if (
                this.currentlyUploadingCount < (this.assessmentInfo.maxConcurrentUploads || 5) &&
                this.uploadQueue.length > 0
            ) {
                const uidToProcess = this.uploadQueue.shift();
                const task = this.uploadTask[uidToProcess];

                if (task && task.status === "waiting") {
                    this.currentlyUploadingCount++;

                    this.uploadSingleFile(uidToProcess)
                        .then((result) => {
                            console.log("Upload success for", uidToProcess, result);
                        })
                        .catch((error) => {
                            console.error("Upload failed for", uidToProcess, error);
                        })
                        .finally(() => {
                            this.currentlyUploadingCount--;
                            this.tryProcessUploadQueue();
                        });
                } else if (task) {
                    console.warn(`Task ${uidToProcess} is not in queued state (state: ${task.status}), skipping.`);
                    this.tryProcessUploadQueue();
                } else {
                    console.warn(`Task ${uidToProcess} not found in uploadTask, skipping.`);
                    this.tryProcessUploadQueue();
                }
            } else if (this.currentlyUploadingCount === 0 && this.uploadQueue.length === 0) {
                // 当没有文件正在上传且队列也为空时，意味着所有通过handleSubmitAnswer提交的任务已处理完毕
                this.isSubmitting = false; // 重置提交按钮的加载状态

                // 检查是否需要提交答案（仅在通过handleSubmitAnswer触发的上传完成后）
                const successfullyUploadedFiles = Object.values(this.uploadTask).filter(
                    (task) => task.status === "success" && task.isUploaded
                );

                if (successfullyUploadedFiles.length > 0 && this.shouldSubmitAnswer) {
                    this.shouldSubmitAnswer = false; // 重置标志，防止重复提交
                    this.serviceSubmitAnswer(successfullyUploadedFiles);
                }
            }
        },
        getTrainingTestInfoByTestId() {
            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestInfoByTestId({
                        testID: this.testId,
                        trainingID: this.trainingId,
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0) {
                            this.testInfo = res.data.data;
                            // 更新页面显示数据
                            if (this.testInfo) {
                                this.title = this.testInfo.title || "未命名考试";
                                this.questionsCount = this.testInfo.questionCount || 0;

                                // 格式化截止时间
                                if (this.testInfo.deadline) {
                                    this.deadline = moment.unix(this.testInfo.deadline).format("YYYY-MM-DD");
                                    console.log(this.deadline, "this.deadline");
                                }
                                this.pagerInfo = this.testInfo.pagerInfo || [];
                            }
                            resolve(res.data.data);
                        } else {
                            console.error(res.data.message || "get exam info failed");
                            reject(new Error(res.data.message || "get exam info failed"));
                        }
                    })
                    .catch((error) => {
                        console.error("getTrainingTestInfoByTestId error:", error);
                        reject(error);
                    });
            });
        },
        getTrainingTestAnswerHistory() {
            return new Promise((resolve, reject) => {
                service
                    .getTrainingTestAnswerHistory({
                        testID: this.testId,
                    })
                    .then((res) => {
                        console.log(res, "res");
                        if (res.data.error_code === 0 && res.data.data && Array.isArray(res.data.data)) {
                            // 处理批改历史数据
                            this.reviewHistoryData = res.data.data.map((item) => {
                                // 从服务器返回的数据中提取文件信息
                                const fileInfo = item.answer && item.answer.length > 0 ? item.answer[0] : {};

                                return {
                                    fileName: fileInfo.fileName,
                                    fileSize: fileInfo.fileSize,
                                    fileUrl: fileInfo.fileUrl,
                                    ...item
                                };
                            });
                            resolve(this.reviewHistoryData);
                        } else {
                            // 如果没有数据，设置为空数组
                            this.reviewHistoryData = [];
                            resolve([]);
                        }
                    })
                    .catch((err) => {
                        console.error("get grading history failed, please try again later",err);
                        this.reviewHistoryData = [];
                        reject(err);
                    });
            });
        },
        serviceSubmitAnswer(uploadedFiles) {
            if (this.isSubmittingAnswer) {
                return;
            }

            this.isSubmittingAnswer = true;

            // 收集已上传文件信息
            const fileInfoList = uploadedFiles.map((file) => ({
                fileName: file.name,
                fileUrl: file.ossImageUrl,
                fileSize: file.size,
                uploadTime: file.uploadTime ? new Date(file.uploadTime).getTime() : new Date().getTime(),
            }));

            console.log("准备提交的文件信息：", fileInfoList);

            // 准备提交的数据
            const submitData = {
                testID: this.testId,
                trainingID: this.trainingId,
                answer: fileInfoList,
            };

            console.log("提交答案数据：", submitData);
            this.isSubmittingAnswer = false;
            // // 调用API提交答案
            service
                .submitTrainingTestFile(submitData)
                .then((res) => {
                    console.log("提交答案响应：", res);
                    if (res.data && res.data.error_code === 0) {
                        this.$message.success(this.lang.submitted_successfully);
                        // 清空已提交的文件
                        this.clearSubmittedFiles(uploadedFiles);
                        // 刷新历史记录
                        this.getTrainingTestAnswerHistory();
                    } else {
                        console.error(res.data?.message || "submit answer failed");

                    }
                })
                .catch((error) => {
                    console.error("submit answer failed, please check network and try again",error);
                })
                .finally(() => {
                    this.isSubmittingAnswer = false;
                });
        },
        clearSubmittedFiles(uploadedFiles) {
            if (!uploadedFiles || uploadedFiles.length === 0) {
                return;
            }

            // 删除uploadTask中对应的已提交文件
            uploadedFiles.forEach((file) => {
                if (file.uid && this.uploadTask[file.uid]) {
                    this.$delete(this.uploadTask, file.uid);
                }
            });

            // 重置fileListForElUpload数组
            this.fileListForElUpload = [];

            // 清理el-upload组件的内部状态
            if (this.$refs.uploadComponent) {
                this.$refs.uploadComponent.clearFiles();
            }

            console.log("已清空提交的文件，当前uploadTask:", this.uploadTask);
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.uploadTestOverview_container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #f7f9fc;

    .custom_body {
        height: 100%;
        padding: 25px;
        overflow-y: auto;
        background-color: #f7f9fc;
    }
}

.top_info_section {
    display: flex;
    margin-bottom: 20px;
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    justify-content: center;
    padding-bottom: 60px;
    border-bottom: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    .icon_area {
        margin-right: 25px;

        .placeholder_icon {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background-color: #ffd700;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 30px;
            color: #fff;
            i {
                font-size: 80px;
            }
        }
    }

    .details_area {
        display: flex;
        flex-direction: column;
        justify-content: center;
        .detail_top {
            max-width: 700px;
            .detail_title {
                font-size: 22px;
                color: #303133;
                margin-bottom: 15px;
                font-weight: bold;
                margin-top: 15px;
            }
        }
        .detail_bottom {
            .deadline {
                font-size: 15px;
                color: #606266;
                background-color: #f8f9fb;
                padding: 5px 12px;
                border-radius: 4px;
                display: inline-block;
                width: 200px;
                text-align: center;
            }
        }
    }

    .upload_btn {
        margin-top: 22px;
        padding: 10px 22px;
        font-size: 15px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    ::v-deep .el-upload {
        width: 100%;
        text-align: center;

        .el-upload__tip {
            color: #909399;
            font-size: 14px;
            margin-top: 10px;
        }
    }
    .warning_text {
        margin-top: 10px;
        color: #F56C6C;
        font-size: 14px;
        text-align: left;
    }

    .success_text {
        margin-top: 10px;
        color: #67C23A;
        font-size: 14px;
        text-align: left;
    }
}

/* 按钮容器和遮罩层样式 */
.upload-button-container,
.submit-button-container {
    position: relative;
    display: inline-block;
}

.button-disabled-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.6); /* 半透明白色遮罩 */
    cursor: not-allowed;
    z-index: 10;
    border-radius: 6px; /* 与按钮的圆角保持一致 */
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.button-disabled-overlay:hover {
    background-color: rgba(255, 255, 255, 0.8); /* 悬停时更明显的遮罩 */
}

.content_section {
    background-color: #fff;
    padding: 25px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border-bottom: none;

    h3 {
        margin-bottom: 18px;
        font-size: 18px;
        color: #303133;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }

    .teacher-remark-text {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.4;
        max-height: 4.2em; /* 3行的高度，1.4 * 3 = 4.2 */
        word-break: break-word;
        cursor: pointer;
        white-space: normal;
    }
}

</style>
<style lang="scss">
.teacher-remark-tooltip {
    max-width: 800px !important;
    word-break: break-word;
    white-space: pre-wrap;
    margin-right: 30px;
}
</style>

