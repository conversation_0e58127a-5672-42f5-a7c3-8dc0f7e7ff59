<template>
    <van-popup
        v-model="dialogVisible"
        position="right"
    >
    <div class="video_members">
        <div class="panel">
            <mrHeader v-if="!isTEAir">
                <template #title>
                    {{ lang.members_manage_title }}({{ allUserList.length }})
                </template>
            </mrHeader>
            <div class="group_members clearfix" @scroll.prevent>
                <div v-for="item of allUserList" class="group_member_item" :key="item.uid">
                    <div class="group_user_wrapper">
                        <mr-avatar
                            :url="getLocalAvatar(item)"
                            :origin_url="item.avatar"
                            :showOnlineState="false"
                            :key="item.avatar"
                        ></mr-avatar>
                        <div class="user_info">
                            <span>{{ item.nickname }}</span>
                            <div class="tag_box">
                                <span class="tag" v-if="item.isHost">{{ lang.moderator }}</span>
                                <span class="tag" v-if="item.userid == user.uid">{{ lang.mine }}</span>
                                <span class="tag" v-if="item.userid == LiveConferenceData.currentMainUltrasyncId">{{
                                    lang.main_stream_screen
                                }}</span>
                            </div>
                            <div class="user_info_introduction">{{item.introduction}}</div>
                        </div>
                    </div>
                    <div class="group_user_operate">
                        <!-- 摄像头操作 -->
                        <span class="operate_btn" v-if="item.userid === user.uid">
                            <i
                                v-if="item.videoStream === 1"
                                @click="toggleSelfVideo(item)"
                                class="icon iconfont icon-shipinluxiang"
                            ></i>
                            <i
                                v-else
                                @click="toggleSelfVideo(item)"
                                class="icon iconfont icon-weifaxianshexiangtou"
                            ></i>
                        </span>
                        <span class="operate_btn" v-else>
                            <i
                                v-if="item.videoStream === 1 && !LiveConferenceData.currentSubscribeAux.includes(item.uid)"
                                @click="toggleRemoteSubscribeVideo(item)"
                                class="icon iconfont icon-shipinluxiang"
                            ></i>
                            <i
                                v-else-if="item.videoStream === 1 && LiveConferenceData.currentSubscribeAux.includes(item.uid)"
                                @click="toggleRemoteSubscribeVideo(item)"
                                class="icon iconfont icon-shipinluxiang"
                            >
                                <i class="icon iconfont icon-duihao"></i>
                            </i>
                            <i
                                v-else-if="item.videoStream === 0"
                                class="icon iconfont icon-weifaxianshexiangtou"
                            ></i>
                        </span>

                        <!-- 麦克风操作 -->
                        <span class="operate_btn" v-if="item.userid === user.uid">
                            <i
                                v-if="item.audioStream === 1"
                                @click="toggleSelfAudio(item)"
                                class="icon iconfont icon-mic-line"
                            ></i>
                            <i
                                v-else
                                @click="toggleSelfAudio(item)"
                                class="icon iconfont icon-mic-off-line"
                            ></i>
                        </span>
                        <span class="operate_btn" v-else>
                            <i
                                v-if="item.audioStream === 1"
                                @click="toggleRemoteMuteAudio(item)"
                                class="icon iconfont icon-mic-line"
                            ></i>
                            <i
                                v-else
                                class="icon iconfont icon-mic-off-line"
                            ></i>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel_operate" v-if="allForbiddenSpeakAuth">
            <van-button type="primary" class="btn" @click="openForbiddenAllSpeakVisible">{{
                lang.all_forbidden_speak
            }}</van-button>
            <!-- <van-button type="primary" class="btn"  @click="unForbiddenAllSpeakVisible=true" v-if="LiveConferenceData.freeSpeakAuth">{{lang.unmute_all}}</van-button> -->
        </div>

        <CommonDialog
            v-model="forbiddenAllSpeakVisible"
            :message="lang.all_members_silenced"
            @confirm="forbiddenAllSpeak"
            @reject="forbiddenAllSpeakVisible = false"
            :confirmButtonText="lang.all_forbidden_speak"
            :showRejectButton=true
        />

        <CommonDialog
            v-model="dealApplyVisible"
            :message="lang.is_agree_user_speak"
            @confirm="confirmHandUps"
            @reject="cancelHandUps"
            :showRejectButton=true
        />

        <div class="custom-modal" @click="dealApplyVisible = false" v-if="dealApplyVisible"></div>

        <CommonDialog
            v-model="unForbiddenAllSpeakVisible"
            :message="lang.is_agree_all_user_speak"
            @confirm="confirmUnForbiddenAllSpeak"
            @reject="unForbiddenAllSpeakVisible = false"
            :showRejectButton=true
        />
        <CommonDialog
            v-model="unForbiddenAllSpeakVisible"
            :message="lang.is_agree_all_user_speak"
            @confirm="confirmUnForbiddenAllSpeak"
            @reject="unForbiddenAllSpeakVisible = false"
            :showRejectButton=true
        />
        <CommonDialog
            v-model="tipsApply"
            :message="lang.ask_want_to_apply_speak_permission"
            @confirm="handsUp"
            @reject="tipsApply = false"
            :showRejectButton=true
        />
         <CommonDialog
            v-model="tipsApply"
            :message="lang.ask_want_to_apply_speak_permission"
            @confirm="handsUp"
            @reject="tipsApply = false"
            :showRejectButton=true
        />
        <CommonDialog
            v-model="tipsCancelApply"
            :message="lang.ask_want_to_cancel_apply_speak_permission"
            @confirm="noHandsUp"
            @reject="tipsCancelApply = false"
            :showRejectButton=true
        />
    </div>
</van-popup>
</template>
<script>
import base from "../../lib/base";
import { Button,Popup } from 'vant';
import CommonDialog from '../../MRComponents/commonDialog.vue'
import { getLiveRoomObj, getLocalAvatar,checkIsCreator,checkIsManager } from "../../lib/common_base";
import DialogManager from "../../lib/dialogManager";
import Tool from "@/common/tool";
export default {
    mixins: [base],
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        cid: {
            type: [String,Number],
            default: 0,
        }
    },
    components: {
        VanButton: Button,
        VanPopup: Popup,
        CommonDialog
    },
    computed: {
        dialogVisible: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            },
        },
        conversation() {
            return this.$store.state.conversationList[this.cid] || {};
        },
        attendeeList() {
            return this.parseObjToArr(this.conversation.attendeeList) || [];
        },
        allUserList() {
            let list = [
                // {
                //     uid:9001,
                //     videoStream:1,
                //     audioStream:1,
                //     userid:128,
                // }
            ];

            if (this.LiveConferenceData.roomUserMap && this.attendeeList) {
                Object.values(this.LiveConferenceData.roomUserMap).map((item) => {
                    this.attendeeList.forEach((ele) => {
                        if (Number(ele.userid) === Number(item.user_id)) {
                            if (item.streamType === "aux") {
                                //只统计辅流
                                if (ele.userid === this.user.uid) {
                                    // 本人
                                    ele.isSelf = 1;
                                } else {
                                    ele.isSelf = 0;
                                }
                                list.push({ ...item, ...ele });
                            }
                        }
                    });
                });
            }
            if (list.length > 0) {
                list.sort(this.sortAttendeeList);
            }
            // this.allForbiddenSpeakAuth = this.checkManagerAuth()
            return list;
        },
        isMeeting() {
            return this.attendeeList.length > this.systemConfig.group_mode_num;
        },
        isVoiceCtrl() {
            //语音控制
            return false;
        },
        isTEAir() {
            return this.$store.state.device.isTEAir;
        },
        allForbiddenSpeakAuth(){
            if(this.cid){
                return this.checkIsCreator(this.cid)||this.checkIsManager(this.cid)||this.LiveConferenceData.isHost
            }else{
                return false
            }
        },
        LiveConferenceData(){
            if(this.$store.state.liveConference[this.cid]&&this.$store.state.liveConference[this.cid].LiveConferenceData){
                console.error(this.$store.state.liveConference[this.cid].LiveConferenceData,this.cid)
                return this.$store.state.liveConference[this.cid].LiveConferenceData
            }else{
                return {
                    currentSubscribeAux:[]
                }
            }

        },
    },
    watch: {
        visible: {
            handler(val) {
                if (!val) {
                    this.forbiddenAllSpeakVisible = false;
                }
            },
            immediate: true,
        },
        // 监听主弹窗显示状态
        dialogVisible(val) {
            if (val && !this.mainDialogId) {
                // 打开弹窗时，注册到 DialogManager
                this.mainDialogId = DialogManager.register(this, {
                    open: () => {
                        // 弹窗已经通过 v-model 显示，这里不需要额外操作
                    },
                    close: () => {
                        this.dialogVisible = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.mainDialogId) {
                // 关闭弹窗时，从 DialogManager 注销
                DialogManager.unregister(this.mainDialogId);
                this.mainDialogId = null;
            }
        },
        // 监听全体禁言弹窗状态
        forbiddenAllSpeakVisible(val) {
            if (val && !this.forbiddenAllSpeakDialogId) {
                this.forbiddenAllSpeakDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.forbiddenAllSpeakVisible = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.forbiddenAllSpeakDialogId) {
                DialogManager.unregister(this.forbiddenAllSpeakDialogId);
                this.forbiddenAllSpeakDialogId = null;
            }
        },
        // 监听处理申请弹窗状态
        dealApplyVisible(val) {
            if (val && !this.dealApplyDialogId) {
                this.dealApplyDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.dealApplyVisible = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.dealApplyDialogId) {
                DialogManager.unregister(this.dealApplyDialogId);
                this.dealApplyDialogId = null;
            }
        },
        // 监听解除全体静音弹窗状态
        unForbiddenAllSpeakVisible(val) {
            if (val && !this.unForbiddenAllSpeakDialogId) {
                this.unForbiddenAllSpeakDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.unForbiddenAllSpeakVisible = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.unForbiddenAllSpeakDialogId) {
                DialogManager.unregister(this.unForbiddenAllSpeakDialogId);
                this.unForbiddenAllSpeakDialogId = null;
            }
        },
        // 监听举手申请提醒弹窗状态
        tipsApply(val) {
            if (val && !this.tipsApplyDialogId) {
                this.tipsApplyDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.tipsApply = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.tipsApplyDialogId) {
                DialogManager.unregister(this.tipsApplyDialogId);
                this.tipsApplyDialogId = null;
            }
        },
        // 监听取消举手申请提醒弹窗状态
        tipsCancelApply(val) {
            if (val && !this.tipsCancelApplyDialogId) {
                this.tipsCancelApplyDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.tipsCancelApply = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.tipsCancelApplyDialogId) {
                DialogManager.unregister(this.tipsCancelApplyDialogId);
                this.tipsCancelApplyDialogId = null;
            }
        }
    },
    data() {
        return {
            getLocalAvatar,
            checkIsCreator,
            checkIsManager,
            forbiddenAllSpeakVisible: false,
            dealApplyVisible: false, //
            forbiddenValue: [],
            forbiddenOptions: [
                {
                    label: "",
                    value: 1,
                },
            ],
            currentDealUser: null, //当前处理的用户
            unForbiddenAllSpeakVisible: false, //解除全体静音
            tipsApply: false, //提醒是否举手申请弹窗
            tipsCancelApply: false, //提醒是否取消举手申请
            // DialogManager 相关的 ID
            mainDialogId: null, // 主弹窗的 DialogManager ID
            forbiddenAllSpeakDialogId: null, // 全体禁言弹窗的 DialogManager ID
            dealApplyDialogId: null, // 处理申请弹窗的 DialogManager ID
            unForbiddenAllSpeakDialogId: null, // 解除全体静音弹窗的 DialogManager ID
            tipsApplyDialogId: null, // 举手申请提醒弹窗的 DialogManager ID
            tipsCancelApplyDialogId: null, // 取消举手申请提醒弹窗的 DialogManager ID
        };
    },
    activated() {
        this.$nextTick(() => {});
    },
    mounted() {
        this.forbiddenOptions = [
            {
                label: this.lang.allows_members_self_mute,
                value: 1,
            },
        ];
    },
    beforeDestroy() {
        // 清理所有 DialogManager 注册
        if (this.mainDialogId) {
            DialogManager.unregister(this.mainDialogId);
            this.mainDialogId = null;
        }
        if (this.forbiddenAllSpeakDialogId) {
            DialogManager.unregister(this.forbiddenAllSpeakDialogId);
            this.forbiddenAllSpeakDialogId = null;
        }
        if (this.dealApplyDialogId) {
            DialogManager.unregister(this.dealApplyDialogId);
            this.dealApplyDialogId = null;
        }
        if (this.unForbiddenAllSpeakDialogId) {
            DialogManager.unregister(this.unForbiddenAllSpeakDialogId);
            this.unForbiddenAllSpeakDialogId = null;
        }
        if (this.tipsApplyDialogId) {
            DialogManager.unregister(this.tipsApplyDialogId);
            this.tipsApplyDialogId = null;
        }
        if (this.tipsCancelApplyDialogId) {
            DialogManager.unregister(this.tipsCancelApplyDialogId);
            this.tipsCancelApplyDialogId = null;
        }
    },
    methods: {
        dealApply(item) {
            //处理申请发言
            this.currentDealUser = item;
            this.dealApplyVisible = true;
        },
        handsUp() {},
        noHandsUp() {},
        confirmHandUps() {
            this.currentDealUser = null;
            this.dealApplyVisible = false;
        },
        cancelHandUps() {
            this.currentDealUser = null;
            this.dealApplyVisible = false;
        },
        // 本人摄像头开关
        toggleSelfVideo: Tool.throttle(function (item) {
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            const isMute = item.videoStream === 1;
            liveRoom.MuteLocalVideoStream({
                uid: liveRoom.data.localAuxUid,
                isMute,
            });
        }, 800, true),

        // 他人辅流视频的订阅/取消
        toggleRemoteSubscribeVideo: Tool.throttle(async function (item) {
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            if (this.LiveConferenceData.currentSubscribeAux.includes(item.uid)) {
                liveRoom.StopSubscribeRemoteStreamAux(item.uid);
            } else {
                const { canSubscribeAuxNum } = await liveRoom.checkAfterSubscribeAux();
                if (canSubscribeAuxNum === 0) {
                    return;
                }
                liveRoom.SubscribeRemoteStreamAux(item.uid);
            }
        }, 800, true),

        // 本人麦克风开关
        toggleSelfAudio: Tool.throttle(function (item) {
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            const isMute = item.audioStream === 1;
            liveRoom.MuteLocalAudioStream({
                uid: liveRoom.data.localAuxUid,
                isMute,
            });
        }, 800, true),

        // 远端用户麦克风静音（仅主持人可操作）
        toggleRemoteMuteAudio: Tool.throttle(function (item) {
            const liveRoom = getLiveRoomObj(this.cid);
            if (!liveRoom) {
                return;
            }
            if (!liveRoom.data.isHost) {
                return;
            }
            liveRoom.forbiddenSpeak({
                isForce: false,
                userId: item.userid,
            });
        }, 800, true),
        //全员禁言
        async forbiddenAllSpeak() {
            let liveRoom = getLiveRoomObj();
            if (!liveRoom) {
                return;
            }
            // let canRevert = 0
            // if(this.forbiddenValue.length>0){ // 选择了可以自由解除禁言
            //     canRevert = 1
            // }else{
            //     canRevert = 0

            // }

            // this.forbiddenAllSpeakVisible = false
            await liveRoom.forbiddenSpeak({
                isForce: false,
            });
        },
        //解除全员禁言
        unForbiddenAllSpeak() {
            console.log("unforbiddenAllSpeak");
            this.unForbiddenAllSpeakVisible = false;
        },
        confirmUnForbiddenAllSpeak() {
            this.unForbiddenAllSpeak();
        },
        handleHandsUp(user) {},
        openForbiddenAllSpeakVisible() {
            if (this.isVoiceCtrl) {
                this.forbiddenValue = [];
            } else {
                this.forbiddenValue = [1];
            }

            this.forbiddenAllSpeakVisible = true;
        },
        sortAttendeeList(a, b) {
            if (a.isHost === b.isHost) {
                if (a.isSelf > b.isSelf || a.isHandsUp > b.isHandsUp) {
                    //3.本人或者举手者再往前排
                    return -1;
                } else {
                    return 1;
                }
            } else {
                if (a.isHost > b.isHost) {
                    //1.主讲人挪到最前面
                    return -1;
                } else {
                    return 1;
                }
            }
        },
        checkManagerAuth(){
            let liveRoom = getLiveRoomObj();
            if (!liveRoom) {
                return false;
            }
            return liveRoom.checkManagerAuth();
        },
        getContainer() {
            return document.querySelector("body");
        },
    },
};
</script>
<style lang="scss" scoped>
.video_members {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
    .panel {
        overflow: hidden;
        background: #fff;
        display: flex;
        flex-direction: column;
        flex: 1;
        .icon-close {
            float: right;
            font-size: 1.4rem;
            line-height: 1;
            color: #fff;
        }
        .speaking {
            border-bottom: 1px solid #aaa;
            padding: 0.3rem 0;
            & > p {
                font-size: 0.7rem;
                color: #000;
            }
            .speaking_members {
                margin-top: 0.4rem;
                .speaking_member_item {
                    width: 25%;
                    float: left;
                    .image_wrapper {
                        position: relative;
                        width: 50%;
                        margin: 0 auto;
                        img {
                            width: 100%;
                            display: block;

                            border-radius: 50%;
                        }
                        .icon-icon- {
                            position: absolute;
                            top: -0.3rem;
                            right: -0.4rem;
                            color: #f22;
                            font-weight: bold;
                            font-size: 1rem;
                            line-height: 1;
                        }
                        .icon-microphone {
                            position: absolute;
                            bottom: -0.3rem;
                            color: #00d63a;
                            font-size: 1rem;
                            line-height: 1;
                            left: -0.3rem;
                        }
                        .voice_manager {
                            background-color: #00c59d;
                            font-size: 0.6rem;
                            color: #fff;
                            position: absolute;
                            right: -1rem;
                            bottom: -0.3rem;
                            padding: 0.1rem 0.2rem;
                            border-radius: 0.2rem;
                        }
                    }
                    p {
                        text-align: center;
                        color: #000;
                        font-size: 0.7rem;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        line-height: 1.8;
                    }
                }
            }
        }
        .applying {
            border-bottom: 1px solid #aaa;
            padding: 0.3rem 0;
            & > p {
                font-size: 0.7rem;
                color: #000;
            }
            .applying_members {
                margin-top: 0.4rem;
                .applying_member_item {
                    width: 25%;
                    float: left;
                    position: relative;
                    img {
                        width: 50%;
                        display: block;
                        margin: 0 auto;
                        border-radius: 50%;
                    }
                    p {
                        text-align: center;
                        color: #000;
                        font-size: 0.7rem;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        line-height: 1.8;
                    }
                    i {
                        font-size: 0.9rem;
                    }
                    .operate_apply {
                        & > div {
                            float: left;
                            margin: 0 5%;
                            width: 40%;
                            line-height: 1;
                            text-align: center;
                            color: #fff;
                            border-radius: 0.2rem;
                        }
                        .agree {
                            background-color: #0e0;
                        }
                        .disagree {
                            background-color: #f22;
                        }
                    }
                }
            }
        }
        .group_title {
            font-size: 0.9rem;
            color: #000;
            padding: 1rem 0;
        }
        .group_members {
            margin-top: 0.4rem;
            width: 100%;
            padding: 0px 20px;
            box-sizing: border-box;
            -webkit-overflow-scrolling: touch;
            overflow: auto;
            flex: 1;
            .group_member_item {
                display: flex;
                min-height: 60px;
                border-bottom: 1px solid #ccc;
                padding: 10px 0px;
                box-sizing: border-box;
                .group_user_wrapper {
                    position: relative;
                    display: flex;
                    flex: 1;
                    .user_info {
                        margin-left: 10px;
                        display: flex;
                        flex-direction: column;
                        font-size: 14px;
                        flex: 1;
                        .tag {
                            background-color: #01c59d;
                            color: #fff;
                            line-height: 1;
                            padding: 2px 4px;
                            border-radius: 4px;
                            font-size: 12px;
                            margin-right: 4px;
                        }
                        .user_info_introduction{
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            line-height: 2em;
                            color: #999;
                        }
                    }
                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }
                    // .icon-i-sound,.icon-microphone{
                    //     position: absolute;
                    //     bottom: -0.3rem;
                    //     color: #00d63a;
                    //     font-size: 1rem;
                    //     line-height: 1;
                    //     left:-0.3rem;
                    //     &.can_no_speak{
                    //         color:#f22;
                    //     }
                    // }
                }
                .group_user_operate {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    .operate_btn {
                        position: relative;
                        i {
                            width: 1.4rem;
                            height: 1.4rem;
                            margin-left: 0.5rem;
                            fill: #ccc;
                        }
                        .iconfont {
                            font-size: 1.4rem;
                            height: 1.4rem;
                            margin-left: 0.5rem;
                            color: #8d8d8d;
                            position: relative;
                        }
                        .icon-duihao {
                            position: absolute;
                            top: 0.5rem;
                            left: 0;
                            color: green;
                        }
                    }
                }
                .voice_manager {
                    background-color: #00c59d;
                    font-size: 0.6rem;
                    color: #fff;
                    position: absolute;
                    right: -1rem;
                    bottom: -0.3rem;
                    padding: 0.1rem 0.2rem;
                    border-radius: 0.2rem;
                }

                .authorize {
                    border-radius: 0.2rem;
                    background-color: #00c59d;
                    display: inline-block;
                    font-size: 0.7rem;
                    padding: 0 0.3rem;
                    color: #fff;
                }
            }
        }
    }
    .panel_operate {
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        .btn {
            flex: 1;
            margin: 0px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
    }
    .forbidden_check {
    }
    .custom-modal {
        z-index: 1999;
        background: #000;
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        opacity: 0.5;
    }
}
</style>
