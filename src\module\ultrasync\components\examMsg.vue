<template>
    <div class="exam_msg_item">
        <div @click="toggleImages(message)" class="exam_title">
            <div class="exam_patient">
                <div class="patient_info clearfix" v-if="message.patientInfo">
                    <p v-if="message.patient_name" class="patient_info_item">
                        {{ message.patientInfo.patient_name_str }}
                    </p>
                    <p v-if="message.patient_age != -1" class="patient_info_item">
                        {{ message.patientInfo.patient_age_str }}
                    </p>
                    <p v-if="message.patient_sex != 2" class="patient_info_item">
                        {{ message.patientInfo.patient_sex_str }}
                    </p>
                    <p v-if="message.exam_type != 9" class="patient_info_item">
                        {{ lang.exam_types[message.exam_type] }}
                    </p>
                    <p class="patient_info_item">{{ lang.image_count }}: {{ message.resourceList.length }}</p>
                </div>
                <div class="patient_info">
                    <p class="date">{{ lang.exam_time }}: {{ formatTime(message.patient_series_datetime) }}</p>
                </div>
            </div>
            <div class="exam_iworks" v-if="message.protocol_name">
                <div class="icon_title">
                    <i class="iconfont icon-quanbuwenjian-weixuanzhong"></i>
                </div>
                <p>iWorks：{{ message.protocol_name }}</p>
            </div>
            <i v-show="!openState" class="iconfont exam_down icon-exam_dowm"></i>
            <i v-show="openState" class="iconfont exam_up icon-exam_up"></i>
        </div>
        <div class="exam_image_list clearfix" v-show="openState">
            <div v-show="loadingImage" class="loading_image">
                <van-loading color="#00c59d" />
            </div>
            <div style="display: none">{{ messageImage.length }}</div>
            <div class="clearfix">
                <div
                    v-for="img of imageList"
                    class="file_item"
                    :class="{ is_create: img.isCreate, is_delete: img.isDelete }"
                    :key="img.resource_id"
                    v-popover.top="{
                        name: 'menu',
                        chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                        callback: () => {
                            showTooltipsMenu(img);
                        },
                    }"
                >
                    <div class="file_container" v-if="getResourceTempState(img.resource_id) === 0">
                        <img src="static/resource/images/default.png" class="file_img" />
                        <p class="view_name">{{ lang.ref_res_expired }}</p>
                    </div>

                    <v-touch class="file_container" @click.native="openGallery(message, img)" v-else>
                        <p class="view_name longwrap" v-if="img.protocol_view_name">{{ img.protocol_view_name }}</p>
                        <common-image v-if="img.url" :fileItem="img" size="mini" :key="img.resource_id"></common-image>
                        <img v-else src="static/resource/images/default.png" class="file_image needsclick" />
                    </v-touch>
                    <div class="file_quality_icon" v-if="showAiAnalyzeIcon(img)">
                        <template v-if="img.isIworksTest ">
                            <span >
                                <span v-if="!img.url">
                                    <span
                                        class="icon iconfont ai_result_deletion_icon icon-wenhao-yuankuang-copy"
                                        :title="lang.view_deletion"
                                    ></span>
                                </span>
                                <template v-else>
                                    <span
                                        v-for="(iconObj, index) in imageStandardIcon(img)"
                                        :key="index"
                                        :class="[iconObj.css]"
                                        :title="iconObj.tips"
                                    >
                                        {{ iconObj.label }}
                                    </span>
                                </template>
                            </span>
                        </template>
                        <template v-else>
                            <span
                                v-for="(iconObj, index) in imageStandardIcon(img)"
                                :key="index"
                                :class="[iconObj.css]"
                                :title="iconObj.tips"
                            >
                                {{ iconObj.label }}
                            </span>
                        </template>
                    </div>
                </div>
                <div
                    v-for="(item, index) in invalidImage"
                    class="file_item"
                    :key="'invalidImage' + index"
                    v-popover.top="{
                        name: 'menu',
                        chat_direction: message.sender_id == user.uid ? 'right' : 'left',
                        callback: () => {
                            showTooltipsInvalidMenu(item);
                        },
                    }"
                >
                    <div class="file_container">
                        <img src="static/resource/images/default.png" class="file_img" />
                        <p class="view_name">{{ lang.ref_res_expired }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import base from "../lib/base";
import iworksTool from '../lib/iworksTool';
import { setIworksInfoToMsg, patientDesensitization, getResourceTempState, isIworksTest, imageStandardIcon } from "../lib/common_base";
import { Toast, Loading } from "vant";
export default {
    mixins: [base,iworksTool],
    name: "ExamMsg",
    components: {
        VanLoading: Loading,
    },
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        //检查到图片列表空，是否反复加载图片
        isReload: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            getResourceTempState,
            imageStandardIcon,
            isIworksTest,
            openState: false,
            loadingImage: false,
            imageList: [],
            invalidImage: [],
            cid: this.$route.params.cid,
        };
    },
    beforeDestroy() {},
    computed: {
        conversation() {
            return this.conversationList[this.cid] || {};
        },
        avatarObj() {
            let obj = {
                avatar: this.message.avatar,
                sex: this.message.sex,
                is_single_chat: 1,
                nickname: this.message.nickname,
            };
            return obj;
        },
        messageImage() {
            let imageList = this.message.imageList;
            if (!imageList && this.isReload) {
                this.reloadResourceList();
            }
            return imageList || [];
        },
        isAiAnalyze(){
            return this.conversation.service_type==this.systemConfig.ServiceConfig.type.AiAnalyze
        },
    },
    methods: {
        showAiAnalyzeIcon(img){
            return this.functionsStatus.breastAI||this.functionsStatus.drAIAssistant//||this.functionsStatus.obstetricalAI
        },
        toggleImages(message) {
            this.openState = !this.openState;
            if (this.openState) {
                this.setImageList();
            }
        },
        reloadResourceList() {
            if (this.openState) {
                this.setImageList();
            }
        },
        setImageList() {
            if (this.loadingImage) {
                return;
            }
            let message = this.message;
            let resource_id_list = [];
            for (let resource of this.message.resourceList) {
                resource_id_list.push(resource.id);
            }
            if (message.imageList) {
                this.imageList = this.message.imageList;
                this.setInvalidImages(resource_id_list);
            } else {
                this.loadingImage = true;
                this.conversation.socket.emit(
                    "get_gallery_messages_by_resource_ids",
                    { resource_id_list },
                    (is_succ, data) => {
                        if (is_succ) {
                            if(data.iworks_protocol_list){
                                for(let key in data.iworks_protocol_list){
                                    let protocol=data.iworks_protocol_list[key];
                                    protocol.protocolTree=[this.setProtocolTree(protocol)]
                                    protocol.viewList=this.setViewList(protocol.protocolTree[0],[])
                                }
                                this.$store.commit('gallery/setCommentToGallery',{iworks_protocol_list:data.iworks_protocol_list})
                            }
                            if(data.comment_list){
                                this.$store.commit('gallery/setCommentToGallery',{list:data.comment_list})
                            }
                            patientDesensitization(data.gallery_list);
                            this.setIworks(data.gallery_list,message.protocol_execution_guid);
                            this.imageList = data.gallery_list;
                            this.$store.commit("conversationList/updateChatMessage", {
                                gmsg_id: message.gmsg_id,
                                group_id: this.cid,
                                imageList: this.imageList,
                            });
                            this.setInvalidImages(resource_id_list);
                        }
                        this.loadingImage = false;
                    }
                );
            }
        },
        setInvalidImages(resource_id_list) {
            let length = resource_id_list.length - this.imageList.length;
            this.invalidImage = [];
            for (let index = 0; index < length; index++) {
                this.invalidImage.push(index);
            }
        },
        setIworks(list,protocol_execution_guid) {
            for (let item of list) {
                setIworksInfoToMsg(item,protocol_execution_guid);
                // if (item.iworks_protocol) {
                //     this.iworksProtocol=item.iworks_protocol;
                //     break;
                // }
            }
        },
        openGallery(item, img) {
            if (!img.url) {
                return;
            }
            if (Number(window.vm.$root.currentLiveCid) === Number(this.cid)) {
                Toast(this.lang.playing_video_tip);
                return;
            }

            let galleryList = this.imageList;
            let index = 0;
            for (let i = 0; i < galleryList.length; i++) {
                if (img.resource_id == galleryList[i].resource_id) {
                    index = i;
                }
            }
            this.$store.commit("gallery/setGallery", {
                list: galleryList,
                index: index,
            });
            // console.error('--------')
            // console.error(this.$route)
            this.$nextTick(() => {
                // this.$router.push(`/index/chat_window/${this.$route.params.cid}/gallery`);
                this.$router.push(`${this.$route.path}/gallery`);
            });
        },
        showTooltipsMenu(message) {
            if (message.is_default_image) {
                return false;
            }
            if (this.getResourceTempState(message.resource_id) === 0) {
                return;
            }
            let msg = Object.assign({}, message);
            this.$emit("showExamMsgMenu", { msg, from: "examImageItem" });
        },
        showTooltipsInvalidMenu(message) {
            return false;
        },
    },
};
</script>
<style lang="scss">
.exam_msg_item {
    padding: 0.7rem 0.4rem 0.15rem 0.4rem;
    background-color: #f2f6f9;
    border-radius: 0.2rem;
    box-shadow: 0.1rem 0.1rem 0.2rem rgba(140, 152, 155, 0.7);
    position: relative;
    overflow: hidden;
    &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 0.1rem;
    }
    .file_item {
        &.is_create {
            .file_container::before {
                content: "";
                position: absolute;
                left: 0;
                top: 0;
                bottom: 0;
                width: 0.1rem;
                background-color: rgb(255, 103, 92);
                z-index: 2;
            }
            .view_name {
                color: rgb(255, 103, 92);
            }
        }
        .file_quality_icon{
            position: relative;
            .qc_standard_icon {
                bottom: 0.1rem;
                right: 0.2rem;
                position: absolute;
                color: rgb(0, 255, 102);
                font-size: 10px;
                z-index: 10;
            }
            .qc_non_standard_icon {
                bottom: 0.1rem;
                right: 0.2rem;
                position: absolute;
                color: rgb(255, 153, 51);
                font-size: 10px;
                z-index: 10;
            }
            .ai_result_deletion_icon {
                bottom: 0.1rem;
                right: 0.2rem;
                position: absolute;
                color: rgb(255, 0, 0);
                font-size: 10px;
                z-index: 10;
            }
        }
        // img{
        //     max-width: 100%;
        //     max-height: 100%;
        // }
    }
    .exam_title {
        font-size: 0.8rem;
        color: #333;
        line-height: 1.6;
        position: relative;
        .exam_uploader {
            display: flex;
            padding-bottom: 0.6rem;
            position: relative;
            .uploader_info {
                flex: 1;
                padding-left: 0.6rem;
                .date {
                    font-size: 0.4rem;
                    color: #999;
                    margin-top: 0.1rem;
                }
            }
            &::after {
                content: "";
                position: absolute;
                height: 1px;
                background-color: rgb(171, 181, 186);
                transform: scaleY(0.25);
                left: 0;
                right: 0;
                bottom: 0;
            }
        }
        .exam_patient {
            font-size: 0.55rem;
            padding-top: 0.55rem;
            color: rgb(101, 109, 112);
            line-height: 1;
            .patient_info {
                .patient_info_item {
                    width: 33%;
                    word-break: break-all;
                    padding-right: 0.2rem;
                    float: left;
                    box-sizing: border-box;
                    margin-bottom: 0.5rem;
                }
                .date {
                    flex: 2;
                    margin-bottom: 0.5rem;
                }
            }
        }
        .exam_iworks {
            display: flex;
            align-items: center;
            color: rgb(101, 109, 112);
            font-size: 0.55rem;
            margin-bottom: 0.5rem;
            .icon_title {
                background-color: #666;
                border-radius: 50%;
                width: 0.85rem;
                height: 0.85rem;
                display: flex;
                align-items: center;
                justify-content: center;
                .icon-quanbuwenjian-weixuanzhong {
                    color: #fff;
                    font-size: 0.6rem;
                    line-height: 1;
                }
            }
            p {
                line-height: 1rem;
                padding-left: 0.4rem;
                font-size: 0.55rem;
            }
        }
        .exam_down,
        .exam_up {
            fill: gray;
            color: gray;
            position: absolute;
            right: 0;
            top: 0;
            width: 0.7rem;
            height: 0.7rem;
        }
        .icon-warning-o {
            position: static;
            vertical-align: sub;
            margin-left: 0.2rem;
        }
        .sender {
            padding-right: 2rem;
            word-break: break-all;
            font-size: 0.6rem;
            color: rgb(101, 109, 112);
            span:after {
                content: ",";
                display: inline-block;
            }
            span:last-child {
                &:after {
                    content: "";
                }
            }
        }
        .unread {
            position: absolute;
            right: 0;
            top: 0;
            background: #f00;
            color: #fff;
            display: inline-block;
            width: 0.7rem;
            height: 0.7rem;
            line-height: 0.7rem;
            font-size: 0.7rem;
            text-align: center;
            border-radius: 50%;
            padding: 0.1rem;
        }
        .comment_num {
            position: absolute;
            right: 1.5rem;
            top: -0.3rem;
            font-size: 1rem;
            color: #333;
            span {
                position: absolute;
                right: -0.3rem;
                top: 0rem;
                background-color: #00c59d;
                color: #fff;
                border-radius: 50%;
                font-size: 0.5rem;
                min-width: 0.7rem;
                min-height: 0.7rem;
                display: inline-block;
                line-height: 0.7rem;
                text-align: center;
            }
        }
    }
    .exam_image_list {
        padding-top: 0.55rem;
        position: relative;
        &::before {
            content: "";
            position: absolute;
            height: 1px;
            background-color: rgb(171, 181, 186);
            transform: scaleY(0.25);
            left: 0;
            right: 0;
            top: 0;
        }
        .loading_image {
            width: 36px;
            margin: 10px auto;

            .van-loading__spinner {
                width: 1.8rem;
                height: 1.8rem;

                .van-loading__circular circle {
                    stroke-width: 0.2rem;
                }
            }
        }
        .file_item {
            float: left;
            width: 25%;
            box-sizing: border-box;
            position: relative;
            background: #f2f6f9;
            padding-right: 0.15rem;
            padding-bottom: 0.5rem;
            overflow: hidden;
            .file_container {
                position: relative;
                padding-top: 75%;
                height: 0;
                background-color: #000;
                overflow: hidden;
                border-radius: 0.2rem;
                &.more_container {
                    background: transparent;
                    .more_text {
                        position: absolute;
                        width: 50%;
                        height: 66%;
                        top: 16%;
                        left: 25%;
                        border: 1px solid #aaa;
                        border-radius: 50%;
                        font-size: 0.6rem;
                        color: #666;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                }
                .review_text {
                    position: absolute;
                    color: yellow;
                    z-index: 1;
                    top: 0;
                    font-size: 0.5rem;
                    width: 100%;
                    text-align: center;
                    top: 5%;
                    transform: scale(0.8);
                }
                .file_image {
                    max-width: 100%;
                    max-height: 100%;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
                .video_icon {
                    width: 0.5rem;
                    height: 0.5rem;
                    position: absolute;
                    bottom: 0.2rem;
                    left: 0.3rem;
                }
                .review_time {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) scale(0.4);
                    font-size: 1rem;
                    color: #fff;
                    text-align: center;
                    white-space: nowrap;
                }
                .view_name {
                    color: #fff;
                    position: absolute;
                    top: 4px;
                    z-index: 9;
                    left: 4px;
                    transform: none;
                    font-size: 0.4rem;
                    right: 4px;
                }
                .supuly_image {
                    position: absolute;
                    width: 50%;
                    height: 66%;
                    top: 16%;
                    left: 25%;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);

                    font-size: 1.2rem;
                    color: #fff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
            &.is_delete {
                .view_name {
                    text-decoration: line-through;
                }
            }
        }
        .consultation_btn {
            margin-top: 0.1rem;
            padding-top: 0.65rem;
            position: relative;
            padding-bottom: 0.65rem;
            &::before {
                content: "";
                position: absolute;
                height: 1px;
                background-color: rgb(171, 181, 186);
                transform: scaleY(0.25);
                left: 0;
                right: 0;
                top: 0;
            }
            .apply_btn {
                float: left;
                font-size: 0.6rem;
                padding: 0.2rem 0.4rem;
                background-color: #01bd97;
                margin: 0rem 1.75rem 0 0;
                border-radius: 0.2rem;
                box-sizing: border-box;
                color: #fff;
                border: none;
                line-height: 0.65rem;
            }
            .transmit_icon,
            .comment_icon,
            .apply_icon {
                width: 0.65rem;
                height: 0.65rem;
                fill: #fff;
                float: left;
            }
        }
        .exam_comments {
            padding-top: 0.65rem;
            position: relative;
            padding-bottom: 0.2rem;
            &::before {
                content: "";
                position: absolute;
                height: 1px;
                background-color: rgb(171, 181, 186);
                transform: scaleY(0.25);
                left: 0;
                right: 0;
                top: 0;
            }
            & > p {
                font-size: 0.6rem;
                line-height: 1;
                padding-bottom: 0.4rem;
                color: rgb(101, 109, 112);
                span {
                    margin-left: 0.3rem;
                }
            }
            .comment_item {
                color: #333;
                font-size: 0.5rem;
                margin-bottom: 0.3rem;
                .time_and_author {
                    color: #00c59d;
                    line-height: 0.95rem;
                    .author {
                        margin-right: 0.3rem;
                    }
                }
                & > p {
                    line-height: 0.85rem;
                    color: rgb(101, 109, 112);
                    span {
                        font-size: 0.5rem;
                        background-color: #01c59d;
                        color: #fff;
                        line-height: 1;
                        padding: 0.2rem 0.3rem;
                        border-radius: 0.2rem;
                        margin-top: 0;
                        margin-left: 0.2rem;
                        display: inline-block;
                    }
                }
            }
        }
        .conclusions {
            font-size: 0.8rem;
            color: #333;
            line-height: 1.6;
        }
    }
}
</style>
