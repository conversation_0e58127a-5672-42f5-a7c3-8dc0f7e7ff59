<template>
    <div class="chat_history" v-loading="!is_loaded_history_list">
        <audio ref="soundPlayer" id="soundPlayer" autoplay="autoplay" :src="voiceSrc">
        </audio>
        <!-- <div v-if="unreadNumber>0" @click="toUnreadMsg" class="unread_number_btn"><i class="iconfont iconup1"></i>{{unreadNumber}}{{lang.unread_message_tip}}</div> -->
        <div class="join_verify_tip" @click="openJoinVerify" v-show="chatType==CHAT_TYPE['CHAT_WINDOW']&&conversation.applyCount>0">
            <i class="iconfont iconuser-round-add"></i>
            <p>{{lang.group_join_verify_btn}}（{{conversation.applyCount}}）</p>
        </div>
	    <DynamicScroller
            :items="chatMessageList"
            :min-item-size="47"
            :class="customClass"
            ref="scroller"
            v-if="chatMessageList.length>0"
            @scroll.native="handleScroll"
            key-field="tmp_index"
            :buffer="1000"
            >
            <template v-slot="{item,index,active}">
                <DynamicScrollerItem
                    :item="item"
                    :active="active"
                    :data-index="index"
                    :size-dependencies="[item.msg_type]"
                    class="scroller-item"
                    :msg-type="item.msg_type">
                    <div :key="item.id">
                        <div v-if="isSystemTip(item)&&getSendTimeTip(index)!=''" class="notify_wrapper">
                            <div class="send_time_tip">
                                {{getSendTimeTip(index,2)}}
                            </div>
                        </div>
                        <template v-if="item.msg_type==systemConfig.msg_type.SYS_JOIN_ATTENDEE">
                            <SysJoinAttendeeMsg :message="item" :attendeeList="conversation.attendeeList"></SysJoinAttendeeMsg>
                        </template>
                        <template v-else-if="item.msg_type==systemConfig.msg_type.SYS_KICKOUT_ATTENDEE">
                            <SysKickoutAttendeeMsg :message="item" :attendeeList="conversation.attendeeList"></SysKickoutAttendeeMsg>
                        </template>
                         <template v-else-if="item.msg_type==systemConfig.msg_type.SYS_START_RT_VOICE">
                            <SysStartRTVoiceMsg :message="item"></SysStartRTVoiceMsg>
                        </template>
                        <template v-else-if="item.msg_type==systemConfig.msg_type.SYS_STOP_RT_VOICE">
                            <SysStopRTVoiceMsg :message="item"></SysStopRTVoiceMsg>
                        </template>
                        <template v-else-if="item.msg_type==systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION">
                            <SysStartRealTimeConsultationMsg :message="item"></SysStartRealTimeConsultationMsg>
                        </template>
                        <template v-else-if="item.msg_type==systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION">
                            <SysStopRealTimeConsultationMsg :message="item" @openLiveDetailDialog="openLiveDetailDialog"></SysStopRealTimeConsultationMsg>
                        </template>
                        <div v-else-if="item.msg_type==systemConfig.msg_type.WITHDRAW" class="notify_wrapper">
                            <div class="system_notify longwrap" v-if="item.sender_id === user.uid">
                                {{lang.revocation_message_by_self}}
                                <span v-show="(isShowReEdit(item)|| (presentTime - new Date(item.send_ts).getTime() <= 180000)) && item.msg_body" @click.stop="reEditMessage(item)" :class="['re_edit_wrapper',`re_edit_${index}`]">{{lang.re_edit}}</span>
                            </div>
                            <div class="system_notify longwrap" v-else>
                                <span >{{item.nickname}}</span>{{lang.revocation_message_by_other}}
                            </div>
                        </div>
                        <div v-else-if="item.msg_type==systemConfig.msg_type.AI_ANALYZE&&item.sender_id!=aiAnalyzeId" @click="openAiAnalyzeGallery($event,item)" class="system_message" @contextmenu="callImageMenu($event,item,'chatComponent')">
                            <v-touch @press="callImageMenu($event,item,'chatComponent')" class="ai_images_wrap" v-if="item.ai_analyze&&item.ai_analyze.messages">
                                <img v-if="!isAllAiMessageDelete(item)"  class="message_image comment_thumbnail" :src="limitImageSize(item.ai_analyze.messages[0].error_image||item.ai_analyze.messages[0].url,300)" @error="setErrorImage(item.ai_analyze.messages[0])">
                                <div v-else class="message_image comment_thumbnail">
                                    <img src='static/resource_pc/images/default.png' />
                                    <div class="delete_resource_tip">
                                        <p>{{lang.ref_res_expired}}</p>
                                    </div>
                                </div>
                                <i v-if="item.ai_analyze.messages.length>1" class="iconfont icon-images"></i>
                            </v-touch>
                            <div class="ai_message_right">
                                <p>
                                    <span class="nickname" @click.stop="openVisitingCard(item,1)">{{item.nickname}}</span>
                                {{lang.share_analyze_result}}</p>
                                <div v-if="item.ai_analyze&&item.ai_analyze.report.error==undefined" class="tip">
                                    {{lang.analyzing}}
                                </div>
                                <div v-else-if="item.ai_analyze&&item.ai_analyze.report.error==0" class="tip">
                                    {{ lang.analyze_result_tip }}
                                    <template v-if="item.ai_analyze ==aiPresetData.typeIndex.breast">
                                        <div v-if="item.ai_analyze.report.tumor_probability">
                                            <p>{{lang.bengin_probability}}{{(item.ai_analyze.report.tumor_probability[0]*100).toFixed(2)+'%'}}</p>
                                            <p>{{lang.malignant_probability}}{{(item.ai_analyze.report.tumor_probability[1]*100).toFixed(2)+'%'}}</p>
                                        </div>
                                        <p v-if="!item.ai_analyze.report.detected_tumor">{{lang.no_ai_result}}</p>
                                        <p v-if="item.ai_analyze.report.mark_list">{{lang.see_trace_tip}}</p>
                                    </template>
                                    <template v-else>
                                        <div v-if="item.ai_analyze.status">
                                            <p>{{lang.obstetric_qc.nalysis_completed}}</p>
                                        </div>
                                        <div v-else>
                                            <p>{{lang.no_ai_result}}</p>
                                        </div>
                                    </template>

                                </div>
                                <div v-else>
                                    {{ lang.analyze_result_tip }}
                                    <p>{{lang.no_ai_result}}
                                        <!-- : {{item.ai_analyze&&item.ai_analyze.report.error_code}} -->
                                    </p>

                                </div>
                                <p class="right_tip">{{lang.from_ai_analyze}}</p>
                            </div>
                        </div>
                        <div v-else-if="item.msg_type==systemConfig.msg_type.SYS_CONFERENCE_PLAN" class="notify_wrapper">
                            <div v-if="item.tip_type==systemConfig.conference_plan_tip_type.New" class="system_notify longwrap">
                                <span @click="openVisitingCard(item,1)">{{item.nickname}}</span>
                                {{lang.reserved_conference_tip}}{{item.conference_plan&&item.conference_plan.subject}}
                            </div>
                            <div v-else-if="item.tip_type==systemConfig.conference_plan_tip_type.Prepare" class="system_notify longwrap">
                                {{lang.conference_begin_tip}}{{item.conference_plan&&item.conference_plan.subject}}
                            </div>
                            <div v-else-if="item.tip_type==systemConfig.conference_plan_tip_type.Cancel" class="system_notify longwrap">
                                <span @click="openVisitingCard(item,1)">{{item.nickname}}</span>
                                {{lang.delete_conference_tip}}{{item.conference_plan&&item.conference_plan.subject}}
                            </div>
                        </div>
                        <div v-else-if="item.msg_type==systemConfig.msg_type.HISTORY_TIP" class="notify_wrapper">
                            <p>---{{lang.history_tip}}---</p>
                        </div>
                        <div v-else-if="item.msg_type==systemConfig.msg_type.MULTICENTER_REJECT" class="notify_wrapper">
                            <div class="system_notify longwrap">
                                {{lang.multicenter_reject_tip}}
                                <span @click="vieReject">{{lang.view_details}}</span>
                            </div>
                        </div>
                        <div v-else-if="item.isConferenceAuxOnline" class="empty_item"></div>
                        <div v-else class="message_item clearfix" :id="'message-'+item.gmsg_id" :ref="'message-'+item.gmsg_id" :class="{self_chat:item.sender_id==user.uid&&chatType!=CHAT_TYPE['CHAT_HISTORY'], highlight: highlightGmsgId === item.gmsg_id}">
                            <!-- 非历史聊天记录界面下才会 -->
                            <div class="message_item_title clearfix">
                                <v-touch @press="pressAvatar(item)" class="avatar_box" @click.native="clickHistoryAvatar(item)">
                                    <mr-avatar :url="getLocalAvatar(item)" :key="item.avatar"></mr-avatar>
                                </v-touch>
                                <template v-if="isAiAnalyze">
                                    <div class="message_item_name longwrap">{{lang.ai_analyze}}</div>
                                </template>
                                <template v-else-if="isDrAiAnalyze">
                                    <div class="message_item_name longwrap">{{lang.dr_ai_analyze}}</div>
                                </template>
                                <template v-else>
                                    <div class="message_item_name longwrap">{{attendeeMap[item.sender_id]||item.nickname}}</div>
                                </template>
                                <div class="message_item_time">{{formatTime(item.send_ts)}}</div>
                            </div>
                            <div class="message_item_wrapper">
                                <div v-if="item.msg_type==systemConfig.msg_type.Text" @contextmenu="callTextMenu($event,item.msg_body,1,item)" @click="clickTextMsg">
                                    <v-touch @press="callTextMenu($event,item.msg_body,1,item)">
                                        <div v-html="item.msg_body" class="message_item_content text_message"></div>
                                    </v-touch>
                                </div>
                                <template v-else-if="(item.msg_type==systemConfig.msg_type.Image||item.msg_type==systemConfig.msg_type.Frame||item.msg_type==systemConfig.msg_type.OBAI )">
                                    <div v-if="getResourceTempState(item.resource_id) ===1"  @click="clickGallery($event,item)"  @contextmenu="callImageMenu($event,item,'chatComponent')" class="message_item_content image_msg img_window" :class="{mc_resource_map_info:(item.mc_resource_map&&item.mc_resource_map.ai_report)||(item.ai_analyze&&item.ai_analyze.type&&(isAiAnalyze||isDrAiAnalyze))}">
                                        <v-touch class="message_image" @press="callImageMenu($event,item,'chatComponent')">
                                            <img :src="item.error_image||item.url_local" @error="setErrorImage(item)">
                                            <span v-for="iconObj,index in imageStandardIcon(item)" :key="index" :class="[iconObj.css]" :title="iconObj.tips">
                                                    {{iconObj.label}}
                                            </span>
                                        </v-touch>
                                        <span class="comment_number" v-show="getCommentNum(gallery.commentObj[item.resource_id]&&gallery.commentObj[item.resource_id].comment_list)">{{getCommentNum(gallery.commentObj[item.resource_id]&&gallery.commentObj[item.resource_id].comment_list)}}</span>
                                        <template v-if="functionsStatus.obstetricalAI&&false">
                                            <div class="" v-if="item.mc_resource_map&&item.mc_resource_map.ai_report">
                                                <p>{{lang.patient_id}}: {{item.patient_id}}</p>
                                                <p v-if="isObstetricQCMulticenter">{{lang.status}}: {{item.mc_resource_map.ai_report.finshed? lang.obstetric_qc.nalysis_completed : lang.obstetric_qc.nalysis_uncompleted}}</p>
                                            </div>
                                        </template>
                                        <template v-if="functionsStatus.breastAI">
                                            <div class="" v-if="item.ai_analyze&&item.ai_analyze.type&&(isAiAnalyze||isDrAiAnalyze)">
                                                <p >{{hasAiAnalyzeResult(item)? lang.obstetric_qc.nalysis_completed : lang.obstetric_qc.nalysis_uncompleted}}</p>
                                            </div>
                                        </template>
                                        <div class="uploading_wrapper2" v-if="item.uploading" @click.stop="handleUploadStatus(item)">
                                            <template v-if="item.uploadId">
                                                <el-progress type="circle" :percentage="item.percent" :width="86" :show-text="!isSupportOssPause">
                                                </el-progress>
                                                <template v-if="isSupportOssPause">
                                                    <i class="icon iconfont iconshangchuan1" v-if="item.pauseUpload&&!item.uploadFail"></i>
                                                    <i class="icon iconfont iconpause-upload" v-if="!item.pauseUpload&&!item.uploadFail"></i>
                                                </template>
                                                <i class="icon iconfont icongantanhao-yuankuang" v-if="item.uploadFail"></i>
                                                <i class="icon iconfont iconclose" @click.stop="handleCancelUpload(item)"></i>
                                            </template>
                                            <template v-else>
                                                <img src="static/resource_pc/images/loading.gif">
                                            </template>
                                        </div>
                                    </div>
                                    <div class="notify_wrapper" v-else>
                                        <div class="error_image">
                                            <img src='static/resource_pc/images/default.png' />
                                        </div>
                                        <div>
                                            <p>{{lang.ref_res_expired}}</p>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="item.msg_type==systemConfig.msg_type.Video||item.msg_type==systemConfig.msg_type.Cine">
                                    <div v-if="getResourceTempState(item.resource_id) ===1" class="message_item_content video_msg img_window" @click="clickGallery($event,item)" @contextmenu="callImageMenu($event,item,'chatComponent')">
                                        <span class="comment_number" v-show="getCommentNum(gallery.commentObj[item.resource_id]&&gallery.commentObj[item.resource_id].comment_list)">{{getCommentNum(gallery.commentObj[item.resource_id]&&gallery.commentObj[item.resource_id].comment_list)}}</span>
                                        <v-touch class="message_image" @press="callImageMenu($event,item,'chatComponent')">
                                            <img :src="item.url_local" >
                                        </v-touch>
                                        <div class="uploading_wrapper2" v-if="item.uploading" @click.stop="handleUploadStatus(item)">
                                            <template v-if="item.uploadId">
                                                <el-progress type="circle" :percentage="item.percent" :width="86" :show-text="!isSupportOssPause">
                                                </el-progress>
                                                <template v-if="isSupportOssPause">
                                                    <i class="icon iconfont iconshangchuan1" v-if="item.pauseUpload&&!item.uploadFail"></i>
                                                    <i class="icon iconfont iconpause-upload" v-if="!item.pauseUpload&&!item.uploadFail"></i>
                                                </template>
                                                <i class="icon iconfont icongantanhao-yuankuang" v-if="item.uploadFail"></i>
                                                <i class="icon iconfont iconclose" @click.stop="handleCancelUpload(item)"></i>
                                            </template>
                                            <template v-else>
                                                <img src="static/resource_pc/images/loading.gif">
                                            </template>
                                        </div>
                                    </div>
                                    <div class="notify_wrapper" v-else>
                                        <div class="error_image">
                                            <img src='static/resource_pc/images/default.png' />
                                        </div>
                                        <div>
                                            <p>{{lang.ref_res_expired}}</p>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="item.msg_type==systemConfig.msg_type.RealTimeVideoReview">
                                    <review-msg :message="item" :ref="`reviewItem${item.gmsg_id}`" @clickGallery="clickGallery" @contextmenu.native="callImageMenu($event,item,'chatComponent')" v-if="getResourceTempState(item.resource_id) ===1">
                                    </review-msg>
                                    <div class="notify_wrapper" v-else>
                                        <div class="error_image">
                                            <img src='static/resource_pc/images/default.png' />
                                        </div>
                                        <div>
                                            <p>{{lang.ref_res_expired}}</p>
                                        </div>
                                    </div>
                                </template>
                                <template v-else-if="item.msg_type==systemConfig.msg_type.VIDEO_CLIP">
                                    <video-clip-msg :message="item" :ref="`videoClipItem${item.gmsg_id}`" @clickGallery="clickGallery" @contextmenu.native="callImageMenu($event,item,'chatComponent')" v-if="getResourceTempState(item.resource_id) ===1"></video-clip-msg>
                                    <div class="notify_wrapper" v-else>
                                        <div class="error_image">
                                            <img src='static/resource_pc/images/default.png' />
                                        </div>
                                        <div>
                                            <p>{{lang.ref_res_expired}}</p>
                                        </div>
                                    </div>
                                </template>

                                <exam-msg
                                    @contextmenu.native="callImageMenu($event,item,'chatComponent_examListItem')"
                                    v-else-if="item.msg_type==systemConfig.msg_type.EXAM_IMAGES"
                                    :message="item"
                                    :isReload="isReload"
                                    :cid="cid"
                                    @clickGallery="clickGallery"
                                    :chatType="chatType"
                                ></exam-msg>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.Sound" class="message_item_content sound_msg" :class="{playing:item.gmsg_id==soundingMsgId}" @click="playSound(item.gmsg_id)" @contextmenu="callImageMenu($event,item,'chatComponent')">
                                    <img v-if="item.uploading || item.downloading" src="static/resource_pc/images/loading.gif">
                                    <span v-else>
                                        {{Math.ceil(item.duration/1000)}}"
                                    </span>
                                </div>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.File" class="message_item_content file_msg" :class="[checkFileExpired(item)?'expired':'']" @contextmenu="callImageMenu($event,item,'chatComponent')">
                                    <div class="file_content">
                                        <div class="file_content-l">
                                            <p class="file_name" >{{item.file_name}}</p>
                                            <p class="file_size" v-if="item.resource_file_size">{{formatSize(item.resource_file_size)}}</p>
                                        </div>
                                        <div class="file_content-r">
                                            <img :src="getFileIcon(item.file_name)" @error="noFileIcon">
                                        </div>
                                    </div>
                                    <div class="file_content-footer" v-if="item.resource_expired_at">
                                        <span>{{lang.expiration_date}}:{{formatTime(item.resource_expired_at)}}</span>
                                        <span v-if="checkFileExpired(item)">{{ lang.group_apply_expire }}</span>
                                    </div>
                                    <div class="uploading_wrapper2" v-if="item.uploading" @click.stop="handleUploadStatus(item)">
                                        <template v-if="item.uploadId">
                                            <el-progress type="circle" :percentage="item.percent" :width="86" :show-text="!isSupportOssPause">
                                            </el-progress>
                                            <template v-if="isSupportOssPause">
                                                <i class="icon iconfont iconshangchuan1" v-if="item.pauseUpload&&!item.uploadFail"></i>
                                                <i class="icon iconfont iconpause-upload" v-if="!item.pauseUpload&&!item.uploadFail"></i>
                                            </template>
                                            <i class="icon iconfont icongantanhao-yuankuang" v-if="item.uploadFail"></i>
                                            <i class="icon iconfont iconclose" @click.stop="handleCancelUpload(item)"></i>
                                        </template>
                                        <template v-else>
                                            <img src="static/resource_pc/images/loading.gif">
                                        </template>
                                    </div>
                                </div>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.IWORKS_PROTOCOL" class="message_item_content iworks_protocol_msg" @contextmenu="callImageMenu($event,item,'chatComponent')">
                                    <div class="iworks_protocol">
                                        <div class="title">iWorks
                                        </div>
                                        <div class="content">
                                            <div>{{getIworksProtocolInfo(item, "name")}}</div>
                                            <div>{{getIworksProtocolInfo(item, "Description")}}</div>
                                            <div>{{getIworksProtocolInfo(item, "Author")}}</div>
                                            <div>{{getIworksProtocolInfo(item, "Version")}}</div>
                                            <div>{{getIworksProtocolInfo(item, "CreateDate")}}</div>
                                        </div>
                                    </div>
                                </div>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.COMMENT||item.msg_type==systemConfig.msg_type.TAG" class="message_item_content clearfix comment_msg" @click="view_comment(item)" @contextmenu="callImageMenu($event,item,'chatComponent')">
                                    <div class="record_content" v-if="
                                            item.img_type_ex == systemConfig.msg_type.RealTimeVideoReview
                                        ">
                                        <template v-if="item.coverUrl" >
                                            <img :src="limitImageSize(item.coverUrl,300)" alt="" srcset="" class="recore_thumbnail" />
                                            <i class="iconfont iconbofang svg_icon_play"></i>
                                        </template>
                                        <img src="static/resource_pc/images/realtime_background.png" v-else />
                                        <div class="diff_time">{{ formatDurationTime(item.duration) }}</div>
                                        <div class="review_item_read_subject">
                                            {{ getRecordSubject(item)}}
                                        </div>
                                        <div class="review_item_read_time">{{ formatTime(item.send_ts) }}</div>

                                    </div>
                                    <div class="record_content" v-else-if="
                                            item.img_type_ex == systemConfig.msg_type.VIDEO_CLIP
                                        ">
                                        <template v-if="item.coverUrl" >
                                            <img :src="limitImageSize(item.coverUrl,300)" alt="" srcset="" class="recore_thumbnail" />
                                            <i class="iconfont iconbofang svg_icon_play"></i>
                                        </template>
                                        <img src="static/resource_pc/images/realtime_background.png" v-else />
                                        <div class="diff_time">{{ formatDurationTime(item.duration) }}</div>
                                        <div class="review_item_read_subject">
                                            {{ getClipSubject(item)}}
                                        </div>
                                        <div class="review_item_read_time">{{ formatTime(item.send_ts) }}</div>

                                    </div>
                                    <div v-else class="message_image comment_thumbnail">
                                        <img :src="item.error_image||item.url_local" @error="setErrorImage(item)">
                                        <i class="iconfont iconbofang svg_icon_play" v-show="item.img_type_ex==systemConfig.msg_type.Video||item.img_type_ex==systemConfig.msg_type.Cine"></i>
                                    </div>


                                    <div v-if="item.msg_type==systemConfig.msg_type.COMMENT">
                                        {{lang.add_comment_msg_text}}
                                        <p>{{item.comment}}</p>
                                    </div>
                                    <div v-else-if="item.msg_type==systemConfig.msg_type.TAG&&item.action==1">
                                        {{lang.add_tag_msg_text}}
                                        <p>{{item.tags}}</p>
                                    </div>
                                    <div v-else-if="item.msg_type==systemConfig.msg_type.TAG&&item.action==2">
                                        {{lang.delete_tag_msg_text}}
                                        <p>{{item.tags}}</p>
                                    </div>
                                </div>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.AI_ANALYZE" class="message_item_content clearfix" @click="openAiAnalyzeGallery($event,item)" @contextmenu="callImageMenu($event,item,'chatComponent')">
                                    <div class="ai_images_wrap" v-if="item.ai_analyze&&item.ai_analyze.messages">
                                        <v-touch class="message_image" @press="callImageMenu($event,item,'chatComponent')">
                                            <img  v-if="!isAllAiMessageDelete(item)" class="comment_thumbnail" :src="limitImageSize(item.ai_analyze.messages[0].error_image||item.ai_analyze.messages[0].url,300)" @error="setErrorImage(item.ai_analyze.messages[0])">
                                            <div v-else class="message_image comment_thumbnail">
                                                <img src='static/resource_pc/images/default.png' />
                                                <div class="delete_resource_tip">
                                                    <p>{{lang.ref_res_expired}}</p>
                                                </div>
                                            </div>
                                        </v-touch>

                                        <i v-if="item.ai_analyze.messages.length>1" class="iconfont icon-images"></i>
                                    </div>

                                    <div v-if="item.ai_analyze&&item.ai_analyze.report.error==undefined">
                                        {{lang.analyzing}}
                                    </div>
                                    <div v-else-if="item.ai_analyze&&item.ai_analyze.report.error==0">
                                        {{ lang.analyze_result_tip }}
                                        <template v-if="item.ai_analyze.type ==aiPresetData.typeIndex.breast">
                                            <div v-if="item.ai_analyze.report.tumor_probability">
                                                <p>{{lang.bengin_probability}}{{(item.ai_analyze.report.tumor_probability[0]*100).toFixed(2)+'%'}}</p>
                                                <p>{{lang.malignant_probability}}{{(item.ai_analyze.report.tumor_probability[1]*100).toFixed(2)+'%'}}</p>
                                            </div>
                                            <p v-if="!item.ai_analyze.report.detected_tumor">{{lang.no_ai_result}}</p>
                                            <p v-if="item.ai_analyze.report.mark_list" class="tip">{{lang.see_trace_tip}}</p>
                                        </template>
                                        <template v-else>
                                            <div v-if="item.ai_analyze.status">
                                                <p>{{lang.obstetric_qc.nalysis_completed}}</p>
                                            </div>
                                            <div v-else>
                                                <p>{{lang.no_ai_result}}</p>
                                            </div>
                                        </template>

                                    </div>
                                    <div v-else>
                                        {{ lang.analyze_result_tip }}
                                        <p>{{lang.no_ai_result}}</p>
                                        <!-- {{item.ai_analyze&&item.ai_analyze.report.error_code}} -->
                                    </div>
                                </div>


                                <div v-else-if="item.msg_type==systemConfig.msg_type.IWORKS_SCORE" class="message_item_content clearfix" @click="openIworksGallery(item)" >
                                    <div class="ai_images_wrap" v-if="limitImageSize(item.ai_result&&item.ai_result.imgUrl,300)">
                                        <v-touch class="message_image">
                                            <img  class="comment_thumbnail" :src="item.ai_result.error_image||item.ai_result.imgUrl" @error="setErrorImage(item.ai_result)">
                                        </v-touch>
                                    </div>

                                    <div v-if="item.ai_result&&!item.ai_result.isError">
                                        <p class="analyze_tip">{{lang.iworks_score_label}}{{item.ai_result.result[0].score}}</p>
                                    </div>
                                    <div v-else>
                                        <p class="analyze_tip">{{lang.iworks_fail_label}}</p>
                                    </div>
                                </div>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.EXPIRATION_RES" class="notify_wrapper">
                                    <div class="error_image">
                                        <img src='static/resource_pc/images/default.png' />
                                    </div>
                                    <div>
                                        <p>{{lang.ref_res_expired}}</p>
                                    </div>
                                </div>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.LIVE_INVITE" class="message_item_content live_image_box" @contextmenu="callLiveMenu($event,item,3,'chatComponent')">
                                    <div class="card-image">
                                        <img :src="limitImageSize(item.liveInfo.cover_image,300)" v-if="item.liveInfo.cover_image">
                                        <img src="static/resource_pc/images/live_cover.png" v-else>
                                    </div>
                                    <div class="card-content">
                                        <div class="card-title">{{item.liveInfo.topic}}</div>
                                        <div class="card-desc">{{item.liveInfo.description}}</div>
                                    </div>
                                    <div class="card-date">
                                        <div class="card-date-text"><i class="el-icon-date"></i>{{formatTime(item.liveInfo.start_ts)}}</div>
                                        <div class="card-date-text"><i class="el-icon-date"></i>{{formatTime(item.liveInfo.end_ts)}}</div>
                                    </div>
                                    <div class="card-info">
                                        <span class="textEllipsis">{{lang.moderator}}:{{item.liveInfo.creator_name}}</span>
                                    </div>
                                    <div class="card-tips">
                                        <p class="card-tips-status0" v-if="item.liveInfo.status===systemConfig.liveManagement.waiting&&!item.liveInfo.is_will_start">{{lang.waiting}}</p>
                                        <p class="card-tips-status1" v-if="item.liveInfo.status===systemConfig.liveManagement.waiting&&item.liveInfo.is_will_start">{{lang.begin_in_minute}}</p>
                                        <p class="card-tips-status2" v-if="item.liveInfo.status===systemConfig.liveManagement.starting">{{lang.live_broadcasting}}</p>
                                        <p class="card-tips-status3" v-if="item.liveInfo.status===systemConfig.liveManagement.end">{{lang.live_broadcast_end}}</p>
                                        <p class="card-tips-status3" v-if="item.liveInfo.status===systemConfig.liveManagement.cancel">{{lang.live_broadcast_cancel}}</p>
                                        <p class="card-tips-link" @click="quickGotoLive(item)" v-if="item.liveInfo.status!==systemConfig.liveManagement.cancel">{{lang.quick_entry}} >></p>
                                    </div>

                                </div>
                                <div v-else-if="item.msg_type==systemConfig.msg_type.HOMEWORK_DETAIL || item.msg_type==systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL" class="message_item_content homework_detail_msg" @contextmenu="callImageMenu($event,item,'chatComponent')" @click="clickCloudExam(item)">
                                    <div class="homework_detail" v-if="item.assignmentInfo && item.assignmentInfo.paperInfo || item.paperInfo">
                                        <div class="title">{{lang.cloud_exam}}</div>
                                        <div v-if="item.assignmentInfo && item.assignmentInfo.paperInfo" class="sub_title">{{item.assignmentInfo.paperInfo.title}}</div>
                                        <div v-else class="sub_title">{{item.paperInfo.title}}</div>
                                        <div class="content">
                                            <div v-if="item.assignmentInfo && item.assignmentInfo.paperInfo">{{lang.author}}：{{item.assignmentInfo.paperInfo.author}}</div>
                                            <div v-else>{{lang.author}}：{{item.paperInfo.author}}</div>
                                            <div v-if="item.assignmentInfo && item.assignmentInfo.paperInfo">{{lang.deadline}}：{{item.assignmentInfo.dueTime | showData}}</div>
                                        </div>
                                        <div class="content">
                                            <div v-if="item.assignmentInfo && item.assignmentInfo.paperInfo">{{lang.paper_total_score}}：{{item.assignmentInfo.paperInfo.score}}{{lang.point_tip}}</div>
                                            <div v-else>{{lang.paper_total_score}}：{{item.paperInfo.score}}{{lang.point_tip}}</div>
                                            <div v-if="item.assignmentInfo && item.assignmentInfo.paperInfo">{{lang.paper_question_count}}：{{item.assignmentInfo.paperInfo.questionCount}}</div>
                                            <div v-else>{{lang.paper_question_count}}：{{item.paperInfo.questionCount}}</div>
                                        </div>
                                    </div>
                                </div>
                                <!-- <div v-else-if="item.msg_type==systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL" class="message_item_content homework_detail_msg">
                                        <div class="title">{{lang.cloud_exam}}</div>
                                        <div class="sub_title">{{item.assignmentInfo.paperInfo.title}}</div>
                                </div> -->
                                <div v-else class="message_item_content">
                                    {{lang.unsupport_msg_type}}：{{item.msg_type}}
                                </div>
                                <div class="uploading_wrapper" v-if="item.downloading">
                                    <div class="upload_progress" :style="{height:100-item.percent+'%'}"></div>
                                </div>
                                <div class="iworks_protocol_wrap" v-if="item.protocol_name&&item.protocol_view_name&&(item.msg_type==systemConfig.msg_type.Frame||item.msg_type==systemConfig.msg_type.Cine||item.msg_type==systemConfig.msg_type.OBAI)">
                                    <p class="longwrap">iWorks：{{item.protocol_name}}</p>
                                    <p class="longwrap">{{lang.view_txt}}：{{item.protocol_view_name}}</p>
                                    <el-popover
                                        placement="right-start"
                                        popper-class="iworks_tree_popper"
                                        trigger="click">
                                        <el-tree
                                        :data="iworksProtocolTree"
                                        :default-expand-all="true"
                                        show-checkbox
                                        node-key="GUID"
                                        :ref="'protocol_tree_'+item.gmsg_id"
                                        ></el-tree>
                                        <p @click="showProtocol(item)" class="iworks_tip" slot="reference">{{lang.checkout_protocol}}</p>
                                    </el-popover>
                                </div>
                            </div>
                            <!-- 引用消息放在message_item_wrapper后面 -->
                            <div v-if="item.msg_type==systemConfig.msg_type.Text && item.quote_message"
                                 class="quote-message"
                                 :class="{'quote-right': item.sender_id==user.uid&&chatType!=CHAT_TYPE['CHAT_HISTORY'],
                                         'quote-left': item.sender_id!=user.uid||chatType==CHAT_TYPE['CHAT_HISTORY']}"
                                 @contextmenu="callQuoteMenu($event, item)">
                                <!-- 引用消息已删除 -->
                                <div v-if="item.quote_message.hasOwnProperty('been_withdrawn') && item.quote_message.been_withdrawn === 1"  class="quote-content-message">
                                    {{ lang.quoted_content_has_been_deleted }}
                                </div>
                                <!-- 引用消息已删除 -->
                                <div v-else-if="item.quote_message.hasOwnProperty('been_withdrawn') && item.quote_message.been_withdrawn === 2" class="quote-content-message">
                                    {{ lang.quoted_content_has_been_withdrawn }}
                                </div>
                                <template v-else>
                                    <div class="quote-sender">{{ item.quote_message.nickname }}</div>
                                    <!-- 图片类型引用 -->
                                    <div v-if="[systemConfig.msg_type.Image, systemConfig.msg_type.Frame, systemConfig.msg_type.OBAI].includes(item.quote_message.msg_type)" class="quote-content-message image-content" @click.stop="openQuotedGallery(item.quote_message)">
                                        <img v-if="item.quote_message.url" :src="item.quote_message.url">
                                        <span>{{ lang.msg_type_image }}</span>
                                    </div>
                                    <!-- 视频类型引用 -->
                                    <div v-else-if="[systemConfig.msg_type.Video, systemConfig.msg_type.Cine, systemConfig.msg_type.RealTimeVideoReview, systemConfig.msg_type.VIDEO_CLIP].includes(item.quote_message.msg_type)" class="quote-content-message video-content" @click.stop="openQuotedGallery(item.quote_message)">
                                        <div class="video-thumbnail">
                                            <img v-if="item.quote_message.coverUrl || item.quote_message.url" :src="item.quote_message.coverUrl || item.quote_message.url">
                                            <div class="video-play-icon">
                                                <i class="iconfont iconbofang svg_icon_play"></i>
                                            </div>
                                        </div>
                                        <span>{{ lang.msg_type_video }}</span>
                                    </div>
                                    <!-- 文本类型引用 -->
                                    <div v-else class="quote-content-message" v-html="item.quote_message.msg_body"></div>
                                </template>

                            </div>
                            <div v-if="item.sending"  v-loading="item.sending" class="sending_spinner" :key="item.gmsg_id||item.tmp_gmsg_id"></div>
                            <i v-if="item.sendFail" @click="resend(index)" class="send_fail icon iconfont iconwarning-o"></i>
                        </div>
                    </div>
                </DynamicScrollerItem>
            </template>
        </DynamicScroller>


        <ReviewEditDialog :message="currentReviewFile" ref="reviewEditDialog"></ReviewEditDialog>
        <ReviewDetail :message="currentReviewFile" ref="reviewDetail" v-model="isShowReviewDetail" :hasMoreDetail="hasMoreDetail"></ReviewDetail>
    </div>
</template>
<script>
import base from '../lib/base'
import iworksTool from "../lib/iworksTool";
import record from '../lib/record_sound.js'
import sendMessage  from '../lib/sendMessage'
import mentionDialog from './mentionDialog.vue'
import Tool from '@/common/tool.js'
import { cloneDeep } from 'lodash'
import { dealObstetricReport } from  '../lib/obstetricTool'
import {
    addRootToUrl,
    openVisitingCard,
    getFileIcon,
    noFileIcon,
    htmlEscape,
    setDialogWrapperPosition,
    getLiveRoomObj,
    getRecordSubject,
    getClipSubject,
    formatDurationTime,
    getLocalAvatar,
    imageStandardIcon,
    hasAiAnalyzeResult,
    formatAttendeeNicknameToMap,
    getResourceTempState,
    setIworksInfoToMsg,
    setExpirationResource,
    parseImageListToLocal,
    patientDesensitization,
    transferPatientInfo,
} from '../lib/common_base'
import moment from "moment";
import service from "../service/service";
import iworksInternational from '@/common/iworksInternational.js'
import SysJoinAttendeeMsg from './messageItem/sysJoinAttendeeMsg.vue'
import SysKickoutAttendeeMsg from './messageItem/sysKickoutAttendeeMsg.vue'
import SysStartRTVoiceMsg from './messageItem/sysStartRTVoiceMsg.vue'
import SysStopRTVoiceMsg from './messageItem/sysStopRTVoiceMsg.vue'
import SysStartRealTimeConsultationMsg from './messageItem/sysStartRealTimeConsultationMsg.vue'
import SysStopRealTimeConsultationMsg from './messageItem/sysStopRealTimeConsultationMsg.vue'
import ReviewEditDialog from "./messageItem/reviewEditDialog.vue"
import ReviewDetail from './messageItem/reviewDetail.vue'
import examMsg from '../components/examMsg.vue'
import reviewMsg from './messageItem/reviewMsg.vue'
import videoClipMsg from './messageItem/videoClipMsg.vue'
import {getOssOptions} from '@/common/oss/index'
import { CHAT_TYPE } from '../lib/constants.js';
export default {
    mixins: [base,record,sendMessage,iworksTool ],
    name: 'ChatMessageList',

    props:{
        chatMessageList:{
            type: Array,
            default:()=>{
                return []
            }
        },
        customClass:{
            type: Array,
            default:()=>{
                return ['scroller']
            }
        },
        chatType:{
            type:Number,
            default:CHAT_TYPE['CHAT_WINDOW']
        },
        isReload:{//正在发送消息
            type: Boolean,
            default: true
        },
        isLoading:{//正在发送消息
            type: Boolean,
            default: false
        },
        cid:{
            type:[String,Number],
            default:0
        }

    },
    components: {
        SysJoinAttendeeMsg,
        SysKickoutAttendeeMsg,
        SysStartRTVoiceMsg,
        SysStopRTVoiceMsg,
        SysStartRealTimeConsultationMsg,
        SysStopRealTimeConsultationMsg,
        ReviewEditDialog,
        ReviewDetail,
        examMsg,
        reviewMsg,
        videoClipMsg,
    },
    data(){
        return {
            CHAT_TYPE,
            getResourceTempState,
            formatDurationTime,
            hasAiAnalyzeResult,
            getRecordSubject,
            getClipSubject,
            imageStandardIcon,
            getLocalAvatar,
            isScrollOnMiddle:false,//组件的滚动是否在中间
            unreadNumber:0, //未读消息
            currentChoiceHistoryId:null,
            isShowReviewDetail:false,
            hasMoreDetail:false,//直播详情是否显示更多：主讲人，群主，管理员
            voiceSrc:'',
            soundingMsgId:null,
            currentReviewFile:{},//当前编辑的回放信息
            presentTime:new Date().getTime(),
            debounceGetHistory:Tool.debounce(this.getMoreHistory,500),
            isPreventClick:false,
            currentGalleryList:[],//当前画廊的列表
            iworksProtocolTree:[],
            oldChatMessageListLength:0,
            oldChatMessageLastId:0,
            is_scroll_bottom_by_reload:false,
            isNeedScrollToOldItem:false,
            loadTargetCountList:false,
            highlightGmsgId:null,
            isPositioningToMessage:false, // 标志是否正在定位到特定消息
        }
    },
    filters:{
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm")
        },
    },
    computed:{
        conversation(){
            this.changeConversation()
            return this.conversationList[this.cid]||{}
        },
        isObstetricQCMulticenter(){
            return this.conversation.multicenter_type == 'obstetric_qc_multicenter'
        },
        is_loaded_history_list() {
            if(this.chatType===CHAT_TYPE['CHAT_HISTORY']){
                return !this.isLoading
            }else{
                return this.conversation.is_loaded_history_list;
            }
        },
        controller(){
            return this.conversation.socket;
        },
        realtimeVoice(){
            return this.$store.state.realtimeVoice[this.cid]||{}
        },
        isCreator(){
            return (this.user.uid==this.conversation.creator_id)&&(this.conversation.is_single_chat==0)
        },
        isManager(){
            let result = false;
            let list=this.parseObjToArr(this.conversation.attendeeList);
            for(let item of list){
                if (item.role == this.systemConfig.groupRole.manager) {
                    if (item.userid === this.user.uid) {
                        result = true;
                        break;
                    }
                }
            }
            return result;
        },
        aiAnalyzeId(){
            let fid=0
            if (this.conversation.service_type==this.systemConfig.ServiceConfig.type.AiAnalyze) {
                fid=this.conversation.fid;
            }
            return fid;
        },
        iworks_protocol_list(){
            return this.gallery.iworks_protocol_list
        },
        currentScrollInfo(){
            return this.$refs.scroller.$refs.scroller.getScroll()
        },
        isAiAnalyze(){
            return this.conversation.service_type==this.systemConfig.ServiceConfig.type.AiAnalyze
        },
        isDrAiAnalyze(){
            return this.conversation.service_type==this.systemConfig.ServiceConfig.type.DrAiAnalyze
        },
        attendeeMap() {
            return formatAttendeeNicknameToMap(this.conversation.attendeeList);
        },
        isSupportOssPause(){
            const isInternalNetworkEnv = this.systemConfig.serverInfo.network_environment === 1
            return isInternalNetworkEnv?false:true
        },
        aiPresetData(){
            return this.$store.state.aiPresetData;
        },
    },
    watch:{
        cid:{
            handler(value){
                this.isScrollOnMiddle=false;
            }
        },
        chatMessageList:{
            handler(value,oldValue){
                console.log('子组件 chatMessageList prop 变化:', {
                    newLength: value ? value.length : 0,
                    oldLength: oldValue ? oldValue.length : 0,
                    isArray: Array.isArray(value),
                    timestamp: new Date().toISOString()
                });

                if(!Array.isArray(value)||value.length===0){
                    return
                }
                if (this.chatType!==CHAT_TYPE['CHAT_HISTORY']) {
                    this.clearUnread(this.cid)
                    this.$store.commit('chatList/clearMention',this.cid);
                    //点击查找的消息定位到该消息
                }else{
                    if(this.$root.isScrollingList[this.cid]){
                        this.isScrollOnMiddle = true
                    }
                }
                let sameLength = false
                if(this.oldChatMessageListLength&&this.oldChatMessageListLength === value.length){
                    sameLength = true

                }

                this.oldChatMessageListLength = value.length
                this.oldChatMessageLastId = value[value.length-1].id
                this.$nextTick(()=>{
                    if(this.loadTargetCountList){
                        setTimeout(()=>{
                            this.loadTargetCountList = false
                        },500)
                        return
                    }
                    if(this.$refs.scroller){

                        if(this.$root.endMessageIds[this.cid]){
                            this.currentChoiceHistoryId = this.$root.endMessageIds[this.cid]
                        }
                        if((this.$root.isScrollingList[this.cid]||this.isNeedScrollToOldItem)&&this.isScrollOnMiddle){
                            this.goToChoiceHistoryItem()
                        }else{
                            if(!sameLength){
                                if(!this.isNeedScrollToOldItem){
                                    this.judgeIfNeedScrollToBottom()
                                }

                            }

                        }
                        if(this.isNeedScrollToOldItem){
                            this.isNeedScrollToOldItem = false
                        }
                    }
                })

            },
            deep:true,
            immediate:true
        },
    },
    mounted(){
        let that = this
        if (this.chatType!==CHAT_TYPE['CHAT_HISTORY']) {
            this.timeinterval = setInterval(this.refreshTime,1000)
            this.$root.eventBus.$on('getMoreHistory',that.getMoreHistory);
            this.$root.eventBus.$on('reloadHistoryList',that.reloadHistoryList);

            // 为每个组件实例创建唯一的事件监听器，避免多个实例之间的冲突
            this.$root.eventBus.$on('positionToMessage',that.handlePositionToMessage);
            //绑定获取历史消息回调
            this.$root.eventBus.$on('historyLoaded',that.historyLoaded);
        }
    },
    beforeDestroy(){
        this.$refs.soundPlayer.removeEventListener('ended',this.resetSounding,false);
        this.$refs.soundPlayer.removeEventListener('pause',this.resetSounding,false);

        // 移除事件监听器，避免内存泄漏
        if (this.chatType!==CHAT_TYPE['CHAT_HISTORY']) {
            this.$root.eventBus.$off('positionToMessage', this.handlePositionToMessage);
            if (this.timeinterval) {
                clearInterval(this.timeinterval);
            }
            this.$root.eventBus.$off('historyLoaded', this.historyLoaded);
            this.$root.eventBus.$off('getMoreHistory', this.getMoreHistory);
            this.$root.eventBus.$off('reloadHistoryList', this.reloadHistoryList);
        }
    },
    methods:{
        //时间刷新
        refreshTime(){
            this.presentTime = new Date().getTime();
        },
        //滚动条获取
        handleScroll(data){
            let parentScroll =  this.$refs.scroller.$refs.scroller
            let scrollSate = parentScroll.getScroll()
            if(parentScroll.$el.scrollTop+parentScroll.$el.clientHeight<parentScroll.$el.scrollHeight){
                this.isScrollOnMiddle = true
                if(parentScroll.$el.scrollTop+parentScroll.$el.clientHeight+100>parentScroll.$el.scrollHeight){
                    this.isScrollOnMiddle = false
                }
                if(parentScroll.$el.scrollTop+parentScroll.$el.clientHeight+5>=parentScroll.$el.scrollHeight){
                    Tool.debounce(this.loadBottomHistory,500)({})
                }
                // 在定位到特定消息过程中，阻止触发历史记录加载
                if(scrollSate.start === 0 && !this.isPositioningToMessage){
                    this.debounceGetHistory({})
                }
            }else{
                this.isScrollOnMiddle = false
            }
            if(this.isScrollOnMiddle){
                this.isNeedScrollToOldItem = false
            }
        },
        //是否滚动底部
        shouldScrollBottom(delay=0){
            setTimeout(()=>{
                if (!this.isScrollOnMiddle) {
                    this.scrollToBottom();
                }
                this.$emit('changeIsSending',false)
            },delay)
        },
        //是否滚动底部
        judgeIfNeedScrollToBottom(){
            this.$root.isScrollingList[this.cid] = false
            if(this.is_scroll_bottom_by_reload){
                this.scrollToBottom();
                this.is_scroll_bottom_by_reload = false
            }else{
                this.shouldScrollBottom()
            }


        },
        //滚动底部
        scrollToBottom(){
            var scroller = this.$refs.scroller
            if(!scroller){
                return
            }
            setTimeout(()=>{
                scroller.scrollToBottom()
            },0)
        },
        // 等待消息元素渲染完成的方法
        waitForMessageElement(gmsg_id, callback, maxRetries = 40, currentRetry = 0) {
            const checkElement = () => {

                let messageElement = this.$refs[`message-${gmsg_id}`];
                // console.log 用于调试，可保留或移除

                // 兼容 $refs 可能为数组的情况
                if (Array.isArray(messageElement)) {
                    messageElement = messageElement[messageElement.length - 1];
                }
                /*
                 * 当 vue-virtual-scroller 克隆隐藏节点时，document.getElementById 可能取到隐藏副本，
                 * 导致给该元素添加 highlight class 不可见。
                 * 这里通过 querySelectorAll 查询所有同 id 的节点，再筛选『可见』节点。
                 */
                const isVisible = (el) => {
                    if (!el) {
                        return false;
                    }
                    const style = window.getComputedStyle(el);
                    return (
                        style.display !== 'none' &&
                        style.visibility !== 'hidden' &&
                        (el.offsetParent !== null || (el.offsetWidth > 0 && el.offsetHeight > 0))
                    );
                };

                // 如果 $refs 未命中或不可见，再从所有 id 相同的节点里选取可见节点
                if (!isVisible(messageElement)) {
                    const nodeList = document.querySelectorAll(`#message-${gmsg_id}`);
                    for (let i = 0; i < nodeList.length; i++) {
                        if (isVisible(nodeList[i])) {
                            messageElement = nodeList[i];
                            break;
                        }
                    }
                }

                if (isVisible(messageElement)) {
                    callback(messageElement);
                    return;
                }

                // 如果达到最大重试次数，执行回调并传入null
                if (currentRetry >= maxRetries) {
                    console.warn(`waitForMessageElement: 达到最大重试次数 (${maxRetries})，未找到元素 #message-${gmsg_id}`);
                    callback(null);
                    return;
                }

                // 使用 $nextTick 确保Vue的DOM更新完成
                this.$nextTick(() => {
                    // 使用 requestAnimationFrame 确保浏览器渲染完成
                    requestAnimationFrame(() => {
                        setTimeout(() => {
                            this.waitForMessageElement(gmsg_id, callback, maxRetries, currentRetry + 1);
                        }, 50); // 每次重试间隔50ms
                    });
                });
            };

            checkElement();
        },
        handlePositionToMessage(eventData) {
            this.doPositionToMessage(eventData);
        },
        async doPositionToMessage({
            gmsg_id,
            group_id,
            currentMessage
        }){
            this.isPositioningToMessage = true;

            // 先检查消息是否在当前列表中
            let index = null
            this.chatMessageList.forEach((element,eIndex) => {
                if(element.gmsg_id === gmsg_id){
                    this.currentChoiceHistoryId = element.gmsg_id
                    index = eIndex
                }
            });

            const positionToGmsgId = () => {
                this.goToChoiceHistoryItem()
                // 使用 MutationObserver 等待DOM元素真正渲染完成
                this.waitForMessageElement(gmsg_id, (messageElement) => {
                    console.error(messageElement,'messageElement')
                    if (messageElement) {
                        this.setHighlight(gmsg_id)
                    } else {
                        // 如果消息在列表中但DOM元素找不到，尝试强制滚动到该位置
                        if (index !== null) {
                            console.warn('消息在列表中但DOM元素未找到，尝试强制滚动定位');
                            this.forceScrollToMessageIndex(index, gmsg_id);
                        } else {
                            this.$message.warning(this.lang.unable_locate_original_message);
                        }
                    }
                    // 定位完成后清除标志
                    this.isPositioningToMessage = false;
                });
            }

            if(index !== null){
                // 消息已在当前列表中，直接定位
                positionToGmsgId()
            }else{
                // 消息不在当前列表中，需要加载历史消息
                window.main_screen.conversation_list[this.cid].getCountBetweenMsgId({
                    start:gmsg_id,
                    end:currentMessage.gmsg_id
                },async (res)=>{
                    if(res.error_code === 0 ){
                        const count = res.data[0].count
                        if(typeof count === 'number'){
                            let distance = 0;
                            for(let i = 0; i < this.chatMessageList.length; i++) {
                                if(this.chatMessageList[i].gmsg_id === currentMessage.gmsg_id) {
                                    distance = i;
                                    break;
                                }
                            }
                            console.log(count,distance)
                            if(count > distance){
                                if(count - distance > 100){
                                    this.$message.warning(this.lang.data_too_old_to_locate);
                                    // 清除定位标志
                                    this.isPositioningToMessage = false;
                                    return
                                }
                                // 如果消息数量大于当前距离，说明需要加载更多历史消息
                                try {
                                    await this.getTargetCountList({
                                        count:count-distance + 10
                                    });
                                    this.$nextTick(() => {
                                        this.loadTargetCountList = false
                                        // 重新查找消息索引
                                        let newIndex = null
                                        this.chatMessageList.forEach((element,eIndex) => {
                                            if(element.gmsg_id === gmsg_id){
                                                this.currentChoiceHistoryId = element.gmsg_id
                                                newIndex = eIndex
                                            }
                                        });
                                        if(newIndex !== null){
                                            index = newIndex; // 更新外层 index 变量
                                            positionToGmsgId()
                                        }else{
                                            this.$message.warning(this.lang.unable_locate_original_message);
                                            // 清除定位标志
                                            this.isPositioningToMessage = false;
                                        }
                                    })
                                } catch (error) {
                                    console.error('加载历史消息失败:', error);
                                    this.$message.warning(this.lang.unable_locate_original_message);
                                    this.isPositioningToMessage = false;
                                }
                            }else{
                                // count小，说明消息可能被删除或撤回了
                                this.$message.warning(this.lang.unable_locate_original_message);
                                // 清除定位标志
                                this.isPositioningToMessage = false;
                            }
                        }else{
                            // 清除定位标志
                            this.isPositioningToMessage = false;
                        }
                    }else{
                        // 清除定位标志
                        this.isPositioningToMessage = false;
                    }
                })
            }
        },
        //滚动到指定位置
        goToChoiceHistoryItem(){

            this.$nextTick(()=>{
                let index= null
                if(this.currentChoiceHistoryId){
                    this.chatMessageList.forEach((element,eIndex) => {
                        if(element.gmsg_id === this.currentChoiceHistoryId){
                            this.currentChoiceHistoryId = element.gmsg_id
                            index =eIndex
                        }
                    });
                }else if(this.currentChoiceHistoryId==0){
                    index = 0
                }else{
                    this.chatMessageList.forEach((element,eIndex) => {
                        if(element.gmsg_id === this.$root.endMessageIds[this.cid]){
                            this.currentChoiceHistoryId = element.gmsg_id
                            index =eIndex
                        }
                    });
                }
                console.log(this.currentChoiceHistoryId,index,'this.currentChoiceHistoryId,index')
                if(this.currentChoiceHistoryId!==null&&index!==null){
                    const scroller = this.$refs.scroller
                    scroller.scrollToItem(index)
                    console.log(scroller,'scroller')
                    console.log(index,'index')
                    setTimeout(()=>{
                        scroller.scrollToItem(index)
                        // this.$root.loadingAndRenderHistory[this.cid] = false
                        this.currentChoiceHistoryId = null
                        delete this.$root.endMessageIds[this.cid]
                        this.$root.isScrollingList[this.cid] = false
                        this.isNeedScrollToOldItem = false
                    },100)

                }else{
                    this.$root.isScrollingList[this.cid] = false
                }

            })
        },
        //未读消息
        toUnreadMsg(){
            let chatMessageList =this.conversation.chatMessageList
            let index=chatMessageList.length-this.unreadNumber
            let msg=chatMessageList[index]
            let item = this.$refs['message_item_'+(msg&&msg.gmsg_id)]
            console.log(item,'item')
            if (item) {
                this.isScrollOnMiddle=true;
                item[0].scrollIntoView(true);
                this.unreadNumber=0;
            }
        },
        //显示未读消息
        showUnreadNumberBtn(){
            for(let chat of this.$store.state.chatList.list){
                if (chat.cid==this.cid) {
                    if (chat.unread>10) {
                        this.unreadNumber=chat.unread;
                        this.unreadCid=this.cid;
                    }
                    break;
                }
            }
            if (this.unreadCid!=this.cid) {
                //切换了会话后置空未读消息按钮
                this.unreadNumber=0;
                this.unreadCid=this.cid
            }
        },
        //清除未读消息
        clearUnread(cid){
            if(!window.main_screen || !window.main_screen.conversation_list ||!window.main_screen.conversation_list[this.cid]){
                return
            }
            const say_ack_count = this.$store.state.chatList.unreadMap[cid]
            if(say_ack_count>0){
                this.$store.commit('chatList/clearUnread',this.cid);
                window.main_screen.conversation_list[this.cid].sayAckAllMessage({},(res)=>{
                    if(res.error_code===0){

                    }
                })
            }
        },
        //会话更改
        changeConversation(){
            this.$nextTick(()=>{

                if (this.chatType!==CHAT_TYPE['CHAT_HISTORY']) {
                    if(this.$root.isScrollingList){
                        Object.keys(this.$root.isScrollingList).map(item=>{
                            if(item!==this.cid){
                                this.$root.isScrollingList[item] = null
                            }
                        })
                    }
                    this.showUnreadNumberBtn()
                    this.resetSounding()
                }
            })
        },
        //系统消息
        isSystemTip(msg){
            switch(msg.msg_type){
            case this.systemConfig.msg_type.SYS_START_RT_VOICE:
            case this.systemConfig.msg_type.SYS_STOP_RT_VOICE:
            case this.systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION:
            case this.systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION:
            case this.systemConfig.msg_type.SYS_START_REALTIME_CONFERENCE:
            case this.systemConfig.msg_type.SYS_STOP_REALTIME_CONFERENCE:
            case this.systemConfig.msg_type.SYS_JOIN_ATTENDEE:
            case this.systemConfig.msg_type.SYS_KICKOUT_ATTENDEE:
            case this.systemConfig.msg_type.SYS_CONFERENCE_PLAN:
            case this.systemConfig.msg_type.AI_ANALYZE:
                return true;
            default:
                return false
            }
        },
        //消息发送时间格式
        getSendTimeTip(index,type=1){
            let list=this.chatMessageList;
            if (!list[index] || !list[index].send_ts) {
                //发送的消息没有send_ts暂时不显示
                return '';
            }
            let currentTime=new Date(list[index].send_ts)
            if (index==0) {
                return Tool.getShowTime(list[index],type)
            }else{
                if(list[index-1] && list[index-1].send_ts){
                    let lastTime=new Date(list[index-1].send_ts);
                    if (currentTime-lastTime>3*60*1000) {
                        return Tool.getShowTime(list[index],type)
                    }else{
                        return ''
                    }
                }else{
                    return ''
                }
            }
        },
        openLiveDetailDialog(message){
            //直播中不打开详情
            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.lang.playing_video_tip)
                return
            }
            // if(this.$route.name=='gallery'){
            //     return
            // }
            this.$set(this,'currentReviewFile',message)
            this.$nextTick(()=>{
                let isHost = message.sender_id == this.user.uid;
                let isCreator = this.isCreator;
                let isManager = this.isManager;
                this.hasMoreDetail = isHost || isCreator || isManager||this.conversation.service_type === this.systemConfig.user_service_type.LiveBroadcast;
                this.isShowReviewDetail = true
            })
        },
        //语音
        playSound(gmsg_id){
            let player_node=this.$refs.soundPlayer
            // let player_node=document.getElementById('soundPlayer')
            player_node.volume = 1;
            if (this.soundingMsgId==gmsg_id) {
                //点击正在播放的语音
                player_node.pause()
                this.resetSounding()
            }else{
                try{
                    this.soundingMsgId=gmsg_id;
                    let message=this.chatMessageList.find(item=>item.gmsg_id === gmsg_id)
                    this.voiceSrc=addRootToUrl(message.url)
                    player_node.onerror = ()=>{
                        this.resetSounding()
                    }
                    player_node.load()
                    player_node.removeEventListener('ended',this.resetSounding,false)
                    player_node.addEventListener('ended',this.resetSounding,false)
                    player_node.removeEventListener('pause',this.resetSounding,false)
                    player_node.addEventListener('pause',this.resetSounding,false)
                    // player_node.play()
                }catch(e){
                    console.log(e)
                }
            }
        },
        resetSounding(){
            this.soundingMsgId=null;
            this.voiceSrc=''
        },
        openJoinVerify(){
            this.$router.push(this.$route.fullPath+'/edit_group_setting/group_manage/join_verify')
        },
        openEditReviewModal(file){
            // this.currentReviewFile = file
            this.$set(this,'currentReviewFile',file)
            service.checkActionPermissions({
                action:'conference.update.recording',
                businessData:{
                    resource_id:this.currentReviewFile.resource_id,
                    group_id:this.cid
                }
            }).then((res)=>{
                if(res.data.error_code===0){
                    if(!res.data.data.hasPermission){
                        this.$message.error(this.lang.no_permission_operate)
                        return
                    }
                    this.$nextTick(()=>{
                        this.$refs[`reviewEditDialog`].openEditTargetModal()
                    })
                }else{
                    this.$message.error(this.lang[res.data.key]);
                }

            }).catch((error)=>{
                console.error(error)
            })
        },
        isShowReEdit(){
            return (item) => {
                if(this.presentTime - this.$root.withDrawTimeList[item.gmsg_id] <= 180000){
                    return true;
                }
                return false;
            }
        },
        loadBottomHistory(){
            if(this.chatType==CHAT_TYPE['CHAT_HISTORY']){
                let start = this.chatMessageList&&this.chatMessageList.length? this.chatMessageList[0].gmsg_id : 0;
                this.currentChoiceHistoryId = start

                this.$emit("loadBottomHistory");
                this.$root.isScrollingList[this.cid]=true;

                // console.error('this.$root.isScrollingList[this.cid]',this.$root.isScrollingList[this.cid])
            }
        },
        //获取更多历史记录
        async getMoreHistory({
            cid=this.cid,
            count=this.systemConfig.historyMessageNum,
            start=null,
        }){ //sort 为0则按照当前位置开始滚动 为1则按照搜索的历史记录滚动
            let chatMessageList =this.conversationList[cid].chatMessageList
            if(this.chatType==CHAT_TYPE['CHAT_HISTORY']){
                let start=chatMessageList[0]?chatMessageList[0].gmsg_id:0;
                this.currentChoiceHistoryId = this.$root.isScrollingList[cid]? this.$root.isScrollingList[cid] : start
                this.$emit("loadTopHistory");
                this.$root.isScrollingList[cid]=true;
            }else{
                if (!this.$root.isScrollingList[cid]) {
                    //历史聊天记录
                    // let start=chatMessageList[0]?chatMessageList[0].gmsg_id:0;
                    // console.log(start,'start')
                    // let params={
                    //     start:start,
                    //     count:this.systemConfig.historyMessageNum
                    // }
                    // this.conversationList[cid].socket.emit("get_history_chat_messages",params);
                    // this.isNeedScrollToOldItem = true
                    // this.$root.isScrollingList[cid]=true;
                    let msgStart;
                    if(start !== null){
                        msgStart = start;
                    }else{
                        if (this.chatMessageList[0]) {
                            msgStart = this.chatMessageList[0].gmsg_id;
                        } else {
                            msgStart = 0;
                        }
                    }

                    this.currentChoiceHistoryId = msgStart
                    let params = {
                        start: msgStart,
                        count,
                        mode: "older",
                    };
                    const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
                    this.$root.isScrollingList[cid]=true;
                    this.isNeedScrollToOldItem = true
                    if(start === 0){
                        this.isNeedScrollToOldItem = false
                    }
                    let data = res.messages || [];
                    this.handleChatMessage(data);

                    this.historyLoaded(data);
                    this.$store.commit("conversationList/updateMessageListIsLoaded", {
                        cid: this.cid,
                        is_loaded_history_list: true,
                    });


                }
            }
        },
        async getTargetCountList({
            cid=this.cid,
            count = 0,
        }){
            let start;

            if (this.chatMessageList[0]) {
                start = this.chatMessageList[0].gmsg_id;
            } else {
                start = 0;
            }
            let params = {
                start: start,
                count,
                mode: "older",
            };
            const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
            this.loadTargetCountList = true
            let data = res.messages || [];
            this.handleChatMessage(data);
            this.historyLoaded(data);
        },
        async reloadHistoryList(cid){
            // if(!this.conversationList[cid].is_need_reload){
            //     return
            // }
            // let unSendMsgList = this.chatMessageList.filter(item=>!item.gmsg_id)
            // this.$store.commit("conversationList/updateMessageList", {cid:this.cid,list:unSendMsgList});
            // let start=0;
            // let params={
            //     start:start,
            //     count:this.systemConfig.historyMessageNum
            // }
            // this.conversationList[cid].socket.emit("get_history_chat_messages",params);
            // this.$root.isScrollingList[cid]=false;
            // this.$store.commit('conversationList/updateMessageListNeedReload',{is_need_reload:false,cid})
            // this.is_scroll_bottom_by_reload= true
            if (!this.conversationList[this.cid].is_need_reload) {
                return;
            }
            await Tool.handleAfterConversationCreated(this.cid);
            let start = 0;
            let params = {
                start: start,
                count: this.systemConfig.historyMessageNum,
                mode: "older",
            };
            const res = await this.conversation.socket.request("get_history_chat_messages_by_search", params);
            this.$store.commit("conversationList/updateMessageListNeedReload", {
                is_need_reload: false,
                cid: this.cid,
            });
            let data = res.messages || [];
            let unSendMsgList = this.chatMessageList.filter((item) => !item.gmsg_id);
            this.$store.commit("conversationList/updateMessageList", { cid: this.cid, list: unSendMsgList });
            this.handleChatMessage(data);
            this.is_scroll_bottom_by_reload= true
        },
        //打开画廊
        openAiAnalyzeGallery(event,msg){
            if (msg.ai_analyze.report.error == undefined) {
                return;
            }
            let imagesList = msg.ai_analyze.messages.reduce((h,v)=>{
                if(v.msg_type != this.systemConfig.msg_type.EXPIRATION_RES && this.getResourceTempState(v.resource_id) === 1){
                    h.push(v)
                }
                return h;
            },[]);

            for (let image of imagesList) {
                if (msg.ai_analyze.report.mark_list) {
                    image.mark_list = msg.ai_analyze.report.mark_list[image.resource_id] || [];
                } else {
                    image.mark_list = [];
                }
                image.summary = msg.ai_analyze.report.summary;
            }
            if(this.isAllAiMessageDelete(msg)||imagesList.length<1){
                return;
            }
            this.$store.commit("gallery/setGallery", {
                list: imagesList,
                openFile:imagesList[0],
                loadMore:false,
                loadMoreCallback:null
            });
            this.$nextTick(()=>{
                if(this.chatType === CHAT_TYPE['CHAT_WINDOW']){
                    this.$router.push(this.$route.fullPath+'/gallery')
                }
            })
        },
        //群内好友
        openVisitingCard(messages,type){
            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.lang.playing_video_tip)
                return
            }
            openVisitingCard(messages,type)
        },
        clickHistoryAvatar(msg){
            if (this.isPreventClick) {
                this.isPreventClick=false
            }else{
                this.openVisitingCard(msg,1);
            }
        },
        pressAvatar(msg){
            this.isPreventClick=true;
            if (this.conversation.is_single_chat||msg.sender_id==this.user.uid) {
                return ;
            }
            this.$parent.$refs.edit_content.focus()
            this.$parent.insertHtmlAtCursor('@'+msg.nickname+'&nbsp;')
        },
        clickTextMsg(event){
            if (event.target.classList.contains('openLinkByDefaultBrowser')) {
                // 处理点击事件
                let url = event.target.dataset.url;
                Tool.openLinkByDefaultBrowser(url)

            }
        },
        async clickGallery(event,msg,callback){
            console.log(this.attendeeMap)
            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.lang.playing_video_tip)
                return
            }
            if (msg.ai_analyze&&msg.ai_analyze.report.error==undefined) {
                // return ;
            }
            if (!msg.gmsg_id) {
                //未发送完的图片消息不进入画廊
                return
            }
            if (msg.msg_type==this.systemConfig.msg_type.AI_ANALYZE) {
                msg=msg.ai_analyze.messages[0]
            }
            const list = this.getChatMessageResourceList()
            if(list.length === 0){
                this.$message.info(this.lang.resource_being_generated)
                return
            }else if(list.length<10&&this.currentGalleryList.length<10){
                const galleryList = this.conversation.galleryObj.gallery_list
                if(galleryList&&galleryList.length>0){
                    this.currentGalleryList = this.deduplicateArrayByResourceId(list.concat(galleryList))
                }else{
                    const moreResourceList = await this.getMoreResourceList(list[list.length-1].resource_id)
                    this.currentGalleryList = this.deduplicateArrayByResourceId(list.concat(moreResourceList))
                }
            }else{
                this.currentGalleryList = this.deduplicateArrayByResourceId(list.concat(this.currentGalleryList))
            }
            this.$store.commit('gallery/setGallery',{
                list:this.currentGalleryList,
                openFile:msg,
                loadMore:true,
                loadMoreCallback:async ()=>{
                    const galleryList = this.conversation.galleryObj.gallery_list
                    const moreResourceList = await this.getMoreResourceList(galleryList[galleryList.length-1].resource_id)
                    this.currentGalleryList = this.deduplicateArrayByResourceId(galleryList.concat(moreResourceList))
                    let loadMore = false

                    if(moreResourceList.length>=this.systemConfig.consultationImageShowNum){
                        loadMore = true
                    }
                    this.$store.commit('gallery/setGallery',{
                        list:this.currentGalleryList,
                        loadMore,
                    })
                },
                listenerGalleryNewDataTag:'conversation'
            })
            if(this.chatType==CHAT_TYPE['CHAT_WINDOW']){
                this.$router.push(this.$route.fullPath+'/gallery')
            }

        },
        getChatMessageResourceList(){
            let list = []
            let resource_list = []
            const msg_type = this.systemConfig.msg_type
            const unLoadResourceTypeArr = [
                msg_type.Sound,
                msg_type.WITHDRAW,
                msg_type.EXAM_IMAGES,
                msg_type.File,
                msg_type.AI_ANALYZE,
                msg_type.IWORKS_PROTOCOL
            ];
            for (var i = this.chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历
                let message=this.chatMessageList[i]
                if(unLoadResourceTypeArr.includes(message.msg_type)){
                    continue
                }
                if(message.resource_id&&!resource_list.includes(message.resource_id)){
                    list.push(message)
                    resource_list.push(message.resource_id)

                }

                if(message.ai_analyze&&
                message.ai_analyze.messages&&
                message.ai_analyze.messages[0]&&
                message.ai_analyze.messages[0].resource_id&&
                !resource_list.includes(message.ai_analyze.messages[0].resource_id)){
                    list.push(message.ai_analyze.messages[0])
                    resource_list.push(message.ai_analyze.messages[0].resource_id)
                }

            }
            console.log(resource_list,JSON.parse(JSON.stringify(list)),this.chatMessageList)
            return list
        },
        deduplicateArrayByResourceId(array) {
            const seen = new Set();
            const deduplicatedArray = [];

            for (let i = 0; i < array.length; i++) {
                const item = array[i];
                const resourceId = item.resource_id;

                if (!seen.has(resourceId)) {
                    seen.add(resourceId);
                    deduplicatedArray.push(item);
                }
            }

            return deduplicatedArray;
        },
        getMoreResourceList(resource_id){
            const msg_type = this.systemConfig.msg_type
            const unLoadResourceTypeArr = [
                msg_type.Sound,
                msg_type.WITHDRAW,
                msg_type.EXAM_IMAGES,
                msg_type.File,
                msg_type.AI_ANALYZE,
                msg_type.IWORKS_PROTOCOL
            ];
            const data = []
            return new Promise((resolve,reject)=>{
                window.main_screen.conversation_list[this.cid].getResourceList(
                    {
                        limit:this.systemConfig.consultationImageShowNum,
                        type:'all',
                        lastResourceID: resource_id,
                    },
                    (res) => {
                        if (res.error_code === 0) {
                            res.data.forEach(item=>{
                                if(!unLoadResourceTypeArr.includes(item.msg_type)){
                                    data.push(item)
                                }
                            })
                            resolve(data)
                        } else {
                            resolve([])
                            this.$message.error("get_gallery_messages error");
                        }
                    }
                );

            })
        },
        getCommentNum(commentList=[]){
            let num = 0
            commentList.forEach((element)=>{
                if(element.status!==0){
                    if(!element.is_private || (element.is_private&&(this.user.uid === element.author_id))){
                        num++
                    }
                }
            })
            return num
        },
        getFileIcon(file_name){
            return getFileIcon(file_name)
        },
        noFileIcon(e){
            return noFileIcon(e)
        },
        view_comment(msg){
            this.clickGallery({},msg,()=>{
                this.$root.eventBus.$emit('positionToComment',msg);
            })
        },
        openIworksGallery(msg){
            const image={
                file_id:new Date().valueOf(),
                group_id:msg.group_id,
                msg_type:1,
                url:msg.ai_result.imgUrl,
                url_local:msg.ai_result.imgUrl,
                realUrl:msg.ai_result.imgUrl,
                thumb:'',
                loaded:true,
            }
            this.$store.commit('gallery/setGallery',{
                list:[image],
                openFile:image,
                loadMore:false,
                loadMoreCallback:null

            });
            this.$nextTick(()=>{
                if(this.chatType==CHAT_TYPE['CHAT_WINDOW']){
                    this.$router.push(this.$route.fullPath+'/gallery')
                }
            })
        },
        cancelUpload(e,index){
            e.stopPropagation()
            let message=this.chatMessageList[index]
            this.$store.commit('conversationList/deleteChatMessage',{
                cid:this.cid,
                index:index
            })
            if (message.is_istation) {
                window.CWorkstationCommunicationMng.cancelTransfer({
                    ImgId:message.img_id
                })
                this.deleteImgInfoByImgId(message.img_id)
            }else{
                message.cancelUpload()
                this.removeFileFromUploadList(message.file_id);
            }
        },
        async showProtocol(msg){
            let protocolInfo=this.gallery.iworks_protocol_list[msg.protocol_execution_guid]
            if (!protocolInfo) {
                const result = await service.getIworksDetail({guid:msg.protocol_execution_guid})
                this.setIworksProtocol(result.data.data)
                protocolInfo = result.data.data;
            }
            this.iworksProtocolTree=protocolInfo.protocolTree
            let component=this.$refs['protocol_tree_'+msg.gmsg_id]
            component.setCheckedKeys([msg.protocol_view_guid])

        },
        historyLoaded(data){
            if (this.$root.isScrollingList[this.cid]&&data.cid==this.cid) {
                if (data.list.length==0) {
                    if(this.chatMessageList&&this.chatMessageList.length!=0){
                        this.$message.info(this.lang.loaded_all_message);
                    }
                    this.$root.isScrollingList[this.cid] = false
                    // this.$root.isScrollingList[this.cid] = false
                    // this.$root.loadingAndRenderHistory[this.cid] = false
                }else{
                    // setTimeout(()=>{
                    //     console.log(this.lastScrollItem)
                    //     if(this.lastScrollItem){
                    //         this.lastScrollItem[0].scrollIntoView(true)
                    //     }
                    // },200)
                    // this.goToChoiceHistoryItem()
                }
                // this.goToChoiceHistoryItem()
            }
        },
        reEditMessage(detail){
            // 清空当前输入框内容，然后设置为要重新编辑的内容
            if (this.$parent && this.$parent.textContent !== undefined) {
                // 直接设置父组件的 textContent 数据属性，这会通过 v-model 自动更新输入框
                this.$parent.textContent = detail.msg_body;

                // 聚焦到输入框并将光标移到末尾
                this.$nextTick(() => {
                    if (this.$parent.$refs.edit_content) {
                        this.$parent.$refs.edit_content.focus();
                        // 将光标移动到文本末尾
                        const textLength = detail.msg_body.length;
                        this.$parent.$refs.edit_content.$refs.textarea.selectionStart = textLength;
                        this.$parent.$refs.edit_content.$refs.textarea.selectionEnd = textLength;
                        // 更新父组件的光标位置记录
                        this.$parent.lastCursorPosition = textLength;
                    }
                });
            }
            window.vm.$root.withDrawTimeList[detail.gmsg_id] = new Date().getTime();
        },
        quickGotoLive(item){
            if(this.$root.currentLiveCid){ //正在直播中 不允许使用快速入口
                this.$message.error(this.lang.live_not_allow_operation)
                return
            }
            window.main_screen.getLiveInfoById({live_id:item.liveInfo.live_id},(liveRes)=>{
                if((liveRes.data.status === this.systemConfig.liveManagement.cancel) && (this.user.uid!==liveRes.data.creator_id)){
                    this.$message.error(this.lang.live_invite_status3.replace('{1}',''))
                    return
                }
                this.$router.push(`/main/education/live_training?live_info=${JSON.stringify(item.liveInfo)}&action=attendee`)
            })

        },
        handleUploadStatus(message){
            this.$root.eventBus.$emit('handleUploadStatus',message)
        },
        handleCancelUpload(message){
            this.$root.eventBus.$emit('handleCancelUpload',message)
        },
        formatSize(size){
            return Tool.formatSize(size)
        },
        vieReject(){
            this.$root.eventBus.$emit("viewReject")
        },
        clickCloudExam(msg){
            if(this.$root.currentLiveCid){ //正在直播中 不允许跳转作业
                return
            }
            if(msg.msg_type==this.systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL){ // 分享的作业 以查看模式打开
                this.$router.push({
                    path: `${this.$route.path}/cloud_exam/exam/1/${msg.paperInfo._id}`,
                    query: this.$route.query,
                });
            }else{
                service.getanswerSheetByAssignmentID({
                    assignmentID:msg.assignmentInfo._id,
                }).then(res=>{
                    if(res.data.error_code===0){
                        const answerSheet = res.data.data.answerSheet;
                        const assignmentInfo = res.data.data.assignmentInfo;

                        if (answerSheet) {
                        // 存在数据，是考生
                            if (answerSheet.status === 3 || answerSheet.status === 4) {
                                if (answerSheet.assignmentInfo.resultCanChecked) {
                                // 已批改能查看详情
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/exam/4/${answerSheet._id}`,
                                        query: this.$route.query,
                                    });
                                }else{
                                    this.$message.error(this.lang.homework_cancheck_tip);//该作业设置不允许查看详情，无法查看。
                                }
                            }else if (answerSheet.status === 2) {
                                this.$message.error(this.lang.homework_correcting_tip);//作业正在批改中，无法查看
                            }else if (answerSheet.status === 1 || answerSheet.status === 0) {
                                if (answerSheet.dueTime > Date.now()) {
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/exam/2/${answerSheet._id}`,
                                        query: this.$route.query,
                                    });
                                }else{
                                    this.$message.error(this.lang.homework_overdue_tip);//作业已超过截止时间，无法查看
                                }
                            }else{
                                this.$message.error(this.lang.userNoAuth);
                            }
                        } else{
                        // 不存在数据，判断是否老师或布置者
                            const teachers = msg.assignmentInfo.teachers;
                            const uid = msg.assignmentInfo.uid;
                            if (teachers.indexOf(this.user.id) > -1 || uid === this.user.id) {
                                // 直接使用getanswerSheetByAssignmentID返回的assignmentInfo
                                this.$store.commit('homework/setCurrentPaper', assignmentInfo);
                                if (teachers.indexOf(this.user.id)> -1) {
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/correcting_exam/2`,
                                        query: this.$route.query,
                                    });
                                } else{
                                    this.$router.push({
                                        path: `${this.$route.path}/cloud_exam/correcting_exam/1`,
                                        query: this.$route.query,
                                    });
                                }
                            } else {
                                this.$message.error(this.lang.userNoAuth);
                            }
                        }
                    }
                })
            }
        },
        isAllAiMessageDelete(msg){
            if(msg.ai_analyze && msg.ai_analyze.messages){
                let delete_resources = msg.ai_analyze.messages.reduce((h,v)=>{
                    if(v.resource_deleted || v.msg_type == this.systemConfig.msg_type.EXPIRATION_RES || !(this.getResourceTempState(v.resource_id) === 1)){
                        h.push(v)
                    }
                    return h
                },[])
                return msg.ai_analyze.messages.length == delete_resources.length
            }else{
                return false
            }
        },
        openQuotedGallery(message) {
            if(!message || !message.resource_id) {
                return;
            }

            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.lang.playing_video_tip);
                return;
            }
            // 准备打开画廊
            this.$store.commit('gallery/setGallery',{
                list:[message],
                openFile:message,
                loadMore:false,
                loadMoreCallback:null
            });

            this.$nextTick(() => {
                if(this.chatType==CHAT_TYPE['CHAT_WINDOW']){
                    this.$router.push(this.$route.fullPath+'/gallery')
                }
            });
        },
        callQuoteMenu(event, message) {
            if (message.quote_message.hasOwnProperty('been_withdrawn')) {
                if(message.quote_message.been_withdrawn === 2 || message.quote_message.been_withdrawn === 1){
                    return;
                }

            }
            event.preventDefault();
            this.$root.eventBus.$emit('showQuoteMenu', {
                event: event,
                quote_message: message.quote_message,
                currentMessage: message
            });
        },
        handleChatMessage(oData) {
            const data = cloneDeep(oData);
            patientDesensitization(data)
            parseImageListToLocal(data,'url')
            setExpirationResource(data,this.cid)
            let last_send_ts=data[0]&&data[0].send_ts;
            for(let message of data){
                message.original_msg_body = message.msg_body
                message.msg_body=this.parseMessageBody(message.msg_body)
                message.patientInfo=transferPatientInfo(message);
                message.sending=false;
                message.downloading=false;
                message.sendFail=message.sendFail||false;
                if (message.msg_type==this.systemConfig.msg_type.AI_ANALYZE) {
                    parseImageListToLocal(message.ai_analyze&&message.ai_analyze.messages,'url')
                }
                if (message.protocol_guid) {
                    //消息存在iworks信息
                    setIworksInfoToMsg(message);
                }
                // last_send_ts=message.send_ts;
            }
            var type = 'prepend';
            if(scroll && scroll.type == "bottom"){

                data.unshift({
                    msg_type:this.systemConfig.msg_type.HISTORY_TIP,
                    sender_id:this.user.uid,
                    send_ts:last_send_ts,
                })
                type = 'splice';
            }
            let obj={
                list:data,
                cid:this.cid,
                type:type,
            }
            this.$store.commit('conversationList/updateMessageListIsLoaded',{cid:this.cid,is_loaded_history_list:true})
            if (0 < data.length && scroll && "bottom" == scroll.type
                    && this.$store.state.conversationList
                    && this.$store.state.conversationList[this.cid]
                    && this.$store.state.conversationList[this.cid].start_type
                    && this.systemConfig.start_type.NewChatMessage == this.$store.state.conversationList[this.cid].start_type.type) {
                this.$store.commit('chatList/addMessageNoSort',data[1]);
            }
            this.$store.commit('conversationList/setChatMessage',obj)
        },
        // 设置高亮消息
        setHighlight(id){
            setTimeout(()=>{
                this.highlightGmsgId = id;
            },500)

            setTimeout(() => {
                if (this.highlightGmsgId === id) {
                    this.highlightGmsgId = null;
                }
            }, 1500);
        },
        // 强制滚动到指定索引的消息并设置高亮
        forceScrollToMessageIndex(index, gmsg_id) {
            const scroller = this.$refs.scroller;
            if (!scroller) {
                this.$message.warning(this.lang.unable_locate_original_message);
                return;
            }

            try {
                // 直接滚动到指定索引
                scroller.scrollToItem(index);

                // 等待滚动完成后再次尝试查找DOM元素并设置高亮
                setTimeout(() => {
                    this.waitForMessageElement(gmsg_id, (messageElement) => {
                        if (messageElement) {
                            this.setHighlight(gmsg_id);
                            console.log('强制滚动定位成功');
                        } else {
                            // 如果仍然找不到，直接设置高亮标志（不显示错误消息）
                            console.warn('强制滚动后仍未找到DOM元素，仅设置高亮标志');
                            this.setHighlight(gmsg_id);
                        }
                    }, 10, 0); // 减少重试次数，加快处理
                }, 200);
            } catch (error) {
                console.error('强制滚动定位失败:', error);
                this.$message.warning(this.lang.unable_locate_original_message);
            }
        },
    }
}
</script>
<style lang="scss">
.chat_mode{
    height:100%;
    display:flex;
    flex-direction: column;
    font-size:16px;
    .header{
        height:40px;
        text-align:center;
        color:#fff;
        background-color: #dee6e6;
        line-height:40px;
        font-size:16px;
        padding:0 20px;
    }
    .delete_resource_tip{
        bottom: 0px;
        position: absolute !important;
    }
    .chat_history{
        flex:1;
        min-height:10px;
        z-index:1;
        position: relative;
        .join_verify_tip{
            position: absolute;
            top: 0;
            width: 100%;
            z-index: 2;
            padding: 6px 10px;
            background: #d9dfe1;
            box-shadow: 0px 1px 2px #999;
            cursor: pointer;
            display: flex;
            align-items: center;
            .iconuser-round-add{
                font-size: 20px;
                margin-right: 8px;
                color: #41c128;
            }
        }
        .loading_hostory{
            height:46px;
            position:relative;
        }
        .history_scroller{
            z-index:1;
            .__panel{
                z-index:1;
                .__view{
                    max-width:100%;
                }
            }
        }
        .unread_number_btn{
            position:absolute;
            right:0;
            top:50px;
            z-index: 2;
            border: 1px solid #eee;
            padding: 4px 12px;
            border-right: none;
            border-radius: 10px 0 0 10px;
            color: #fff;
            background: #a1b7b6;
            cursor:pointer;
            .iconup1{
                font-size: 12px;
                margin-right: 8px;
            }
        }
        .message_item{
            padding:10px 16px;
            .message_item_title{
                margin-bottom: 15px;
                font-size:14px;
                .avatar_box{
                    float:left;
                    margin-right:10px;
                    cursor:pointer;
                }
                .message_item_name{
                    float:left;
                    line-height: 30px;
                    max-width: calc(100% - 186px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    padding-right: 20px;
                }
                .message_item_time{
                    float:left;
                    line-height: 30px;
                    width: 140px;
                    color:#999;
                }
            }
            .message_item_wrapper{
                position: relative;
                float: left;
                padding: 10px;
                line-height: 20px;
                background-color: #fff;
                border-radius: 3px;
                clear: both;
                line-height: 26px;
                word-break: break-word;
                &:before{
                    display:block;
                    content:'';
                    width:0;
                    height:0;
                    position:absolute;
                    border:8px solid transparent;
                    border-bottom-color:#fff;
                    border-top:none;
                    top:-8px;
                }

                .message_item_content{
                    .qc_standard_icon{
                        right: 3px;
                        bottom: 0px;
                        position: absolute;
                        color: rgb(0,255,102);
                        font-size: 10px;
                    }
                    .qc_non_standard_icon{
                        right: 3px;
                        bottom: 0px;
                        position: absolute;
                        color: rgb(255,153,51);
                        font-size: 10px;
                    }
                    .ai_result_deletion_icon{
                        right: 3px;
                        bottom: 0px;
                        position: absolute;
                        color: rgb(255,0,0);
                        font-size: 10px;
                    }
                    &.video_msg .message_image:after{
                        width:40px;
                        height:40px;
                        display:block;
                        content:'';
                        position:absolute;
                        left:50%;
                        top:50%;
                        transform:translate(-50%,-50%);
                        background:transparent url('../../../../static/resource_pc/images/poster_video.png') left top no-repeat;
                        background-size: 100% 100%;
                        cursor:pointer;
                    }
                    &.image_msg{
                        img:hover{
                            transform:scal(1.2);
                        }
                    }
                    &.exam_msg{
                        position: relative;
                        .message_image{
                            margin-right: 50px;
                            border-radius: 8px;
                            .patient_info{
                                position: absolute;
                                width: 200%;
                                line-height: 1.2;
                                left: 0;
                                bottom: 0;
                                background: #aab4b9;
                                color: #fff;
                                font-size: 20px;
                                padding: 0 12px;
                                box-sizing: border-box;
                                transform: scale(.5);
                                transform-origin: left bottom;
                                span{
                                    width: 50%;
                                    display: inline-block;
                                }
                            }
                        }
                        .resource_deleted{
                            background: #fff;
                            &:hover{
                                background: #fff;
                            }
                        }
                        &::after{
                            content: '';
                            display: block;
                            position: absolute;
                            width: 20px;
                            height: 90%;
                            right: 33px;
                            bottom: 4px;
                            background: url('/static/resource_pc/images/exam_images.png');
                            background-size: 100% 100%;
                        }
                        .piece_tip{
                            position: absolute;
                            right: 4px;
                            bottom: 0;
                            display: flex;
                            font-size: 12px;
                            color: #00c59d;
                            align-items: center;
                            white-space: nowrap;
                            letter-spacing: 2px;
                            line-height: 1;
                            img{
                                width: 10px;
                                height: 10px;
                                margin-right: 4px;
                            }
                        }
                    }
                    &.comment_msg{
                        .message_image{
                            flex-direction: column;
                            color: #fff;
                        }
                        .review_text{
                            color: yellow;
                        }
                        .review_time{
                            font-size: 12px;
                        }
                    }
                    &.img_window{
                        position:relative;
                        display:block;
                        background: #000;
                        overflow:hidden;
                    }
                    &.mc_resource_map_info{
                        flex-direction: column;
                        background-color: #fff;
                        background: #fff;
                        img{
                            flex-direction: column;
                        }
                        div{
                            font-size:12px;
                        }

                    }
                    &.live_image_box{
                        position: relative;
                        padding: 16px;
                        width: 240px;
                        cursor: pointer;
                        overflow: hidden;
                        .card-image{
                            width: 100%;
                            display: flex;
                            justify-content: center;
                            position: relative;
                            background: #fff;
                            img {
                                width: 100%;
                                height: 138px;
                                object-fit: contain;
                                border-top-left-radius: 8px;
                                border-top-right-radius: 8px;
                            }

                        }
                        .card-date{
                            font-size: 14px;
                            margin-bottom: 8px;
                        }
                        .card-info{
                            display: flex;
                            justify-content: space-between;
                             font-size: 14px;
                            margin-bottom: 8px;
                        }
                        .card-content{
                            // padding: 5px 0;
                            padding: 8px 0;
                            .card-title {
                                font-size: 15px;
                                font-weight: bold;
                                margin-bottom: 4px;
                            }
                            .card-desc {
                                line-height:1.4;
                                font-size: 14px;
                                color: rgba(0,0,0, 0.7);
                                line-height: 1.5;
                            }
                        }
                        .card-tips{
                            display: flex;
                            font-size: 12px;
                            justify-content: space-between;
                            margin-top: 8px;
                            .card-tips-status0{
                                color: #0c7bea;
                            }
                            .card-tips-status1{
                                color: #c4a20a;
                            }
                            .card-tips-status2{
                                color: #3dc40a;
                            }
                            .card-tips-status3{
                                color: #fb1212;
                            }
                            .card-tips-link{
                                cursor: pointer;
                                color: #0c7bea;
                                text-transform: uppercase;
                                font-weight: 500;
                                padding: 4px 0;
                            }
                        }
                    }
                    .record_content{
                        width: 180px;
                        height: 135px;
                        position: relative;
                        overflow: hidden;
                        cursor: pointer;
                        &>img{
                            width: 100%;
                            height: 100%;
                            object-fit: contain;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%,-50%);
                            background: #000;
                        }
                        .diff_time {
                            position: absolute;
                            color: #fff;
                            font-size: rgba(255, 177, 66);
                            color: rgba(255, 177, 66);
                            right: 2px;
                            bottom: 2px;
                            font-size: 10px;
                            font-weight: 800;
                            line-height: 1;
                        }
                        .review_item_read_subject{
                            font-size: 12px;
                            color: #fff;
                            word-break: break-all;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            position: absolute;
                            top: 0;
                            left: 4px;
                            line-height: 2;
                        }
                        .review_item_read_time{
                            font-size: 10px;
                            color: #fff;
                            word-break: break-all;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            position: absolute;
                            left: 2px;
                            bottom: 2px;
                            line-height: 1;
                        }
                    }
                    .svg_icon_play{
                        position: absolute;
                        // width: 25%;
                        // height: 25%;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        font-size: 2.5rem;
                        opacity: 1;
                        color: #fff;
                    }
                    .message_image{
                        width:144px;
                        height:108px;
                        cursor:pointer;
                        display:block;
                        position:relative;
                        overflow:hidden;
                        margin:0 auto;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background: #000;
                        &>img{
                            width: 100%;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%,-50%);
                            height: 100%;
                            object-fit: scale-down;
                        }
                        &:hover{
                            background-color: #727478;
                            &>img{
                                opacity: .8;
                                transform:translate(-50%,-50%) scale(1.3);
                            }
                        }
                    }
                    &.text_message{
                        user-select: text;
                        max-width: 320px;
                        &>img{
                            margin:0 2px;
                            vertical-align: text-bottom;
                        }
                    }
                    &.file_msg{
                        // text-align:center;
                        // img{
                        //     width:80px;
                        //     height:80px;
                        //     cursor:pointer;
                        // }
                        // p{
                        //     text-align:center;
                        //     line-height:1;
                        // }
                        &.expired{
                            background: #e3e3e3;
                            cursor: not-allowed;
                        }
                        background: #fff;
                        .file_content{
                            width: 260px;
                            display: flex;
                            padding: 10px;
                            .file_content-l{
                                flex: 1;
                                display: flex;
                                flex-direction: column;
                                justify-content: space-between;
                                padding-right: 10px;
                                .file_name{
                                    margin-bottom: 15px;
                                }
                                .file_size{
                                    color: #393939;
                                    font-size: 12px;
                                }
                            }
                            .file_content-r{
                                width: 60px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                img{
                                    width:100%;
                                    height:auto;
                                }
                            }
                        }
                        .file_content-footer{
                            font-size: 12px;
                            color: red;
                            padding: 3px 10px;
                            border-top: 1px solid #ccc;
                            display: flex;
                            justify-content: space-between;
                        }
                    }
                    &.iworks_protocol_msg{
                        .iworks_protocol {
                            border: 2px solid;
                            border-radius: 5px;

                            .title{
                                font-weight:bold;
                                margin:10px;
                            }
                            .content{
                                & > div{
                                    padding-left:40px;
                                    margin-bottom:10px;
                                }
                                margin-right:10px;
                                line-height:1;
                            }
                        }
                    }
                    &.homework_detail_msg{
                        cursor: pointer;
                        .homework_detail {
                            width: 300px;
                            .title{
                                font-weight:bold;
                                text-align: center;
                            }
                            .sub_title{
                                font-size: 14px;
                            }
                            .content{
                                display: flex;
                                flex-wrap: wrap;
                                & > div{
                                    flex:1;
                                    color: #666;
                                    font-size: 12px;
                                    line-height: 1.6;
                                    white-space: nowrap;
                                }
                            }
                        }
                    }
                    &.sound_msg{
                        & > span{
                            padding-left:20px;
                            cursor:pointer;
                            background: transparent url('../../../../static/resource_pc/images/sound_other.png') left center no-repeat;
                            background-size: 20px 20px;
                        }
                        img{
                            max-width:40px;
                        }
                        &.playing{
                            & > span{
                                background-image:url('../../../../static/resource_pc/images/sound_other.gif')
                            }
                        }
                    }
                    .ai_images_wrap{
                        width: 144px;
                        height: 108px;
                        background: #333 !important;
                        overflow: hidden;
                        position: relative;
                        border-radius: 0;
                        img{
                            width: 100%;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                        }
                        .icon-images{
                            position: absolute;
                            right: 8px;
                            bottom: 0;
                            font-size: 24px;
                        }
                    }

                    .comment_number{
                        position: absolute;
                        left: 2px;
                        top: 2px;
                        color: #fff;
                        background: #56C7FD;
                        border: 1px solid white;
                        width: 20px;
                        border-radius: 50%;
                        height: 20px;
                        font-size: 12px;
                        line-height: 20px;
                        text-align: center;
                        z-index:2;
                    }
                    .unread_tip{
                        position: absolute;
                        right: 0.2rem;
                        top: 0.2rem;
                        border-radius: 50%;
                        background-color: #f00;
                        width:8px;
                        height:8px;
                        z-index:2;
                    }

                }
                .uploading_wrapper2{
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                    .el-progress__text{
                        color: #20a0ff;
                    }
                    .iconpause-upload,.iconshangchuan1,.icongantanhao-yuankuang{
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%,-50%);
                        color: #20a0ff;
                        font-size: 20px;
                    }
                    .icongantanhao-yuankuang{
                        color: red;
                    }
                    .iconclose{
                        position: absolute;
                        top: 8px;
                        right: 12px;
                        color: red;
                    }
                    // .van-icon-upgrade,.van-icon-pause-circle-o,.van-icon-warning-o{
                    //     position: absolute;
                    //     top: 50%;
                    //     left: 50%;
                    //     transform: translate(-50%,-50%);
                    // }
                    // .van-icon-close{
                    //     position: absolute;
                    //     top: 0.5rem;
                    //     right: 0.5rem;
                    // }



                }
                .uploading_wrapper{
                    position:absolute;
                    width: calc(100% - 20px);
                    height: calc(100% - 20px);
                    top:10px;
                    left:10px;
                    overflow:hidden;
                    .iconclose{
                        position: absolute;
                        left: 50%;
                        top: 40%;
                        transform: translate(-50%,-50%);
                        font-size: 26px;
                        color: #fff;
                        cursor: pointer;
                        z-index: 2;
                    }
                    .upload_progress{
                        width: 100%;
                        height: 100%;
                        position: absolute;
                        top: 0;
                        left:0;
                        background: rgba(0,0,0,.3);
                    }
                    span{
                        position: absolute;
                        color: #fff;
                        z-index: 2;
                        top: 50%;
                        width: 100%;
                        text-align: center;
                    }
                }
                .iworks_protocol_wrap{
                    font-size:14px;
                    line-height: 1.4;
                    margin-top: 4px;
                    max-width: 220px;
                }
                .iworks_tip{
                    float:right;
                    cursor: pointer;
                    font-size:14px;
                }
                .image_menu{
                    position: absolute;
                    right: -30px;
                    bottom: 0px;
                    background: #fff;
                    font-size: 14px;
                    line-height: 16px;
                    border-radius: 6px;
                    cursor: pointer;
                    .el-popover__reference{
                        padding:0 6px
                    }
                }
                .error_image{
                    width:144px;
                    height:108px;
                    cursor:no-drop;
                    display:block;
                    position:relative;
                    overflow:hidden;
                    margin:0 auto;
                    &>img{
                        width:144px;
                        height:auto;
                        position:absolute;
                        top:50%;
                        left:50%;
                        transform:translate(-50%,-50%);
                    }
                }
            }
            &.self_chat{
                .message_item_title{
                    .avatar_box{
                        float:right;
                        margin-left: 10px;
                        margin-right: 0;
                    }
                    &>.message_item_name{
                        float:right;
                        padding-left: 20px;
                        padding-right: 0px;
                    }
                    &>.message_item_time{
                        float:right;
                        text-align:right;
                    }
                }
                .message_item_wrapper{
                    float:right;
                    background-color: #BFD1D1;
                    &:before{
                        right:10px;
                        border-bottom-color:#BFD1D1;
                    }
                    .sound_msg{
                        & > span{
                            padding-left:0;
                            padding-right:20px;
                            cursor:pointer;
                            background: transparent url('../../../../static/resource_pc/images/sound_me.png') right center no-repeat;
                            background-size: 20px 20px;
                        }
                        &.playing{
                            & > span{
                                background-image:url('../../../../static/resource_pc/images/sound_me.gif')
                            }
                        }
                    }
                    .image_menu{
                        left:-30px;
                        right:auto;
                        background-color:#b9cacb;
                        color:#fff;
                    }
                    .exam_msg{
                        .message_image{
                            margin-right: 0;
                            margin-left: 50px;
                        }
                        &::after{
                            left: 33px;
                            right: auto;
                            background: url('/static/resource/images/exam_images2.png');
                            background-size: 100% 100%;
                        }
                        .piece_tip{
                            left: 4px;
                            right: auto;
                        }
                    }
                }
                .sending_spinner{
                    float: right;
                    height: 20px;
                    width: 20px;
                    margin-right: 6px;
                    background-color:transparent;
                    position:relative;
                    .el-loading-spinner{
                        top:0;
                        margin-top:0;
                        .circular{
                            width:20px;
                            height:20px;
                        }
                    }
                }
                .send_fail{
                    float: right;
                    color: #e33a48;
                    font-size: 22px;
                    margin-right: 6px;
                }
            }

            /* 引用消息样式 */
            .quote-message {
                margin-top: 3px;
                margin-bottom: 10px;
                padding: 8px 10px;
                background-color: #eaeaea;
                border-left: 3px solid #ccc;
                border-radius: 0px;
                font-size: 12px;
                position: relative;
                box-sizing: border-box;
                width: auto;
                max-width: 90%;
                clear: both;

                /* 左侧引用消息样式 */
                &.quote-left {
                    float: left;
                    text-align: left;
                }

                /* 右侧引用消息样式 */
                &.quote-right {
                    float: right;
                    text-align: left;
                    border-left: 3px solid #ccc;
                    border-right: none;
                }

                .quote-sender {
                    font-size: 12px;
                    color: #999;
                    margin-bottom: 3px;
                    font-weight: normal;
                    display: block;
                }

                .quote-content-message {
                    font-size: 12px;
                    color: #666;
                    line-height: 1.4;
                    /* 文本内容样式 */
                    white-space: normal;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    max-width: 100%;

                    /* 图片和视频内容样式 */
                    &.image-content, &.video-content {
                        display: flex;
                        align-items: center;
                        cursor: pointer;
                        transition: background-color 0.2s ease;
                        display: flex;
                        -webkit-line-clamp: unset;
                        -webkit-box-orient: unset;
                        white-space: nowrap;
                        line-height: initial;
                        padding: 0 5px;
                        &:hover {
                            background-color: rgba(0, 0, 0, 0.05);
                            border-radius: 3px;
                        }
                    }

                    &.image-content {
                        img {
                            width: 40px;
                            height: 40px;
                            object-fit: cover;
                            margin-right: 8px;
                            border-radius: 0;
                        }
                    }

                    &.video-content {
                        display: flex;
                        align-items: center;

                        .video-thumbnail {
                            position: relative;
                            width: 50px;
                            height: 30px;
                            margin-right: 8px;
                            border-radius: 0;
                            overflow: hidden;
                            background-color: #000;

                            img {
                                width: 100%;
                                height: 100%;
                                object-fit: cover;
                            }

                            .video-play-icon {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 100%;
                                height: 100%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                i {
                                    font-size: 14px;
                                    color: #fff;
                                }
                            }
                        }

                        span {
                            color: #999;
                            font-size: 11px;
                        }
                    }
                }
            }
        }
        .notify_wrapper{
            text-align: center;
            .system_notify,.send_time_tip{
                padding: 5px 10px;
                display: inline-block;
                max-width: 100%;
                box-sizing: border-box;
                color: #aaa;
                font-size: .7rem;
                margin: .3rem 0;
                border-radius: .1rem;
                white-space: break-spaces;
                &>span{
                    color:#1755b3;
                    cursor:pointer;
                }
                &>.re_edit_wrapper{
                    padding-left: 10px;
                    display: inline;
                }
            }
            &>p{
                line-height:2;
            }
        }
        .system_message{
            display:flex;
            margin:10px auto;
            max-width:60%;
            background: #BFD1D1;
            color: #fff;
            padding: 8px;
            border-radius: 4px;
            position:relative;
            cursor:pointer;
            .ai_images_wrap{
                width: 144px;
                height: 108px;
                background: #333 !important;
                overflow: hidden;
                position: relative;
                border-radius: 0;
                img{
                    width: 100%;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                }
                .icon-images{
                    position: absolute;
                    right: 8px;
                    bottom: 0;
                    font-size: 24px;
                }
            }
            .ai_message_right{
                flex:1;
                font-size: 0.8rem;
                padding-left: 0.4rem;
                padding-bottom: 0.8rem;
                .nickname{
                    color: #0066ff;
                }
                .tip{
                    margin-top:0.5rem;
                }
                .right_tip{
                    text-align:right;
                    font-size:0.6rem;
                    position: absolute;
                    right: 0.4rem;
                    bottom: 0.4rem;
                }
            }
        }
    }

    &.gallery_type{
        .chat_history{
            .message_item{
                .message_item_wrapper{
                    background-color:#EEE8EE;
                    &:before{
                        border-bottom-color:#EEE8EE;
                    }
                }
            }
            .system_message{
                background-color:#EEE8EE;
                color:#333;
                max-width:80%;
            }
            .self_chat{
                .message_item_wrapper{
                    background-color:#EEE8EE;
                    &:before{
                        border-bottom-color:#EEE8EE;
                    }
                }

            }
        }

    }

}
.iworks_tree_popper{
    padding-right:30px;
    max-height: 100%;
    overflow: auto;
}

.vue-recycle-scroller.direction-vertical::-webkit-scrollbar-track{
  background-color: rgba(0,0,0,0);

}
.vue-recycle-scroller.direction-vertical{
    overflow: auto;
}
// .vue-recycle-scroller.direction-vertical:hover {
//     overflow: auto;
// }
.vue-recycle-scroller.direction-vertical::-webkit-scrollbar-thumb{
  display:none;
}

.vue-recycle-scroller.direction-vertical:hover::-webkit-scrollbar-thumb{
    display:block;
}
.scroller{
    height: 100%;
    &.showPadding50{
        padding-bottom: 50px;
    }

    // overflow: auto;
    .scroller-item{
        min-height: 40px;
        width: 100%;
        // display: flex;
        // min-height: 150px;
        // padding: 12px;
        box-sizing: border-box;
    }
    .empty_item{
        height:60px;
    }
}
.vue-recycle-scroller::-webkit-scrollbar{
    width: 10px;
    height: 10px;
}
.vue-recycle-scroller::-webkit-scrollbar-track{
    background: rgb(239,239,239);
    border-radius: 2px;
}
.vue-recycle-scroller::-webkit-scrollbar-thumb{
    background: rgb(169,191,190);
    border-radius: 10px;
    cursor: pointer;
}
.record_start{
    width: 22px;
    height: 22px;
    background: red;
    border-radius: 50%;
    display: inline-block;
    cursor: pointer;
    position: relative;
    &::after{
        content:'';
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate3d(-50%,-50%,0);
        background: #fff;
        width: 8px;
        height: 8px;

    }
}
// .vue-recycle-scroller::-webkit-scrollbar-thumb:hover{
//     background: #333;
// }
// .vue-recycle-scroller::-webkit-scrollbar-corner{
//     background: #179a16;
// }

@keyframes highlight-flash {
  0% {
    background-color: transparent;
  }
  25% {
    /* 可以用一个更饱和的颜色，但透明度低一些 */
    background-color: rgba(150, 200, 255, 0.5); /* 示例：淡蓝色 */
  }
  75% { /* 快速达到顶峰然后开始消失 */
    background-color: rgba(150, 200, 255, 0.5);
  }
  100% {
    background-color: transparent;
  }
}

.message_item {
  &.highlight {
    /* 非常短的时间，例如 0.8s 到 1.2s */
    animation: highlight-flash 1s linear; /* linear 或 ease-out 更直接 */
  }
}
</style>

