<template>
       <div class="video_members">
            <div class="panel">
                <div class="group_members clearfix" @scroll.prevent v-if="allUserList.length > 0">
                    <div v-for="item of allUserList" class="group_member_item" :key="item.uid">
                        <div class="group_user_wrapper">
                            <mr-avatar
                                url="static/resource/images/b3-1.png"
                                origin_url="static/resource/images/b3-1.png"
                                :showOnlineState="false"
                                :key="item.avatar"
                            ></mr-avatar>
                            <div class="user_info">
                                <span>{{ item.nickname }}</span>
                                <div class="tag_box">
                                    <span class="tag" v-if="item.isHost">{{ lang.moderator }}</span>
                                    <span class="tag" v-if="item.isSelf">{{ lang.mine }}</span>
                                    <span class="tag" v-if="item.hasMain">{{
                                        lang.main_stream_screen
                                    }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="group_user_operate">
                            <!-- 摄像头操作 -->
                            <span class="operate_btn" v-if="item.isSelf">
                                <i
                                    v-if="item.videoStream === 1"
                                    @click="toggleSelfVideo(item)"
                                    class="icon iconfont icon-shipinluxiang"
                                ></i>
                                <i
                                    v-else
                                    @click="toggleSelfVideo(item)"
                                    class="icon iconfont icon-weifaxianshexiangtou"
                                ></i>
                            </span>
                            <span class="operate_btn" v-else>
                                <i
                                    v-if="
                                        item.videoStream === 1 &&
                                        !LivingData.currentSubscribeAux.includes(item.uid)
                                    "
                                    @click="toggleRemoteSubscribeVideo(item)"
                                    class="icon iconfont icon-shipinluxiang"
                                ></i>
                                <i
                                    v-else-if="
                                        item.videoStream === 1 &&
                                        LivingData.currentSubscribeAux.includes(item.uid)
                                    "
                                    @click="toggleRemoteSubscribeVideo(item)"
                                    class="icon iconfont icon-shipinluxiang"
                                >
                                    <i class="icon iconfont icon-duihao"></i>
                                </i>
                                <i
                                    v-else-if="item.videoStream === 0"
                                    class="icon iconfont icon-weifaxianshexiangtou"
                                ></i>
                            </span>

                            <!-- 麦克风操作 -->
                            <span class="operate_btn" v-if="item.isSelf">
                                <i
                                    v-if="item.audioStream === 1"
                                    @click="toggleSelfAudio(item)"
                                    class="icon iconfont icon-mic-line"
                                ></i>
                                <i v-else @click="toggleSelfAudio(item)" class="icon iconfont icon-mic-off-line"></i>
                            </span>
                            <span class="operate_btn" v-else>
                                <i
                                    v-if="item.audioStream === 1"
                                    class="icon iconfont icon-mic-line"
                                ></i>
                                <i v-else class="icon iconfont icon-mic-off-line"></i>
                            </span>
                        </div>
                    </div>
                </div>
                <no-data v-else :text="'暂无成员数据'" class="nodata_page"></no-data>
            </div>
        </div>
</template>
<script>
import base from "../../lib/base";
import DialogManager from "../../lib/dialogManager";
import Tool from "@/common/tool";
import NoData from "../../MRComponents/noData.vue";
export default {
    mixins: [base],
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        channelId: {
            type: [String, Number],
            default: 0,
        },
        liveRoom: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    components: {
        NoData,
    },
    computed: {
        dialogVisible: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            },
        },
        allUserList() {
            let list = [];

            if (this.LivingData.roomUserMap) {
                // 直接基于 roomUserMap 生成成员列表
                Object.values(this.LivingData.roomUserMap).forEach((item) => {
                    // 只统计辅流用户且在线的用户
                    if (item.streamType === "aux" && item.isOnline === 1) {
                        // 构建用户信息对象
                        const userInfo = {
                            uid: item.uid,
                            nickname: item.nickname || `用户${item.uid}`,
                            videoStream: item.videoStream,
                            audioStream: item.audioStream,
                            streamType: item.streamType,
                            isHost: item.isHost || 0,
                            isOnline: item.isOnline,
                            token: item.token,
                            auxInfo: item.auxInfo,
                            isSelf: item.uid === this.LivingData.localAuxUid ? 1 : 0,
                            hasMain:item.hasMain
                        };

                        list.push(userInfo);
                    }
                });
            }

            // 排序：主讲人 > 本人 > 其他用户
            if (list.length > 0) {
                list.sort(this.sortAttendeeList);
            }
            console.error('allUserList',list);
            this.$emit('updateLiveUserListLength', list.length);
            return list;
        },
        LivingData() {
            const data =
                (this.$store.state.livingData[this.channelId] &&
                    this.$store.state.livingData[this.channelId].LivingData) ||
                {};
            this.isConferenceVideoStream = data.localVideoStream > 0 ? 1 : 0;
            this.isConferenceAudioStream = data.localAudioStream > 0 ? 1 : 0;
            console.error(data);
            return data;
        },
    },
    data() {
        return {
            currentDialogId: null
        };
    },
    watch: {
        show: {
            handler(val) {
                if (val) {
                    // 弹窗打开时，注册到DialogManager
                    this.currentDialogId = DialogManager.register(this, {
                        open: this.showDialog.bind(this),
                        close: this.closeDialog.bind(this),
                        canCloseOnPopstate: this.checkCanCloseOnPopstate(),
                        canClose: this.checkCanClose()
                    });
                } else {
                    // 弹窗关闭时，从DialogManager注销
                    if (this.currentDialogId) {
                        DialogManager.unregister(this.currentDialogId);
                        this.currentDialogId = null;
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {

    },
    methods: {
        sortAttendeeList(a, b) {
            if (a.isSelf > b.isSelf) {
                //3.本人或者举手者再往前排
                return -1;
            } else {
                return 1;
            }
        },
        // 本人摄像头开关
        toggleSelfVideo: Tool.throttle(
            function (item) {
                const isMute = item.videoStream === 1;
                this.liveRoom.MuteLocalVideoStream({
                    uid: this.liveRoom.data.localAuxUid,
                    isMute,
                });
            },
            800,
            true
        ),

        // 他人辅流视频的订阅/取消
        toggleRemoteSubscribeVideo: Tool.throttle(
            async function (item) {
                if (this.LivingData.currentSubscribeAux.includes(item.uid)) {
                    this.liveRoom.StopSubscribeRemoteStreamAux(item.uid);
                } else {
                    const { canSubscribeAuxNum } = await this.liveRoom.checkAfterSubscribeAux();
                    if (canSubscribeAuxNum === 0) {
                        return;
                    }
                    this.liveRoom.SubscribeRemoteStreamAux(item.uid);
                }
            },
            800,
            true
        ),

        // 本人麦克风开关
        toggleSelfAudio: Tool.throttle(
            function (item) {
                const isMute = item.audioStream === 1;
                this.liveRoom.MuteLocalAudioStream({
                    uid: this.liveRoom.data.localAuxUid,
                    isMute,
                });
            },
            800,
            true
        ),
        getContainer() {
            return document.querySelector("body");
        },

        // DialogManager 相关方法
        checkCanCloseOnPopstate() {
            return true;
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        checkCanClose() {
            return true;
        },
        showDialog() {
            this.dialogVisible = true;
        },
    },
    beforeDestroy() {
        // 组件销毁前清理DialogManager中的注册
        if (this.currentDialogId) {
            DialogManager.unregister(this.currentDialogId);
            this.currentDialogId = null;
        }
    },
};
</script>
<style lang="scss" scoped>
.video_members {
    display: flex;
    flex-direction: column;
    flex: 1;
    .panel {
        overflow: hidden;
        background: #fff;
        display: flex;
        flex-direction: column;
        flex: 1;
        .icon-close {
            float: right;
            font-size: 1.4rem;
            line-height: 1;
            color: #fff;
        }
        .speaking {
            border-bottom: 1px solid #aaa;
            padding: 0.3rem 0;
            & > p {
                font-size: 0.7rem;
                color: #000;
            }
            .speaking_members {
                margin-top: 0.4rem;
                .speaking_member_item {
                    width: 25%;
                    float: left;
                    .image_wrapper {
                        position: relative;
                        width: 50%;
                        margin: 0 auto;
                        img {
                            width: 100%;
                            display: block;

                            border-radius: 50%;
                        }
                        .icon-icon- {
                            position: absolute;
                            top: -0.3rem;
                            right: -0.4rem;
                            color: #f22;
                            font-weight: bold;
                            font-size: 1rem;
                            line-height: 1;
                        }
                        .icon-microphone {
                            position: absolute;
                            bottom: -0.3rem;
                            color: #00d63a;
                            font-size: 1rem;
                            line-height: 1;
                            left: -0.3rem;
                        }
                        .voice_manager {
                            background-color: #00c59d;
                            font-size: 0.6rem;
                            color: #fff;
                            position: absolute;
                            right: -1rem;
                            bottom: -0.3rem;
                            padding: 0.1rem 0.2rem;
                            border-radius: 0.2rem;
                        }
                    }
                    p {
                        text-align: center;
                        color: #000;
                        font-size: 0.7rem;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        line-height: 1.8;
                    }
                }
            }
        }
        .applying {
            border-bottom: 1px solid #aaa;
            padding: 0.3rem 0;
            & > p {
                font-size: 0.7rem;
                color: #000;
            }
            .applying_members {
                margin-top: 0.4rem;
                .applying_member_item {
                    width: 25%;
                    float: left;
                    position: relative;
                    img {
                        width: 50%;
                        display: block;
                        margin: 0 auto;
                        border-radius: 50%;
                    }
                    p {
                        text-align: center;
                        color: #000;
                        font-size: 0.7rem;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        line-height: 1.8;
                    }
                    i {
                        font-size: 0.9rem;
                    }
                    .operate_apply {
                        & > div {
                            float: left;
                            margin: 0 5%;
                            width: 40%;
                            line-height: 1;
                            text-align: center;
                            color: #fff;
                            border-radius: 0.2rem;
                        }
                        .agree {
                            background-color: #0e0;
                        }
                        .disagree {
                            background-color: #f22;
                        }
                    }
                }
            }
        }
        .group_title {
            font-size: 0.9rem;
            color: #000;
            padding: 1rem 0;
        }
        .group_members {
            margin-top: 0.4rem;
            width: 100%;
            padding: 0px 20px;
            box-sizing: border-box;
            -webkit-overflow-scrolling: touch;
            overflow: auto;
            flex: 1;
            .group_member_item {
                display: flex;
                min-height: 60px;
                border-bottom: 1px solid #ccc;
                padding: 10px 0px;
                box-sizing: border-box;
                .group_user_wrapper {
                    position: relative;
                    display: flex;
                    flex: 1;
                    .user_info {
                        margin-left: 10px;
                        display: flex;
                        flex-direction: column;
                        font-size: 14px;
                        flex: 1;
                        .tag {
                            background-color: #01c59d;
                            color: #fff;
                            line-height: 1;
                            padding: 2px 4px;
                            border-radius: 4px;
                            font-size: 12px;
                            margin-right: 4px;
                        }
                        .user_info_introduction {
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            line-height: 2em;
                            color: #999;
                        }
                    }
                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                    }
                    // .icon-i-sound,.icon-microphone{
                    //     position: absolute;
                    //     bottom: -0.3rem;
                    //     color: #00d63a;
                    //     font-size: 1rem;
                    //     line-height: 1;
                    //     left:-0.3rem;
                    //     &.can_no_speak{
                    //         color:#f22;
                    //     }
                    // }
                }
                .group_user_operate {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    .operate_btn {
                        position: relative;
                        i {
                            width: 1.4rem;
                            height: 1.4rem;
                            margin-left: 0.5rem;
                            fill: #ccc;
                        }
                        .iconfont {
                            font-size: 1.4rem;
                            height: 1.4rem;
                            margin-left: 0.5rem;
                            color: #8d8d8d;
                            position: relative;
                        }
                        .icon-duihao {
                            position: absolute;
                            top: 0.5rem;
                            left: 0;
                            color: green;
                        }
                    }
                }
                .voice_manager {
                    background-color: #00c59d;
                    font-size: 0.6rem;
                    color: #fff;
                    position: absolute;
                    right: -1rem;
                    bottom: -0.3rem;
                    padding: 0.1rem 0.2rem;
                    border-radius: 0.2rem;
                }

                .authorize {
                    border-radius: 0.2rem;
                    background-color: #00c59d;
                    display: inline-block;
                    font-size: 0.7rem;
                    padding: 0 0.3rem;
                    color: #fff;
                }
            }
        }
        .nodata_page{
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    .panel_operate {
        height: 60px;
        display: flex;
        justify-content: center;
        align-items: center;
        .btn {
            flex: 1;
            margin: 0px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
        }
    }
}
</style>
