<template>
	<transition name="slide">
		<div class="subject_page fourth_level_page">
            <mrHeader>
                <template #title>

                </template>
            </mrHeader>
			<div class="container">
				<input type="hidden" name="" v-model="conversation.subject">
				<p class="modify_tip">{{lang.group_modify_subject_title}}</p>
				<input type="text" class="commont_input" @click="scrollInput" v-model="subject" maxlength="50" ref="subject">
				<button class="primary_bg modify_subject_btn" @click="submit">{{lang.save_txt}}</button>
			</div>
		</div>
	</transition>
</template>
<script>
import base from '../lib/base'
export default {
    mixins: [base],
    name: 'GroupModifySubject',
    components: {},
    data(){
    	return {
    		cid:this.$route.params.cid,
    		subject:''
    	}
    },
    activated(){
        this.cid=this.$route.params.cid
    },
    mounted(){
        this.$nextTick(()=>{
            // this.subject=this.conversation.subject;
        })
    },

    computed:{
        conversation(){
        	let conversation=this.conversationList[this.cid]||{}
        	this.subject=conversation.subject;
            return conversation
        },
    },
    methods:{
    	submit(){
    		if (this.subject=='') {
    			return
    		}
    		this.conversation.socket.emit('edit_subject',this.subject)
    		this.back();
    	},
    	scrollInput(){
    		setTimeout(()=>{
    			this.$refs.subject.scrollIntoViewIfNeeded(true);
    		},300)
    	}
    }
}

</script>
<style lang="scss">
	.subject_page{
		.container{
			margin:.8rem;
			.modify_tip{
				font-size:.8rem;
				color:#707070;
				margin:.1rem 0;
			}
			input{
				background-color:transparent;
				margin:0;
				color:#333;
				transform: translateZ(0px);
			}
			.modify_subject_btn{
				display: block;
			    width: 100%;
			    border: none;
			    font-size: 1rem;
			    line-height: 2rem;
			    margin: 1rem 0 .6rem;
			    border-radius: .2rem;
			}
		}
	}
</style>
