<template>
    <transition name="slide">
        <div class="cloud_exam_detail fourth_level_page">
            <mrHeader>
                <template #title>
                    {{ examDetail.title }}
                </template>
            </mrHeader>
            <div class="cloud_exam_container">
                <div class="exam_detail_header" v-if="type === 1">{{lang.author}}：{{examDetail.author}}</div>
                <div class="exam_detail_content">
                    <div v-if="type === 1" class="assign_homework">
                        <van-button class="gradient_btn" @click="arrangeExam">{{lang.assign_homework}}</van-button>
                        <van-button class="primary_btn" @click="shareExam">{{lang.share_paper}}</van-button>
                        <van-button class="error_btn" @click="deletePaper">{{lang.homework.delete_paper}}</van-button>
                    </div>
                    <div class="topic_type_content" v-for="(topicType,index) of examDetail.content" :key="index" v-show="topicTypeStep === index+1">
                        <div class="topic_summary" v-show = "type !== 3">{{lang.topic_type[topicType.type]}}（{{getTopicTypeSummary(topicType)}}）</div>
                        <div class="topic_content" v-for="(topic,j_index) of topicType.list" :key="j_index" v-show="topicStep === topic.index">
                            <div class="topic_summary" v-show="type === 3 &&topicTypeStep === index+1">
                                <p>{{lang.correction_progress}}：{{topicStep}}/{{examDetail.questionCount}}</p>
                                <p>{{lang.topic_type[topicType.type]}}</p>
                                <p>{{lang.question_score}}：{{topic.score}}{{lang.point_tip}}</p>
                            </div>
                            <div class="topic_detail" >
                                <div class="topic_title">{{topic.index}}、{{topic.title}}({{getTopicCount(topic)}})</div>
                                <div class="topic_images">
                                    <div v-for="(image,k_index) of topic.imageList" :key="k_index" @click="viewImage(topic.imageList,k_index)">
                                        <img v-if="image.msg_type === 3" :src="image.url">
                                        <div v-else-if="image.msg_type === 4" class="video">
                                            <i class="icon iconfont icon-videoplay"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="topic_radio" v-if="topicType.type === 'singleSelect'">
                                    <van-radio-group v-model="topic.value">
                                        <div class="check_container" v-for="option of topic.options" :key="topic.index+option.label" >
                                            <van-radio :name="option.label" :disabled="disableModify">{{option.label}}</van-radio>
                                            <span @click="toggleRadio(topic,option)">{{option.content}}</span>
                                        </div>
                                    </van-radio-group>
                                </div>
                                <div class="topic_radio" v-else-if="topicType.type === 'multiSelect'">
                                    <van-checkbox-group v-model="topic.value">
                                        <div  class="check_container" v-for="option of topic.options" :key="topic.index+option.label">
                                            <van-checkbox   :name="option.label" :disabled="disableModify" shape="square">{{option.label}}</van-checkbox>
                                            <span @click="toggleCheckbox(topic.value,option.label)">{{option.content}}</span>
                                        </div>
                                    </van-checkbox-group>
                                </div>
                                <div class="topic_radio" v-else-if="topicType.type === 'shortAnswer'">
                                    <textarea v-model="topic.value" rows="15" style="height: 200px;" :disabled="disableModify" @input="updateShortAnswerProgress(topic)"></textarea>
                                </div>
                                <template v-else-if="topicType.type === 'operation'">
                                    <pre class="topic_description" v-html="topic.description"></pre>
                                    <pre class="step_tip" v-html="lang.homework_operation_step"></pre>
                                    <div class="subtopic_item" v-for="(subtopic,subIndex) of topic.subTopic" :key="subIndex">
                                        <p class="sub_title" v-if="subtopic.title && !(topic.subTopic?.length === 1 && topic.subTopic?.[0]?.title === 'NOSHOWSUBTOPICASTOPIC')">{{subIndex + 1}}、{{subtopic.title}}</p>
                                        <pre class="sub_description">{{subtopic.description}}</pre>
                                        <div class="collect_btn">
                                            <van-button class="gradient_btn" @click="openCollect(index,j_index,subIndex,subtopic)" v-if="!disableModify&&!subtopic.collecting">{{lang.start_collecting}}</van-button>
                                            <van-button class="danger_btn" @click="closeCollect(index,j_index,subIndex,subtopic)" v-if="!disableModify&&subtopic.collecting">{{lang.stop_collecting}}</van-button>
                                        </div>
                                        <div class="views_list" >
                                            <div class="views_item" v-for="(image,k_index) of subtopic.value" :key="k_index" @click="viewImage(subtopic.value,k_index)">
                                                <img v-if="image.msg_type === 3" :src="image.url">
                                                <div v-else-if="image.msg_type === 4" class="video">
                                                    <i class="icon iconfont icon-videofill"></i>
                                                </div>
                                                <i v-if="type === 2" @click.stop="deleteImage(subtopic.value,k_index)" class="iconfont icon-delete"></i>
                                            </div>
                                            <div class="views_item" v-if="!disableModify" @click="uploadStart(index,j_index,subIndex)">
                                                <i class="icon iconfont icon-plus1"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <input v-show="false" ref="uploadComponent" type="file" :accept="acceptFileTypes" multiple @change="handleFileChange($event)"/>
                                </template>
                                <div class="correct_panel" v-if="type === 3">
                                    <div class="full_score" @click="correctSelect(topic,1)" :class="{active:topic.correctScore === topic.score}">{{lang.full_mark}}</div>
                                    <div class="empty_score" @click="correctSelect(topic,0)" :class="{active:topic.correctScore === 0}">{{lang.zero_point}}</div>
                                    <div class="current_score" v-if="topicType.type === 'shortAnswer' || topicType.type === 'operation'" @click="correctPicker(topic)" :class="{active:topic.correctScore !== 0 && topic.correctScore !== topic.score}">{{topic.correctScore}}</div>
                                </div>
                                <div class="correct_score" v-else-if="type === 4">
                                    {{lang.paper_results}}：{{topic.correctScore}}{{lang.point_tip}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="exam_detail_operation">
                    <van-button class="primary_btn" @click="changeStep(-1)" v-show="topicStep > 1">{{lang.prev_question}}</van-button>
                    <van-button class="primary_btn" @click="changeStep(1)" v-show="topicStep < examDetail.questionCount">{{lang.next_question}}</van-button>
                    <van-button class="error_btn" @click="submit" v-show="(type === 2 || type === 3) &&topicStep === examDetail.questionCount">{{lang.submit_btn}}</van-button>
                </div>
                <van-popup v-model="showPicker"  position="bottom">
                    <van-picker
                      title=""
                      show-toolbar
                      :columns="columns"
                      @confirm="onConfirm"
                      @cancel="onCancel"
                    />
                </van-popup>

                <!-- 添加进度指示器组件 -->
                <progress-indicator
                    v-if="type === 2 || type === 3"
                    :progressList="progressList"
                    :topicType="type"
                    :totalScore="getTotalScore()"
                    :topicStep="topicStep"
                    :examDetail="examDetail"
                    :isImageViewerOpen="isImageViewerOpen"
                    @jump="jumpToQuestion"
                    @save="save"
                    @submit="submit"
                />
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from '../../lib/base';
import {formatDurationTime,findServiceId} from '../../lib/common_base';
import moment from 'moment';
import service from '../../service/service.js'
import {uploadFile} from '@/common/oss/index'
import Tool from '@/common/tool'
import { Toast,RadioGroup, Radio,Button,Checkbox, CheckboxGroup,Picker,Popup  } from 'vant';
import ProgressIndicator from './components/progressIndicator.vue';

export default {
    mixins: [base,],
    name: "cloudExamDetail",
    components: {
        vanRadioGroup:RadioGroup,
        vanRadio:Radio,
        vanCheckboxGroup:CheckboxGroup,
        vanCheckbox:Checkbox,
        vanButton:Button,
        vanPicker:Picker,
        vanPopup:Popup,
        ProgressIndicator
    },
    data() {
        return {
            paperId:0,
            type:1, // 查看类型 1：查看试卷 2：答卷 3： 批卷 4：查看结果
            cid:0, // 群id ，0为全局进入
            examDetail:{},
            currentAssignment:{
                assignmentInfo:{},
                studentInfo:{}
            },
            useTime:0,
            interval:null,
            uploadIndex:[0,0,0],
            collectIndex:[0,0,0],
            fileTransferAssistant:null,
            topicStep:1,
            topicTypeStep:1,
            columns:[],
            pickerTopic:null,
            showPicker:false,
            confirmSave:false,
            progressList: [], // 添加进度列表
            studentName: '',
            studentOrg: '',
            isImageViewerOpen: false, // 添加图片预览状态变量
        };
    },
    filters:{
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm:ss z")
        },
        useTime(duration){
            return formatDurationTime(duration)
        },
    },
    computed:{
        disableModify(){
            return this.type !== 2;
        },
        acceptFileTypes() {
            const imageTypes = this.getSupportImageType().join(',.').toLowerCase();
            const videoTypes = this.getSupportVideoType().join(',.').toLowerCase();
            return `.${imageTypes},.${videoTypes}`;
        }
    },
    created(){
        this.type = parseInt(this.$route.params.type);
        this.cid = parseInt(this.$route.params.cid) || 0;
        this.paperId = this.$route.params.id;
    },
    mounted() {
        let fullPath = this.$route.fullPath
        this.$root.eventBus.$off('arrangeTransmitCallback').$on('arrangeTransmitCallback',(data) =>{
            const id = data.from === 'group' ? data.cid : data.id;
            window.directPath = fullPath +'/exam_setting/'+id+'?from='+data.from;
            // setTimeout(() =>{
            //     this.$router.push(this.$route.fullPath+'/exam_setting/'+data.cid);
            // },500)
        });

        this.$root.eventBus.$off('shareExamTransmitCallback').$on('shareExamTransmitCallback',(data) =>{
            // 分享试题回调
            let uidList = [];
            let gidList = [];
            if (data.from === 'group') {
                gidList.push(data.cid)
            }
            if (data.from === 'friend') {
                uidList.push(data.uid)
            }
            service.sharePaper({
                paperID:this.paperId,
                uidList,
                gidList,
            }).then(res => {
                if (res.data.error_code === 0) {
                    Toast(this.lang.share_to_wechat_succeed);
                    this.back();
                }
            })
        });

        // 监听路由变化，检测图片预览器的状态
        this.$router.afterEach((to, from) => {
            // 检查是否进入或离开图片预览器
            if (to.path.includes('/image_viewer')) {
                this.isImageViewerOpen = true;
            } else if (from.path.includes('/image_viewer')) {
                this.isImageViewerOpen = false;
            }
        });

        if (this.type === 1) {
            // 查看试卷
            this.getPaperDetail()
        }else if (this.type === 2) {
            // 考生获取答题卡
            this.getanswerSheetDetail();
        }else if (this.type === 3) {
            // 老师批卷获取答题卡
            // this.getanswerSheetDetail();
            this.getCorrectDetail();
        }else if (this.type === 4) {
            // 考生查看详情
            this.getanswerSheetDetail();
        }
    },
    beforeRouteLeave(to, from, next) {
        // 在离开组件前提示保存
        if (this.type !== 2 || this.confirmSave) {
            next();
            return;
        }
        next(false);
        setTimeout(()=>{
            Tool.openMobileDialog({
                message:this.lang.unsave_tip,
                confirmButtonText:this.lang.save_txt,
                rejectButtonText:this.lang.cancel_btn,
                showRejectButton:true,
                confirm:()=>{
                    let {answer} = this.getAnswer();
                    this.submitAnswer(false,answer)
                    this.back();
                },
                reject:()=>{
                    console.error("reject")
                    this.back();
                }
            })
        },100)
        this.confirmSave = true;

    },
    destroyed(){
        clearInterval(this.interval);
        this.closeFileTransferListener();
        if (this.type === 3) {
            this.unlockAnswerSheet()
        }
    },
    methods:{
        getSupportImageType(){
            return Tool.getSupportImageType();
        },
        getSupportVideoType(){
            return Tool.getSupportVideoType();
        },
        arrangeExam(){
            if (this.cid !== 0) {
                this.$router.push(this.$route.path+'/exam_setting/'+this.cid+'?from=group');
            } else {
                this.$router.push(this.$route.path+'/transmit/arrange');
            }
        },
        shareExam(){
            this.$router.push(this.$route.path+'/transmit/share');
        },
        getTopicTypeSummary(topicType){
            let summary = this.lang.topic_summary
            summary = summary.replace('{a}',topicType.count)
            summary = summary.replace('{b}',topicType.totalScore)
            return summary
        },
        getTopicCount(topic){
            let topicCount = this.lang.topic_count;
            topicCount = topicCount.replace('{a}',topic.score);
            return topicCount;
        },
        viewImage(imageList,index){
            this.$store.commit("gallery/setGallery", {
                list: imageList,
                index: index,
            });
            this.$nextTick(() => {
                this.isImageViewerOpen = true; // 设置图片预览状态为打开
                this.$router.push(`${this.$route.path}/image_viewer`);
            });
            // this.$refs.imageViewer.init(imageList,index);
        },
        deleteImage(imageList,index){
            imageList.splice(index,1);

            // 添加删除图片后更新进度状态的逻辑
            if (this.type === 2) {
                // 找到当前实操题
                let currentTopicIndex = null;
                let hasValue = false;
                this.examDetail.content.forEach((topicType, typeIndex) => {
                    if (topicType.type === 'operation') {
                        topicType.list.forEach((topic, topicIndex) => {
                            topic.subTopic.forEach((subTopic) => {

                                if (subTopic.value === imageList) {
                                    currentTopicIndex = topic.index;
                                    topic.subTopic.forEach((st) => {
                                        if (st.value && st.value.length > 0) {
                                            hasValue = true;
                                        }
                                    });
                                }
                            });
                        });
                    }
                });

                // 如果找到了当前题目，更新其进度状态
                if (currentTopicIndex !== null) {
                    this.updateProgress(currentTopicIndex, hasValue);
                }
            }
        },
        getPaperDetail(){
            service.getPaperDetail({
                paperID:this.paperId
            }).then(res => {
                if (res.data.error_code === 0) {
                    this.examDetail = res.data.data
                    this.initAnswer([]);
                    this.initProgressList();
                }
            })
        },
        getCorrectDetail(){
            // 获取批改详情先检查锁，已被别的批改老师锁定则退出
            service.lockAnswerSheet({
                answerSheetID:this.paperId,
            }).then(res=>{
                if (res.data.error_code === 0) {
                    this.getanswerSheetDetail();
                }else{
                    this.back();
                }
            })
        },
        getanswerSheetDetail(){
            service.getanswerSheetDetail({
                answerSheetID:this.paperId
            }).then(res=>{
                if (res.data.error_code === 0) {
                    this.examDetail = res.data.data.paperInfo
                    this.currentAssignment = res.data.data;
                    this.useTime = res.data.data.useTime;
                    if (this.type === 2) {
                        this.studentName = this.currentAssignment.studentName || '';
                        this.studentOrg = this.currentAssignment.studentOrg || '';
                    }
                    this.initAnswer(this.currentAssignment.answer);
                    if (this.type === 2) {
                        this.useTimeInterval();
                    }else if (this.type === 3) {
                        this.initScoreDetail(this.currentAssignment.scoreDetail)
                        this.teacherCorrectLock();
                    }else if (this.type === 4) {
                        this.initScoreDetail(this.currentAssignment.scoreDetail)
                    }
                    this.initProgressList();
                }
            })
        },
        save(){
            if (this.type === 2) {
                let {answer} = this.getAnswer();
                this.submitAnswer(false, answer, {
                    studentName: this.studentName,
                    studentOrg: this.studentOrg
                });
            }else if (this.type ===3) {
                this.submitCorrect()
            }
        },
        submit(){
            if (this.type === 2) {
                let {answer,completeCount} = this.getAnswer();

                let message = this.lang.submit_paper_tip
                message = message.replace('{a}',completeCount)
                message = message.replace('{b}',this.examDetail.questionCount)

                // 创建HTML以包含学生信息输入字段
                let customMessage = `
                    <div>${message}</div>
                    <div style="margin-top: 15px; text-align: left;">
                        <div style="margin-bottom: 10px;">
                            <div style="margin-bottom: 5px;">${this.lang.student_name}:</div>
                            <input
                                id="studentNameInput"
                                type="text"
                                value="${this.studentName}"
                                style="width: 100%; padding: 8px; box-sizing: border-box; border: 1px solid #dcdfe6; border-radius: 4px;"
                                placeholder="${this.studentName ? this.studentName : this.lang.input_enter_tips}"
                            />
                        </div>
                        <div>
                            <div style="margin-bottom: 5px;">${this.lang.admin_hospital_name}:</div>
                            <input
                                id="studentOrgInput"
                                type="text"
                                value="${this.studentOrg}"
                                style="width: 100%; padding: 8px; box-sizing: border-box; border: 1px solid #dcdfe6; border-radius: 4px;"
                                placeholder="${this.studentOrg ? this.studentOrg : this.lang.input_enter_tips}"
                            />
                        </div>
                    </div>
                `;

                Tool.openMobileDialog({
                    message: customMessage,
                    confirmButtonText: this.lang.submit_btn,
                    rejectButtonText: this.lang.save_txt,
                    showRejectButton: true,
                    confirm: () => {
                        this.studentName = document.getElementById('studentNameInput').value;
                        this.studentOrg = document.getElementById('studentOrgInput').value;
                        this.submitAnswer(true, answer);
                    },
                    reject: () => {
                        this.studentName = document.getElementById('studentNameInput').value;
                        this.studentOrg = document.getElementById('studentOrgInput').value;
                        this.submitAnswer(false, answer);
                    }
                });
            } else if (this.type === 3) {
                this.submitCorrect();
            }
        },
        submitAnswer(isFinish,answer){
            this.confirmSave = true;
            service.submitAnswer({
                answerSheetID:this.paperId,
                useTime:this.useTime,
                isFinish,
                answer,
                studentName: this.studentName,
                studentOrg: this.studentOrg,
            }).then(res=>{
                if (res.data.error_code === 0) {
                    Toast(this.lang.operate_success)
                    if (isFinish) {
                        this.$root.eventBus.$emit("refreshCompletePaper")
                        this.$root.eventBus.$emit("refreshIncompletePaper")
                        this.back();
                    }
                }
            })
        },
        submitCorrect(){
            // 提交批改
            try{
                let detail = this.getScoreDetail();
                let message = this.lang.correct_paper_tip
                message = message.replace('{a}',detail.score)
                Tool.openMobileDialog({
                    message,
                    confirmButtonText:this.lang.submit_btn,
                    rejectButtonText:this.lang.cancel_btn,
                    showRejectButton:true,
                    confirm:()=>{
                        service.submitCorrect({
                            answerSheetID:this.paperId,
                            score:detail.score,
                            scoreDetail:detail.scoreDetail,
                        }).then(res=>{
                            if (res.data.error_code === 0) {
                                Toast(this.lang.operate_success)
                                this.$root.eventBus.$emit("refreshCorrectData")
                                this.back();
                            // }else if(res.data.key === 'paperAssignmentHasBeenRevoked') {
                            } else if (res.data.error_code === -1) {
                                Toast(this.lang.error.paperAssignmentHasBeenRevoked)
                                setTimeout(() => {
                                    this.$root.eventBus.$emit("refreshCorrectData")
                                    this.back();
                                }, 1500);
                            }
                        })
                    },
                    reject:()=>{

                    }
                })
            }catch(e){
                console.log(e);
                Toast(e.message || this.lang.enter_correct_number);
            }
        },
        deletePaper() {
            Tool.openMobileDialog({
                message: this.lang.homework.confirm_delete_paper,
                title: this.lang.tip_title,
                confirmButtonText: this.lang.confirm_btn,
                rejectButtonText: this.lang.cancel_btn,
                showRejectButton: true,
                confirm: () => {
                    service.deletePaper({
                        paperID: this.paperId
                    }).then(res => {
                        if (res.data.error_code === 0) {
                            Toast(this.lang.homework.delete_success);
                            this.$root.eventBus.$emit('refreshPaperList');
                            setTimeout(() => {
                                this.back();
                            }, 100);
                        }
                    }).catch(err => {
                        this.$message.error(this.lang.homework.delete_failed);
                    });
                },
                reject: () => {
                    // this.back();
                }
            });
        },
        useTimeInterval(){
            this.interval = setInterval(()=>{
                this.useTime += 1;
            },1000);
        },
        teacherCorrectLock(){
            this.interval = setInterval(()=>{
                service.lockAnswerSheet({
                    answerSheetID:this.paperId,
                })
            },55000);
        },
        unlockAnswerSheet(){
            service.unlockAnswerSheet({
                answerSheetID:this.paperId,
            })
        },
        initAnswer(answer = []){
            let index = 0;
            this.examDetail.content.forEach(topictype=>{
                topictype.list.forEach(item=>{
                    if (topictype.type === 'operation') {
                        let j_index = 0;
                        let currentAnswer = answer[index] || {};
                        item.subTopic.forEach(subTopic=>{
                            const value = currentAnswer.value&&currentAnswer.value[j_index]
                            if (value) {
                                subTopic.value = value;
                            }else{
                                subTopic.value = [];
                            }
                            window.vm.$set(subTopic, 'collecting', false);
                            j_index++;
                        });
                    } else {
                        if (answer[index]) {
                            window.vm.$set(item,'value',answer[index].value)
                        } else {
                            if (topictype.type === 'multiSelect') {
                                window.vm.$set(item,'value',[])
                            } else {
                                window.vm.$set(item,'value','')
                            }
                        }
                    }
                    window.vm.$set(item,'index',++index)
                })
            })
        },
        initScoreDetail(scoreDetail = []){
            let index = 0;
            const answer = this.currentAssignment.answer;
            this.examDetail.content.forEach(topictype=>{
                topictype.list.forEach(item=>{
                    const correctScore = scoreDetail[index];
                    if (correctScore) {
                        // 批改过，有分数详情
                        window.vm.$set(item,'correctScore',correctScore.score)
                    }else{
                        // 未批改过，无分数详情
                        if (this.type === 3) {
                            // 教师批改模式，仅对提供参考答案的单选和多选题自动批改
                            if ((topictype.type === 'singleSelect' || topictype.type === 'multiSelect') && item.answer) {
                                const studentAnswer = answer[index].value;
                                if (topictype.type === 'singleSelect') {
                                    // 单选题
                                    if (item.answer === studentAnswer) {
                                        window.vm.$set(item, 'correctScore', item.score);
                                    } else {
                                        window.vm.$set(item, 'correctScore', 0);
                                    }
                                } else if (topictype.type === 'multiSelect') {
                                    // 多选题
                                    let refAnswer = Array.isArray(item.answer) ? item.answer.slice().sort().join(',') : '';
                                    let stuAnswer = Array.isArray(studentAnswer) ? studentAnswer.slice().sort().join(',') : '';
                                    if (refAnswer === stuAnswer) {
                                        window.vm.$set(item, 'correctScore', item.score);
                                    } else {
                                        window.vm.$set(item, 'correctScore', 0);
                                    }
                                }
                            } else {
                                // 对于未提供参考答案的单选/多选，或全部简答和实操题，不自动批改，置空默认分数
                                window.vm.$set(item, 'correctScore', null);
                            }
                        }else{
                            // 非批改模式
                            window.vm.$set(item,'correctScore',item.score)
                        }
                    }
                    index++;
                })
            })
        },
        getAnswer(){
            let answer = [];
            let completeCount = 0;
            this.examDetail.content.forEach(topictype=>{
                topictype.list.forEach(item=>{
                    if (topictype.type === 'operation') {
                        let arr = []
                        let complete = false;
                        item.subTopic.forEach(subTopic=>{
                            arr.push(subTopic.value);
                            if (subTopic.value.length !== 0 ) {
                                complete = true;
                            }
                        });

                        answer.push({
                            id:item.id,
                            value:arr
                        });

                        if (complete) {
                            completeCount++;
                        }
                    } else {
                        if (topictype.type === 'multiSelect') {
                            item.value.sort();
                        }
                        answer.push({
                            id:item.id,
                            value:item.value
                        });
                        if (typeof item.value === 'string' && item.value !== '') {
                            completeCount++;
                        }else if (typeof item.value === 'object' && item.value.length !== 0) {
                            completeCount++;
                        }
                    }
                });
            });
            return {answer,completeCount};
        },
        getScoreDetail(){
            let scoreDetail = [];
            let score = 0;
            this.examDetail.content.forEach(topictype=>{
                topictype.list.forEach(item=>{
                    // 若批改分数为空，则说明该题未进行批改（教师需要手动赋分）
                    if(item.correctScore === null || item.correctScore === undefined || item.correctScore === ''){
                        throw new Error(this.lang.enter_correct_number);
                    }
                    const intScore = Number(item.correctScore);
                    if (isNaN(intScore) || intScore > Number(item.score) || intScore < 0) {
                        throw new Error(this.lang.enter_correct_number);
                    }
                    scoreDetail.push({
                        id:item.id,
                        score:intScore,
                    })
                    score += intScore;
                })
            })
            return {scoreDetail,score};
        },
        async uploadStart(index,j_index,subIndex){
            await Tool.queryAppPermissions(['CAMERA'])
            this.uploadIndex = [index,j_index,subIndex];
            this.$refs.uploadComponent[0].value = '';   //处理无法连续上传相同文件
            this.$refs.uploadComponent[0].click();
        },
        handleFileChange(e){
            let files = e.target.files
            if (!files || files.length === 0) {
                return;
            }
            // 创建要上传的文件队列
            let uploadQueue = [];

            for (const file of files) {
                if (file.size > 20 * 1024 * 1024) {
                    Toast(`${this.lang.upload_max_text}20M`)
                    return
                }
                if (file.size == 0) {
                    Toast(`${this.lang.upload_min_text}0M`)
                    return
                }

                // 检查文件格式
                const fileExt = file.name.split('.').pop().toLowerCase()
                // const isImage = file.type.startsWith('image/')
                // const isVideo = file.type.startsWith('video/')

                //Chromeium类浏览器flv识别有问题
                // console.log('fileType',file.type.split('/')[0])
                // console.log('fileExt',fileExt)
                if (!Tool.getSupportVideoType().includes(fileExt) && !Tool.getSupportImageType().includes(fileExt)) {
                    Toast(this.lang.supply_exam_image.err_tip.unsupported_file_format)
                    return
                }
                uploadQueue.push(file);
            }

            if (uploadQueue.length > 0) {
                Toast.loading({
                    message: this.lang.uploading,
                    forbidClick: true,
                    duration: 60000 // 设置60秒上限时间
                });
                // 确保主屏幕连接成功后再上传
                // Tool.handleAfterMainScreenCreated().then(() => {
                // setTimeout(() => {
                this.processUploadQueue(uploadQueue, this.uploadIndex);
                // }, 0);
                // })
                // }).catch(error => {
                //     Toast.clear();
                //     Toast(this.lang.network_error || '网络连接失败，请稍后重试');
                // });
            }
        },

        async processUploadQueue(queue, uploadIndex, currentIndex = 0) {
            await Tool.handleAfterMainScreenCreated();
            if (currentIndex >= queue.length) {
                Toast.clear();
                Toast.success(this.lang.upload_success);
                return;
            }

            // 处理当前文件
            const file = queue[currentIndex];
            this.uploadCollectImage(file, uploadIndex, () => {
                // 上传完成后处理下一个文件
                this.processUploadQueue(queue, uploadIndex, currentIndex + 1);
            });
        },

        uploadCollectImage(file, uploadIndex, callback){
            let dir = new Date().getTime() + parseInt(Math.random() * 1000 + 1000, 10)  // 目录
            var date = new Date();
            var time = date.getFullYear()
                + '-'
                + ((date.getMonth() < 9) ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1))
                + '-'
                + (date.getDate()  < 10 ? ('0' + date.getDate()) : date.getDate());
            let filePath = `homework/operationImage/${time}/${dir}/${file.name}`
            let uploadConfig = this.systemConfig.serverInfo.file_upload_config;
            //OSS
            uploadFile({
                bucket:uploadConfig.ossInfo.bucket,
                filePath:filePath,
                file,
                callback:(event, data)=>{
                    console.log('uploadFile',event,data)
                    if ("complete" == event) {
                        let url=uploadConfig.ossInfo.playback_https_addr+'/'+filePath
                        const index = uploadIndex[0]
                        const j_index = uploadIndex[1]
                        const subIndex = uploadIndex[2]
                        const msg_type = Tool.getMsgType(filePath);
                        this.examDetail.content[index].list[j_index].subTopic[subIndex].value.push({
                            msg_type:msg_type,
                            url,
                        });

                        if (this.type === 2) {
                            const currentTopic = this.examDetail.content[index].list[j_index];
                            this.updateProgress(currentTopic.index, true);
                        }

                        if (typeof callback === 'function') {
                            callback();
                        }
                    } else if ("error" == event) {
                        Toast(this.lang.upload_file_error_text);
                        // 即使出错也继续处理下一个文件
                        if (typeof callback === 'function') {
                            callback();
                        }
                    }
                }
            })
        },
        correctSelect(topic,type){
            if (type === 0) {
                topic.correctScore = 0;
            }else if (type === 1) {
                topic.correctScore = topic.score;
            }

            this.updateProgress(this.topicStep, true);
        },
        correctPicker(topic){
            this.pickerTopic = topic;
            let arr = [];
            for(let index =0 ; index <= topic.score;index++){
                arr.push(index);
            }
            this.columns = arr;
            this.showPicker = true;
        },
        openCollect(index,j_index,subIndex,subtopic){
            try{
                const [index,j_index,subIndex] = this.collectIndex;
                this.examDetail.content[index].list[j_index].subTopic[subIndex].collecting=false;
            }catch(e){} finally{
                this.collectIndex = [index,j_index,subIndex];
                window.vm.$set(subtopic,'collecting',true);
                this.openFileTransfer();
            }
        },
        closeCollect(index,j_index,subIndex,subtopic){
            this.collectIndex = [0,0,0];
            window.vm.$set(subtopic,'collecting',false);
            this.closeFileTransferListener();
        },
        openFileTransfer:async function(){
            const service_type=this.systemConfig.ServiceConfig.type.FileTransferAssistant
            let fileTransferAssistant=await findServiceId(service_type)
            if (fileTransferAssistant.cid) {
                this.openConversation(fileTransferAssistant.cid,13,(is_succ,conversation)=>{
                    this.openFileTransferListener(conversation.id)
                });
            }else{
                this.$root.socket.emit("request_start_single_chat_conversation",{
                    list:[fileTransferAssistant.id,this.user.uid],
                    start_type:undefined,
                    mode:this.systemConfig.ConversationConfig.mode.Single,
                    type:this.systemConfig.ConversationConfig.type.Single
                },async(is_succ,data)=>{
                    if (is_succ) {
                        this.$store.commit('conversationList/initConversation',data)
                        this.$store.commit('examList/initExamObj',data)
                        await Tool.handleAfterConversationCreated(data,'openConversation')
                        this.openFileTransferListener(data)
                    }else{
                        this.$message.error(this.lang.start_conversation_error)
                    }
                })
            }
        },
        openFileTransferListener(cid){
            // 监听传输助手消息通知
            const controler = this.conversationList[cid].socket;
            if (!controler) {
                setTimeout(()=>{
                    this.openFileTransferListener(cid);
                },1000)
            }else{
                this.closeFileTransferListener();
                controler.on("other_say",this.collectImage);
                this.fileTransferAssistant = controler;
            }

        },
        closeFileTransferListener(){
            if (this.fileTransferAssistant) {
                this.fileTransferAssistant.off("other_say",this.collectImage)
                this.fileTransferAssistant = null;
            }
        },
        collectImage(messageList){
            console.log('collectImage1',messageList)
            const msg_type = this.systemConfig.msg_type;
            messageList.forEach(async (msg)=>{
                if (msg.msg_type === msg_type.Frame || msg.msg_type === msg_type.Cine || msg.msg_type === msg_type.EXAM_IMAGES || msg.msg_type === msg_type.OBAI) {
                    //只采集单帧，多帧和聚合消息,和产科质控
                    let type =msg.msg_type;
                    if (msg.msg_type === msg_type.EXAM_IMAGES) {
                        type = msg.cover_msg_type;
                    }
                    let downloadUrl = '';
                    if(type === msg_type.Frame) {
                        downloadUrl=msg.url.replace("thumbnail.jpg",`SingleFrame.${msg.img_encode_type}`);
                    }else if (type === msg_type.Cine) {
                        downloadUrl=msg.url.replace("thumbnail.jpg",`DeviceVideo.${msg.img_encode_type}`);
                    }else if (type === msg_type.OBAI) {
                        downloadUrl=msg.url.replace("thumbnail.jpg",`ScreenShot.${msg.img_encode_type}`);
                    }
                    if (downloadUrl) {
                        const response = await fetch(downloadUrl);
                        const blob = await response.blob();
                        const file = new File([blob], `${msg.file_id}.${msg.img_encode_type}`, {
                            type: blob.type, // 你可以从blob中获取MIME类型
                        });
                        this.uploadCollectImage(file,this.collectIndex)
                        console.log('collectImage',file)
                    }
                }
            })
        },
        changeStep(step){
            this.topicStep+=step;
            let topicLength = 0;
            let topicTypeIndex = 0;
            for(let topictype of this.examDetail.content){
                topicLength +=topictype.list.length;
                if (this.topicStep <= topicLength) {
                    this.topicTypeStep = topicTypeIndex+1;
                    break
                }
                topicTypeIndex++;
            }
            this.updateProgressStatus();
        },
        onConfirm(value){
            this.pickerTopic.correctScore = value;
            this.showPicker = false;

            // 更新批改进度
            this.updateProgress(this.topicStep, true);
        },
        onCancel(){
            this.showPicker = false;
        },
        toggleCheckbox(list,label){
            if (this.disableModify) {
                return;
            }
            const index =list.indexOf(label)
            if (index>-1) {
                list.splice(index,1)
            }else{
                list.push(label);
            }

            // 更新进度
            this.updateProgress(this.topicStep, list.length > 0);
        },
        toggleRadio(topic,option){
            if (this.disableModify) {
                return;
            }
            topic.value = option.label;

            // 更新进度
            this.updateProgress(this.topicStep, true);
        },
        jumpToQuestion(step) {
            this.topicStep = step;
            let topicLength = 0;
            let topicTypeIndex = 0;
            for(let topictype of this.examDetail.content){
                topicLength +=topictype.list.length;
                if (this.topicStep <= topicLength) {
                    this.topicTypeStep = topicTypeIndex+1;
                    break
                }
                topicTypeIndex++;
            }
            // 更新进度状态
            this.updateProgressStatus();
        },
        getTotalScore() {
            if (this.type !== 3 && this.type !== 4) {
                return 0;
            }

            let totalScore = 0;
            try {
                this.examDetail.content.forEach(topictype => {
                    topictype.list.forEach(item => {
                        if (item.correctScore) {
                            totalScore += parseInt(item.correctScore);
                        }
                    });
                });
            } catch(e) {
                console.error(e);
            }
            return totalScore;
        },
        initProgressList() {
            this.progressList = [];
            let index = 0;

            this.examDetail.content.forEach(topictype => {
                topictype.list.forEach(item => {
                    let status = 'none';
                    let completed = false;

                    // 先判断题目是否已完成
                    if (this.type === 2) {
                        // 答题模式
                        if (topictype.type === 'operation') {
                            let hasValue = false;
                            item.subTopic.forEach(subTopic => {
                                if (subTopic.value && subTopic.value.length > 0) {
                                    hasValue = true;
                                }
                            });
                            completed = hasValue;
                        } else if (topictype.type === 'multiSelect') {
                            completed = item.value && item.value.length > 0;
                        } else if (topictype.type === 'shortAnswer') {
                            completed = item.value && item.value.trim() !== '';
                        } else {
                            completed = item.value !== '';
                        }
                    } else if (this.type === 3) {
                        // 批改模式
                        completed = item.hasOwnProperty('correctScore') && item.correctScore !== null;
                    }

                    // 然后判断是否是当前题目
                    if (item.index === this.topicStep) {
                        status = completed ? 'done' : 'current';
                    } else {
                        status = completed ? 'done' : 'none';
                    }

                    this.progressList.push({
                        index: item.index,
                        status: status,
                        completed: completed
                    });

                    index++;
                });
            });
        },
        updateProgressStatus() {
            if (!this.progressList.length) {
                return;
            }

            // 更新当前题目状态
            this.progressList.forEach((item, index) => {
                if (index + 1 === this.topicStep) {
                    item.status = 'current';  // 当前题目始终显示为current状态
                } else {
                    item.status = item.completed ? 'done' : 'none';
                }
            });
        },
        updateProgress(index, completed) {
            if (!this.progressList[index - 1]) {
                return;
            }

            this.progressList[index - 1].completed = completed;
            this.progressList[index - 1].status = index === this.topicStep ? 'current' : (completed ? 'done' : 'none');
        },
        updateShortAnswerProgress(topic) {
            this.updateProgress(topic.index, topic.value && topic.value.trim() !== '');
        },
    },
};
</script>
<style lang="scss">
.cloud_exam_detail {
    .cloud_exam_container{
        display: flex;
        flex-direction: column;
        .exam_detail_header{
            font-size: .7rem;
            padding: .4rem .6rem;
            background: #efefef;
        }
        .exam_detail_content{
            flex:1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .assign_homework{
                display: flex;
                padding: .3rem;
                button{
                    flex: 1;
                    margin: 0.3rem;
                    height: auto;
                    padding: .4rem;
                    border-radius: 0.8rem;
                    font-size: .8rem;
                }
            }
            .topic_summary{
                font-size: .7rem;
                padding: .4rem .6rem;
                background: #ebf8f5;
                display: flex;
                justify-content: space-between;
                font-size: .7rem;
            }
            .topic_type_content{
                flex:1;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }
            .topic_content{
                flex:1;
                overflow: auto;
            }
            .topic_detail{
                padding: .5rem;
                font-size: .8rem;
                .topic_title{
                    padding: .4rem 0;
                }
                .topic_images{
                    display: flex;
                    flex-wrap:wrap;
                    &>div{
                        width: 5rem;
                        height: 3.8rem;
                        background: #000;
                        border-radius: 0.3rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: .2rem;
                        overflow: hidden;
                    }
                    img{
                        max-width: 100%;
                        max-height: 100%;
                    }
                    .video{
                        width: 5rem;
                        height: 3.8rem;
                        background: #000;
                        margin: 0 2px;
                        cursor: pointer;
                        position: relative;
                        .icon-videoplay{
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            font-size: 38px;
                            color: #fff;
                            line-height: 1;
                            z-index: 2;
                            transform: translate(-50%, -50%);
                        }
                    }
                }
                .topic_radio{
                    margin: .5rem 0 ;
                    padding: 0 .2rem;
                    box-sizing: border-box;
                    .van-radio-group,.van-checkbox-group{
                        .check_container{
                            display: flex;
                            position: relative;
                            margin: 16px 0;
                            display: flex;
                            align-items: center;
                            position: relative;
                            cursor: pointer;
                        }
                    }
                    .van-icon{
                        display: none;
                    }
                    .van-radio__icon,.van-checkbox__icon{
                        position: absolute;
                        left: 1px;
                        top: 50%;
                        width: 1.5rem;
                        height: 1.5rem;
                        border-radius: 50%;
                        background: #fff;
                        border: 1px solid #eceff2;
                        padding: 0;
                        line-height: 1.5rem;
                        text-align: center;
                        transform: translateY(-50%);
                    }
                    .van-radio__label,.van-checkbox__label{
                        position: absolute;
                        width: 1.5rem;
                        height: 1.5rem;
                        line-height: 1.5rem;
                        text-align: center;
                        margin-left: 0;
                    }
                    .van-radio + span,.van-checkbox + span{
                        background: #ebeff2;
                        flex:1;
                        padding: .25rem 1rem .25rem 2rem;
                        border-radius: 1rem;
                        line-height: 1rem;
                        color: #333;
                        border: 1px solid #ebeff2;
                    }
                    .van-radio[aria-checked='true'],.van-checkbox[aria-checked='true']{
                        .van-radio__icon,.van-checkbox__icon{
                            background-color: #00c59d;
                        }
                        .van-radio__label,.van-checkbox__label{
                            color: #fff;
                        }
                        & + span{
                            background-color: #cdf3eb;
                        }
                    }
                    textarea{
                        color: #333;
                        width: 100%;
                        padding: .3rem;
                        box-sizing: border-box;
                        border-radius: .4rem;
                        background: #f3f6f9;
                        border: none;
                    }
                }
                .topic_description{
                    white-space: break-spaces;
                    margin: .3rem 0 ;
                    line-height: 1.6;
                    font-size: .7rem;
                    color: #666;
                    margin-bottom: 1rem;
                }
                .step_tip{
                    white-space: break-spaces;
                    margin: .3rem 0 ;
                    line-height: 1.6;
                    font-size: .7rem;
                }
                .subtopic_item{
                    margin: .5rem -.5rem;
                    background: #f3f6f9;
                    padding: .5rem;
                    .sub_title{
                        color: #00c59d;
                        margin: .5rem 0;
                    }
                    .sub_description{
                        white-space: pre-wrap;
                        color: #666;
                    font-size: .7rem;
                }
                .collect_btn{
                    text-align: center;
                    button{
                        height: auto;
                        padding: 0.4rem 1.2rem;
                        border-radius: 0.2rem;
                        font-size: 0.8rem;
                    }
                }
                .views_list{
                    display: flex;
                    margin: .5rem 0 ;
                    flex-wrap: wrap;
                    .views_item{
                        position: relative;
                        width: 5rem;
                        height: 3.8rem;
                        background: #000;
                        border-radius: 0.3rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: .2rem;
                        overflow: hidden;
                        &>img{
                            max-width: 100%;
                            max-height: 100%;
                        }
                        .video{
                            width: 100%;
                            height: 100%;
                            background: #000;
                            .icon-videofill{
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                font-size: 1.6rem;
                                color: #fff;
                                line-height: 1;
                                z-index: 2;
                                transform: translate(-50%, -50%);
                            }
                        }
                        .icon-plus1{
                            width: 2rem;
                            height: 2rem;
                            text-align: center;
                            line-height: 2rem;
                            border: 1px solid #00c59d;
                            border-radius: 50%;
                            color: #00c59d;
                            font-size: 1rem;
                        }
                        .icon-delete{
                            position: absolute;
                            top: .2rem;
                            right: .2rem;
                            color: #ff675c;
                        }
                        }
                    }
                }
                .correct_panel{
                    position: fixed;
                    left: 1rem;
                    bottom: 5rem;
                    display: flex;
                    flex-direction: row;
                    gap: 0.5rem;
                    padding: 0.5rem;
                    border-radius: 2rem;
                    background: rgba(255, 255, 255, 0.2);
                    backdrop-filter: blur(10px);
                    -webkit-backdrop-filter: blur(10px);
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    &>div{
                        width: 3rem;
                        height: 3rem;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: rgba(255, 255, 255, 0.3);
                        backdrop-filter: blur(5px);
                        -webkit-backdrop-filter: blur(5px);
                        text-align: center;
                        transition: all 0.3s ease;
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                    }
                    .full_score{
                        color: #00c59d;
                        border: 1px solid #00c59d;
                        &.active{
                            color: #fff;
                            background: rgba(0, 197, 157, 0.9);
                            backdrop-filter: blur(5px);
                            -webkit-backdrop-filter: blur(5px);
                            border: 1px solid #00c59d;
                        }
                    }
                    .empty_score{
                        color: #ff675c;
                        border: 1px solid #ff675c;
                        &.active{
                            color: #fff;
                            background: rgba(255, 103, 92, 0.9);
                            backdrop-filter: blur(5px);
                            -webkit-backdrop-filter: blur(5px);
                            border: 1px solid #ff675c;
                        }
                    }
                    .current_score{
                        color: #00c59d;
                        border: 1px solid #00c59d;
                        &.active{
                            color: #fff;
                            background: rgba(0, 197, 157, 0.9);
                            backdrop-filter: blur(5px);
                            -webkit-backdrop-filter: blur(5px);
                            border: 1px solid #00c59d;
                        }
                    }
                }
                .correct_score{
                    padding: .5rem .3rem;
                }
            }
        }
        .exam_detail_operation{
            display: flex;
            button{
                flex: 1;
                margin: 0.3rem;
                height: auto;
                padding: .4rem;
                border-radius: 0.8rem;
                font-size: .8rem;
            }
        }
    }
}
</style>
