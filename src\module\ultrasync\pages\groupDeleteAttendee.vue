<template>
    <transition name="slide">
        <div class="group_delete_attendee_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.groupset_delete_attendee}}({{attendeeArray.length}})
                </template>
                <template #right>
                    <span class="delete_attendee_btn" @click="submit" :class="{enable:enable}">{{lang.confirm_txt}}({{groupUser.length}})</span>
                </template>
            </mrHeader>
            <div class="group_setting_container">
                <ContactSelectList :optionList="checkOption" v-model="groupUser" v-if="activatedComponent"></ContactSelectList>
            </div>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import ContactSelectList from "../components/contactSelectList.vue";
export default {
    mixins: [base],
    name: 'group_setting_delete_attendee',
    components: {
        ContactSelectList
    },
    data(){
        return {
            cid:this.$route.params.cid,
            groupUser:[],
            activatedComponent:true
        }
    },
    activated(){
        this.groupUser=[]
        this.cid=this.$route.params.cid
        this.activatedComponent = true
    },
    deactivated(){
        this.activatedComponent = false
    },
    beforeDestroy(){
    },
    mounted(){
        this.$nextTick(()=>{
        })
    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{}
        },
        attendeeList(){
            return this.conversation.attendeeList
        },
        attendeeArray(){
            let arr=[];
            for(let key in this.attendeeList){
                if (this.attendeeList[key].attendeeState!=0) {
                    arr.push(this.attendeeList[key])
                }
            }
            return arr
        },
        checkOption(){
            var arr=[]
            for(let item of this.parseObjToArr(this.attendeeList)){
                let option={}
                option.label=item.nickname
                option.value=item.userid
                option.avatar=item.avatar
                if (this.conversation.creator_id !== this.user.uid && item.role > 0 ) {
                    option.disabled=true
                }
                if ((item.userid != this.user.id) && (item.attendeeState != 0)) {
                    arr.push(option)
                }

            }
            return arr;
        },
        enable(){
            return this.groupUser.length>0
        },
    },
    methods:{
        submit(){
            if (this.enable) {
                this.back();
                setTimeout(()=>{
                    let list=[];
                    let currentGroupUser = []
                    Object.keys(this.attendeeList).map(item=>{
                        if(this.attendeeList[item].attendeeState===1){
                            currentGroupUser.push({uid:this.attendeeList[item].userid, avatar:this.attendeeList[item].avatar})
                        }
                    })
                    console.log("before:",currentGroupUser)
                    for(let user of this.groupUser){
                        list.push({uid:user});
                    }
                    currentGroupUser=currentGroupUser.map(item=>{
                        for(let i = 0;i<list.length;i++){
                            if(item.uid === list[i].uid){
                                return null
                            }
                        }
                        return item
                    }).filter(item=>item)
                    console.log("after:",currentGroupUser)
                    var data = {
                        attendees: list,
                        isActiveQuit: 0,
                    };
                    this.conversation.socket.emit('request_delete_attendees', data, (is_succ, info)=>{
                        console.log("callback request_delete_attendees");
                        if(is_succ){
                            this.$root.eventBus.$emit('createGroupAvatar',{conversation:this.conversation, userList:currentGroupUser})
                        }else{
                            console.log(info);
                            Toast(this.lang.user_exit_group_fail);
                        }
                    });
                },300)
            }
        }
    }
}

</script>
<style lang="scss">
.group_delete_attendee_page{
    .choose_list{
        background:#fff;
        .group_user_item{
            padding:0.5rem;
        }
    }
    .group_setting_container{
        flex:1;
        overflow: hidden;
    }
    .delete_attendee_btn{
        width: 4rem;
        color: #fff;
        opacity: 0.6;
        font-size: 0.8rem;
        text-align: right;
        &.enable{
            opacity:1;
        }
    }
}
</style>
