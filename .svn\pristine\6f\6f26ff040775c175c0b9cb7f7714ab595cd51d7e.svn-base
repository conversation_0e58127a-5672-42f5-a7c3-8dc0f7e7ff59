<template>
    <div class="student-overview">
        <!-- 顶部统计图表区域 -->
        <div class="statistics-section">
            <!-- 当两个数组都没有数据时显示暂无数据 -->
            <div v-if="totalStudentsData.length === 0 && passedStudentsData.length === 0" class="no-data-container">
                <no-data :text="lang.no_data"></no-data>
            </div>
            <!-- 其他情况显示图表 -->
            <div v-else class="chart-container">
                <!-- 左侧：学员总人数环形图 -->
                <div class="chart-item">
                    <div class="chart-wrapper">
                        <!-- 新的 HTML 结构 -->
                        <div class="echart-custom-container">
                            <div class="custom-outer-ring-background"></div>
                            <div class="custom-inner-ring-display"></div>
                            <div ref="totalStudentsChart" class="chart"></div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-items">
                            <div v-for="(item, index) in totalStudentsData" :key="index" class="legend-item"
                                @click="handleLegendClick('totalStudents', index)"
                                :class="{ 'legend-item-disabled': !totalStudentsLegendStatus[index] }">
                                <div class="legend-item-left">
                                    <span class="legend-dot"
                                        :class="{ 'legend-dot-disabled': !totalStudentsLegendStatus[index] }" :style="{
                                            backgroundColor: chartColors[index]
                                        }"></span>
                                    <span class="legend-label"
                                        :class="{ 'text-disabled': !totalStudentsLegendStatus[index] }">{{ item.name
                                        }}</span>
                                </div>
                                <div class="legend-item-right">
                                    <p class="legend-value"
                                        :class="{ 'text-disabled': !totalStudentsLegendStatus[index] }">{{ item.value
                                        }}/{{ item.percentage }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：已通过人数环形图 -->
                <div class="chart-item">
                    <div class="chart-wrapper">
                        <div class="echart-custom-container">
                            <div class="custom-outer-ring-background"></div>
                            <div class="custom-inner-ring-display"></div>
                            <div ref="passedStudentsChart" class="chart"></div>
                        </div>
                    </div>
                    <div class="chart-legend">
                        <div class="legend-items">
                            <div v-for="(item, index) in passedStudentsData" :key="index" class="legend-item"
                                @click="handleLegendClick('passedStudents', index)"
                                :class="{ 'legend-item-disabled': !passedStudentsLegendStatus[index] }">
                                <div class="legend-item-left">
                                    <span class="legend-dot"
                                        :class="{ 'legend-dot-disabled': !passedStudentsLegendStatus[index] }" :style="{
                                            backgroundColor: chartColors[index]
                                        }"></span>
                                    <span class="legend-label"
                                        :class="{ 'text-disabled': !passedStudentsLegendStatus[index] }">{{ item.name
                                        }}</span>
                                </div>
                                <div class="legend-item-right">
                                    <p class="legend-value"
                                        :class="{ 'text-disabled': !passedStudentsLegendStatus[index] }">{{ item.value
                                        }}/{{ item.percentage }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部学员表格区域 -->
        <div class="table-section">
            <div class="table-header">
                <el-button type="primary" size="small" @click="handleExport" class="ai-theme-background export-button">
                    {{ lang.export_comment }}
                </el-button>
                <div class="query-form-container">
                    <el-form :inline="true" :model="studentFilters" class="custom-query-form">
                        <el-form-item :label="lang.full_name">
                            <el-input v-model="studentFilters.studentName" :placeholder="lang.input_enter_tips" size="small"></el-input>
                        </el-form-item>
                        <el-form-item :label="lang.place_of_work">
                            <el-input v-model="studentFilters.hospital" :placeholder="lang.input_enter_tips" size="small"></el-input>
                        </el-form-item>
                        <el-form-item :label="lang.did_it_pass">
                            <el-select v-model="studentFilters.finalStatus" :placeholder="lang.input_select_tips" size="small" clearable
                                style="width: 160px;">
                                <el-option v-for="item in finalStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" size="small" @click="handleSearch" class="ai-theme-background"
                                style="width: 120px;">{{ lang.query_btn }}</el-button>
                            <el-button size="small" @click="handleReset" style="width: 120px;">{{ lang.register_reset_btn }}</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <div class="student-table">
                <el-table :data="studentTableData" style="width: 100%; height: 100%;" border stripe
                    :header-cell-style="{ background: '#D7DFE1', color: '#000' }" v-loading="loadingStudents">
                    <el-table-column prop="account" :label="lang.register_account" min-width="100"></el-table-column>
                    <el-table-column prop="name" :label="lang.full_name" min-width="100"></el-table-column>
                    <el-table-column prop="hospital" :label="lang.place_of_work" min-width="150"
                        show-overflow-tooltip></el-table-column>
                    <el-table-column prop="supervisorName" label="Supervisor" min-width="120">
                        <template slot-scope="scope">
                            <template v-if="scope.row.supervisorList && scope.row.supervisorList.length > 0">
                                <el-tag
                                    v-for="(supervisor, index) in scope.row.supervisorList"
                                    :key="index"
                                    size="small"
                                    type="info"
                                    class="supervisor-tag"
                                    style="margin-right: 5px; margin-bottom: 5px;"
                                >
                                    {{ supervisor.nickname || '' }}
                                </el-tag>
                            </template>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createdAt" :label="lang.admin_register_date" min-width="120">
                        <template slot-scope="scope">
                            {{ formatTime(scope.row.createdAt) }}
                        </template>
                    </el-table-column>

                    <el-table-column v-for="column in assessmentColumns" :key="column.testID" :label="column.title"
                        min-width="120">
                        <template slot-scope="scope">
                            <div class="status-tag"
                                :class="getAssessmentStatusTagClass(scope.row['assessment_status_' + column.testID])"
                                @click="handleAssessmentCellClick(scope.row, column)" style="cursor: pointer;">
                                <span>{{ scope.row['assessment_' + column.testID] }}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="finalStatus" :label="lang.did_it_pass" min-width="100">
                        <template slot-scope="scope">
                            <div v-if="getFinalStatusText(scope.row.finalStatus)" class="status-tag" :class="getRequiredStatusTagClass(scope.row.finalStatus)">
                                <span>{{ getFinalStatusText(scope.row.finalStatus) }}</span>
                            </div>
                        </template>
                    </el-table-column>

                    <el-table-column prop="passTime" :label="lang.passed_date" min-width="120">
                        <template slot-scope="scope" v-if="scope.row.passTime">
                            {{ formatTime(scope.row.passTime *1000) }}
                        </template>
                    </el-table-column>
                </el-table>

            </div>

            <div class="pagination-container">
                <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page="studentFilters.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="studentFilters.pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="studentFilters.total"></el-pagination>
            </div>
        </div>
       <router-view></router-view>
    </div>
</template>

<script>
import * as echarts from "echarts";
import service from "../../../service/service";
import { SMART_TECH_TRAINING_TEST_CORRECT_STATUS, SMART_TECH_TRAINING_FINAL_STATUS,SMART_TECH_TRAINING_TEST_TYPE,SMART_TECH_TRAINING_STUDENT_STATUS } from "@/module/ultrasync_pc/lib/constants";
import base from "@/module/ultrasync_pc/lib/base";
import Tool from "@/common/tool";
import { cloneDeep } from "lodash";
import noData from "@/module/ultrasync_pc/MRComponents/noData.vue";
export default {
    name: "studenOverview",
    mixins: [base],
    components: {
        noData
    },
    data() {
        return {
            studentFilters: {
                studentName: '',
                hospital: '',
                finalStatus: '', // 初始为空，或设为 '全部'
                currentPage: 1,
                pageSize: 10,
                total: 0,
            },
            totalStudentsChart: null,
            passedStudentsChart: null,
            chartColors: ["#4F9DFF", "#52C41A", "#FADB14", "#FF4D4F", "#9254DE", "#13C2C2"],
            // 存储resize事件处理函数的引用，便于移除
            resizeHandler: {
                totalStudents: null,
                passedStudents: null
            },
            chartsInitialized: false, // 追踪图表是否已初始化

            // 学员总人数数据（将由API数据填充）
            totalStudentsData: [],
            // 学员总人数图例状态（true表示显示，false表示隐藏）
            totalStudentsLegendStatus: [],

            // 已通过人数数据（将由API数据填充）
            passedStudentsData: [],
            // 已通过人数图例状态（true表示显示，false表示隐藏）
            passedStudentsLegendStatus: [],
            assessmentColumns: [],
            studentTableData: [],
            trainingID: '',
            loadingStudents: false,
            // 缓存最后一次查询参数，用于导出功能
            lastSearchParams: {}
        };
    },
    watch: {
        $route: {
            handler(to) {
                if (to.name === "SmartTechTrainingStudentOverview") {
                    this.$nextTick(async() => {
                        this.trainingID = this.$route.params.trainingId;
                        // 在activated钩子中检查图表状态
                        this.getTrainingPiDashboard();
                        await this.getTrainingInfo();
                        await this.getTrainingStudentList();
                    });
                }
            },
            immediate: true,
        },
    },
    async activated() {

    },

    deactivated() {
        // 在组件被缓存但不显示时清理资源
        this.removeResizeListeners();
    },

    beforeDestroy() {
        console.log('beforeDestroy');
        // 组件被彻底销毁前清理资源
        this.removeResizeListeners();
        this.disposeCharts();
    },
    computed: {
        FINAL_STATUS_CHINESE_MAP(){
            return {
                'UNCOMPLETED': this.lang.incomplete,
                'PENDING': this.lang.case_exam_status.submited,
                'PASSED': this.lang.passed,
                'FAILED': this.lang.not_pass,
            }
        },
        finalStatusOptions() {
            const options = [];
            const numericKeys = Tool.extractNumericKeys(SMART_TECH_TRAINING_FINAL_STATUS);
            for (const key in numericKeys) {
                const value = numericKeys[key];
                options.push({
                    value: key,
                    label: this.FINAL_STATUS_CHINESE_MAP[value] || value
                });
            }
            options.unshift({
                value: -1,
                label: this.lang.exam_status['-1']
            });
            return options.sort((a, b) => Number(a.value) - Number(b.value));
        }
    },
    methods: {
        /**
         * 初始化所有图表
         */
        initCharts() {
            this.initTotalStudentsChart();
            this.initPassedStudentsChart();
        },

        /**
         * 销毁所有图表实例
         */
        disposeCharts() {
            if (this.totalStudentsChart) {
                this.totalStudentsChart.dispose();
                this.totalStudentsChart = null;
            }
            if (this.passedStudentsChart) {
                this.passedStudentsChart.dispose();
                this.passedStudentsChart = null;
            }
        },

        /**
         * 重新调整所有图表大小
         */
        resizeCharts() {
            if (this.totalStudentsChart) {
                this.totalStudentsChart.resize();
            }
            if (this.passedStudentsChart) {
                this.passedStudentsChart.resize();
            }
        },

        /**
         * 使用新数据更新图表
         */
        updateChartsWithNewData() {
            // 更新学员总人数图表
            if (this.totalStudentsChart && this.totalStudentsData.length > 0) {
                const totalStudents = this.calculateTotal(this.totalStudentsData, this.totalStudentsLegendStatus);

                const seriesData = this.totalStudentsData.map((item, index) => ({
                    ...item,
                    itemStyle: {
                        color: this.chartColors[index % this.chartColors.length],
                        borderColor: "#fff",
                        borderWidth: 2,
                    }
                }));

                this.totalStudentsChart.setOption({
                    title: {
                        subtext: totalStudents.toString()
                    },
                    series: [{
                        data: seriesData
                    }]
                });
            }

            // 更新已通过人数图表
            if (this.passedStudentsChart && this.passedStudentsData.length > 0) {
                const passedStudents = this.calculateTotal(this.passedStudentsData, this.passedStudentsLegendStatus);

                const seriesData = this.passedStudentsData.map((item, index) => ({
                    ...item,
                    itemStyle: {
                        color: this.chartColors[index % this.chartColors.length],
                        borderColor: "#fff",
                        borderWidth: 2,
                    }
                }));

                this.passedStudentsChart.setOption({
                    title: {
                        subtext: passedStudents.toString()
                    },
                    series: [{
                        data: seriesData
                    }]
                });
            }
        },

        /**
         * 移除所有resize事件监听器
         */
        removeResizeListeners() {
            if (this.resizeHandler.totalStudents) {
                window.removeEventListener('resize', this.resizeHandler.totalStudents);
                this.resizeHandler.totalStudents = null;
            }
            if (this.resizeHandler.passedStudents) {
                window.removeEventListener('resize', this.resizeHandler.passedStudents);
                this.resizeHandler.passedStudents = null;
            }
        },
        /**
         * 将API返回的数据转换为图表期望的格式
         * @param {Array} apiData - API返回的医院数据数组
         * @param {number} total - 总数
         * @returns {Array} 转换后的图表数据
         */
        transformApiDataToChartFormat(apiData, total) {
            if (!apiData || !Array.isArray(apiData) || apiData.length === 0) {
                return [];
            }

            return apiData.map(item => ({
                name: item._id === '其他' ? this.lang.unknown : item._id,
                value: item.count || 0,
                percentage: total > 0 ? Math.round((item.count / total) * 100) : 0
            }));
        },

        getTrainingPiDashboard(){
            return new Promise((resolve, reject) => {
                service.getTrainingPiDashboard({
                    trainingID: this.trainingID
                }).then(result=>{
                    const res = result.data;
                    if (res.error_code === 0) {
                        const apiData = res.data;

                        // 转换学员总人数数据
                        this.totalStudentsData = this.transformApiDataToChartFormat(
                            apiData.studentsByHospital,
                            apiData.totalStudents
                        );

                        // 转换已通过人数数据
                        this.passedStudentsData = this.transformApiDataToChartFormat(
                            apiData.passedByHospital,
                            apiData.totalPassed
                        );

                        // 重新初始化图例状态数组
                        this.totalStudentsLegendStatus = this.totalStudentsData.map(() => true);
                        this.passedStudentsLegendStatus = this.passedStudentsData.map(() => true);

                        // 数据获取成功后，强制重新初始化图表
                        this.$nextTick(() => {
                            // 先清理现有图表
                            this.disposeCharts();
                            // 重新初始化图表
                            if (this.totalStudentsData.length > 0 || this.passedStudentsData.length > 0) {
                                this.initCharts();
                                this.chartsInitialized = true;
                            }
                        });
                        resolve(res);
                    }else{
                        reject(res);
                    }
                }).catch(error => {
                    console.error('获取培训数据失败:', error);
                    // 保持默认数据，不做任何更改
                    reject(error);
                });
            })
        },
        /**
         * 处理图例点击事件
         * @param {string} chartType - 图表类型（'totalStudents' 或 'passedStudents'）
         * @param {number} index - 点击的图例索引
         */
        handleLegendClick(chartType, index) {
            if (chartType === 'totalStudents') {
                // 切换图例状态，使用Vue.set确保响应式更新
                this.$set(this.totalStudentsLegendStatus, index, !this.totalStudentsLegendStatus[index]);

                // 只获取当前激活的图例数据
                const activeData = this.totalStudentsData
                    .filter((_, i) => this.totalStudentsLegendStatus[i])
                    .map((item, i) => {
                        // 找出原始数组中的颜色索引
                        const originalIndex = this.totalStudentsData.findIndex(d => d.name === item.name);
                        return {
                            ...item,
                            itemStyle: {
                                color: this.chartColors[originalIndex],
                                borderColor: "#fff",
                                borderWidth: 2,
                            }
                        };
                    });

                // 计算新的总人数
                const totalStudents = this.calculateTotal(this.totalStudentsData, this.totalStudentsLegendStatus);

                // 完全更新图表数据
                if (this.totalStudentsChart) {
                    this.totalStudentsChart.setOption({
                        title: {
                            subtext: totalStudents.toString()
                        },
                        series: [{
                            data: activeData
                        }]
                    });
                }
            } else if (chartType === 'passedStudents') {
                // 切换图例状态，使用Vue.set确保响应式更新
                this.$set(this.passedStudentsLegendStatus, index, !this.passedStudentsLegendStatus[index]);
                console.log('图例状态更新：', this.passedStudentsLegendStatus);

                // 强制刷新页面
                this.$forceUpdate();

                // 只获取当前激活的图例数据
                const activeData = this.passedStudentsData
                    .filter((_, i) => this.passedStudentsLegendStatus[i])
                    .map((item, i) => {
                        // 找出原始数组中的颜色索引
                        const originalIndex = this.passedStudentsData.findIndex(d => d.name === item.name);
                        return {
                            ...item,
                            itemStyle: {
                                color: this.chartColors[originalIndex],
                                borderColor: "#fff",
                                borderWidth: 2,
                            }
                        };
                    });

                // 计算新的已通过人数
                const passedStudents = this.calculateTotal(this.passedStudentsData, this.passedStudentsLegendStatus);

                // 完全更新图表数据
                if (this.passedStudentsChart) {
                    this.passedStudentsChart.setOption({
                        title: {
                            subtext: passedStudents.toString()
                        },
                        series: [{
                            data: activeData
                        }]
                    });
                }
            }
        },

        initPieChart(chartRef, titleText, titleSubtext, seriesName, seriesData, handlerName) {
            // 确保组件引用存在
            if (!this.$refs[chartRef]) {
                return null;
            }

            // 先尝试清理旧的实例
            const existingChart = this[handlerName === 'totalStudents' ? 'totalStudentsChart' : 'passedStudentsChart'];
            if (existingChart) {
                existingChart.dispose();
            }

            // 使用$refs初始化
            let chart = echarts.init(this.$refs[chartRef]);
            const option = {
                backgroundColor: "transparent",
                title: {
                    text: titleText,
                    subtext: titleSubtext,
                    left: "center",
                    top: "35%",
                    textStyle: {
                        fontSize: 11,
                        color: "#000000",
                        lineHeight: 14,
                        fontWeight: 700,
                        letterSpacing: "0.4px",
                        width: 110, // 限制文本宽度，确保在圆圈内
                        overflow: 'break', // 自动换行
                        textAlign: 'center', // 文本居中对齐
                    },
                    subtextStyle: {
                        fontSize: 28,
                        fontWeight: 400,
                        color: "#000000",
                        lineHeight: 32,
                        width: 110, // 限制数字宽度
                        textAlign: 'center', // 数字居中对齐
                    },
                },
                tooltip: {
                    trigger: "item",
                    formatter: function(params) {
                        // 使用与图例相同的四舍五入逻辑，确保精度一致
                        const percentage = Math.round(params.percent);
                        return `${params.name}: ${percentage}%`;
                    }
                },
                legend: { show: false },
                series: [
                    {
                        name: seriesName,
                        type: "pie",
                        radius: ["76.5%", "88%"],
                        center: ["50%", "50%"],
                        avoidLabelOverlap: false,
                        emphasis: {
                            focus: "series",
                            blurScope: "coordinateSystem",
                            itemStyle: {
                                borderWidth: 1,
                                borderColor: "#fff",
                                shadowBlur: 5,
                                shadowColor: "rgba(0,0,0,0.2)",
                            },
                        },
                        label: { show: false },
                        labelLine: { show: false },
                        itemStyle: { borderColor: "#fff", borderWidth: 2 },
                        data: seriesData.map((item, index) => ({
                            ...item,
                            itemStyle: {
                                color: this.chartColors[index],
                                borderColor: "#fff",
                                borderWidth: 2,
                            },
                        })),
                    },
                ],
            };
            chart.setOption(option);

            // 创建具名的resize处理函数并保存引用
            const resizeHandler = () => {
                if (chart) {
                    chart.resize();
                }
            };
            this.resizeHandler[handlerName] = resizeHandler;
            window.addEventListener("resize", resizeHandler);

            return chart;
        },

        /**
         * 计算总人数
         * @param {Array} data - 数据数组
         * @param {Array} status - 状态数组
         * @returns {number} - 激活项的总和
         */
        calculateTotal(data, status) {
            return data
                .filter((_, i) => status[i])
                .reduce((sum, item) => sum + item.value, 0);
        },

        initTotalStudentsChart() {
            // 计算当前激活的总人数
            const totalStudents = this.calculateTotal(this.totalStudentsData, this.totalStudentsLegendStatus);

            this.totalStudentsChart = this.initPieChart(
                "totalStudentsChart",
                this.lang.total_students_number,
                totalStudents.toString(),
                this.lang.student_distribution,
                this.totalStudentsData,
                "totalStudents"
            );
        },

        initPassedStudentsChart() {
            // 计算当前激活的已通过人数
            const passedStudents = this.calculateTotal(this.passedStudentsData, this.passedStudentsLegendStatus);

            this.passedStudentsChart = this.initPieChart(
                "passedStudentsChart",
                this.lang.number_of_people_passed,
                passedStudents.toString(),
                this.lang.already_distributed,
                this.passedStudentsData,
                "passedStudents"
            );
        },

        handleExport() {
            if (!this.trainingID) {
                console.error('training_id_cannot_be_empty');
                return;
            }

            // 使用缓存的查询参数构建导出参数
            const params = {
                ...this.lastSearchParams, // 包含最后一次查询的条件
                translateMap: {
                    StudentName:this.lang.full_name,
                    UserID:this.lang.student_id_number,
                    Hospital:this.lang.place_of_work,
                    RegistrationTime:this.lang.admin_register_date,
                    IsPass:this.lang.did_it_pass,
                    PassTime:this.lang.passed_date,
                    unSubmit:this.lang.not_submitted,
                    pass:this.lang.passed,
                    unPass:this.lang.not_pass,
                    waitCheck:this.lang.exam_status[2],
                    Supervisor:'Supervisor',
                    Account:this.lang.admin_login_name,
                },
                timezoneOffset: new Date().getTimezoneOffset(),
            };

            // 调用导出接口
            service.exportTrainingStudent(params)
                .then(result => {
                    const blob = new Blob([result.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                    let filename = `student_data_${new Date().toLocaleDateString()}.xlsx`;
                    const contentDisposition = result.headers['content-disposition'];
                    if (contentDisposition) {
                        const filenameMatch = contentDisposition.match(/filename="?(.+?)"?$/);
                        if (filenameMatch && filenameMatch.length > 1) {
                            filename = decodeURIComponent(filenameMatch[1]);
                        }
                    }

                    const url = window.URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.setAttribute('download', filename);
                    document.body.appendChild(link);
                    link.click();

                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                })
                .catch(err => {
                    // 尝试将blob错误响应解析为JSON
                    if (err.response && err.response.data instanceof Blob) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            try {
                                const errorData = JSON.parse(reader.result);
                                this.$message.error(this.lang.operate_failed);
                            } catch (e) {
                                this.$message.error('export_failed');
                            }
                        };
                        reader.onerror = () => {
                            this.$message.error(this.lang.operate_failed);
                        };
                        reader.readAsText(err.response.data);
                    } else {
                        console.error('export_failed:', err);
                        this.$message.error(this.lang.operate_failed);
                    }
                });
        },

        /**
         * 构建查询参数
         * @param {boolean} includePageInfo - 是否包含分页信息
         * @returns {Object} 构建的查询参数对象
         */
        buildSearchParams(includePageInfo = true) {
            const params = {
                trainingID: this.trainingID,
                status:SMART_TECH_TRAINING_STUDENT_STATUS.PASSED//只查询已认证的学员状态
            };

            // 添加查询条件
            if (this.studentFilters.studentName) {
                params.name = this.studentFilters.studentName;
            }
            if (this.studentFilters.hospital) {
                params.hospital = this.studentFilters.hospital;
            }
            if (this.studentFilters.finalStatus !== -1 && this.studentFilters.finalStatus !== '') {
                params.finalStatus = Number(this.studentFilters.finalStatus);
            }

            // 根据需要添加分页信息
            if (includePageInfo) {
                params.page = this.studentFilters.currentPage;
                params.pageSize = this.studentFilters.pageSize;
            }

            return params;
        },

        handleSizeChange(newSize) {
            this.studentFilters.pageSize = newSize;
            this.getTrainingStudentList();
        },

        handleCurrentChange(newPage) {
            this.studentFilters.currentPage = newPage;
            this.getTrainingStudentList();
        },



        getTrainingStudentList() {
            if (!this.trainingID) {
                this.$message.error('training_id_cannot_be_empty');
                return;
            }

            this.loadingStudents = true;
            // 使用新的参数构建方法
            const params = this.buildSearchParams(true);

            // 缓存查询参数（不包含分页信息，用于导出）
            this.lastSearchParams = this.buildSearchParams(false);


            // 调用API
            service.getTrainingStudentList(params)
                .then(result => {
                    const res = result.data;
                    if (res.error_code === 0) {
                        const studentData = res.data.data || [];
                        this.studentTableData = studentData.map(student => {
                            const testStatusMap = new Map(student.testList.map(t => [t.testID, {
                                status: t.status,
                                isPass: t.isPass
                            }]));
                            const newStudent = { ...student };

                            this.assessmentColumns.forEach(column => {
                                const testResult = testStatusMap.get(column.testID);
                                // 保存原始状态信息和格式化文本
                                newStudent['assessment_' + column.testID] = this.getAssessmentStatusText(testResult);
                                newStudent['assessment_status_' + column.testID] = testResult;
                            });
                            return newStudent;
                        });
                        this.studentFilters.total = res.data.total;
                    } else {
                        // this.$message.error('get_student_list_failed');
                    }
                })
                .catch(err => {
                    console.error('get_student_list_failed:', err);
                    this.$message.error('get_student_list_failed');
                })
                .finally(() => {
                    this.loadingStudents = false;
                });
        },

        getFinalStatusText(status) {
            // 只有明确是2（已通过）和3（不通过）时才展示对应的标签，否则不展示
            if (status === SMART_TECH_TRAINING_FINAL_STATUS.PASSED || status === SMART_TECH_TRAINING_FINAL_STATUS.FAILED) {
                const englishStatus = SMART_TECH_TRAINING_FINAL_STATUS[status] || '';
                return this.FINAL_STATUS_CHINESE_MAP[englishStatus] || englishStatus;
            }
            return '';
        },

        getAssessmentStatusText(testResult) {
            if (!testResult) {
                return this.lang.not_submitted;
            }
            const { status, isPass } = testResult;
            const { UNSUBMITTED, UNCORRECTED, CORRECTED } = SMART_TECH_TRAINING_TEST_CORRECT_STATUS;

            switch (status) {
            case UNSUBMITTED:
                return this.lang.not_submitted;
            case UNCORRECTED:
                return this.lang.exam_status[2];
            case CORRECTED:
                return isPass ? this.lang.passed : this.lang.not_pass;
            default:
                return '';
            }
        },

        getRequiredStatusTagClass(finalStatus) {
            const { PASSED, FAILED } = SMART_TECH_TRAINING_FINAL_STATUS;
            // 只有明确是2（已通过）和3（不通过）时才展示对应的样式，否则不展示
            switch (finalStatus) {
            case PASSED:
                return 'status-tag-passed-new';
            case FAILED:
                return 'status-tag-failed-new';
            default:
                return ''; // 其他状态不展示任何样式
            }
        },

        getAssessmentStatusTagClass(testResult) {
            if (!testResult) {
                return 'status-tag-unsubmitted-new';
            }
            const { status, isPass } = testResult;
            const { UNSUBMITTED, UNCORRECTED, CORRECTED } = SMART_TECH_TRAINING_TEST_CORRECT_STATUS;

            switch (status) {
            case UNSUBMITTED:
                return 'status-tag-unsubmitted-new';
            case UNCORRECTED:
                return 'status-tag-pending-new';
            case CORRECTED:
                return isPass ? 'status-tag-passed-new' : 'status-tag-failed-new';
            default:
                return 'status-tag-default-new';
            }
        },

        handleSearch() {
            this.studentFilters.currentPage = 1;
            this.getTrainingStudentList();
        },

        handleReset() {
            this.studentFilters = {
                studentName: '',
                hospital: '',
                finalStatus: '',
                currentPage: 1,
                pageSize: 10,
                total: this.studentFilters.total
            };
            this.getTrainingStudentList();
        },

        getTrainingInfo() {
            return new Promise((resolve, reject) => {
                service.getTrainingInfo({
                    trainingID: this.trainingID
                }).then(result => {
                    const res = result.data;
                    if (res.error_code === 0) {
                        this.assessmentColumns = res.data.testList || [];
                        resolve(res.data);
                    } else {
                        this.$message.error('get_training_info_failed');
                        reject(new Error(res.message || 'get_training_info_failed'));
                    }
                }).catch(err => {
                    console.error('get_training_info_failed:', err);
                    reject(err);
                });
            });
        },

        handleAssessmentCellClick(row, column) {
            console.log("当前行信息:", row);
            const testInfo = row.testList.find(test => test.testID === column.testID);
            console.log(`考核项 "${column.title}" 的信息:`, testInfo);
            console.log("进入批改:", row);
            if(testInfo.status === SMART_TECH_TRAINING_TEST_CORRECT_STATUS.UNSUBMITTED){
                return;
            }

            // 根据考核类型跳转到不同的批改页面
            if (testInfo.type === SMART_TECH_TRAINING_TEST_TYPE.ATTACHMENT_UPLOAD) {
                // 附件上传类型 - 跳转到附件批改页面
                Tool.loadModuleRouter({
                    name: 'SmartTechTrainingStudentOverview_CorrectingUploadTestOverview',
                    params: {
                        ...this.$route.params,
                        testId: testInfo.testID,
                        studentInfo:row
                    }
                });
            } else if (testInfo.type === SMART_TECH_TRAINING_TEST_TYPE.ONLINE_QUIZ) {
                // 在线答题类型 - 跳转到在线批改页面
                Tool.loadModuleRouter({
                    name: 'SmartTechTrainingStudentOverview_CorrectingOnlineTestOverview',
                    params: {
                        ...this.$route.params,
                        testId: testInfo.testID,
                        studentInfo:row
                    }
                });
            } else {
                // 未知类型，提示错误
                this.$message.error('unknown_test_type');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";

.student-overview {
    padding: 20px;
    background: #f5f5f5;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .statistics-section {
        background: #fff;
        border-radius: 8px;
        padding: 30px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        overflow-x: auto;
        flex-shrink: 0;

        .chart-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;

            .chart-item {
                flex: 1;
                display: flex;
                align-items: flex-start;
                gap: 20px;
                justify-content: center;

                .chart-wrapper {
                    .echart-custom-container {
                        position: relative;
                        width: 250px;
                        height: 250px;
                    }

                    .custom-outer-ring-background {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        border-radius: 50%;
                        background-color: #ffffff;
                        box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.44);
                        z-index: 1;
                    }

                    .custom-inner-ring-display {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 55%;
                        height: 55%;
                        border-radius: 50%;
                        background-color: #ffffff;
                        border: 1px solid #a9bfbe;
                        box-shadow: 0 0 10px 4px rgba(189, 189, 189, 0.3);
                        z-index: 2;
                    }

                    .chart {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: transparent !important;
                        box-shadow: none !important;
                        border-radius: 0 !important;
                        z-index: 3;
                    }
                }

                .chart-legend {
                    padding-left: 30px;
                    display: flex;
                    flex-direction: column;
                    height: 100%;

                    .legend-title {
                        font-size: 18px;
                        font-weight: 600;
                        color: #202226;
                        margin-bottom: 20px;
                    }

                    .legend-items {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-around;
                        height: 100%;

                        .legend-item {
                            display: flex;
                            align-items: center;
                            justify-content: flex-start;
                            width: 100%;
                            font-size: 16px;
                            color: #202226;
                            line-height: 24px;
                            font-weight: 400;
                            min-width: 300px;
                            max-width: 500px;
                            cursor: pointer;
                            transition: all 0.3s ease;

                            .legend-item-left {
                                flex: 7;
                                margin-right: 20px;
                                display: flex;
                                flex-wrap: nowrap;
                                align-items: center;
                            }

                            .legend-item-right {
                                flex: 3;
                                min-width: 102px;
                                max-width: 150px;
                                flex-shrink: 0;
                            }

                            .legend-dot {
                                width: 10px;
                                height: 10px;
                                border-radius: 3px;
                                margin-right: 10px;
                                flex-shrink: 0;
                                display: inline-block;
                            }

                            .legend-label {
                                color: #202226;
                                margin-right: auto;
                                display: -webkit-box;
                                -webkit-line-clamp: 2;
                                -webkit-box-orient: vertical;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                word-break: break-word;
                                transition: opacity 0.3s ease;
                            }

                            .legend-item-disabled {
                                opacity: 0.6;
                                filter: grayscale(80%);
                            }

                            .text-disabled {
                                color: #999;
                                text-decoration: line-through;
                            }

                            .legend-dot {
                                transition: all 0.3s ease;
                            }

                            .legend-dot-disabled {
                                opacity: 0.3;
                                filter: grayscale(100%);
                                box-shadow: none;
                                border: 1px dashed #999;
                            }

                            .legend-data {
                                color: #333;
                                font-weight: 500;

                            }

                            .legend-value {
                                text-align: left;
                                letter-spacing: 1px;
                            }
                        }
                    }
                }
            }
        }

        .no-data-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            width: 100%;
        }
    }

    .table-section {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        .table-header {
            flex-shrink: 0;

            .export-button {
                width: 120px;
                height: 36px;
                border-radius: 4px;
                font-size: 16px;
            }

            .query-form-container {
                padding: 15px;
                border-radius: 8px;
            }
        }

        .student-table {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            min-height: 500px;

            ::v-deep .el-table {
                flex: 1;
                display: flex;
                flex-direction: column;

                .el-table__header-wrapper {
                    flex-shrink: 0;
                }

                .el-table__body-wrapper {
                    flex: 1;
                    overflow-y: auto;
                }
            }
        }

        .pagination-container {
            flex-shrink: 0;
            padding: 15px 0;
            border-top: 1px solid #ebeef5;
            margin-top: 10px;
        }
    }
}

.custom-query-form .el-form-item {
    margin-bottom: 0;
    margin-right: 15px;
}

.custom-query-form .el-form-item:last-child {
    margin-right: 0;
}
</style>
