<template>
	<div class="register_welcome_page second_level_page">
        <mrHeader>
            <template #title>
                {{lang.register_title}}
            </template>
        </mrHeader>
        <div class="container">
            <div class="logo">
                <img src="static/resource/images/pure_logo.png">
                <p class="app_name">{{lang.app_name}}
                    <span v-if="systemConfig.server_type&&systemConfig.server_type.hostname">({{systemConfig.server_type.hostname}})</span>
                </p>
            </div>
            <div class="mobile_register_wraper">
                <button class="primary_bg common_btn" @click="gotoRegister(1)">{{lang.register_by_mobile}}</button>
            </div>
            <div class="other_register_wraper">
                <div class="other_register_title_wrap">
                    <p>{{lang.other_register_way}}</p>
                </div>
                <div class="other_register_item">
                    <i v-show="ifShowWeChatLogin" class="iconfont icon-dyndetailwechat" @click="wehcatRegister"></i>
                    <i class="iconfont icon-email" @click="gotoRegister(2)"></i>
                    <i v-show="!isOutlineShow" class="iconfont icon-qianbipencil83" @click="gotoRegister(3)"></i>
                </div>
            </div>
        </div>
        <router-view></router-view>
	</div>
</template>
<script>
import base from '../lib/base'
export default {
    mixins: [base],
    name:'register_welcome',
    data(){
        return {

        }
    },
    computed:{
        isOutlineShow(){
            return this.systemConfig.server_type.enable_sms_identification;
        },
        globalParams(){
            return this.$store.state.globalParams;
        },
        ifShowWeChatLogin(){
            return (this.isApp)&&this.osName!='ios'&&this.isShowWechat
        }
    },
    methods:{
        gotoRegister(type){
            this.$router.push(`/login/register_welcome/register/${type}`)
        },
        wehcatRegister(){
            window.CWorkstationCommunicationMng.wechatLogin();
            this.back();
        }
    }
}
</script>
<style lang="scss">
.register_welcome_page{
    .container{
        .logo{
            img{
                width: 6.5rem;
                margin: 0.3rem auto 0;
                display: block;
            }
            .app_name{
                font-size:0.9rem;
                color:#333;
                margin:0 0 1rem;
                text-align: center;
                span{
                    font-size:0.7rem;
                }
            }
        }
        .mobile_register_wraper{
            position:absolute;
            top:40%;
            left:1rem;
            right:1rem;
            transform:translateY(-50%);
        }
        .other_register_wraper{
            position: absolute;
            bottom: 1rem;
            left:0;
            right:0;
            padding: 0 2rem;
            .other_register_title_wrap{
                border-top:1px solid #333;
                display: flex;
                justify-content: center;
                p{
                    background:#fff;
                    transform: translateY(-50%);
                    font-size: 0.7rem;
                    color: #333;
                    padding: 0 1rem;
                }
            }
            .other_register_item{
                display: flex;
                justify-content: center;
                align-items: center;
                .iconfont{
                    color: #666;
                    line-height: 1;
                    margin: 0 0.5rem;
                }
                .icon-dyndetailwechat,.icon-email{
                    font-size: 2.5rem;
                }
                .icon-qianbipencil83{
                    font-size: 2.3rem;
                }
            }
        }
    }
}
</style>
