<template>
<div>
    <CommonDialog
      class="favorites"
      :title="lang.cloud_favorites"
      :show.sync="visible"
      :close-on-click-modal="false"
      width="660px"
      :modal="false"
      @closed="back"
      :footShow = "false"
      :append-to-body="false"
      >
        <div class="favorites_container" v-loading="!loadedFavorites">
            <div v-for="(group,g_index) of favoritesGroups" class="favorites_group_item clearfix" :key="group.favorite_id">
                <p class="group_subject">
                    {{formatTime(group.send_ts)}}
                    <i class="iconfont iconunlock" v-show="group.public_status>0" @click="changePublicStatus(group)"></i>
                    <i class="iconfont iconlock" v-show="group.public_status==0" @click="changePublicStatus(group)"></i>
                </p>

                <div class="clearfix">
                        <gallery-file-list
                        v-if="group.list"
                        :galleryList="group.list"
                        :span="4"
                        :from="`personalFavorite`"
                        @contextmenu="favoritesMenu($event,g_index)"
                        @openGallery="openGallery($event,g_index)"
                        ></gallery-file-list>
                </div>
            </div>
        </div>
        <router-view></router-view>
    </CommonDialog>
</div>

</template>
<script>
import base from '../lib/base'
import {parseImageListToLocal,cancelFavoriteCommit} from '../lib/common_base'
import GalleryFileList from '../components/galleryFileList.vue'
import service from '../service/service'
import moment from 'moment'
import CommonDialog from "../MRComponents/commonDialog.vue";

export default {
    mixins: [base],
    name: 'FavoritesPC',
    components: {
        GalleryFileList,
        CommonDialog
    },
    data(){
        return {
            favorites:[],
            loadedFavorites:false,
            sortedFavorites:[],
            visible: false,
        }
    },
    computed:{
        favoritesGroups(){
            return this.$store.state.userFavorites
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.visible = true;
            this.$root.eventBus.$off('deleteFavoritesImage').$on('deleteFavoritesImage',this.deleteFavoritesImage)
            this.getFavorites();
        })
    },
    methods:{
        getFavorites(){
            let controller=window.main_screen.controller;
            var that=this;
            controller.emit("query_user_favorites",null,function(is_succ,data){
                if (is_succ) {

                    that.loadedFavorites=true;
                    that.favorites=data;
                    parseImageListToLocal(that.favorites,'url')
                    that.parseFavorites();
                }else{
                    that.$message.error('get favorites error')
                }
            })
        },
        parseFavorites(){
            let groups={}
            for(let item of this.favorites){
                let cid=item.conversation_list[0].group_id;
                item.group_id=item.conversation_list[0].group_id;
                let public_status=item.public_status||0;
                let send_ts=item.send_ts;
                item.send_ts=send_ts;
                item.is_private=true;
                item.resource_id = item.favorite_id
                if (groups[send_ts]) {
                    groups[send_ts].list.push(item)
                }else{
                    groups[send_ts]={
                        cid:cid,
                        send_ts:item.send_ts,
                        list:[item],
                        public_status:public_status
                    }
                }
            }
            var tempArr=[]
            this.sortedFavorites=[];
            for(let key in groups){
                tempArr.push(groups[key])
                this.sortedFavorites=this.sortedFavorites.concat(groups[key].list)
            }
            this.$store.commit('userFavorites/setFavoritesGroups',tempArr)
        },
        openGallery(event,g_index){
            console.log(event,g_index,event.index)
            let index=this.getIndex(g_index,event.index);
            let file=this.sortedFavorites[index];
            console.log(file)
            //打开画廊
            this.$store.commit('gallery/setGallery',{
                list:this.sortedFavorites,
                openFile:file,
            })
            this.$nextTick(()=>{
                this.$router.push(this.$route.fullPath+'/gallery')
            })
        },
        getIndex(g_index,f_index){
            let index=0;
            for (var i = 0; i < g_index; i++) {
                index+=this.favoritesGroups[i].list.length
            }
            index+=parseInt(f_index);
            return index;
        },
        deleteFavoritesImage(file){
            console.log(file)
            let del_index_list=[];
            let g_index=file.g_index;
            let f_index=file.f_index;
            let target=this.favoritesGroups[g_index].list[f_index]
            let index=0;
            for (var i = 0; i < g_index; i++) {
                index+=this.favoritesGroups[i].list.length
            }
            index+=parseInt(f_index);
            del_index_list.push(index);
            this.loadedFavorites=true;
            cancelFavoriteCommit({
                resource_id:target.resource_copied_from,
                cid:target.group_id,
            },()=>{
                this.removeFavorites(del_index_list)
            })
        },
        removeFavorites(index_list){
            let removeNum=0;
            index_list=index_list.sort(function(a,b){
                return a-b
            })
            for(let index of index_list){
                let file=this.sortedFavorites[index-removeNum]
                console.log('delete',file.favorite_id)
                this.sortedFavorites.splice(index-removeNum,1);

                removeNum++;
            }
            let log=[]
            for(let file of this.sortedFavorites){
                log.push(file.favorite_id)
            }
            console.log('exist id',log)
            this.favorites=this.sortedFavorites;
            this.parseFavorites();
            this.loadedFavorites=true;
        },
        favoritesMenu($event,g_index){
            console.log($event,g_index)
            $event.file.g_index=g_index;
            $event.file.f_index=$event.file.index;

            this.callImageMenu($event.event,$event.file,'user_favorites')
        },
        changePublicStatus(group){
            let tip='';
            let targetStatus=0;
            if (group.public_status==0) {
                tip=this.lang.favorites_public_tip;
                targetStatus=1;
            } else if (group.public_status>0) {
                tip=this.lang.favorites_private_tip;
                targetStatus=0;
            }
            let userFavoriteIdList=[]
            for(let item of group.list){
                userFavoriteIdList.push(item.favorite_id);
            }
            this.$confirm(tip,this.lang.tip_title,{
                confirmButtonText:this.lang.confirm_button_text,
                cancelButtonText:this.lang.cancel_button_text,
                type:'info'
            }).then(()=>{
                service.updatePublicStatus({
                    userFavoriteIdList:userFavoriteIdList,
                    publicStatus:targetStatus
                }).then(res=>{
                    if (res.data.error_code==0) {
                        group.public_status=targetStatus;
                        for(let item of group.list){
                            item.public_status=targetStatus;
                        }
                    }
                })
            }).catch(()=>{})
        }
    }
}
</script>
<style lang="scss">
.favorites{
    .favorites_container{
        height:100%;
        overflow:auto;
        .favorites_group_item{
            margin: 10px 0;
            .group_subject{
                font-size: 16px;
                line-height: 2;
                .iconunlock,.iconlock{
                    margin-left: 6px;
                    color: #5f92f6;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>
