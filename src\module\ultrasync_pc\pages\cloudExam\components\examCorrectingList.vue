<template>
    <div class="correcting_exam_list" v-loading="loadingPage">
        <div class="search_container">
            <el-input
            :placeholder="lang.homework_search_key"
            prefix-icon="el-icon-search"
            @input="debounceSearch"
            v-model="searchKey"
            ></el-input>
        </div>
        <template  v-if="examList.length">
            <div class="cloud_exam_item" v-for="exam of examList" :key="exam._id">
                <div class="cloud_exam_item_card">
                    <span v-if="uncorrectedMap[exam._id]" class="unread"></span>
                    <p class="exam_title">{{exam.paperInfo.title}}</p>
                    <p class="exam_author">{{exam.paperInfo.author}}</p>
                    <div class="exam_detail" >
                        <div class="exam_type_icon">
                            <template v-if="exam.paperInfo.contentType">
                                <img class="title_icon" :src="`static/resource_pc/images/homework_type${exam.paperInfo.contentType}.png`">
                                <p>{{lang['homework_type'+exam.paperInfo.contentType]}}</p>
                            </template>
                            <template v-else>
                                <img class="title_icon" src="static/resource_pc/images/homework_type5.png">
                                <p>{{lang.homework_type5}}</p>
                            </template>
                            
                        </div>
                        <div class="exam_description">
                            <p>{{lang.paper_total_score}}：{{exam.paperInfo.score}}{{lang.point_tip}}</p>
                            <p>{{lang.paper_question_count}}：{{exam.paperInfo.questionCount}}</p>
                            <p>{{lang.release_time}}：{{exam.createdAt | showData}}</p>
                        </div>
                    </div>
                    <div class="operation_modal">
                        <el-button type="primary" size="small" @click="enterCorrecting(exam)">{{lang.enter_correction}}</el-button>
                    </div>
                </div>
            </div>
        </template>
        <no-data v-else :text="lang.no_data_txt"></no-data>
        <el-pagination
          v-show="examList.length"
          background
          :current-page="queryForm.pageNo"
          :layout="layout"
          :page-size="queryForm.pageSize"
          :total="examListTotal"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          style="margin-top:20px"
        />
    </div>
</template>
<script>
import base from '../../../lib/base';
import service from '../../../service/service.js'
import moment from 'moment';
import NoData from "../../../MRComponents/noData.vue";
import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    name: 'cloudExamList',
    components: {
        NoData,
    },
    data(){
        return {
            cid:0,
            layout: 'total, sizes, prev, pager, next, jumper',
            queryForm:{
                pageNo:1,
                pageSize:30,
            },
            examListTotal:0,
            examList:[],
            loadingPage:false,
            searchKey:'',
            debounceSearch:null,
        }
    },
    filters:{
        showData(ts){
            return moment(ts).format("YYYY-MM-DD HH:mm")
        }
    },
    computed:{
        uncorrectedMap(){
            if (this.cid) {
                return this.$store.state.homework.conversationUnCorrect[this.cid] || {}
            }else{
                return this.$store.state.homework.globalUnCorrect || {}
            }
        }
    },
    created(){
        this.cid = parseInt(this.$route.params.cid);
        this.debounceSearch = Tool.debounce(this.fetchData,600);
    },
    mounted(){
        this.fetchData();
    },
    watch:{
        
    },
    methods:{
        handleSizeChange(val) {
            this.queryForm.pageSize = val
            this.fetchData()
        },
        handleCurrentChange(val) {
            this.queryForm.pageNo = val
            this.fetchData()
        },
        fetchData(){
            this.loadingPage = true;
            service.getCorrectingList({
                gid:this.cid,
                page:this.queryForm.pageNo,
                pageSize:this.queryForm.pageSize,
                searchKey:this.searchKey,
            }).then(res=>{
                this.loadingPage = false;
                if (res.data.error_code === 0) {
                    this.examListTotal = res.data.data.total;
                    this.examList = res.data.data.data;
                }
            })
        },
        enterCorrecting(exam){
            this.$store.commit('homework/setCurrentPaper',exam);
            this.$router.push(this.$route.fullPath+'/correcting_exam/2')
        }
    },
    destroyed(){
    },

}

</script>
<style lang="scss">
.correcting_exam_list{
    
}
</style>
