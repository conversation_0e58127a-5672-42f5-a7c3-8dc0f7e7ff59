<template>
    <transition name="fade" appear>
    	<div class="visiting_card_page third_level_page">
            <mrHeader>
                <template #title>
                    {{lang.visiting_card_title}}
                </template>
            </mrHeader>
            <div class="container">
                <div class="card_item">
                    <div class="card_line card_avatar clearfix">
                        <div class="avatar">
                            <mr-avatar :url="getLocalAvatar(personalObj)" :origin_url="personalObj.avatar" :preview="true" :onlineState="personalObj.state" :radius="4" :showOnlineState="true" :key="personalObj.userid"></mr-avatar>
                        </div>
                        <div class="card_titile">
                            <p class="nickname">
                                <span v-if="alias" class="nickname_text longwrap">
                                    {{alias}}
                                </span>
                                <span v-else class="nickname_text longwrap">
                                    {{getPersonName(personalObj)}}
                                </span>
                                <i v-show="personalObj.sex==0" class="icon iconfont icon-sexw"></i>
                                <i v-show="personalObj.sex==1" class="icon iconfont icon-sexm"></i>
                            </p>
                            <p v-if="alias" class="sub_nickname">
                                <span class="nickname_text longwrap">{{lang.nickname}}: {{personalObj.nickname}}</span>
                            </p>
                            <p class="introduction" @click="showIntroductionTips">{{personalObj.introduction}}</p>
                        </div>
                    </div>
                    <!-- <div class="card_line" v-if="personalObj.introduction">
                        <p class="card-line-label">{{lang.personal_profile}}</p>
                        <p class="card-line-content">{{personalObj.introduction}}</p>
                    </div> -->
                    <div v-if="!isService&&isFriend" class="card_line" @click="openRemarkPage">
                        <p class="card-line-label">{{lang.set_remark}}</p>
                        <p class="card-line-content"><i class="iconfont icon-right"></i></p>
                    </div>
                </div>
                <div class="card_item" v-if="isShowFavorite">
                    <div class="card_line" @click="openFavorite">
                        <p class="card_label">{{lang.personal_favorite_text}}</p>
                        <template v-if="userFavorites.length==0">
                            <div class="favorite_list">
                                <div class="favorite_item empty">
                                    <div class="favorite_item_container ">
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template v-if="userFavorites.length>0">
                            <div class="favorite_list">
                                <div class="favorite_item" v-for="file of userFavorites" :key="file.resource_id">
                                    <div class="favorite_item_container">
                                        <common-image :fileItem="file"></common-image>
                                    </div>
                                </div>
                            </div>
                        </template>

                        <i class="iconfont icon-right"></i>
                    </div>
                </div>
                <div class="card_item" v-if="!isSelf&&!isDestroy">
                    <div class="card_line card_btn" v-if="isService" @click="openChat">{{lang.card_chat_btn}}</div>
                    <template v-else-if="isFriend">
                        <div class="card_line card_btn" @click="openChat">{{lang.card_chat_btn}}</div>
                    </template>

                    <div class="card_line card_btn" v-else-if="!isApplying" @click="requestAddFriend">{{lang.add_friend_text}}</div>
                    <div class="card_line applying_tip" v-else >{{lang.card_applying_tip}}</div>
                </div>
            </div>
            <div class="apply_modal needsclick" v-if="isShowApplyPanel">
                <div class="apply_panel">
                    <p class="apply_title">{{lang.apply_add_friend}}</p>
                    <div class="apply_info">
                        <mr-avatar :url="getLocalAvatar(personalObj)" :origin_url="personalObj.avatar" :onlineState="personalObj.state" :radius="1.5" :showOnlineState="true"></mr-avatar>
                        <p>{{personalObj.nickname}}</p>
                    </div>
                    <div class="apply_form">
                        <p>{{lang.apply_friend_info}}</p>
                        <input type="text" v-model="description" maxlength="30" name="apply_description" class="needsclick">
                        <p>{{lang.apply_friend_remark}}</p>
                        <input v-model="remark" maxlength="16" type="text" name="apply_remark">
                    </div>
                    <div class="apply_btns">
                        <div class="btn" @click="cancelApply">{{lang.cancel_btn}}</div>
                        <div class="divider"></div>
                        <div class="btn" @click="sendApply">{{lang.send_message_txt}}</div>
                    </div>
                </div>
            </div>
            <!-- <van-popup v-model="showIntroductionTips" position="bottom" :style="{ height: '30%' }">{{lang.personal_profile}}:{{personalObj.introduction}}</van-popup> -->
            <router-view></router-view>
    	</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import {  Dialog, Toast } from 'vant';
import { cloneDeep } from 'lodash'
import {getLocalAvatar} from '../lib/common_base'
import Tool from '@/common/tool'
export default {
    mixins: [base],
    name: 'visiting_card',
    components: {
        [Dialog.Component.name]: Dialog.Component,
    },
    data(){
        return {
            getLocalAvatar,
            isShowApplyPanel:false,
            description:'',
            remark:'',
            userFavorites:[],
        }
    },
    beforeRouteEnter(to,from,next){
        if (!from.name) {
            //二级页面刷新时返回上级页面
            next(false)
            window.location.replace(`#/index`);
        }else{
            next()
        }
    },
    computed:{
        personalObj(){
            let obj=cloneDeep(this.$store.state.relationship.personalObj);
            if (!obj.userid) {
                //attendeeList的id被占用，只能用userid
                obj.userid=obj.id;
            }
            return obj
        },
        alias(){
            return this.$store.state.friendList.remarkMap[this.personalObj.userid];
        },
        conversation(){
            let cid=this.$route.params.cid
            let conversation=this.$store.state.conversationList[cid]||{}
            return conversation;
        },
        isPlayingVideo(){
            //所在会话是否正在看直播
            return this.conversation.is_playing_video
        },
        isSelf(){
            // console.log("++++personalObj: ",this.personalObj)
            // console.log("++++user: ",this.user)
            return this.personalObj.userid==this.user.uid;
        },
        isDestroy(){
            const userStatus=this.personalObj.user_status||this.personalObj.status
            return userStatus===this.systemConfig.userStatus.Destroy;
        },
        isService(){
            return this.personalObj.service_type&&this.personalObj.service_type!==0;
        },
        isFriend(){
            let friendList=this.$store.state.friendList.list;
            for(let friend of friendList){
                if (friend.id==this.personalObj.userid) {
                    return true
                }
            }
            return false;
        },
        isApplying(){
            let applyFriendList=this.$store.state.relationship.applyFriendList;
            for(let id of applyFriendList){
                if (id==this.personalObj.userid) {
                    return true
                }
            }
            return false;
        },
        isShowFavorite(){
            const result = this.isSelf||(!this.isService&&this.isFriend);
            // if (result) {
            //     this.initUserFavorites();
            // }
            return result;
        }
    },
    mounted(){
        this.$nextTick(()=>{
            var that = this;
            var controller = window.main_screen.controller;
            controller.on("request_add_friend_ack",function(data){
                if ("request_add_friend_succ" == data.code) {
                    that.addFriendSuccess(data);
                } else {
                    that.addFriendFail(data);
                }
            });
            if (this.isSelf||(!this.isService&&this.isFriend)) {
                this.initUserFavorites();
            }
        })
    },
    activated(){
        if (this.isSelf||(!this.isService&&this.isFriend)) {
            this.initUserFavorites();
        }
    },
    methods:{
        getPersonName(personalObj){
            if(personalObj.service_type==this.systemConfig.ServiceConfig.type.DrAiAnalyze){
                return this.lang.dr_ai_analyze
            }
            if(personalObj.service_type==this.systemConfig.ServiceConfig.type.AiAnalyze){
                return this.lang.ai_analyze
            }
            if(personalObj.service_type==this.systemConfig.ServiceConfig.type.FileTransferAssistant){
                return this.lang.file_transfer_assistant
            }
            return personalObj.nickname
        },
        openChat(){
            //已是好友打开会话
            if (this.isPlayingVideo) {
                Toast(this.lang.playing_video_tip)
                return
            }
            var that=this;
            let level=this.$route.meta.level;
            history.go(1-level);
            setTimeout(function(){
                that.openConversation(that.personalObj.userid,3)
            },50)
        },
        requestAddFriend(){
            this.isShowApplyPanel=true;
            this.remark='';
            this.description='';
            if (this.conversation.subject) {
                this.description=this.lang.apply_description.replace('${group}',this.conversation.subject)
                this.description=this.description.replace('${nickname}',this.user.nickname)
            }
            // var user=this.personalObj;
            // this.$root.socket.emit("request_add_friend",{id:user.userid})
            // this.$store.commit('relationship/addApplyFriend',user)
        },
        addFriendSuccess(){
            Toast(this.lang.card_request_sended);
        },
        addFriendFail(){
            Toast(this.lang.card_request_fail);
        },
        openRemarkPage(){
            this.$router.push(this.$route.fullPath+'/remark');
        },
        cancelApply(){
            this.isShowApplyPanel=false;
        },
        sendApply(){
            window.main_screen.applyAddFriend({
                fid:this.personalObj.userid,
                alias:this.remark,
                description:this.description
            },(data)=>{
                this.cancelApply();
                if (data.error_code==0) {
                    this.$store.commit('relationship/addApplyFriend',this.personalObj)
                }
            })
        },
        openFavorite(){
            this.$router.push(this.$route.fullPath+'/public_favorite/'+this.personalObj.userid);
        },
        initUserFavorites(){
            this.userFavorites=[];
            service.getUserFavorite({
                friendId:this.personalObj.userid,
                page:1,
                pagesize:3
            }).then((res)=>{
                if (res.data.error_code==0) {
                    this.userFavorites=res.data.data.list;
                }
            })
        },
        showIntroductionTips(){
            Tool.openMobileDialog(
                {
                    message:this.personalObj.introduction,
                    title:this.lang.personal_profile,
                    messageAlign:'left'
                }
            )
        }
    }
}

</script>
<style lang="scss">
.visiting_card_page{
    .container{
        background:#eee;
        padding-bottom:1rem;
        flex: 1;
        .card_item{
            background: #fff;
            margin-bottom: .4rem;
            padding: 0 .6rem;
            .card_line{
                position: relative;
                padding: .6rem 0;
                font-size: .8rem;
                border-bottom: 1px solid #eee;
                display: flex;
                align-items: center;
                &:last-child{
                    border: none;
                }
                .card_label{
                    max-width: 4rem;
                }
                .icon-right{
                    font-size: 1.2rem;
                }
                .card-line-label{
                    width: 4rem;
                }
                .card-line-content{
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    flex: 1;
                }
                .applying_tip{
                    text-align: center;
                    margin: 0.5rem 0;
                    color: #666;
                }
                .favorite_list{
                    flex:1;
                    padding-left: .4rem;
                    .favorite_item{
                        width: 33%;
                        float: left;
                        background: #333;
                        .favorite_item_container{
                            height: 0;
                            position: relative;
                            padding-top: 100%;
                            border: 1px solid #fff;
                        }

                    }
                    .empty{
                        background: transparent;
                    }
                }
            }
            .card_btn{
                text-align: center;
                font-size: .9rem;
                color: #00c59d;
                display: block;
            }
            .card_avatar{
                padding: 1rem 0 .5rem;
                display: flex;
                .card_titile{
                    flex: 1;
                    overflow: hidden;
                    .introduction{
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: #999;
                    }
                }
            }
        }
        .avatar{
            display:block;
            width:4rem;
            height:4rem;
            background-size: 100% 100%;
            margin-right: 1rem;
            margin-bottom: .5rem;
            border-radius: 50%;
            box-shadow: 0px 0.3rem 0.5rem #aaa;
        }
        .nickname{
            position: relative;
            display: inline-block;
            font-size: 1.2rem;
            color: #333;
            line-height: 2rem;
            height: 2rem;
            max-width: 90%;
            .icon-sexm{
                position: absolute;
                right: -1rem;
                top: 0;
                font-size: 0.8rem;
                color: #2288ff;
            }
            .icon-sexw{
                position: absolute;
                right: -1rem;
                top: 0;
                font-size: 0.8rem;
                color: #ff6699;
            }
        }
        .sub_nickname{
            padding: 0 ;
            color: #666;
            font-size: .9rem;
        }
        .nickname_text{
            display: inline-block;
            width: 100%;
        }
    }
    .apply_modal{
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 2;
        .apply_panel{
            background-color: #fff;
            margin: 0 0.6rem;
            position: absolute;
            width: calc(100% - 1.2rem);
            top: 20%;
            border-radius: 0.2rem;
            padding: .8rem .8rem 0;
            box-sizing:border-box;
            .apply_title{
                text-align: center;
                font-size: .8rem;
                margin: .5rem 0 .6rem;
            }
            .apply_info{
                display: flex;
                border-bottom:1px solid #eee;
                padding: .5rem 0;
                &>p{
                    margin-left: 0.5rem;
                    color: #777;
                    font-size: .7rem;
                    line-height: 1.5rem;
                }
            }
            .apply_form{
                &>p{
                    font-size: .6rem;
                    color: #aaa;
                    margin: .8rem 0 .3rem;
                }
                &>input{
                    width: 100%;
                    border: none;
                    background: #f3f7fb;
                    border-radius: .2rem;
                    padding: 0 .4rem;
                    box-sizing: border-box;
                    font-size: .7rem;
                    line-height: 2.2;
                    color: #333;
                }
            }
            .apply_btns{
                display: flex;
                margin-top: 1rem;
                border-top:1px solid #eee;
                padding: .6rem 0;
                .divider{
                    width: 1px;
                    background: #eee;
                }
                .btn{
                    flex:1;
                    text-align: center;
                    font-size: .8rem;
                    color: #00c59d;
                }
            }
        }
    }
}
</style>
