import Vue from 'vue'
import App from './App'
import store from './store'
import router from './router'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
import '../../common/console'
import commonImage from './components/commonImage';
import mrHeader from './components/mrHeader';
import VueVirtualScroller from 'vue-virtual-scroller';
import VueTouch from 'vue-touch';
import lazyload from 'vue-lazyload';
import '../../common/rem'
import './directive'
import '/static/resource_pc/theme-ultrasync/index.css'
// 全局使用插件
Vue.use(lazyload, {
    preload: 1.3,   // preload配置项用来设置预加载的高度(默认值为1.3)
    loading: '',   // loading配置项用来配置加载时显示的图片路径
    error: '',    // error配置项用来配置加载错误时的显示的图片路径
    attemp: 3   // attemp配置项用来设置加载错误时的重传次数
})
Vue.use(ElementUI);
Vue.use(VueVirtualScroller)
Vue.use(VueTouch,{name:'v-touch'})
Vue.component(commonImage.name,commonImage)
Vue.component(mrHeader.name,mrHeader)


window.vm=new Vue({
    el: '#app',
    store,
    router,
    template: '<App/>',
    components: { App },
    data(){
        return {
            eventBus:new Vue(),
        }
    }
})
