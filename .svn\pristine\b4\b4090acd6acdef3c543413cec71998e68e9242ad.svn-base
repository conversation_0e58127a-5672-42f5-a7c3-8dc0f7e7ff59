<template>
    <div class="supervisor-management-container">
        <!-- 筛选/操作区域 -->
        <div class="filter-section">
            <el-form :inline="true" :model="filters" class="filter-form">
                <el-form-item :label="lang.user_name">
                    <el-input v-model="filters.nickname" :placeholder="lang.input_enter_tips" clearable></el-input>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" @click="getTrainingSupervisorList" :loading="loading">{{ lang.query_btn }}</el-button>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="el-icon-plus" @click="handleAddNewSupervisor">{{ lang.admin_add }}</el-button>
                </el-form-item>
            </el-form>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
            <div class="table-container">
                <el-table :data="supervisorList" border stripe style="width: 100%; height: 100%;" v-loading="loading">
                <el-table-column type="index" :label="capitalizeFirstLetter(lang.number_text)" width="80"></el-table-column>
                <el-table-column prop="nickname" :label="capitalizeFirstLetter(lang.user_name)">
                    <template slot-scope="scope">
                        <el-select
                            v-if="scope.row.isNew"
                            v-model="scope.row.uid"
                            filterable
                            remote
                            reserve-keyword
                            :placeholder="lang.input_enter_tips"
                            clearable
                            :remote-method="remoteSearch"
                            :loading="searchLoading"
                            :ref="`userSelectRef_${scope.$index}`"
                            @clear="handleSelectClear(scope.$index)"
                            @visible-change="refreshUserSelectRef"
                            >
                            <el-option
                                v-for="item in searchResults"
                                :key="item.id"
                                :label="formatUserName(item)"
                                :value="item.id">
                            </el-option>
                        </el-select>
                        <span v-else>
                            {{ scope.row.alias ? `${scope.row.nickname}(${scope.row.alias})` : scope.row.nickname }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="role" :label="capitalizeFirstLetter(lang.role_permission)">
                    <template slot-scope="scope">
                        <el-select v-if="scope.row.isNew" v-model="scope.row.role" disabled>
                            <el-option
                                v-for="item in roleOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                            </el-option>
                        </el-select>
                        <span v-else>{{ getRoleLabel(scope.row.role) }}</span>
                    </template>
                </el-table-column>
                <el-table-column :label="capitalizeFirstLetter(lang.operation)">
                    <template slot-scope="scope">
                        <div v-if="scope.row.isNew">
                            <el-button type="primary" size="small" @click="handleSave(scope.row, scope.$index)" :loading="scope.row.isSaving">{{ lang.confirm_txt }}</el-button>
                            <el-button size="small" @click="handleCancelEdit(scope.row, scope.$index)">{{ lang.cancel_btn }}</el-button>
                        </div>
                        <div v-else>
                            <el-button type="danger" size="small" @click="handleDelete(scope.row, scope.$index)">{{ lang.action_delete_text }}</el-button>
                        </div>
                    </template>
                </el-table-column>
                </el-table>
            </div>

            <div class="pagination-container">
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="filters.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="filters.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="totalCount">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../../lib/base";
import service from "../../../service/service";
import Tool from "@/common/tool.js";
export default {
    name: "SupervisorManagement",
    mixins: [base],
    data() {
        return {
            capitalizeFirstLetter: Tool.capitalizeFirstLetter,
            filters: {
                nickname: '',
                page: 1,
                pageSize: 20
            },
            loading: false,
            totalCount: 0,
            supervisorList: [],
            roleOptions: [
                { label: 'Supervisor', value: 1 },
            ],
            nextId: 1, // 用于前端生成临时 ID
            originalDataBeforeEdit: null, // 用于存储编辑前的数据副本
            searchLoading: false, // 搜索加载状态
            searchResults: [], // 搜索结果
            trainingID:''
        };
    },

    created() {
        this.trainingID=this.$route.params.trainingId;
        this.getTrainingSupervisorList();
    },

    methods: {
        /**
         * 检查是否有行处于编辑状态
         * @param {Object} currentRow 当前行（可选，如果提供则排除当前行）
         * @returns {Object|null} 正在编辑的行，如果没有则返回 null
         */
        checkEditingStatus(currentRow = null) {
            return this.supervisorList.find(item =>
                item.isNew &&
                (!currentRow || item !== currentRow)
            );
        },
        // 远程搜索方法
        remoteSearch(query) {
            if (!query) {
                this.searchResults = [];
                return;
            }
            this.searchLoading = true;
            const data = {
                precise: 0,
                keys: [query],
            };
            this.$root.socket.emit("query_user_info_list", data, (res) => {
                this.searchLoading = false;
                if (res.isSuccess && res.data) {
                    this.searchResults = res.data;
                } else {
                    this.searchResults = [];
                }
            });
        },
        // 格式化用户显示名称
        formatUserName(user) {
            if (user.alias) {
                return `${user.nickname}(${user.alias})`;
            }
            return user.nickname;
        },
        handleAddNewSupervisor() {
            // 检查是否有任何行处于编辑状态
            const editingRow = this.checkEditingStatus();
            if (editingRow) {
                this.$message.warning(this.lang.only_one_data_can_be_operated);
                return;
            }

            const newSupervisor = {
                id: null, // 临时 ID，保存时生成
                uid: null, // 存储选中用户的ID
                nickname: '',
                alias: '', // 存储用户别名
                role: 1, // 默认设置为 Supervisor
                isNew: true,
                isSaving: false
            };
            this.supervisorList.unshift(newSupervisor); // 添加到列表顶部
        },
        async handleSave(row, index) {
            // 验证必填字段
            if (!row.uid || !row.role) {
                this.$message.warning(this.lang.please_fill_in_all_require_fields);
                return;
            }

            // 根据 uid 获取完整的用户信息
            const selectedUser = this.searchResults.find(user => user.id === row.uid);
            if (!selectedUser) {
                console.error('selected_user_is_invalid');
                return;
            }

            // 更新行数据
            row.nickname = selectedUser.nickname;
            row.alias = selectedUser.alias;

            row.isSaving = true;
            try {
                await this.addSupervisorByPI(row.uid)
                this.$message.success(this.lang.operate_success);
                row.id = this.nextId++; // 分配临时 ID
                this.getTrainingSupervisorList()
            } catch (error) {
                console.error("Save operation failed:", error);
                this.$message.error(this.lang.operate_failed);
            } finally {
                row.isSaving = false;
            }
            this.originalDataBeforeEdit = null; // 清除编辑前数据副本
        },
        handleCancelEdit(row, index) {
            this.supervisorList.splice(index, 1);
            this.originalDataBeforeEdit = null; // 清除编辑前数据副本
            this.searchResults = []; // 清除搜索结果
        },
        handleDelete(row, index) {
            // 格式化显示名称，包含别名（如果有）
            const displayName = row.alias ? `${row.nickname}(${row.alias})` : row.nickname;

            this.$confirm(this.lang.sure_you_want_delete_this_use, this.lang.tip_title, {
                confirmButtonText: this.lang.confirm_txt,
                cancelButtonText: this.lang.cancel_btn,
                type: 'warning'
            }).then(() => {
                this.deleteSupervisorByPI(row.uid).then(res=>{
                    this.supervisorList.splice(index, 1);
                })
            }).catch(() => {

            });
        },

        getRoleLabel(roleValue) {
            const role = this.roleOptions.find(option => option.value === roleValue);
            return role ? role.label : roleValue; // 如果找不到匹配的label，直接返回value
        },
        handleSelectClear(index){
            this.searchResults=[];
            this.$nextTick(() => {
                const selectComponent = this.$refs[`userSelectRef_${index}`];
                if (selectComponent && typeof selectComponent.focus === 'function') {
                    selectComponent.focus();
                }
            });
        },
        getTrainingSupervisorList(){
            return new Promise((resolve,reject)=>{
                this.loading = true;
                const params={
                    trainingID:this.trainingID,
                    page: this.filters.page,
                    pageSize: this.filters.pageSize,
                    name:this.filters.nickname
                }
                service.getTrainingSupervisorList(params).then(res=>{
                    if(res.data.error_code === 0){
                        // 处理新的分页数据结构
                        const { total, data } = res.data.data;
                        this.totalCount = total;
                        this.supervisorList = data.map(item=>{
                            return {
                                ...item,
                                isNew:false,
                                isSaving: false
                            }
                        });
                        resolve(res)
                    }else{
                        reject(res)
                    }
                    this.loading = false;
                },err=>{
                    reject(err)
                    this.loading = false;
                })
            })
        },
        addSupervisorByPI(uid){
            return new Promise((resolve,reject)=>{
                const params={
                    trainingID:this.trainingID,
                    supervisorID:uid
                }
                service.addSupervisorByPI(params).then(res=>{
                    if(res.data.error_code === 0){
                        resolve(res)
                    }else{
                        reject(res)
                    }
                },err=>{
                    reject(err)
                })
            })
        },
        deleteSupervisorByPI(uid){
            return new Promise((resolve,reject)=>{
                const params={
                    trainingID:this.trainingID,
                    supervisorID:uid
                }
                service.deleteSupervisorByPI(params).then(res=>{
                    if(res.data.error_code === 0){
                        resolve(res)
                    }else{
                        reject(res)
                    }
                },err=>{
                    reject(err)
                })
            })
        },
        refreshUserSelectRef(val){
            console.log(val)
            if(!val){
                // setTimeout(() => {
                //     this.searchResults=this.supervisorList;
                // }, 600);
            }
        },
        // 处理分页大小变化
        handleSizeChange(size){
            this.filters.pageSize = size;
            this.filters.page = 1;
            this.getTrainingSupervisorList();
        },

        // 处理页码变化
        handleCurrentChange(page){
            this.filters.page = page;
            this.getTrainingSupervisorList();
        },
    }
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/aiChat.scss"; // 假设这个文件存在且包含通用样式

.supervisor-management-container {
    padding: 20px;
    background-color: #f5f7fa; // 参考 studentManagement
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .filter-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); // 参考 studentManagement
        flex-shrink: 0;

        .filter-form {
            .el-form-item {
                margin-bottom: 0;
                margin-right: 15px;
                &:last-child {
                    margin-right: 0;
                }
            }
        }
    }

    .content-section {
        background-color: #fff;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1); // 参考 studentManagement
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .table-container {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;

            .el-table {
                flex: 1;
                display: flex;
                flex-direction: column;

                ::v-deep .el-table__header-wrapper {
                    flex-shrink: 0;
                }

                ::v-deep .el-table__body-wrapper {
                    flex: 1;
                    overflow-y: auto;
                }

                // 表格内输入框和选择器样式调整
                ::v-deep .el-input input,
                ::v-deep .el-select .el-input__inner {
                    height: 32px; // 调整行内编辑时组件的高度，使其更紧凑
                    line-height: 32px;
                }
                ::v-deep .el-select .el-input .el-select__caret {
                    line-height: 32px; // 调整下拉箭头位置
                }
            }
        }
        .pagination-container {
            margin-top: 20px;
            padding: 15px 0;
            border-top: 1px solid #ebeef5;
            text-align: right;
            flex-shrink: 0;
        }
    }
}
</style>
