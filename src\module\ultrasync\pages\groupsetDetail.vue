<template>
    <transition name="slide" appear>
    	<div class="groupset_detail_page third_level_page" >
            <mrHeader>
                <template #title>
                    {{currentGroupset.subject}}
                </template>
                <template #right>
                    <div class="more_panel" @click="openGroupsetSetting" v-if="isMyGroupSet">
                        <svg class="svg_icon_more">
                            <use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#icon-more"></use>
                        </svg>
                    </div>
                </template>
            </mrHeader>
            <div class="second_level_content container">
                <div class="groupset_operate">
                    <!-- <van-button v-if="EnableCloudStatistics" @click="openIWorksStatistics" color="#00c59d" class="operate_btn" size="small">{{this.lang.cloud_statistics_iworks2}}</van-button> -->
                    <van-button v-if="EnableQc_statistics" @click="openBIDataShow" color="#00c59d" class="operate_btn" size="small">{{this.lang.bi_data_display}}</van-button>
                </div>
                <van-index-bar highlight-color="#00c59d" class="groupset_detail_list" :index-list="indexBarList">
                    <div  v-for="(item,index) of indexedList" :key="index">
                        <van-index-anchor :index="item.title" />
                        <div v-for="item of item.list" class="group_item clearfix needsclick" @click="openConversationFn(item.id,14)" :key="item.id">
                            <mr-avatar :url="getLocalAvatar(item)" :origin_url="item.avatar" :showOnlineState="false" :key="item.avatar"></mr-avatar>
                            <p class="longwrap needsclick">
                                {{remarkMap[item.fid]||item.subject}}
                            </p>
                        </div>
                    </div>
                </van-index-bar>
            </div>
            <router-view></router-view>
        </div>
    </transition>
</template>
<script>
import base from '../lib/base'
import groupsetTool from '../lib/groupsetTool'
import { Button, IndexBar, IndexAnchor } from 'vant';
import {getIndexedList,parseSingleChat,getLocalAvatar} from '../lib/common_base'
export default {
    mixins: [base,groupsetTool],
    name: 'addGroupset',
    components: {
        VanButton: Button,
        VanIndexBar: IndexBar,
        VanIndexAnchor: IndexAnchor
    },
    data(){
        return {
            id:this.$route.params.id,
            getLocalAvatar,
            loading:false
        }
    },
    computed:{
        details(){
            return this.$store.state.groupset.details
        },
        currentGroupset(){
            let detail=this.details[this.id]
            if (!detail) {
                detail={}
                // this.initDetail()
            }
            return detail
        },
        indexedList(){
            let indexedListTemp = getIndexedList(this.currentGroupset.groupList||[],'subject');
            let indexList =[]
            for (let i = 0; i < indexedListTemp.length; i++) {
                if(indexedListTemp[i].list.length > 0){
                    indexList.push(indexedListTemp[i]);
                }
            }
            return indexList
        },
        indexBarList(){
            let indexedListTemp = getIndexedList(this.currentGroupset.groupList||[],'subject');
            let barList = []
            for (let i = 0; i < indexedListTemp.length; i++) {
                if(indexedListTemp[i].list.length > 0){
                    barList.push(indexedListTemp[i].title)
                }
            }
            return barList;
        },
        EnableCloudStatistics(){ // 是否允许查看云端统计
            return this.$store.state.systemConfig.serverInfo.cloud_statistics
                &&this.$store.state.systemConfig.serverInfo.cloud_statistics.enable
        },
        EnableQc_statistics(){
            return this.functionsStatus.qcStatistics&&this.$store.state.systemConfig.serverInfo.qc_statistics
                &&this.$store.state.systemConfig.serverInfo.qc_statistics.enable
        },
        remarkMap(){
            return this.$store.state.friendList.remarkMap;
        },
        isMyGroupSet() {
            return this.currentGroupset.groupSetType == "myGroupSet";
        },
    },
    mounted(){
        this.initDetail()
    },
    methods:{
        initDetail(){
            this.loading = true
            window.main_screen.getGroupsetDetail({
                groupSetID:this.id
            },(data)=>{
                console.log('data',data)
                this.loading = false
                if (data.error_code==0) {
                    data.data.groupList=parseSingleChat(data.data.groupList)
                    data.data.type=3
                    this.$store.commit('groupset/updateGroupsetDetail',data.data)
                }
            })
        },
        openIWorksStatistics(){
            this.$router.push(this.$route.fullPath+`/iworks_statistics`)
        },
        openBIDataShow(){
            this.$router.push(this.$route.fullPath+`/bi_data?type=groupset`)
        },
        openGroupsetSetting(){
            this.$router.push(this.$route.fullPath+`/groupset_setting`)
        },
        openConversationFn(id,type){
            if(this.isMyGroupSet){
                this.openConversation(id,type)
            }
        }
    }
}
</script>
<style lang="scss">
.groupset_detail_page{
    background-color:#fff;
    .container{
        flex:1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        .groupset_operate{
            padding: 0.2rem;
            background-color: #ffffff;
            .operate_btn{
                margin: 0.1rem;
                font-size: 0.6rem;
            }
        }
        .groupset_detail_list{
            flex: 1;
            overflow: auto;
            position: relative;

            .van-index-bar__index {
                line-height: 22px;
                font-size: .6rem;
                flex: 1;
            }
            .van-index-anchor--sticky{
               transform: translate3d(0, 0, 0) !important;
               position: inherit;
            }
        }
        .group_item{
            background-color:#fff;
            border-bottom:1px solid #eee;
            padding:.4rem .6rem;
            font-size:0.8rem;
            display: flex;
            align-items: center;
            & > p{
                line-height:2rem;
                padding-left:0.5rem;
                color:#333;
            }
        }
    }
}
</style>
