<template>
    <van-dialog
        v-model="show"
        @close="handleClose"
        v-bind="$attrs"
        class="common_dialog"
        :show-cancel-button="showRejectButton"
        :show-confirm-button="showConfirmButton"
        :before-close="handleBeforeClose"
        :confirm-button-text="confirmButtonText"
        :cancel-button-text="rejectButtonText"
        :message-align="messageAlign"
        @confirm="handleConfirm"
        @cancel="handleReject"
        :closeOnClickOverlay="closeOnClickOverlay"
        :close-on-popstate="false"
        :key="currentDialogId"
    >
        <div class="common_dialog-title">{{ title }}</div>
        <div class="common_dialog-content" :class="[`align_${messageAlign}`]">
            <slot><p v-html="message" class="common_dialog-message"></p></slot>
        </div>
        <div class="cancel_button" v-if="showCancelButton" @click="handelCancelButton">
            <van-icon name="close" size="24" color="#f54040" />
        </div>
    </van-dialog>
    <!-- <div style="position:fixed;top:0;bottom:0;left:0;right:0;background: #000;" v-if="show"></div> -->
</template>
<script>
import { Dialog, Icon } from "vant";
import base from "../lib/base";
import Tool from '@/common/tool'

// 导入DialogManager
let DialogManager = null;
try {
    DialogManager = require('@/module/ultrasync/lib/dialogManager').default;
} catch (e) {
    console.warn('DialogManager not available in functionalDialog');
}

export default {
    mixins: [base],
    name: "FunctionalDialog",
    components: {
        [Dialog.Component.name]: Dialog.Component,
        VanIcon: Icon,
    },
    data() {
        return {
            show: false,
            title: "",
            showConfirmButton: false,
            showCancelButton: false,
            showRejectButton: false,
            beforeClose: null,
            confirmButtonText: "",
            rejectButtonText: "",
            message:'',
            messageAlign:'center',
            confirm:null,
            reject:null,
            cancel:null,
            close:null,
            closeOnClickOverlay:false,
            closeOnPopstate:true,
            currentDialogId:0,
            forbiddenClose:false,
            dialogManagerId: null // DialogManager中的注册ID

        };
    },

    mounted() {},
    beforeDestroy() {
        // 组件销毁前清理DialogManager中的注册
        if (DialogManager && this.dialogManagerId) {
            DialogManager.unregister(this.dialogManagerId);
            this.dialogManagerId = null;
        }
    },
    computed: {},
    methods: {
        showDialog:Tool.debounce(async function ({
            showRejectButton = false,
            showConfirmButton = true,
            showCancelButton = true,
            confirmButtonText = "",
            rejectButtonText = "",
            title = "",
            beforeClose = null,
            confirm = null,
            reject=null,
            cancel=null,
            message='',
            close=null,
            messageAlign='center',
            closeOnClickOverlay=false,
            closeOnPopstate = true,
            forbiddenClose = false,
            id = null // 接收外部传入的ID
        } = {}) {
            if(this.show === true){
                return
            }
            this.show = true;
            this.title = title||this.lang.tip_title;
            this.showConfirmButton = showConfirmButton;
            this.showRejectButton = showRejectButton;
            this.showCancelButton = showCancelButton;
            this.confirmButtonText = confirmButtonText||this.lang.confirm_txt;
            this.rejectButtonText = rejectButtonText||this.lang.cancel_btn;
            this.beforeClose = beforeClose;
            this.message = message
            this.messageAlign = messageAlign
            this.confirm = confirm;
            this.reject = reject;
            this.cancel = cancel;
            this.closeOnClickOverlay = closeOnClickOverlay
            this.closeOnPopstate = closeOnPopstate
            this.forbiddenClose = forbiddenClose
            this.close = close;
            this.show = true;
            this.currentDialogId = id || Tool.genID(3) // 使用外部传入的ID，如果没有则生成新的

            // 注册到DialogManager
            if (DialogManager) {
                this.dialogManagerId = DialogManager.register(this, {
                    open: () => {
                        // 弹窗已经显示，这里不需要额外操作
                    },
                    close: () => {
                        this.closeDialog();
                    },
                    canCloseOnPopstate: this.closeOnPopstate && !this.forbiddenClose,
                    canClose: !this.forbiddenClose,
                    id: this.currentDialogId
                });
            }
        },500,true),
        handleClose() {
            if (this.close) {
                this.close();
                this.show = false
            }
        },
        handleBeforeClose(action, done) {
            if (this.beforeClose) {
                this.beforeClose(action, done);
            } else {
                done();
            }
        },
        handleConfirm(){
            // 只关闭UI，不注销DialogManager，让外部回调处理注销
            this.show = false;
            if (this.confirm) {
                this.confirm();
            }
        },
        handleReject(){
            // 只关闭UI，不注销DialogManager，让外部回调处理注销
            this.show = false;
            if (this.reject) {
                this.reject();
            }
        },
        handelCancelButton(){
            // 只关闭UI，不注销DialogManager，让外部回调处理注销
            this.show = false;
            if (this.cancel) {
                this.cancel();
            }
        },
        closeDialog() {
            this.show = false;

            // 从DialogManager中注销，但只在确实注册了的情况下
            if (DialogManager && this.dialogManagerId && DialogManager.isDialogRegistered(this.dialogManagerId)) {
                DialogManager.unregister(this.dialogManagerId);
                this.dialogManagerId = null;
            }
        },
        checkCanClose(){
            return !this.forbiddenClose
        },
        checkCanCloseOnPopstate(){
            return this.closeOnPopstate&&!this.forbiddenClose
        },
        checkIsShow(){
            return this.show
        }
    },
};
</script>
<style lang="scss">
.common_dialog {
    min-width: 300px;
    width: 80%;
    max-width: 540px;
    border-radius: 5px;
    .common_dialog-title {
        font-size: 0.82rem; // 18px = 0.82rem
        font-weight: 600;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 2.45rem; // 54px = 2.45rem
        line-height: 1.4;
        padding: 0.55rem 0.91rem 0.36rem 0.91rem; // 12px 20px 8px 20px
        text-align: center;
        word-break: break-word;
        white-space: normal;
    }
    .common_dialog-content {
        min-height: 2rem; // 44px = 2rem
        padding: 0.55rem 1rem; // 12px 22px
        font-size: 0.73rem; // 16px = 0.73rem
        .common_dialog-message{
            word-break: break-word;
        }
    }

    .cancel_button {
        position: absolute;
        top: 0;
        z-index: 10;
        right: 0;
    }
    .align_left{
        text-align: left;
    }
    .align_center{
        text-align: center;
    }
    .align_right{
        text-align: right;
    }
    .van-checkbox-group {
        position: relative !important;
        border-radius: 0rem !important;

        .van-checkbox {
            padding: 0px !important;
        }
    }
    .van-dialog__footer {
        .van-button {
            color: #00c59d;
        }
    }

}
</style>
