<template>
<div>
    <el-dialog
      class="cloud_exam cloud_exam_dialog"
      :title="lang.cloud_exam"
      :visible="true"
      :close-on-click-modal="false"
      width="90%"
      :modal="false"
      :before-close="back">
        <cloud-exam-entry></cloud-exam-entry>
    </el-dialog>
</div>
</template>
<script>
import base from '../../lib/base';
import cloudExamEntry from './components/cloudExamEntry.vue'
export default {
    mixins: [base],
    name: 'cloudExam',
    components: {
        cloudExamEntry,
    },
    data(){
        return {
        }
    },
    computed:{
    },
    created(){
        
    },
    mounted(){
        this.$nextTick(()=>{
            
        })
    },
    watch:{

    },
    methods:{
        
    },
    destroyed(){
    },

}

</script>
<style lang="scss">
.cloud_exam{
    .el-dialog:not(.self_define_height) {
        margin-top: 5vh !important;
        height: 80% !important;
    }
}
.cloud_exam_dialog{
    .el-dialog{
        border-radius: 6px;
    }
    .el-dialog__header{
        display: none !important;
    }
    .el-dialog__body{
        height: 100% !important;
        padding: 0 !important;
        display: flex;
        flex-direction: column;
    }
}
</style>
