<template>
    <div>
        <van-popup
            v-model="dialogVisible"
            :get-container="getContainer"
            class="review-action-sheet"
            position="bottom"
            :close-on-click-overlay="false"
            :overlay="false"
            :lockScroll="false"
        >
            <div class="van-action-sheet__header">
                <div class="van-action-sheet__title">
                    <p class="longwrap" @click="editReviewForm.isEditingSubject = true">
                        {{ editReviewForm.subject }}
                    </p>
                </div>
            </div>
            <van-form ref="editReviewForm" label-width="280">
                <van-cell
                    :title="lang.label_txt"
                    is-link
                    :value="editReviewForm.type | formatReviewType(that)"
                    @click.stop="openSelectType"
                />
                <van-field
                    v-model="editReviewForm.subject"
                    :label="lang.reserved_conference_subject"
                    name="speaker"
                    maxlength="24"
                    show-word-limit
                    ref="speaker"
                    @blur.stop
                ></van-field>

                <van-field
                    v-model="editReviewForm.speaker"
                    :label="lang.moderator"
                    name="speaker"
                    maxlength="24"
                    show-word-limit
                    ref="speaker"
                    @blur.stop
                ></van-field>

                <van-field
                    v-model="editReviewForm.description"
                    :label="lang.describe"
                    name="description"
                    rows="2"
                    autosize
                    type="textarea"
                    maxlength="70"
                    show-word-limit
                    ref="description"
                    @blur.stop
                />
                <div class="van-cell van-field">
                    <p class="van-cell__title van-field__label" style="width:280px">
                        <span>{{ lang.cover_upload }}</span>
                    </p>
                    <van-uploader
                        :after-read="afterRead"
                        :before-read="beforeRead"
                        v-model="editReviewForm.fileList"
                        accept="image/png, image/jpeg"
                        :max-size="10000 * 1024"
                        @oversize="onOversize"
                        max-count="1"
                        preview-size="200px"
                        :preview-full-image="false"
                        :deletable="false"
                        ref="Uploader"
                        multiple
                    >
                        <template #preview-cover="{}">
                            <div class="preview-cover van-ellipsis">
                                <van-icon name="search" class="preview-btn" @click.stop="previewImage" />
                                <van-icon name="edit" class="preview-btn" @click.stop="openCropperModal" />
                                <van-icon name="delete" class="preview-btn" @click.stop="clearUploadFileList" />
                            </div>
                            <van-circle
                                v-if="progressShow"
                                v-model="percentage"
                                :rate="percentage"
                                :speed="100"
                                :text="rateText"
                                layer-color="#ebedf0"
                                :stroke-width="60"
                            />
                        </template>
                    </van-uploader>
                    <div class="invisible_modal" @click="clickUpload" v-if="!hasCameraPermissions"></div>
                </div>
            </van-form>
            <div class="van-hairline--top van-dialog__footer" ref="footer">
                <van-button type="default" class="van-dialog__cancel" size="large" @click="closeEditFieldSheet">{{
                    lang.cancel_btn
                }}</van-button>
                <van-button
                    type="default"
                    class="van-dialog__confirm van-hairline--left"
                    size="large"
                    @click="submit"
                    :loading="isSubmitting"
                    >{{ lang.confirm_txt }}</van-button
                >
            </div>
            <div :style="{ height: keyBoardHeight ? `${keyBoardHeight}px` : 'auto' }"></div>
            <van-popup v-model="editReviewForm.isShowSexPicker" round position="bottom" :get-container="getContainer">
                <van-picker
                    :title="lang.patient_sex"
                    show-toolbar
                    :columns="sexColumn"
                    :default-index="Number(editReviewForm.gender)"
                    @confirm="onSelectSexType"
                    @cancel="editReviewForm.isShowSexPicker = false"
                />
            </van-popup>
            <van-popup
                v-model="isShowSelectTag"
                round
                position="bottom"
                :get-container="getContainer"
                safe-area-inset-bottom
            >
                <van-picker
                    :title="lang.label_txt"
                    show-toolbar
                    :columns="tagColumn"
                    :default-index="0"
                    @confirm="onSelectTag"
                    @cancel="isShowSelectTag = false"
                />
            </van-popup>

            <CropperImage
                :option="cropperOption"
                @getFile="getFile"
                :hideInput="true"
                ref="cropper"
                class="cropperContainer"
                :show.sync="cropperVisible"
            ></CropperImage>
        </van-popup>
    </div>
</template>
<script>
import base from "../../lib/base";
import { Icon, Dialog, Form, Field, Cell, Button, Picker, Popup, Toast, Uploader, ImagePreview, Circle } from "vant";
import CropperImage from "../../MRComponents/cropperImage.vue";
import {getRecordSubject} from '../../lib/common_base'
import Tool from '@/common/tool'
import {uploadFile} from '@/common/oss/index.js'
import DialogManager from "../../lib/dialogManager";
export default {
    mixins: [base],
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    filters: {
        formatSexType: (value, that) => {
            const arr = [
                that.lang.male,
                that.lang.female,
                that.lang.unknown,
            ];
            return arr[Number(value)];
        },
        formatReviewType: (value, that) => {
            const arr = ["", that.lang.universal_live, that.lang.consultation_live, that.lang.teaching_live];
            return arr[Number(value)];
        },
    },
    data() {
        return {
            getRecordSubject,
            that: this,
            sexColumn: [],
            isShowSelectTag: false,
            dialogVisible: false,
            isSubmitting: false,
            editReviewForm: {
                subject: "",
                description: "",
                type: 1,
                description: "",
                speaker: "",
                isShowSexPicker: false,
                imageUrl: "",
                ossImageUrl: "",
                file: null,
                fileList: [],
            },
            keyBoardHeight: 0,
            percentage: 0,
            progressShow: false,
            cropperVisible: false,
            cropperOption: {
                cancelButtonText: "",
                confirmButtonText: "",
                fixedBox: false,
                canMoveBox: true,
            },
            originCoverImage:'',
            previewInstance:{
                instance:false,
                checkCanCloseOnPopstate:()=>{},
                closeDialog:()=>{},
                checkCanClose:()=>{},
                checkIsShow:()=>{},
                isShow:false
            },
            hasCameraPermissions:false,
            currentDialogId:0,
            mainDialogId: null, // 主弹窗的 DialogManager ID
            sexPickerDialogId: null, // 性别选择器弹窗的 DialogManager ID
            tagPickerDialogId: null, // 标签选择器弹窗的 DialogManager ID
            imagePreviewDialogId: null, // 图片预览弹窗的 DialogManager ID
        };
    },
    components: {
        VanIcon: Icon,
        [Dialog.Component.name]: Dialog.Component,
        VanForm: Form,
        VanField: Field,
        VanCell: Cell,
        VanButton: Button,
        VanPicker: Picker,
        VanPopup: Popup,
        VanUploader: Uploader,
        VanCircle: Circle,
        CropperImage,
    },
    computed: {
        rateText() {
            return this.percentage.toFixed(0) + "%";
        },
    },
    watch: {
        // 监听主弹窗状态
        dialogVisible(val) {
            if (val && !this.mainDialogId) {
                // 打开主弹窗时，注册到 DialogManager
                this.mainDialogId = DialogManager.register(this, {
                    open: () => {
                        // 弹窗已经通过 v-model 显示，这里不需要额外操作
                    },
                    close: () => {
                        this.closeEditFieldSheet();
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.mainDialogId) {
                // 关闭主弹窗时，从 DialogManager 注销
                DialogManager.unregister(this.mainDialogId);
                this.mainDialogId = null;
            }
        },
        // 监听性别选择器弹窗状态
        'editReviewForm.isShowSexPicker'(val) {
            if (val && !this.sexPickerDialogId) {
                this.sexPickerDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.editReviewForm.isShowSexPicker = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.sexPickerDialogId) {
                DialogManager.unregister(this.sexPickerDialogId);
                this.sexPickerDialogId = null;
            }
        },
        // 监听标签选择器弹窗状态
        isShowSelectTag(val) {
            if (val && !this.tagPickerDialogId) {
                this.tagPickerDialogId = DialogManager.register(this, {
                    open: () => {},
                    close: () => {
                        this.isShowSelectTag = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.tagPickerDialogId) {
                DialogManager.unregister(this.tagPickerDialogId);
                this.tagPickerDialogId = null;
            }
        }
    },
    created() {
        this.tagColumn = [
            { text: this.lang.universal_live, id: 1 },
            { text: this.lang.consultation_live, id: 2 },
            { text: this.lang.teaching_live, id: 3 },
        ];
        this.sexColumn = [
            { text: this.lang.male, id: 0 },
            { text: this.lang.female, id: 1 },
            { text: this.lang.unknown, id: 2 },
        ];
        this.cropperOption.cancelButtonText = this.lang.cancel_btn;
        this.cropperOption.confirmButtonText = this.lang.confirm_txt;
    },
    mounted() {
        this.$root.eventBus.$off("reviewEditKeyboardChanged").$on("reviewEditKeyboardChanged", (data) => {
            if (data.keyboard) {
            } else {
                this.keyBoardHeight = 0;
            }
        });
    },
    beforeDestroy() {
        // 清理所有 DialogManager 注册
        if (this.mainDialogId) {
            DialogManager.unregister(this.mainDialogId);
            this.mainDialogId = null;
        }
        if (this.sexPickerDialogId) {
            DialogManager.unregister(this.sexPickerDialogId);
            this.sexPickerDialogId = null;
        }
        if (this.tagPickerDialogId) {
            DialogManager.unregister(this.tagPickerDialogId);
            this.tagPickerDialogId = null;
        }
        if (this.imagePreviewDialogId) {
            DialogManager.unregister(this.imagePreviewDialogId);
            this.imagePreviewDialogId = null;
        }
    },
    methods: {
        openSelectType(templateType) {
            this.isShowSelectTag = true;
        },
        getContainer() {
            return document.querySelector("body");
        },
        clearEditForm() {
            this.$refs.editReviewForm.resetValidation(Object.keys(this.editReviewForm));
            this.editReviewForm = {
                subject: "",
                description: "",
                type: 1,
                description: "",
                speaker: "",
                isShowSexPicker: false,
                imageUrl: "",
                ossImageUrl: "",
                file: null,
                fileList: [],
            };
            this.percentage = 0
            this.progressShow = false
        },
        validatorSubjectLength(val, maxlength) {
            console.log(val, maxlength);
            if (val.length > 70) {
                return false;
            } else {
                return true;
            }
        },
        limitAgeFn(val) {
            if (!val) {
                this.editReviewForm.age = 0;
                return;
            }
            if (val > 150) {
                this.editReviewForm.age = 150;
            } else if (val < 0) {
                this.editReviewForm.age = 0;
            } else {
                this.editReviewForm.age = Number(val);
            }
        },
        onSelectSexType(item) {
            this.editReviewForm.gender = item.id;
            this.editReviewForm.isShowSexPicker = false;
        },
        onSelectTag(item) {
            this.editReviewForm.type = item.id;
            this.isShowSelectTag = false;
        },
        closeEditFieldSheet() {
            this.dialogVisible = false;
            setTimeout(() => {
                this.clearEditForm();
            }, 500);
        },
        async submit() {
            if (this.progressShow) {
                Toast(this.lang.file_in_progress);
                return;
            }
            if (this.editReviewForm.imageUrl.includes("blob")) {
                await this.uploadLiveCoverImage();
            }
            this.isSubmitting = true;
            let params = {
                resourceId: this.message.resource_id,
                type: this.editReviewForm.type,
                group_id: this.message.group_id,
                subject: this.editReviewForm.subject,
                description: this.editReviewForm.description,
                speaker: this.editReviewForm.speaker,
                filePath: this.editReviewForm.ossImageUrl,
            };
            window.main_screen.editReviewRecording(params, async (res) => {
                this.isSubmitting = false;
                Toast(this.lang.operate_success);
                this.closeEditFieldSheet();
                this.$emit('updateReviewForm',params)
                // this.$store.commit("conversationList/updateChatMessageLiveRecordData", {
                //     group_id: this.message.group_id,
                //     resource_id: this.message.resource_id,
                //     live_record_data: this.editReviewForm,
                // });
                // this.$store.commit("consultationImageList/updateConsultationLiveRecordData", {
                //     resource_id: this.message.resource_id,
                //     live_record_data: this.editReviewForm,
                // });
                if(this.editReviewForm.type === 1){ //通用直播不加群标签
                    return
                }
                let name = [this.lang.universal_live, this.lang.consultation_live, this.lang.teaching_live];
                let insertParams = {
                    resource_id: this.message.resource_id,
                    name: name[this.editReviewForm.type - 1],
                    gid: this.message.group_id,
                };
                const category_data = await this.insertResourceToCategoryByName(insertParams);
            });
        },
        formatTemplateType(value) {
            let arr = ["", "common", "consultation", "teaching"];
            return arr[Number(value)];
        },
        openEditTargetModal() {
            this.dialogVisible = true;
            const val = this.message;
            if (val.hasOwnProperty("live_record_data") && val.live_record_data.hasOwnProperty("type")) {
                Object.keys(val.live_record_data).forEach((key) => {
                    if (key === "subject") {
                    } else {
                        this.editReviewForm[key] = val.live_record_data[key];
                    }
                });
                this.editReviewForm["subject"] = this.getRecordSubject(val);
                this.editReviewForm.imageUrl = val.coverUrl;
                if(val.coverUrl){
                    this.editReviewForm.fileList = [{ url: val.coverUrl, isImage: true }];
                }

                this.originCoverImage = val.coverUrl
            }
        },
        insertResourceToCategoryByName(params) {
            return new Promise((resolve, reject) => {
                window.main_screen.insertResourceToCategoryByName(params, (res) => {
                    if (!res.error_code) {
                        resolve(res.data);
                    } else {
                        reject(res.error_msg);
                    }
                });
            });
        },
        beforeRead(file) {
            const isJPG = file.type === "image/jpeg";
            const isPNG = file.type === "image/png";
            const isLt10M = file.size / 1024 / 1024 < 10;

            if (!isJPG && !isPNG) {
                Toast(this.lang.upload_forbidden_file_type_text);
            }
            if (!isLt10M) {
                Toast(`${this.lang.upload_max_text} 10M`);
            }
            return (isJPG || isPNG) && isLt10M;
        },
        afterRead(file) {
            this.editReviewForm.imageUrl = URL.createObjectURL(file.file);
            this.editReviewForm.file = { raw: file.file, name: file.file.name };
            console.log(this.editReviewForm);
        },
        onOversize(file) {
            console.log(file);
            Toast(`${this.lang.upload_max_text} 10M`);
        },
        handlePreviewClose(e) {
            console.log(e);
        },
        clearUploadFileList() {
            this.editReviewForm.fileList = [];
            this.editReviewForm.file = null;
            this.editReviewForm.imageUrl = "";
            this.editReviewForm.ossImageUrl = "";
        },
        previewImage(file) {
            let images = this.editReviewForm.imageUrl;

            // 使用 DialogManager 管理图片预览弹窗
            this.imagePreviewDialogId = DialogManager.register(this, {
                open: () => {
                    this.previewInstance.instance = ImagePreview({
                        images: [images],
                        closeOnPopstate: false,
                        onClose: () => {
                            // 当用户手动关闭预览时，从 DialogManager 注销
                            if (this.imagePreviewDialogId) {
                                DialogManager.unregister(this.imagePreviewDialogId);
                                this.imagePreviewDialogId = null;
                            }
                            this.previewInstance.isShow = false;
                        },
                    });
                    this.previewInstance.isShow = true;
                },
                close: () => {
                    if (this.previewInstance.instance) {
                        this.previewInstance.instance.close();
                        this.previewInstance.isShow = false;
                    }
                },
                canCloseOnPopstate: true,
                canClose: true
            });
        },
        uploadLiveCoverImage() {
            return new Promise(async (resolve, reject) => {
                let file = this.editReviewForm.file;
                let form = new FormData();
                form.append("file", file.raw);
                let uploadConfig = this.systemConfig.serverInfo.file_upload_config;
                let format = Tool.getFileType(file.name);
                let filePath = `${uploadConfig.subDir.liveCoverImage}/${new Date().getTime()}.${format}`;
                this.progressShow = true;
                this.percentage = 0;
                uploadFile({
                    bucket:uploadConfig.ossInfo.bucket,
                    filePath:filePath,
                    file:file.raw,
                    callback:(event, data)=>{
                        console.log("uploadFile", event, data);
                        if ("complete" == event) {
                            this.editReviewForm.ossImageUrl = filePath;
                            setTimeout(() => {
                                this.progressShow = false;
                                resolve(true);
                            }, 600);
                        } else if ("progress" === event) {
                            // this.$set(this.addForm,'percentage',data)
                            this.percentage = data;
                        } else if ("error" == event) {
                            Toast("upload error");
                            this.progressShow = false;
                            this.percentage = 0
                            reject(false);
                        }
                    }
                })
            });
        },
        getFile(file) {
            this.editReviewForm.imageUrl = file.blobUrl;
            this.editReviewForm.file = file;
            this.editReviewForm.fileList = [{ url: file.blobUrl, isImage: true }];
        },
        openCropperModal() {
            this.cropperVisible = true;
            this.$nextTick(() => {
                this.$refs.cropper.loadImageUrl(this.editReviewForm.imageUrl);
            });
        },
        async clickUpload(){
            await Tool.queryAppPermissions(['CAMERA'])
            this.hasCameraPermissions = true
            this.$refs.Uploader.chooseFile()
        }
    },
};
</script>
<style lang="scss">
.review-action-sheet {
    // height: 100%;
    position: absolute;
    top: 0px;
    display: flex;
    flex-direction: column;
    .van-action-sheet__header {
        display: flex;
        justify-content: center;
        .van-action-sheet__title {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 65%;
            .van-icon {
                flex-shrink: 0;
            }
        }
    }
    .van-form{
        flex: 1;
        overflow: auto;
    }
    .van-dialog__footer {
        // position: absolute;
        // width: 100%;
        // bottom: 0;
    }
    .van-form {
        .van-field {
            flex-direction: column;
            .van-field__label {
            }
            .van-field__value {
                border: 1px solid #dcdfe6;
                border-radius: 5px;
                padding: 0.3rem;
            }
        }
    }
    .van-cell::after {
        border: none;
    }
    .van-uploader {
        .van-uploader__preview {
            width: 100%;
        }
        .van-uploader__upload {
            width: 100% !important;
        }
        .van-uploader__preview-image {
            width: 100% !important;
            img {
                object-fit: contain !important;
            }
        }
        .van-uploader__preview-delete {
            width: 24px;
            height: 24px;
        }
        .van-uploader__preview-delete-icon {
            transform: scale(1);
            font-size: 16px;
            top: 2px;
            right: 2px;
        }
        .van-circle {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate3d(-50%, -50%, 0);
            .van-circle__text {
                color: #fff;
            }
        }
        .preview-cover {
            position: absolute;
            bottom: 0;
            box-sizing: border-box;
            width: 100%;
            padding: 4px;
            color: #fff;
            font-size: 12px;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: flex-end;
            .preview-btn {
                width: 1.2rem;
                height: 1.2rem;
                color: #fff;
                font-size: 1.2rem;
                margin-left: 0.6rem;
            }
        }
    }
    .invisible_modal{
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
    }
}
</style>
