<template>
	<transition name="slide">
		<div class="storage_setting third_level_page">
            <mrHeader>
                <template #title>
                    {{lang.ftp_info}}
                </template>
            </mrHeader>
			<div class="container">
				<div class="row">
                    <p>{{lang.ftp_anonymous}}：</p>
                    <div class="anonymous needsclick">
						<van-switch v-model="isAnonymous"  active-color="#00c59d" inactive-color="#D9D9D9" />
                    </div>
                </div>
				<div class="row" v-show="!isAnonymous">
					<p class="modify_tip">{{lang.ftp_account}}</p>
					<input type="text" v-model="ftp_account" maxlength="40" ref="ftp_account" >
				</div>
				<div class="row" v-show="!isAnonymous">
					<p class="modify_tip">{{lang.ftp_password}}</p>
					<input type="password" v-model="ftp_password" maxlength="40" ref="ftp_password">
				</div>
				<div class="row">
					<p class="modify_tip">{{lang.ftp_port}}</p>
					<input type="text" v-model="ftp_port" maxlength="60" ref="ftp_port">
				</div>
				<div class="row">
					<p class="modify_tip">{{lang.ftp_path}}</p>
					<input type="text" v-model="ftp_path" maxlength="60" ref="ftp_path">
				</div>
				<div class="row">
					<p class="comment">{{lang.ftp_path_tip}}</p>
				</div>
			</div>
			<div class="btn_warp">
				<button class="primary_bg modify_subject_btn" @click="submit">{{lang.confirm_txt}}</button>
			</div>

		</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import { Toast, Switch } from 'vant';
export default {
    mixins: [base],
    name: 'DeviceStorageSetting',
    components: {
        VanSwitch: Switch
    },
    data(){
    	return {
    		ftp_account:'',
    		ftp_password:'',
    		isAnonymous:false,
    		ftp_path:'',
    		ftp_port:22,
    		device_id:this.$route.params.device_id,
    		device:{}
    	}
    },
    mounted(){
        this.$nextTick(()=>{
        	this.device=this.$store.state.deviceCtrl[this.device_id]
        	this.ftp_account = this.device.ftp_account;
        	this.ftp_password = this.device.ftp_password;
        	this.isAnonymous = this.device.isAnonymous;
        	this.ftp_path = this.device.ftp_path;
        	this.ftp_port = this.device.ftp_port;
        })
    },
    computed:{
    },
    methods:{
    	submit(){
    		if (!this.isAnonymous) {
    			if (this.ftp_account=='') {
	    			Toast(this.lang.please_enter_ftp_account)
	    			return;
	    		} else if(this.ftp_password==''){
	    			Toast(this.lang.please_enter_ftp_password);
	    			return;
	    		}
    		}
    		if (this.ftp_path=='') {
    			Toast(this.lang.please_enter_ftp_path)
    			return;
    		}
    		if (this.ftp_port=='') {
    			Toast(this.lang.please_enter_ftp_path)
    			return;
    		}
    		this.$root.eventBus.$emit('deviceToggleFTP',{
    			isAnonymous:this.isAnonymous,
    			ftp_account:this.ftp_account,
    			ftp_password:this.ftp_password,
    			ftp_path:this.ftp_path,
    			ftp_port:this.ftp_port,
    		})
    		this.back();

    	},
    	toggleAnonymous(){

    	}
    }
}

</script>
<style lang="scss">
	.storage_setting{
		.container{
			padding: 0 .8rem;
		    background: #fff;
		    margin-top: 1rem;
		    border-top: 1px solid #c8c7cc;
		    border-bottom: 1px solid #c8c7cc;
		    color:#333;
		    .row{
		    	border-top: 1px solid #c8c7cc;
			    display: flex;
			    flex-direction: row;
			    &:first-child{
			    	border-top:none;
			    }
			    p{
			    	line-height: 2rem;
				    font-size: 1rem;
				    margin-right: 1rem;
			    }
			    & > input{
			    	flex: 1;
				    border: none;
				    line-height: 2rem;
				    font-size: 1rem;
				    display: block;
				    color: #333;
			    }
			    .anonymous{
			    	padding:0.3rem 0;
			    }
			    .modify_tip{
			    	word-break: keep-all
			    }
			    .comment{
			    	font-size: 0.7rem;
			    	color: red;
			    }
		    }
		}
		.btn_warp{
			margin:1rem;
			.modify_subject_btn{
				display: block;
			    width: 100%;
			    border: none;
			    font-size: 1rem;
			    line-height: 2rem;
			    margin: 1rem 0 .6rem;
			    border-radius: .2rem;
			}
		}

	}
</style>
