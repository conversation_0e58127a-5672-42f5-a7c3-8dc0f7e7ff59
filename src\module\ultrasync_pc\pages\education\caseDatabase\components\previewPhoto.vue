 <template>
    <div class="case_gallery" >
        <div v-if="isCaseImage">
            <div class="image_gallery">
                <div class="wrap_header" @click="hideImage(false)">
                    <i class="iconfont iconsearchclose"></i>
                </div>
                <span class="images_index" v-if="pathologyInfo.ultrasonic_diagnosis_list!=undefined&&pathologyInfo.ultrasonic_diagnosis_list.length>0">
                    {{sliderKey+1}}/{{pathologyInfo.ultrasonic_diagnosis_list.length}}
                </span>
                <span class="right_btn" @click="nextPage"  v-if="pathologyInfo.ultrasonic_diagnosis_list!=undefined&&isRight&&pathologyInfo.ultrasonic_diagnosis_list.length>1"></span>
                <span class="left_btn" @click="prevPage" v-if="isLeft"></span>
                <div class="mui-slider" id="case_disease" ref="thumb_scroll_wrap">
                    <div class="mui-slider-group" ref="sliderGroup">
                         <div v-for="(items,index) in pathologyInfo.ultrasonic_diagnosis_list" class="mui-slider-item mui-zoom-wrapper" :key="index">
                            <div class="image_wrap mui-zoom-scroller">
                                 <img :src="prefUrl+items.path" class="file mui-zoom" draggable="false">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="case_result">
                <div class="color_frame">
                    <div v-for="index of 5" :key="index" :class="'color_item_'+index" ></div>
                </div>
                <div class="case_dignose">
                    <p class="title">{{lang.ultrasonic_diagnosis}}:</p>
                    <p class="info">{{pathologyResults}}</p>
                </div>
                <div class="case_pathology">
                    <p class="title">{{lang.pthological_conclusion}}:</p>
                    <p class="info">{{pathologyInfo.postoperative_pathology}}:{{pathologyInfo.benign_malignant}}</p>
                </div>
            </div>
         </div>
        <div  v-else>
            <canvas style="display:none" id="ai_canvas"></canvas>
            <div class="image_gallery">
                <div class="wrap_header" @click="hideImage(false)">
                    <i class="iconfont iconsearchclose"></i>
                </div>

                <!-- <div class="mui-slider" id="case_disease" ref="thumb_scroll_wrap"> -->
                    <!-- <div  class="mui-slider-item mui-zoom-wrapper"> -->
                    <div  class=" not_case_image" ref="notCaseImage" id="notCaseImage">
                        <img :src="showUrl" class="not_case_image_content" ref="notCaseImageWrap" id="notCaseImageWrap">
                    </div>
                    <!-- </div> -->
                <!-- </div> -->
            </div>
            <div class="case_result case_result_image" >
                <div class="color_frame">
                    <div v-for="index of 5" :key="index" :class="'color_item_'+index" ></div>
                </div>
               <div class="case_dignose ">
                    <p class="title">{{lang.ai_searc_in_case_database}}:</p>

                    <p class="info">
                        {{ lang.searc_in_case_database_result.replace('{1}',rois.length)}}
                    </p>
                    <template v-if="rois.length>1">
                        <!-- <p class="title">选择病灶：</p> -->
                        <div>
                            <div >
                                <el-radio-group v-model="selectRoi">
                                    <el-radio v-for="(roi,index) in rois"
                                    :key="index"
                                    :label="index"
                                    :class="'radion-column'"
                                    :name="index+''">
                                    <div class='color_sig' :style="{background:colors[index]}"></div>
                                    {{lang.thyroid.focus }}{{index+1}}
                                </el-radio>

                                </el-radio-group>
                            </div>
                        </div>
                    </template>

                </div>
                <p class="edit_comment_btn" @click='hideImage(true)' v-if="rois.length>1" >{{lang.search}}</p>
            </div>
        </div>
    </div>
</template>
<script>
import base from '../../../../lib/base';
import Tool from '@/common/tool.js'
export default {
    mixins: [base],
    components: {},
    props:{
        pathologyInfo:{
            type:Object,
            default:()=>{
                return {}
            },
        },
        isCaseImage: {
            type: Boolean,
            default: true
        },
        imageObj:{
            type: Object,
            default: null
        }
    },
    data(){
        return {
            sliderKey:0,
            pathologyResults:'',
            sliderObj:{},
            isRight:true,
            isLeft:false,
            colors:['#FF0000','#00FF00','#0000FF','#8000FF','#FFFFFF','#FFFF00'],
            selectRoi: 0,
            showUrl: '',
            oldSelectRoi:0,
        }
    },
    mounted(){
        let that = this
        setTimeout(()=>{
            if(this.isCaseImage){
                document.getElementById('case_disease').addEventListener('slide',this.slideHandler);
                var gallery=window.mui('#case_disease')
                console.log(gallery)
                var sliderObj=gallery.slider()
                console.log(sliderObj)
                console.log(this.sliderKey)
                sliderObj.gotoItem(this.sliderKey,0)
                // window.mui('.mui-zoom-wrapper').zoom();
                this.pathologyResults=Tool.getPathologyConclusion(this.pathologyInfo,this.sliderKey)
                console.log(typeof this.sliderKey)
                // var pathologyInfoLength=`$(pathologyInfo.ultrasonic_diagnosis_list)`
            } else{
                if(that.imageObj.imageUrl.indexOf('blob:http://')>-1 || that.imageObj.imageUrl.indexOf('blob:https://')>-1){
                    let reader = new FileReader()
                    reader.onload = function (e) {
                        let data = e.target.result
                        let image = new Image()
                        image.src= data
                        image.onload=function(){
                            that.showUrl=that.drawCanvasToImage(image)
                        }
                        // console.error('that.imageObj.file:',that.imageObj.file)
                    };
                    reader.readAsDataURL(that.imageObj.file);
                }else{
                    let image=new Image()
                    image.onerror=function(e){
                        console.log('preloadError',e)
                    }
                    image.onload=function(){
                        that.showUrl=that.drawCanvasToImage(image)
                    }
                    if(that.systemConfig.serverInfo.network_environment === 1){
                        that.imageObj.file = Tool.replaceInternalNetworkEnvImageHost(that.imageObj.file)
                    }
                    image.setAttribute("crossOrigin",'anonymous');
                    image.src=`${that.imageObj.file}?temp=${Date.now()}`;
                }
            }
        },0)
    },
    watch:{

    },
    computed:{
        prefUrl(){
            return this.systemConfig.serverInfo.oss_attachment_server.playback_https_addr + '/' + 'AiSearch' + '/'
        },
        rois(){
            // let xis = [50,150,290,200]
            // xis = [500,850,550,690]
            // xis = [700,1150,750,890]
            // xis = [900,650,950,790]
            // return [
            //     [50,150,290,200],
            //     [500,850,550,690],
            //     [700,1150,750,890],
            // ]
            if(this.imageObj && this.imageObj.selectRoi){
                this.selectRoi = this.imageObj.selectRoi||0
                this.oldSelectRoi = this.imageObj.selectRoi||0
            }
            return this.imageObj.roi || []
        }
    },
    methods:{
        slideHandler(event){
            console.log(event)
            this.sliderKey = event.detail.slideNumber
            console.log(this.sliderKey)
            if(this.sliderKey<=0){
                this.isRight=true;
                this.isLeft=false;
            }else if(this.sliderKey>0){
                this.isRight=false;
                this.isLeft=true;
            }
        },
        hideImage(isSubmit){
            if(this.isCaseImage){
                this.$emit('showImageEvent',false);
            }else{
                this.selectRoi = isSubmit? this.selectRoi : (this.oldSelectRoi)
                this.$emit('showSearchImageEvent',{flag:this.selectRoi!=this.oldSelectRoi,roiIndex:this.selectRoi});
            }
        },
        nextPage(){
            this.sliderKey++;
            var gallery=window.mui('#case_disease')
            var sliderObj=gallery.slider()
            sliderObj.gotoItem(this.sliderKey,400)
        },
        prevPage(){
            this.sliderKey--;
            var gallery=window.mui('#case_disease')
            var sliderObj=gallery.slider()
            sliderObj.gotoItem(this.sliderKey,400)
        },
        drawCanvasToImage(realImage){
            let that = this
            //左右[x1,y1,x2,y2]
            var canvas=document.getElementById('ai_canvas')
            var context=canvas.getContext('2d');
            canvas.width=realImage.width;
            canvas.height=realImage.height;
            context.drawImage(realImage,0,0);
            context.strokeStyle="#f00"
            context.lineWidth=2

            if(this.rois){
                let i = 0
                for(let roi of this.rois){
                    context.beginPath();
                    context.moveTo(roi[0], roi[1]); //把画笔移动到指定的坐标
                    context.lineTo(roi[2], roi[1]);  //绘制一条从当前位置到指定坐标(200, 50)的直线.
                    context.lineTo(roi[2], roi[3]);
                    context.lineTo(roi[0], roi[3]);
                    //闭合路径。会拉一条从当前点到path起始点的直线。如果当前点与起始点重合，则什么都不做
                    context.closePath();
                    context.strokeStyle = this.colors[i];
                    context.lineWidth=8;
                    // context.fillStyle = this.colors[i];
                    // context.fill();
                    context.stroke(); //绘制路径。
                    ++i
                }
            }
            // context.beginPath()
            // context.arc(,8,0,2*Math.PI);
            // context.strokeStyle = "black"
            // context.fillStyle = "";
            // context.fill() //填充当前路径颜色
            // context.stroke() //绘制已/定义的路径颜色
            // context.fillStyle = "black"
            // context.font = "20px Times New Roman"
            // context.textBaseline = "middle"
            // context.fillText(1, (xis[2] + xis[0]) / 2,(xis[3] + xis[1]) / 2)
            let base64=canvas.toDataURL("image/jpeg");
            return base64;
        },
    },
}
</script>
<style lang="scss">
.case_gallery{
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    display:flex;
    z-index: 1;
    .image_gallery{
        position:absolute;
        top:0;
        left:0;
        right:0;
        bottom:0;
        background-color:#000000;
        flex:1;
        .case_name{
            color:#fff;
            position: absolute;
            left: 6rem;
            top: 1.5rem;
            font-size:1rem;
            z-index:100;
        }
        .iconsearchclose{
            z-index:10;
            color:white;
            cursor: pointer;
        }

        .cancel_wrap{
            color:#fff;
            position: absolute;
            right: 1rem;
            top: 1.5rem;
            i{
                font-size:1rem;
            }
        }
        .images_index{
            position: absolute;
            right: 260px;
            top: 40px;
            border: 5px solid gray;
            text-align: center;
            background-color: gray;
            border-radius: 20px;
            font-size: 0.5rem;
            color: white;
            letter-spacing: 1px;
            z-index: 999;
            user-select: none;
        }
        .right_btn{
            position: absolute;
            top: 50%;
            right: 14rem;
            width:21px;
            height:21px;
            transform:rotate(45deg);
            border-top:2px solid white;
            border-right:2px solid white;
            z-index:999;
            cursor:pointer;
        }
        .left_btn{
            position: absolute;
            top: 50%;
            left: 1rem;
            width:21px;
            height:21px;
            transform:rotate(45deg);
            border-bottom:2px solid white;
            border-left:2px solid white;
            z-index:999;
            cursor:pointer;
        }
        .mui-slider{
         width: calc(100% - 250px);
         height:100%;
         overflow:hidden;
        .mui-slider-group{
            display: flex;
            flex-direction: row;
            height: 100%;
            width:100%;
        .mui-slider-item{
            width: 100%;
            height: 100%;
            flex-shrink: 0;
            .image_wrap{
                width:100%;
                height:100%;
                display: flex;
                align-items: center;
                justify-content: center;
                img{
                    max-width:100%;
                    max-height:100%;
                        }
                    }
                }
            }
        }
        .wrap_header{
            position: absolute;
            right: 260px;
            top: 10px;
            z-index:2;
        }
    }
    .case_result{
        position:absolute;
        top:0;
        right:0;
        height: 100%;
        width:250px;
        display: flex;
        flex-direction: column;
        align-items: left;
        background-color: #212121;
        font-size: 0.7rem;
        line-height: 1.25rem;
        .color_frame{
            height: 0.2rem;
            width: 100%;
            display: flex;
            .color_item_1{
                width: 20%;
                background-color:rgb(255,103,92);
            }
            .color_item_2{
                width: 20%;
                background-color:#00c59d;
            }
            .color_item_3{
                width: 20%;
                background-color:rgb(255,177,68);
            }
            .color_item_4{
                width: 20%;
                background-color:rgb(86,200,255);
            }
            .color_item_5{
                width: 20%;
                background-color:rgb(116,128,234);
            }

        }
        .case_dignose,.case_pathology{
            padding: 1rem;
            .title{
                color: #00c59d;
                font-size: 0.9rem;
                margin: 0.5rem 0;
            }
            .info{
                word-wrap:normal;
                word-break:normal;
                color:#c9cfce;
            }
        }
    }
    .not_case_image{
        text-align:center;
        display:flex;
        justify-content:center;
        width:calc(100% - 250px);
        height:calc(100%);
    }
    .not_case_image_content{
        object-fit: contain;
        width:calc(100%);
        height:calc(100%);
    }
    .radion-column{
        display:block;
        padding:5px;
        color:white;
    }
    .edit_comment_btn{
        font-size: 0.7rem;
        padding: 0.2rem 0.3rem;
        background-color: #01bd97;
        line-height: 1rem;
        margin: 0.4rem 0.6rem;
        border-radius: 0.2rem;
        text-align: center;
        color:white;
        cursor:default;
    }
    .color_sig{
        width:12px;
        height:12px;
        display:inline-block;
    }
}
</style>
