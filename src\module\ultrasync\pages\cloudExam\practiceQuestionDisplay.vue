<template>
    <div class="question-container">
        <div class="question-header">
            <div class="title-bar"></div>
            <span class="title">{{ hideField?`${lang.part}-`:title }}({{ bodyPart | formatTopicType }})</span>
        </div>

        <div class="clinical-info">
            <!-- 文字内容渲染 -->
            <div v-if="textContent && textContent.length" class="text-section">
                <div v-for="(item, index) in textContent" :key="`text-${index}`">
                    <h5 class="section-title">{{ item.title }}</h5>
                    <p class="section-content" v-html="renderMarkdown(item.content)"></p>
                </div>
            </div>

            <!-- 图片内容渲染 -->
            <div class="images-grid" v-if="images && images.length">
                <div v-for="(image, index) in images" :key="`image-${index}`" class="image-item">
                    <van-image :src="getImageUrl(image.path)" fit="cover" @click="previewImage(index)" />
                    <p class="image-desc" v-html="renderMarkdown(image.desc)"></p>
                </div>
            </div>

            <!-- 视频内容渲染 - 使用VideoPlayer组件 -->
            <div class="videos-container" v-if="videos && videos.length">
                <h5 class="video-title">{{lang.video_materials}}</h5>
                <div v-for="(video, index) in videos" :key="`video-${index}`" class="video-item">
                    <VideoPlayer :videoSrc="getVideoUrl(video.path)" :autoPlay="false" />
                    <p class="video-desc" v-html="renderMarkdown(video.desc)"></p>
                </div>
            </div>
        </div>
        <div class="question_tips">
            <h4 class="tips_title">{{lang.answering_requirements}}</h4>
            <p class="tips_content">{{ questionTips }}</p>
        </div>
    </div>
</template>

<script>
import { ImagePreview, Image } from "vant";
import AIBookServiceInstance from "@/common/aiBookService";
import DialogManager from "../../lib/dialogManager";
import VideoPlayer from "../../MRComponents/videoPlayer.vue";
import base from "../../lib/base";
export default {
    mixins: [base],
    name: "QuestionDisplay",
    props: {
        clinicalData: {
            type: Object,
            required: true,
        },
        imagePreviewShow: {
            type: Boolean,
            default: false,
        },
    },
    filters: {
        formatTopicType(topicType) {
            switch (topicType) {
            case "superficial":
                return "浅表介入";
            case "abdomen":
                return "腹部";
            case "cardio":
                return "心血管";
            case "gyn":
                return "妇产";
            default:
                return "未知";
            }
        },
    },
    components: {
        [ImagePreview.name]: ImagePreview,
        [Image.name]: Image,
        VideoPlayer
    },
    data() {
        return {
            players: [],
            aiBookService: AIBookServiceInstance,
            imagePreview: null,
            imagePreviewDialogId: null,
        };
    },
    watch: {
        // 监听外部控制的预览状态
        imagePreviewShow(val) {
            if (!val && this.imagePreviewDialogId) {
                // 外部要求关闭预览
                DialogManager.unregister(this.imagePreviewDialogId);
                this.imagePreviewDialogId = null;
            }
        }
    },
    computed: {
        title() {
            return this.clinicalData.title;
        },
        textContent() {
            if (!this.clinicalData.content || !this.clinicalData.content.txt) {
                return [];
            }
            return this.clinicalData.content.txt || [];
        },
        images() {
            if (!this.clinicalData.content || !this.clinicalData.content.image) {
                return [];
            }
            return this.clinicalData.content.image || [];
        },
        videos() {
            if (!this.clinicalData.content || !this.clinicalData.content.video) {
                return [];
            }
            return this.clinicalData.content.video || [];
        },
        questionTips() {
            return this.clinicalData.questionTips || "";
        },
        bodyPart() {
            return this.clinicalData.bodyPart || "";
        },
        hideField() {
            return this.clinicalData.hideField || false;
        },
    },
    methods: {
        renderMarkdown(content) {
            if (!content) {
                return "";
            }
            return this.aiBookService.renderMarkdown(content);
        },
        previewImage(index) {
            const imageUrls = this.images.map((img) => this.getImageUrl(img.path));

            // 通知父组件预览已打开
            this.$emit('update:imagePreviewShow', true);

            // 使用 DialogManager 管理图片预览弹窗
            this.imagePreviewDialogId = DialogManager.register(this, {
                open: () => {
                    this.imagePreview = ImagePreview({
                        images: imageUrls,
                        startPosition: index,
                        closeable: true,
                        closeOnPopstate: false,
                        onClose: () => {
                            // 当用户手动关闭预览时，从 DialogManager 注销
                            if (this.imagePreviewDialogId) {
                                DialogManager.unregister(this.imagePreviewDialogId);
                                this.imagePreviewDialogId = null;
                            }
                            // 通知父组件预览已关闭
                            this.$emit('update:imagePreviewShow', false);
                        },
                    });
                },
                close: () => {
                    if (this.imagePreview) {
                        this.imagePreview.close();
                        this.imagePreview = null;
                    }
                },
                canCloseOnPopstate: true,
                canClose: true
            });
        },
        getImageUrl(path) {
            // 根据您的实际情况处理图片URL
            return path;
        },
        getVideoUrl(path) {
            // 根据您的实际情况处理视频URL
            return path;
        },
    },
    mounted() {
        // 不再需要手动初始化视频播放器
    },
    updated() {
        // 不再需要手动初始化视频播放器
    },
    beforeDestroy() {
        // 清理图片预览弹窗
        if (this.imagePreviewDialogId) {
            DialogManager.unregister(this.imagePreviewDialogId);
            this.imagePreviewDialogId = null;
        }
        if (this.imagePreview) {
            this.imagePreview.close();
            this.imagePreview = null;
        }
    },
};
</script>

<style lang="scss" scoped>
.question-container {
    background-color: #fff;
    box-shadow: 0 0.087rem 0.437rem rgba(0, 0, 0, 0.05);
    margin-bottom: 0.5rem;
    * {
        user-select: text !important;
    }
    .question-header {
        padding: 0.8rem 0.8rem;
        display: flex;
        align-items: center;
        border-bottom: 0.044rem solid #f5f5f5;

        .title-bar {
            width: 0.175rem;
            height: 0.7rem;
            background-color: #1989fa;
            margin-right: 0.4rem;
            border-radius: 0.087rem;
        }

        .title {
            font-size: 0.8rem;
            font-weight: 600;
            color: #333;
            user-select: text !important;
        }
    }

    .clinical-info {
        padding: 0.8rem;
        background: #fff;
        .info-title {
            font-size: 0.7rem;
            margin-bottom: 0.6rem;
            color: #333;
            font-weight: 500;
        }

        .text-section {
            margin-bottom: 0.8rem;

            .section-title {
                font-size: 0.7rem;
                font-weight: 600;
                color: #333;
                margin-bottom: 0.3rem;
            }

            .section-content {
                font-size: 0.65rem;
                line-height: 1.8;
                color: #333;
                margin-bottom: 0.8rem;
                user-select: text !important;
                :deep(p) {
                    user-select: text !important;
                }
            }
        }

        .images-grid {
            margin: 0.8rem 0;
            display: flex;
            flex-direction: column;
            gap: 0.6rem;
            margin: 0.8rem 0;

            .image-item {
                display: flex;
                flex-direction: column;

                .van-image {
                    width: 100%;
                    height: 8rem;
                    border-radius: 0.3rem;
                    overflow: hidden;
                    box-shadow: 0 0.087rem 0.437rem rgba(0, 0, 0, 0.1);
                }

                .image-desc {
                    font-size: 0.6rem;
                    color: #666;
                    margin-top: 0.5rem;
                    line-height: 1.4;
                    user-select: text !important;
                    :deep(p) {
                        user-select: text !important;
                    }
                }
            }
        }

        .videos-container {
            margin: 1rem 0;

            .video-title {
                font-size: 0.7rem;
                font-weight: 500;
                color: #333;
                margin-bottom: 0.6rem;
            }

            .video-item {
                margin-bottom: 1.2rem;
                .video-desc {
                    font-size: 0.6rem;
                    color: #666;
                    margin-top: 0.5rem;
                    line-height: 1.4;
                }
            }
        }
    }
    .question_tips {
        padding: 0.6rem;
        background-color: #f5f7fa;
        border-left: 0.15rem solid #1989fa;

        .tips_title {
            font-size: 0.7rem;
            color: #303133;
            margin: 0 0 0.4rem 0;
            font-weight: 600;
        }

        .tips_content {
            font-size: 0.65rem;
            color: #606266;
            line-height: 1.6;
            margin: 0;
        }
    }
}

/* 确保Plyr样式正确应用 */
:deep(.plyr) {
    --plyr-color-main: #1989fa;
    border-radius: 0.3rem;
}
</style>
