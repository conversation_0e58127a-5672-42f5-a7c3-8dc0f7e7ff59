import Tool from '@/common/tool'
import { uploadTaskMap } from './index'

// AWS SDK 加载状态管理
let AWS = null;
let isLoading = false;
let loadPromise = null;

// 全局变量保持不变
let currentBucket = ''
let currentFilePath = ''
let currentFile = null
let currentProgressFn = null
let currentSuccessFn = null
let currentErrorFn = null
let currentClient = null

// 内部加载函数
const ensureAWS = async () => {
    if (AWS) {
        return AWS;
    }

    if (isLoading && loadPromise) {
        return loadPromise;
    }

    isLoading = true;
    loadPromise = import(/* webpackPrefetch: true */ 'aws-sdk').then(module => {
        AWS = module.default;
        isLoading = false;
        return AWS;
    }).catch(error => {
        isLoading = false;
        loadPromise = null;
        throw error;
    });

    return loadPromise;
};

function handleMinioProgress ({percentage,callback,ossClient,uploadId}) {
    let progress = Math.round(percentage * 100)
    if(!uploadTaskMap.hasOwnProperty(uploadId)){
        uploadTaskMap[uploadId] = {}
    }
    if(ossClient){
        uploadTaskMap[uploadId].ossClient =  ossClient
    }
    uploadTaskMap[uploadId].callback = callback
    uploadTaskMap[uploadId].percentage = percentage
    uploadTaskMap[uploadId].uploading = true
    uploadTaskMap[uploadId].paused = false // 初始化暂停状态
    uploadTaskMap[uploadId].lastActivity = Date.now() // 添加活跃时间跟踪
    console.log("progress",uploadId,progress)
    callback && callback("progress", progress,uploadId);
}

const handleMinioUploadSuccess = async (res,callback,uploadId) => {
    console.log("[event] uploadMinioFile -- success", res);
    const url = res.Location;
    callback && callback("complete", url);
    delete uploadTaskMap[uploadId]
}

const handleMinioUploadError = async ({e,callback,uploadId}) => {
    console.log("[event] uploadFile -- error",e,uploadTaskMap,uploadId);
    if(!uploadTaskMap[uploadId]){
        return
    }
    uploadTaskMap[uploadId].error = true
    uploadTaskMap[uploadId].uploading = false
    callback && callback("error", e, uploadId);
}

const handleUpload = ({ossClient,file,filePath},{progress,success,error})=>{
    currentFilePath = filePath
    currentFile = file
    currentProgressFn = progress
    currentSuccessFn = success
    currentErrorFn = error
    currentClient = ossClient
    const params = {
        Bucket: currentBucket,
        Key: filePath,
        Body: file,
        ContentType: file.type
    };

    ossClient.uploadManaged = ossClient.upload(params)

    ossClient.uploadManaged.on('httpUploadProgress', function(evt) {
        const percent = evt.loaded / evt.total;
        console.log(`已上传: ${percent}`);
        progress&&progress(percent);
    })
    ossClient.uploadManaged.send((err, data) => {
        if (err) {
            console.log(err, err.stack);
            error&&error(err)
        } else {
            console.log(data);
            success&&success(data)
        }
    });
}

const handleCancelUpload = ()=>{
    currentClient.uploadManaged.abort();
}

export async function MinioGetOssClient(bucket,fileName) {
    try {
        let ping = true
        if(Tool.checkAppClient('Huawei')){
            ping = false
        }

        await Tool.handleAfterMainScreenCreated(ping)
        const minioConfig = window.vm.$store.state.systemConfig.serverInfo.minio
        const storageReplaceInfo = window.vm.$store.state.systemConfig.serverInfo.storageReplaceInfo
        const nginx_address = storageReplaceInfo.nginx_address
        const isReplace = storageReplaceInfo.replace
        const storage_address = storageReplaceInfo.storage_address
        let minioEndPoint = `${minioConfig.protocol}${minioConfig.endPoint}:${minioConfig.port}`
        if(isReplace&&nginx_address){
            minioEndPoint = nginx_address
        }
        currentBucket = bucket

        // 动态加载 AWS SDK
        const AWS = await ensureAWS();

        AWS.config.update({
            region: 'your-region',
            endpoint: new AWS.Endpoint(minioEndPoint),
            accessKeyId: minioConfig.accessKey,
            secretAccessKey: minioConfig.secretKey,
            s3ForcePathStyle: true,
        });

        return new AWS.S3();
    } catch (error) {
        throw error;
    }
}

export function MinioUploadFile ({
    bucket='',
    file=null,
    filePath ='',
    callback = null,
    timeout = 30000
}){
    return new Promise(async(resolve, reject) => {
        const systemConfig = window.vm.$store.state.systemConfig;
        const bucketName = bucket || systemConfig.serverInfo.oss_attachment_server.bucket;
        let ossClient = null
        try {
            ossClient = await MinioGetOssClient(bucketName, filePath);
        } catch (error) {
            callback && callback("error", {name:'tokenError'});
            return
        }

        let uploadId = Tool.genID(11)
        handleUpload({ossClient,file,filePath},{
            progress:async(percentage)=>{
                handleMinioProgress({percentage,callback,ossClient,uploadId})
            },
            success:(res)=>{
                handleMinioUploadSuccess(res,callback,uploadId)
            },
            error:(e)=>{
                handleMinioUploadError({e,callback,uploadId})
            },
        })
        resolve(ossClient)
    });
}

export function MinioCancelUpload(uploadId){
    let ossInfo= uploadTaskMap[uploadId]
    if(ossInfo){
        ossInfo.callback && ossInfo.callback("cancel", {name:'cancel'}, uploadId);
        const ossClient = ossInfo.ossClient
        handleCancelUpload({ossClient})
        delete uploadTaskMap[uploadId]
    }
}

// 实际执行暂停上传的函数
const executeMiniosPauseUpload = (uploadId, skipCallback = false) => {
    const uploadInfo = uploadTaskMap[uploadId];
    if (uploadInfo && uploadInfo.paused) { // 只有在状态为暂停时才执行
        try {
            uploadInfo.uploading = false;
            console.log("Minio executing pause upload", uploadId);

            // 暂停实际的上传操作
            if (uploadInfo.ossClient && uploadInfo.ossClient.uploadManaged) {
                uploadInfo.ossClient.uploadManaged.abort();
            }

            // 如果需要调用 callback（用于首次暂停）
            if (!skipCallback) {
                handleMinioUploadError({ e: { name: 'pause' }, callback: uploadInfo.callback, uploadId });
            }
        } catch (error) {
            console.error(`Failed to pause Minio upload with UploadId: ${uploadId}`, error);
        }
    }
};

// 实际执行恢复上传的函数
const executeMinioResumeUpload = async (uploadId, skipCallback = false) => {
    const uploadInfo = uploadTaskMap[uploadId];
    if (!uploadInfo) {
        console.log("!uploadInfo", uploadId, uploadInfo);
        return;
    }

    // 关键：检查最终状态，如果最终状态是暂停，则不执行恢复
    if (uploadInfo.paused) {
        console.log("MinioResumeUpload final state is paused, skip resume", uploadId, uploadInfo);
        return;
    }

    console.log("Minio executing resume upload", uploadId);
    uploadInfo.uploading = true;

    try {
        // 如果需要调用 callback（用于首次恢复）
        if (!skipCallback && uploadInfo.callback) {
            uploadInfo.callback("progress", Math.round(uploadInfo.percentage * 100), uploadId);
        }

        // 重新开始上传（Minio 不支持断点续传，需要重新上传）
        const ossClient = uploadInfo.ossClient;
        const file = currentFile; // 使用全局变量保存的文件
        const filePath = currentFilePath; // 使用全局变量保存的路径

        if (ossClient && file && filePath) {
            handleUpload({ ossClient, file, filePath }, {
                progress: (percentage) => {
                    handleMinioProgress({ percentage, callback: uploadInfo.callback, ossClient, uploadId });
                },
                success: (res) => {
                    handleMinioUploadSuccess(res, uploadInfo.callback, uploadId);
                },
                error: (e) => {
                    handleMinioUploadError({ e, callback: uploadInfo.callback, uploadId });
                },
            });
        }
    } catch (error) {
        console.error("Error resuming Minio upload:", error);
        uploadInfo.uploading = false;
    }
};

// 防抖执行暂停上传（用于频繁操作）
const debouncedMinioPauseUpload = Tool.debounce((uploadId) => executeMiniosPauseUpload(uploadId, true), 1000);

// 防抖执行恢复上传（用于频繁操作）
const debouncedMinioResumeUpload = Tool.debounce((uploadId) => executeMinioResumeUpload(uploadId, true), 1000);

// 暂停上传 - 智能处理首次暂停和频繁操作
export function MinioPauseUpload(uploadId) {
    const uploadInfo = uploadTaskMap[uploadId];
    if (!uploadInfo) {
        return;
    }

    const now = Date.now();
    const lastOperationTime = Math.max(uploadInfo.lastPauseTime || 0, uploadInfo.lastResumeTime || 0);
    const isFrequentOperation = (now - lastOperationTime) < 2000; // 2秒内的操作视为频繁操作

    // 立即修改状态和UI
    uploadInfo.paused = true;
    uploadInfo.lastPauseTime = now;

    // 立即更新UI状态
    if (uploadInfo.callback) {
        uploadInfo.callback("error", { name: 'pause' }, uploadId);
    }

    if (isFrequentOperation) {
        // 频繁操作：延迟执行实际动作
        debouncedMinioPauseUpload(uploadId);
    } else {
        // 首次暂停：立即执行实际暂停
        executeMiniosPauseUpload(uploadId, true); // 跳过callback，因为UI已经更新了
    }
}

// 恢复上传 - 永远延迟执行，以最终状态为准
export function MinioResumeUpload({ uploadId }) {
    const uploadInfo = uploadTaskMap[uploadId];
    if (!uploadInfo) {
        console.log("!uploadInfo", uploadId, uploadInfo);
        return;
    }

    // 立即修改状态和UI
    uploadInfo.paused = false;
    uploadInfo.lastResumeTime = Date.now();

    // 立即更新UI状态，让用户看到恢复状态
    if (uploadInfo.callback) {
        uploadInfo.callback("progress", Math.round(uploadInfo.percentage * 100), uploadId);
    }

    // 重新上传永远延迟执行
    debouncedMinioResumeUpload(uploadId);
}

