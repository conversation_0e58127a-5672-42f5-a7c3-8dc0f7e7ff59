// dialog.js - 重构版本，集成DialogManager
let DialogId = null;
import { Dialog } from 'vant';
import Tool from '@/common/tool'

// 尝试导入DialogManager，如果不存在则使用传统模式
let DialogManager = null;
try {
    DialogManager = require('@/module/ultrasync/lib/dialogManager').default;
} catch (e) {
    console.warn('DialogManager not available, using legacy mode');
}
/**
 * 平台适配器 - 统一不同平台的弹窗处理
 */
class PlatformAdapter {
    static detectPlatform() {
        if (Tool.checkAppClient('Cef')) {
            return 'cef';
        }
        if (Tool.checkAppClient('PCBrowser')) {
            return 'pcBrowser';
        }
        if (Tool.checkAppClient('App') || Tool.checkAppClient('MobileBrowser')) {
            return 'mobile';
        }
        return 'unknown';
    }

    static showDialog(platform, config) {
        switch (platform) {
        case 'cef':
            return this.showCefDialog(config);
        case 'pcBrowser':
            return this.showPCBrowserDialog(config);
        case 'mobile':
            return this.showMobileDialog(config);
        default:
            return this.showFallbackDialog(config);
        }
    }

    static showCefDialog(config) {
        const params = {
            title: config.title,
            message: config.message,
            des: config.des || 'common'
        };

        if (config.buttons[0]) {
            params.yes_button = config.buttons[0];
        }
        if (config.buttons[1]) {
            params.no_button = config.buttons[1];
        }
        if (config.buttons[2]) {
            params.append_button = config.buttons[2];
        }

        Tool.createCWorkstationCommunicationMng({
            name: 'ShowConfirmDialog',
            emitName: 'NotifyShowConfirmDialogByUser',
            params,
            timeout: null,
            des: 'common'
        }).then((res) => {
            if (res.no === 0) {
                config.confirm();
            } else if (res.no === 1) {
                config.reject();
            } else {
                config.cancel();
            }
        });
    }

    static showPCBrowserDialog(config) {
        window.vm.$MessageBox.confirm(config.message, config.title, {
            confirmButtonText: config.buttons[0],
            cancelButtonText: config.buttons[1],
            showCancelButton: !!config.buttons[1],
            showConfirmButton: !!config.buttons[0],
            closeOnClickModal: false,
            customClass: config.id,
            callback: (action) => {
                if (action === 'confirm') {
                    config.confirm();
                } else if (action === 'cancel') {
                    config.reject();
                }
            }
        });
    }

    static showMobileDialog(config) {
        if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
            window.vm.$root.functionalDialog.showDialog({
                title: config.title,
                message: config.message,
                closeOnPopstate: true,
                showConfirmButton: !!config.buttons[0],
                showRejectButton: !!config.buttons[1],
                confirmButtonText: config.buttons[0],
                rejectButtonText: config.buttons[1],
                confirm: config.confirm,
                reject: config.reject,
                cancel: config.cancel
            });
        } else {
            Dialog.confirm({
                title: config.title,
                message: config.message,
                closeOnPopstate: true,
                showConfirmButton: !!config.buttons[0],
                showCancelButton: !!config.buttons[1],
                confirmButtonText: config.buttons[0],
                cancelButtonText: config.buttons[1]
            }).then(() => {
                config.confirm();
            }).catch(() => {
                config.reject();
            });
        }
    }

    static showFallbackDialog(config) {
        // 使用浏览器原生confirm作为后备方案
        const result = confirm(`${config.title}\n${config.message}`);
        if (result) {
            config.confirm();
        } else {
            config.reject();
        }
    }
}

export function openCommonDialog({
    buttons = [],
    title = '',
    message = '',
    des = 'common',
    confirm = function(){},
    reject = function(){},
    cancel = function(){},
    id = ''
}) {
    const lang = window.vm.$store.state.language;

    if (!Array.isArray(buttons)) {
        console.warn('openCommonDialog: buttons must be an array');
        return null;
    }

    DialogId = id || Tool.genID(3);

    // 包装原始回调函数，在执行时自动注销dialog
    const wrappedConfirm = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        confirm();
    };

    const wrappedReject = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        reject();
    };

    const wrappedCancel = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        cancel();
    };

    const config = {
        id: DialogId,
        title: title || lang.tip_title,
        message,
        des,
        buttons,
        confirm: wrappedConfirm,
        reject: wrappedReject,
        cancel: wrappedCancel
    };

    const platform = PlatformAdapter.detectPlatform();

    // 如果有DialogManager，注册dialog
    if (DialogManager) {
        // 创建一个对象来存储弹窗实例的引用
        const dialogInstance = { instance: null };

        DialogManager.register(dialogInstance, {
            open: () => {
                const result = PlatformAdapter.showDialog(platform, config);
                // 根据平台获取弹窗实例
                if (platform === 'mobile' && window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
                    dialogInstance.instance = window.vm.$root.functionalDialog;
                }
                return result;
            },
            close: () => {
                // 根据平台执行相应的关闭操作
                if (platform === 'cef') {
                    try {
                        window.CWorkstationCommunicationMng.RemoveConfirmDialog();
                    } catch (error) {
                        console.error('Error closing CEF dialog:', error);
                    }
                } else if (platform === 'pcBrowser') {
                    // PC浏览器的MessageBox会自动处理关闭
                } else if (platform === 'mobile') {
                    if (dialogInstance.instance && dialogInstance.instance.closeDialog) {
                        // 使用注册时获取的弹窗实例
                        dialogInstance.instance.closeDialog();
                    } else if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
                        // 后备方案：使用全局functionalDialog
                        window.vm.$root.functionalDialog.closeDialog();
                    } else {
                        Dialog.close();
                    }
                }
            },
            canCloseOnPopstate: true,
            canClose: true,
            id: DialogId
        });
    } else {
        // 后备方案：直接显示dialog
        PlatformAdapter.showDialog(platform, config);
    }

    return DialogId;
}

export function getCommonDialogId(){
    return DialogId
}

export function openMobileDialog({
    showRejectButton = false,
    showConfirmButton = true,
    showCancelButton = true,
    confirmButtonText = '',
    rejectButtonText = '',
    title = '',
    beforeClose = null,
    confirm = null,
    reject = null,
    cancel = null,
    message = '',
    close = null,
    messageAlign = 'center',
    closeOnClickOverlay = false,
    closeOnPopstate = true,
} = {}) {
    // 只在移动端环境下工作
    if (!Tool.checkAppClient('App') && !Tool.checkAppClient('MobileBrowser')) {
        console.warn('openMobileDialog: Not in mobile environment');
        return null;
    }

    DialogId = Tool.genID(3);
    const lang = window.vm.$store.state.language;

    // 包装原始回调函数，在执行时自动注销dialog
    const wrappedConfirm = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        confirm && confirm();
    };

    const wrappedReject = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        reject && reject();
    };

    const wrappedCancel = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        cancel && cancel();
    };

    const wrappedClose = function() {
        if (DialogManager) {
            DialogManager.unregister(DialogId);
        }
        close && close();
    };

    const config = {
        showRejectButton,
        showConfirmButton,
        showCancelButton,
        confirmButtonText,
        rejectButtonText,
        title: title || lang.tip_title,
        beforeClose,
        message,
        close: wrappedClose,
        messageAlign,
        closeOnClickOverlay,
        confirm: wrappedConfirm,
        reject: wrappedReject,
        cancel: wrappedCancel,
        closeOnPopstate
    };

    // 直接显示dialog，让具体的dialog组件负责自己的DialogManager注册
    if (window.vm && window.vm.$root && window.vm.$root.functionalDialog) {
        // 传入DialogId，避免ID冲突
        window.vm.$root.functionalDialog.showDialog({...config, id: DialogId});
    } else {
        // 后备方案：使用vant Dialog，需要自己注册到DialogManager
        if (DialogManager) {
            const dialogInstance = { closeDialog: () => Dialog.close() };

            DialogManager.register(dialogInstance, {
                open: () => {
                    Dialog.confirm({
                        title: config.title,
                        message: config.message,
                        closeOnPopstate: config.closeOnPopstate,
                        showConfirmButton: config.showConfirmButton,
                        showCancelButton: config.showRejectButton,
                        confirmButtonText: config.confirmButtonText,
                        cancelButtonText: config.rejectButtonText,
                    }).then(() => {
                        config.confirm && config.confirm();
                    }).catch(() => {
                        config.reject && config.reject();
                        config.cancel && config.cancel();
                    });
                },
                close: () => {
                    Dialog.close();
                },
                canCloseOnPopstate: closeOnPopstate,
                canClose: true,
                id: DialogId
            });
        } else {
            // 传统方式：直接使用vant Dialog
            Dialog.confirm({
                title: config.title,
                message: config.message,
                closeOnPopstate: config.closeOnPopstate,
                showConfirmButton: config.showConfirmButton,
                showCancelButton: config.showRejectButton,
                confirmButtonText: config.confirmButtonText,
                cancelButtonText: config.rejectButtonText,
            }).then(() => {
                config.confirm && config.confirm();
            }).catch(() => {
                config.reject && config.reject();
                config.cancel && config.cancel();
            });
        }
    }

    return DialogId;
}
/**
 * 统一的弹窗关闭处理
 */
class DialogCloser {
    static closeAll() {
        // 优先使用DialogManager
        if (DialogManager) {
            return DialogManager.closeAllDialogs();
        }
    }

    static closeLast() {
        // 优先使用DialogManager
        if (DialogManager) {
            return DialogManager.closeLastDialog();
        }
    }

    static closeByPlatform() {
        if (Tool.checkAppClient('Cef')) {
            try {
                window.CWorkstationCommunicationMng.RemoveConfirmDialog();
                return true;
            } catch (error) {
                console.error('Error closing CEF dialog:', error);
                return false;
            }
        } else {
            return this.closeLast();
        }
    }
}

export function closeAllDialog() {
    // 优先使用DialogManager
    if (DialogManager) {
        return DialogManager.closeAllDialogs();
    }
    // 后备方案
    return DialogCloser.closeAll();
}

export function closeMobileDialog() {
    // 优先使用DialogManager
    if (DialogManager) {
        return DialogManager.closeLastDialog();
    }
    // 后备方案
    return DialogCloser.closeLast();
}

export function closeCommonDialog() {
    // 优先使用DialogManager
    if (DialogManager) {
        return DialogManager.closeLastDialog();
    }
    // 后备方案
    return DialogCloser.closeByPlatform();
}

/**
 * 弹窗状态检查器
 */
class DialogChecker {
    static hasOpenDialogs() {
        // 优先使用DialogManager
        if (DialogManager) {
            return DialogManager.hasOpenDialogs();
        }
    }

    static canCloseLastDialog() {
        // 优先使用DialogManager
        if (DialogManager) {
            const lastDialog = DialogManager.getLastOpenDialog();
            return lastDialog ? lastDialog[1].canClose : true;
        }
    }
    static canCloseOnPopstate() {
        // 优先使用DialogManager
        if (DialogManager) {
            const lastDialog = DialogManager.getLastOpenDialog();
            return lastDialog ? lastDialog[1].canCloseOnPopstate : true;
        }
    }
}

export function checkMobileDialogShow() {
    return DialogChecker.hasOpenDialogs();
}

export function checkMobileCanCloseDialog() {
    return DialogChecker.canCloseLastDialog();
}

export function checkMobileCanCloseOnPopstate() {
    return DialogChecker.canCloseOnPopstate();
}

