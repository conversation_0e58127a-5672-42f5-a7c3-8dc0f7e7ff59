<template>
    <div>
        <van-popup
            v-model="dialogVisible"
            :get-container="getContainer"
            class="share_conference_modal"
            position="bottom"
            :close-on-click-overlay="true"
            @open="handlePopupOpen"
            @close="handlePopupClose"
            :overlay="true"
            :lockScroll="false"
            :style="{ height: '80%' }"
            :closeable="true"
        >
                <div class="share-container">
                    <div class="qr_code">
                        <div ref="qrCode"></div>
                        <div class="scanner_text">{{lang.scanner_text}}</div>
                    </div>
                    <van-button type="primary" class="submit-button" block @click="shareLiveUrlToWeChat" v-if="isShowShareWeChatBtn">{{lang.share_link_to_wx}}</van-button>
                    <van-button type="primary" class="submit-button" block @click="copyLiveAddress" v-else>{{lang.copy_link}}</van-button>
                </div>
        </van-popup>
    </div>
</template>
<script>
import base from "../../lib/base";
import Tool from "@/common/tool.js";
import { Button, Popup, Toast } from "vant";
import share_to_wechat from '../../lib/share_to_wechat'
import {getLiveRoomObj} from '../../lib/common_base'
import DialogManager from "../../lib/dialogManager";
export default {
    name:'moreConferenceOperationComponent',
    mixins: [base,share_to_wechat],
    props: {
        show: {
            type: Boolean,
            default: false,
        },
        LiveConferenceData: {
            type: Object,
            default: () => {
                return {};
            },
        },
        currentSubject:{
            type:String,
            default:''
        }
    },
    filters: {},
    data() {
        return {
            recordingRequestLoading:false,
            shareDialogId: null, // DialogManager 中弹窗的ID
        };
    },
    components: {
        VanButton: Button,
        VanPopup: Popup,
    },
    computed: {
        dialogVisible: {
            get() {
                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            },
        },
        cid(){
            return this.$route.params.cid||0
        },
        conversation(){
            return this.$store.state.conversationList[this.cid]||{}
        },
        isUltraSoundMobile(){
            return this.$store.state.device.isUltraSoundMobile
        },
        isShowShareWeChatBtn(){
            const isCE= process.env.VUE_APP_PROJECT_NOV==='CE';
            const isPlus = this.isApp
            return !this.isUltraSoundMobile&&!isCE&&isPlus
        }
    },
    watch: {
        // 监听弹窗显示状态
        dialogVisible(val) {
            if (val && !this.shareDialogId) {
                // 打开弹窗时，注册到 DialogManager
                this.shareDialogId = DialogManager.register(this, {
                    open: () => {
                        // 弹窗已经通过 v-model 显示，这里不需要额外操作
                    },
                    close: () => {
                        this.dialogVisible = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.shareDialogId) {
                // 关闭弹窗时，从 DialogManager 注销
                DialogManager.unregister(this.shareDialogId);
                this.shareDialogId = null;
            }
        }
    },
    created() {},
    mounted() {},
    beforeDestroy() {
        // 清理 DialogManager 中的弹窗注册
        if (this.shareDialogId) {
            DialogManager.unregister(this.shareDialogId);
            this.shareDialogId = null;
        }
    },
    methods: {
        getContainer() {
            return document.querySelector("body");
        },
        handlePopupOpen() {
            setTimeout(()=>{
                this.getQrcode()
            },200)
        },
        handlePopupClose() {

        },
        async shareLiveUrlToWeChat(){
            let url = this.systemConfig.server_type.protocol + this.systemConfig.server_type.host + this.systemConfig.server_type.port
            let thumb=`${url}/mobile/static/resource/images/new_logo.png`
            try {
                let address = await this.getLiveUrl()

                let shareContent = {
                    img_url: '1',
                    video_url: address,
                    title: `${this.currentSubject}${this.lang.is_in_conference}`,
                    content: this.lang.mindray_final_mission,
                    thumb: thumb
                };
                console.log(shareContent)
                this.shareLinkToWeChat(shareContent);
            } catch (error) {
                console.error(error)
            }
        },

        async getLiveUrl(){
            let cid = this.cid
            let live_id = this.$route.query.live_id
            let address = ''

            if(live_id){
                address = await this.getLiveUrlByLiveId(live_id)
                this.live_addr = address
                return this.live_addr
            }

            var channelId = this.GetChannelId();
            console.info('live_id',live_id,channelId)
            if (!channelId) {
                Toast(this.lang.get_live_addr_error)
                this.live_addr = ''
                return this.live_addr;
            }
            let channelInfo = `channel_id=${channelId}`
            let groupInfo = `group_id=${cid}`
            let str = window.btoa(`${channelInfo}#####${groupInfo}`)

            let serverInfo=this.systemConfig.serverInfo;
            let url = this.systemConfig.server_type.protocol + this.systemConfig.server_type.host + this.systemConfig.server_type.port
            this.live_addr = Tool.transferLocationToCe(`${url}/activity/activity.html#/webLive/${str}`)
            return this.live_addr
        },
        getLiveUrlByLiveId(live_id){
            return new Promise((resolve,reject)=>{
                window.main_screen.getLiveInfoById({live_id},(liveRes)=>{
                    console.log(liveRes)
                    if(!liveRes.error_code){
                        let live_Info = `live_id=${live_id}`
                        let creator_Info = `creator_id=${liveRes.data.creator_id}`
                        let str = window.btoa(`${live_Info}#####${creator_Info}`)
                        let url = this.systemConfig.server_type.protocol + this.systemConfig.server_type.host + this.systemConfig.server_type.port
                        resolve(Tool.transferLocationToCe(`${url}/activity/activity.html#/webLive/${str}`))
                    }else{
                        resolve('')
                    }
                })
            })
        },
        GetChannelId(){
            let liveRoom = getLiveRoomObj()
            if(!liveRoom){
                return
            }
            let channelId  = liveRoom.data.channelId
            return channelId
        },
        async getQrcode(){
            this.$refs.qrCode.innerHTML = '';
            try {
                let address  =await this.getLiveUrl()
                new window.QRCode(this.$refs.qrCode, {
                    text: address,
                    width : 220,
                    height : 220
                });
            } catch (error) {
                console.error(error)
            }
        },
        async copyLiveAddress(){
            let address  =await this.getLiveUrl()
            if (Tool.checkAppClient('IOS')&&!Tool.checkAppClient('Browser')) {
                window.CWorkstationCommunicationMng.setClipboard({ str: address });
            } else {
                Tool.copyToClipboard(address);
            }
            Toast(this.lang.text_has_copied);
        }
    },
};
</script>
<style lang="scss">
.share_conference_modal {
.share-container{
    padding: 1rem;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .qr_code{
        flex:1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .scanner_text{
            font-size: 1rem;
            margin-top: .5rem;
            text-align: center;
        }
    }
}
}
</style>
