<template>
    <div class="smart-tech-training-main">
        <div class="smart-tech-training-container">
            <div class="custom-header">
                <el-tabs v-model="activeTab" class="smart-tech-tabs smart-tech-tabs--small" v-if="tabsConfig.length">
                    <el-tab-pane
                        v-for="tabItem in tabsConfig"
                        :key="tabItem.name"
                        :label="tabItem.label"
                        :name="tabItem.name"
                    ></el-tab-pane>
                </el-tabs>
            </div>
            <div class="smart-tech-training-content">
                <div class="welcome-message">{{ lang.welcome_smartTechTraining_tips }}</div>
                <div class="card-grid">
                    <div class="card" @click="handleClickProject">
                        <div class="card-image-placeholder">
                            <img src="static/resource_pc/images/ultrasonic_skill_certification.jpg" alt="" srcset="">
                        </div>
                        <div class="card-title">{{ lang.ultrasonic_skill_certification }}</div>
                        <el-button class="enter-button" plain size="small">{{lang.enter }}</el-button>
                    </div>
                </div>
            </div>
        </div>
        <router-view></router-view>

        <CommonDialog
            class="project_detail"
            :title="lang.please_select_your_certification_project"
            :show.sync="selectTrainingDialogVisible"
            :close-on-click-modal="true"
            width="800px"
            @closed="handleCloseSelectTrainingDialog"
            :modal="false"
            :append-to-body="true"
            :footShow="false"
        >
            <div class="project-select-container">
                <!-- 第一行：表单搜索 -->
                <div class="search-filter-bar">
                    <div class="filter-selectors">
                        <el-select v-model="specialty" :placeholder="lang.professional_affiliation" size="small" clearable>
                            <el-option :label="lang.homework_type4" :value="BODY_PART.GYN"></el-option>
                            <el-option :label="lang.homework_type1" :value="BODY_PART.ABDOMEN"></el-option>
                            <el-option :label="lang.homework_type3" :value="BODY_PART.CARDIO"></el-option>
                            <el-option :label="lang.homework_type2" :value="BODY_PART.SUPERFICIAL"></el-option>
                        </el-select>
                        <el-select
                            v-model="countryCode"
                            :placeholder="lang.please_select_country_region"
                            size="small"
                            clearable
                            filterable
                            :filter-method="filterCountryOptions"
                            class="country-select"
                        >
                            <el-option
                                v-for="item in filteredCountryOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </div>
                    <div class="search-input-area">
                        <el-input :placeholder="lang.input_enter_tips" v-model="trainingName" size="small" clearable>
                            <el-button
                                slot="append"
                                icon="el-icon-search"
                                @click="searchTraining"
                                :loading="isLoading"
                                :disabled="isLoading"
                            ></el-button>
                        </el-input>
                    </div>
                </div>

                <!-- 第二行及以后：项目列表 -->
                <div class="project-list" v-loading="isLoading">
                    <div
                        :class="['project-item', { 'disabled-item': isTrainingDisabled(training) }]"
                        v-for="training in trainings"
                        :key="training._id"
                        @click="selectTraining(training)"
                    >
                        <div class="project-item-details">
                            <div class="project-item-main-content">
                                <div class="project-item-title">{{ getTrainingDisplayTitle(training) }}</div>
                            </div>
                            <div
                                v-if="getStudentStatusInfo(training)"
                                :class="['project-item-status-badge', getStudentStatusInfo(training).class]"
                            >
                                {{ getStudentStatusInfo(training).text }}
                            </div>
                            <div
                                :class="['project-item-status-badge', getRoleBadgeClass(training)]"
                                v-if="getRoleBadgeText(training)"
                            >
                                {{ getRoleBadgeText(training) }}
                            </div>
                        </div>
                    </div>
                    <div v-if="trainings.length === 0" class="empty-list-placeholder">{{ lang.no_data_txt }}</div>
                </div>
            </div>
        </CommonDialog>

        <CommonDialog
            class="registration_form_dialog"
            :title="lang.student_information_registration"
            :show.sync="registrationFormDialogVisible"
            :close-on-click-modal="false"
            width="800px"
            @closed="handleCloseRegistrationFormDialog"
            :modal="true"
            :append-to-body="true"
            :footShow="true"
            :submitText="lang.submit_btn"
            :disabledSubmit="isSubmitting"
            @submit="submitRegistrationForm"
        >
            <div class="registration-form-container">
                <el-form :model="registrationForm" :rules="formRules" ref="registrationFormRef" label-width="200px">
                    <el-form-item label="Full Name:" prop="fullName">
                        <el-input
                            v-model="registrationForm.fullName"
                            :placeholder="lang.input_enter_tips"
                            maxlength="100"
                            clearable
                            @blur="trimField('fullName')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="Email:" prop="email">
                        <el-input
                            v-model="registrationForm.email"
                            :placeholder="lang.input_enter_tips"
                            clearable
                            @blur="trimField('email')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="IC/Passport:" prop="icPassport">
                        <el-input
                            v-model="registrationForm.icPassport"
                            :placeholder="lang.input_enter_tips"
                            maxlength="50"
                            clearable
                            @blur="trimField('icPassport')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item label="MMC No.:" prop="mmcNo">
                        <el-input
                            v-model="registrationForm.mmcNo"
                            :placeholder="lang.input_enter_tips"
                            maxlength="50"
                            clearable
                            @blur="trimField('mmcNo')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item :label="lang.place_of_work" prop="placeOfWork">
                        <el-input
                            v-model="registrationForm.placeOfWork"
                            :placeholder="lang.input_enter_tips"
                            maxlength="100"
                            clearable
                            @blur="trimField('placeOfWork')"
                        ></el-input>
                    </el-form-item>
                    <el-form-item :label="lang.job_description" prop="jobDescription">
                        <el-select
                            v-model="registrationForm.jobDescription"
                            :placeholder="lang.please_select"
                            clearable
                        >
                            <el-option
                                v-for="option in jobDescriptionOptions"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </CommonDialog>
    </div>
</template>

<script>
import base from "../../../lib/base";
import Tool from "@/common/tool";
import CommonDialog from "../../../MRComponents/commonDialog";
import { SMART_TECH_TRAINING_ROLE, BODY_PART } from "../../../lib/constants";
import { SMART_TECH_TRAINING_STUDENT_STATUS } from "../../../lib/constants";
import service from "../../../service/service";

const getDefaultRegistrationForm = () => ({
    fullName: "",
    email: "",
    icPassport: "",
    mmcNo: "",
    placeOfWork: "",
    jobDescription: "",
});

export default {
    mixins: [base],
    name: "SmartTechTraining",
    components: {
        CommonDialog,
    },
    data() {
        return {
            BODY_PART,
            selectTrainingDialogVisible: false,
            specialty: "",
            countryCode: "",
            trainingName: "",
            trainings: [],
            countryOptions: [],
            filteredCountryOptions: [],
            registrationFormDialogVisible: false,
            selectedTrainingForForm: null,
            registrationForm: getDefaultRegistrationForm(),
            jobDescriptionOptions: [],

            formRules: {},
            isLoading: false, // 新增加载状态
            isSubmitting: false, // 添加表单提交状态
            activeTab: "smartTeaching",
        };
    },
    computed: {
        tabsConfig(){
            return [
                { label: this.lang.smart_teaching, name: 'smartTeaching' },
            ];
        },
        trainingRoleMap(){
            return {
                [SMART_TECH_TRAINING_ROLE.PI_TEACHER]: { text: "PI", class: "status-pi-teacher" },
                [SMART_TECH_TRAINING_ROLE.SUPERVISOR]: { text: "SUPERVISOR", class: "status-supervisor" },
                [SMART_TECH_TRAINING_ROLE.STUDENT]: { text: this.lang.student, class: "status-student" },
            };
        },
        studentStatusMap() {
            return {
                [SMART_TECH_TRAINING_STUDENT_STATUS.APPLYING]: {
                    text: this.lang.applying_txt,
                    class: "status-applying",
                },
                [SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED]: {
                    text: this.lang.Rejected,
                    class: "status-rejected",
                },
                [SMART_TECH_TRAINING_STUDENT_STATUS.DISABLED]: {
                    text: this.lang.Disabled,
                    class: "status-disabled",
                },
            };
        },
    },
    watch: {
        selectTrainingDialogVisible: {
            handler(newVal) {
                if (newVal) {
                    this.getTrainingList();
                } else {
                    // 弹窗关闭时清空搜索条件
                    this.clearSearchConditions();
                }
            },
            immediate: true,
        },
    },
    created() {
        this.getTrainingCountryList();
        this.jobDescriptionOptions = [
            { label: "Specialist", value: "Specialist" },
            { label: "Medical Officer", value: "Medical Officer" },
        ];
        this.formRules = {
            fullName: [
                { required: true, message: "Full Name is required", trigger: "blur" },
            ],
            email: [
                { required: true, message: "Email is required", trigger: "blur" },
                { type: "email", message: "Please enter a valid email address", trigger: ["blur"] },
            ],
            icPassport: [
                { required: true, message: "IC/Passport is required", trigger: "blur" },
            ],
            mmcNo: [
                { required: true, message: "MMC No is required", trigger: "blur" },
            ],
            placeOfWork: [
                { required: true, message: "Place of Work is required", trigger: "blur" },
            ],
            jobDescription: [
                { required: true, message: "Job Description is required", trigger: "change" },
            ],
        };
    },
    methods: {
        getDefaultRegistrationForm,

        // 处理字段trim，在失去焦点时去除首尾空格
        trimField(fieldName) {
            if (this.registrationForm[fieldName] && typeof this.registrationForm[fieldName] === 'string') {
                this.registrationForm[fieldName] = this.registrationForm[fieldName].trim();
            }
        },

        getTrainingDisplayTitle(training) {
            let prefix = "";
            if (training.specialty) {
                // 直接使用中文，根据之前对 BODY_PART 和 lang 的分析
                // 确保 training.specialty 的值与 BODY_PART 中的值匹配
                let specialtyName = "";
                switch (training.specialty) {
                case BODY_PART.GYN:
                    specialtyName = this.lang.homework_type4;
                    break;
                case BODY_PART.ABDOMEN:
                    specialtyName = this.lang.homework_type1;
                    break;
                case BODY_PART.CARDIO:
                    specialtyName = this.lang.homework_type3;
                    break;
                case BODY_PART.SUPERFICIAL:
                    specialtyName = this.lang.homework_type2;
                    break;
                }
                if (specialtyName) {
                    prefix += `【${specialtyName}】`;
                }
            }
            if (training.countryCode && this.countryOptions && this.countryOptions.length > 0) {
                const country = this.countryOptions.find((c) => c.value === training.countryCode);
                if (country) {
                    prefix += `【${country.label}】`;
                }
            }

            return prefix + training.name;
        },
        goBack() {
            const cid = this.$route.params.cid;
            this.$router.replace(`/main/index/chat_window/${cid}`);
        },
        handleClickProject(i) {
            this.selectTrainingDialogVisible = true;
        },
        getRoleBadgeClass(training) {
            if (training.roleData) {
                if (
                    training.roleData.role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                    training.roleData.roleInfo === null
                ) {
                    return "status-no-role";
                }
                return this.trainingRoleMap[training.roleData.role]
                    ? this.trainingRoleMap[training.roleData.role].class
                    : "";
            }
            return "";
        },
        getRoleBadgeText(training) {
            if (training.roleData) {
                if (
                    training.roleData.role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                    training.roleData.roleInfo === null
                ) {
                    return this.lang.not_certified;
                }
                return this.trainingRoleMap[training.roleData.role]
                    ? this.trainingRoleMap[training.roleData.role].text
                    : "";
            }
            return "";
        },
        selectTraining(training) {
            if (!training.roleData) {
                return;
            }
            const role = training.roleData.role;
            const roleInfo = training.roleData.roleInfo;

            // 对于已拒绝的学生，允许重新申请
            if (
                role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                roleInfo &&
                roleInfo.status === SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED
            ) {
                this.selectedTrainingForForm = training;
                this.loadPreviousApplicationData(training);
                return;
            }

            // 对于其他非通过状态的学生，不允许操作
            if (
                role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                roleInfo &&
                roleInfo.status !== SMART_TECH_TRAINING_STUDENT_STATUS.PASSED &&
                roleInfo.status !== SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED
            ) {
                return;
            }

            const needsAuthentication =
                role === SMART_TECH_TRAINING_ROLE.STUDENT && training.roleData.roleInfo === null;

            if (needsAuthentication) {
                this.selectedTrainingForForm = training;
                this.registrationFormDialogVisible = true;
                console.log('选择项目进行报名:', training);
                return;
            }

            if (role === SMART_TECH_TRAINING_ROLE.PI_TEACHER || role === SMART_TECH_TRAINING_ROLE.SUPERVISOR) {
                console.log(
                    `角色${role === SMART_TECH_TRAINING_ROLE.PI_TEACHER ? 'PI导师' : '导师'}，准备跳转至学员总览页面:`,
                    training
                );
                this.selectTrainingDialogVisible = false;
                Tool.loadModuleRouter({
                    name: 'SmartTechTrainingStudentOverview',
                    params: {
                        ...this.$route.params,
                        trainingId: training._id,
                        role: role,
                    },
                });
            } else if (role === SMART_TECH_TRAINING_ROLE.STUDENT) {
                this.selectTrainingDialogVisible = false;
                Tool.loadModuleRouter({
                    name: 'SmartTechTrainingExamProject',
                    params: {
                        ...this.$route.params,
                        trainingId: training._id,
                        role: role,
                        extraData: training.roleData.roleInfo,
                    },
                });
            }
        },
        getStudentStatusInfo(training) {
            if (
                training.roleData &&
                training.roleData.role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                training.roleData.roleInfo &&
                training.roleData.roleInfo.status !== SMART_TECH_TRAINING_STUDENT_STATUS.PASSED
            ) {
                return this.studentStatusMap[training.roleData.roleInfo.status];
            }
            return null;
        },
        isTrainingDisabled(training) {
            if (!training.roleData) {
                return false;
            }
            const role = training.roleData.role;
            const roleInfo = training.roleData.roleInfo;

            // 已拒绝的项目不禁用，允许重新申请
            return (
                role === SMART_TECH_TRAINING_ROLE.STUDENT &&
                roleInfo &&
                roleInfo.status !== SMART_TECH_TRAINING_STUDENT_STATUS.PASSED &&
                roleInfo.status !== SMART_TECH_TRAINING_STUDENT_STATUS.REJECTED
            );
        },
        handleCloseRegistrationFormDialog() {
            this.registrationFormDialogVisible = false;
            this.clearRegistrationFormFields(true);
            if (this.$refs.registrationFormRef) {
                this.$refs.registrationFormRef.clearValidate();
            }
        },
        submitRegistrationForm() {
            this.$refs.registrationFormRef.validate((valid) => {
                if (valid) {
                    if (!this.selectedTrainingForForm || !this.selectedTrainingForForm._id) {
                        console.error("培训项目信息不完整，请重试");
                        return false;
                    }

                    this.isSubmitting = true; // 开始提交，设置加载状态

                    // 准备提交数据
                    const submitData = {
                        trainingID: this.selectedTrainingForForm._id,
                        uuid: this.registrationForm.icPassport, // 使用IC/Passport作为唯一标识
                        name: this.registrationForm.fullName,
                        email: this.registrationForm.email,
                        uuid: this.registrationForm.icPassport,
                        MMCNo: this.registrationForm.mmcNo,
                        hospital: this.registrationForm.placeOfWork,
                        job: this.registrationForm.jobDescription,
                    };

                    // 调用接口提交信息
                    service
                        .applyTrainingStudent(submitData)
                        .then((result) => {
                            const res = result.data;
                            if (res.error_code === 0) {
                                this.$message.success(this.lang.submitted_successfully);
                                this.registrationFormDialogVisible = false;
                                this.clearRegistrationFormFields(true); // 清空表单
                                this.getTrainingList(); // 刷新培训列表
                            } else {
                                console.error(res);
                                // this.$message.error(this.lang.submission_failed);
                            }
                        })
                        .catch((err) => {
                            console.error("提交学员认证信息失败:", err);
                            this.$message.error(this.lang.submission_failed);
                        })
                        .finally(() => {
                            this.isSubmitting = false; // 结束提交，取消加载状态
                        });
                } else {
                    console.log("Form validation failed");
                    return false;
                }
            });
        },
        getTrainingCountryList() {
            const currentLang = this.lang.currentLanguage;
            service
                .getTrainingCountryList()
                .then((result) => {
                    const res = result.data;
                    if (res.error_code === 0) {
                        // 将接口返回的数据转换为组件需要的格式
                        const countryList = res.data.map((item) => ({
                            label: currentLang === 'CN' ? item.zh : item.en, // 使用中文名称作为显示标签
                            value: item.code, // 使用国家代码作为值
                        }));

                        // 更新countryOptions，用于filter2下拉列表
                        this.countryOptions = [...countryList];
                        // 初始化筛选后的国家选项
                        this.filteredCountryOptions = [...countryList];
                    }
                })
                .catch((err) => {
                    console.error("获取国家/地区列表失败:", err);
                });
        },
        getTrainingList(params = {}) {
            this.isLoading = true; // 开始加载
            this.trainings = [];
            const searchParams = {
                countryCode: this.countryCode,
                specialty: this.specialty,
                name: this.trainingName,
                ...params,
            };

            service
                .getTrainingList(searchParams)
                .then((result) => {
                    const res = result.data;
                    if (res.error_code === 0) {
                        // 更新培训列表数据
                        if (res.data && Array.isArray(res.data)) {
                            this.trainings = res.data;
                        } else {
                            this.trainings = [];
                        }
                    }
                })
                .catch((err) => {
                    console.error("获取智教培考试列表失败:", err);
                    this.trainings = []; // 发生错误时清空列表
                })
                .finally(() => {
                    this.isLoading = false; // 结束加载
                });
        },
        searchTraining() {
            this.getTrainingList();
        },

        // 国家地区筛选方法
        filterCountryOptions(query) {
            if (query !== '') {
                this.filteredCountryOptions = this.countryOptions.filter(item => {
                    return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1;
                });
            } else {
                this.filteredCountryOptions = [...this.countryOptions];
            }
        },

        // 处理选择培训项目弹窗关闭
        handleCloseSelectTrainingDialog() {
            this.selectTrainingDialogVisible = false;
        },

        // 清空搜索条件
        clearSearchConditions() {
            this.specialty = "";
            this.countryCode = "";
            this.trainingName = "";
            // 重置筛选后的国家选项
            this.filteredCountryOptions = [...this.countryOptions];
        },

        clearRegistrationFormFields(clearId = true) {
            const defaultForm = getDefaultRegistrationForm();
            for (const key in this.registrationForm) {
                if (this.registrationForm.hasOwnProperty(key)) {
                    if (key === "icPassport") {
                        if (clearId) {
                            this.registrationForm.icPassport = defaultForm.icPassport || "";
                        }
                    } else {
                        this.registrationForm[key] = defaultForm.hasOwnProperty(key) ? defaultForm[key] : "";
                    }
                }
            }
            for (const key in defaultForm) {
                if (!this.registrationForm.hasOwnProperty(key)) {
                    this.registrationForm[key] = defaultForm[key];
                } else if (key !== "icPassport") {
                    this.registrationForm[key] = defaultForm[key];
                } else if (key === "icPassport" && clearId) {
                    this.registrationForm.icPassport = defaultForm.icPassport;
                }
            }
        },

        // 加载之前的申请数据
        loadPreviousApplicationData(training) {
            if (!training.roleData || !training.roleData.roleInfo) {
                console.error('无法获取学生申请信息');
                this.registrationFormDialogVisible = true;
                return;
            }

            const roleInfo = training.roleData.roleInfo;
            // 如果没有UUID，直接使用roleInfo中的信息
            this.registrationForm = {
                fullName: roleInfo.name || '',
                email: roleInfo.email || '',
                icPassport: roleInfo.uuid || '',
                mmcNo: roleInfo.MMCNo || '',
                placeOfWork: roleInfo.hospital || '',
                jobDescription: roleInfo.job || '',
            };
            this.registrationFormDialogVisible = true;
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/module/ultrasync_pc/style/smartTechTraining.scss";
.smart-tech-training-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.smart-tech-training-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
}

.custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding: 0 10px;
    padding-left: 40px;
    position: relative;

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        cursor: pointer;
        margin-right: 10px;
        font-weight: 600;
        i {
            font-size: 25px;
            color: #000;
        }

        &:hover i {
            color: #409eff;
        }
    }
    :deep(.el-tabs__content){
        margin-top: 0;
    }
}
.smart-tech-training-content {
    flex: 1;
    position: relative;
    padding: 40px;
    box-sizing: border-box;
    overflow-y: auto;
    .welcome-message {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #303133;
    }
    .card-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 30px;
        width: 100%;
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
            &:hover {
                transform: translateY(-5px);
            }

            .card-image-placeholder {
                width: 100%;
                height: 300px;
                background-color: #e9f0f8;
                border-radius: 6px;
                margin-bottom: 15px;
                img{
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                }
            }

            .card-title {
                font-size: 18px;
                font-weight: bold;
                color: #303133;
                margin-bottom: 8px;
            }

            .enter-button {
                width: 150px;
                border: 1px solid #D8D8D8;
                border-radius: 5px;
                font-size: 16px;
                color: #000000;
                text-align: center;
                font-weight: 500;
                background-color: #FFFFFF;
                margin-top: 5px;
                &:hover {
                    color: #409EFF;
                    border-color: #c6e2ff;
                    background-color: #ecf5ff;
                }
                &:active {
                    color: #3a8ee6;
                    border-color: #3a8ee6;
                }
            }
        }
    }
}

.project-select-container {
    display: flex;
    flex-direction: column;
    height: 500px;
    max-height: 70vh;

    .search-filter-bar {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #ebeef5;
        background-color: #fff;
        flex-shrink: 0;

        .filter-selectors {
            display: flex;
            gap: 10px;
            .el-select {
                width: 180px;
            }
            .country-select {
                width: 340px;
            }
        }

        .search-input-area {
            margin-left: 10px;
            flex: 1;
            .el-input {
                width: 100%;
            }
        }
    }

    .project-list {
        flex-grow: 1;
        overflow-y: auto;
        padding: 15px;
        background-color: #f7f9fc;

        .project-item.disabled-item {
            cursor: not-allowed;
            opacity: 0.6;
            &:hover {
                box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
                transform: translateY(0);
            }
        }

        .project-item {
            display: flex;
            align-items: center;
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
            padding: 15px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: box-shadow 0.2s ease, transform 0.2s ease;
            justify-content: space-between;

            &:hover {
                box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
                transform: translateY(-2px);
            }

            .project-item-details {
                flex-grow: 1;
                min-width: 0;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .project-item-main-content {
                    flex-grow: 1;
                    min-width: 0;
                }

                .project-item-title {
                    font-size: 15px;
                    font-weight: 600;
                    color: #303133;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }

        .project-item-status-badge {
            font-size: 12px;
            padding: 3px 8px;
            border-radius: 4px;
            margin-left: 10px;
            flex-shrink: 0;

            &.status-applying {
                background-color: #fdf6ec;
                color: #e6a23c;
                border: 1px solid #faecd8;
            }
            &.status-rejected {
                background-color: #fef0f0;
                color: #f56c6c;
                border: 1px solid #fde2e2;
            }
            &.status-disabled {
                background-color: #f4f4f5;
                color: #909399;
                border: 1px solid #e9e9eb;
            }
            &.status-no-role {
                background-color: #f4f4f5;
                color: #909399;
                border: 1px solid #e9e9eb;
            }
            &.status-pi-teacher {
                background-color: #fdf6ec;
                color: #e6a23c;
                border: 1px solid #faecd8;
            }
            &.status-supervisor {
                background-color: #ecf5ff;
                color: #409eff;
                border: 1px solid #d9ecff;
            }
            &.status-student {
                background-color: #f0f9eb;
                color: #67c23a;
                border: 1px solid #e1f3d8;
            }
        }

        .empty-list-placeholder {
            text-align: center;
            color: #909399;
            padding: 20px;
            font-size: 14px;
        }
    }
}

.registration-form-container {
    padding: 20px;
    min-height: 480px;

    .el-form-item {
        margin-bottom: 18px;
    }
    .el-select {
        width: 100%;
    }
    .el-input-group__prepend .el-select .el-input {
        width: 100px;
    }
    .form-actions {
        text-align: right;
        margin-top: 20px;
    }

    // 隐藏number类型输入框的控制按钮
    :deep(.no-number-controls input[type="number"]) {
        -moz-appearance: textfield;
        appearance: textfield;

        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            appearance: none;
            margin: 0;
        }
    }

}
</style>
