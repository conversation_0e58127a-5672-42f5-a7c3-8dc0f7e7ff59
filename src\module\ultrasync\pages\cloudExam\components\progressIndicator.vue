<template>
  <div>
    <!-- 悬浮按钮 - 当图片预览打开时隐藏 -->
    <div class="progress-float-btn" @click="openProgress" v-if="!showProgress && !isImageViewerOpen">
      <span >{{ topicStep }}/{{ progressList.length }}</span>
    </div>

    <!-- 进度指示器弹窗 -->
    <van-popup v-model="showProgress" position="right" :style="{ width: '13.125rem', height: '100%' }" @open="onPopupOpen" @closed="onPopupClosed">
      <div class="progress-indicator">
        <div class="header">
          <van-icon name="arrow-left" class="back-icon" @click="closeProgress" />
          <span class="header-title">{{ headerTitle }}</span>
          <i class="iconfont iconcuohao" @click="closeProgress"></i>
        </div>
        <div class="content">
          <div class="question-list">
            <div
              v-for="(item, index) in progressList"
              :key="index"
              class="question-item"
              :class="item.status"
              @click="jumpTo(index)"
            >
              {{ index + 1 }}
            </div>
          </div>
          <div class="progress-text">
            <template v-if="topicType === 3">
              {{ lang.homework.progress.completionStatus.replace('{done}', doneCount).replace('{total}', progressList.length) }}
              <div class="total-score">{{ lang.paper_results }}: <span class="score-value">{{ totalScore }}</span> {{ lang.point_tip }}</div>
            </template>
            <template v-else>
              {{ lang.homework.progress.completionStatus.replace('{done}', doneCount).replace('{total}', progressList.length) }}
            </template>
          </div>
          <div class="actions">
            <van-button v-if="topicType !== 3" class="save-btn" @click="$emit('save')">{{ lang.save_txt }}</van-button>
            <van-button class="submit-btn" @click="handleSubmit">{{ lang.submit_btn }}</van-button>
          </div>
          <div class="legend">
            <span class="legend-item">
              <span class="box none"></span>{{ topicType === 2 ? lang.homework.progress.legend.notDone : lang.homework.progress.legend.ungraded }}</span>
            <span class="legend-item">
              <span class="box done"></span>{{ topicType === 2 ? lang.homework.progress.legend.alreadyDone : lang.homework.progress.legend.graded }}</span>
            <span class="legend-item">
              <span class="box current"></span>{{ lang.homework.progress.legend.current }}</span>
          </div>

          <!-- 参考答案显示 -->
          <div class="reference-answer-section" v-if="topicType === 3 && currentTopic && currentTopic.answer">
            <div class="reference-answer-display">
              <span class="ref-label">{{lang.homework.reference_answer}}: </span>
              <span class="ref-content">{{formatAnswer(currentTopic.answer, currentTopicType)}}</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import base from '../../../lib/base';
import DialogManager from '../../../lib/dialogManager';
import { Popup, Button, Icon } from 'vant';

export default {
    mixins: [base],
    name: "ProgressIndicator",
    components: {
        vanPopup: Popup,
        vanButton: Button,
        vanIcon: Icon
    },
    props: {
        progressList: {
            type: Array,
            required: true,
        },
        topicType: {
            type: Number,
            required: true
        },
        totalScore: {
            type: [Number, String],
            default: 0
        },
        topicStep: {
            type: Number,
            required: true
        },
        examDetail: {
            type: Object,
            default: () => ({})
        },
        isImageViewerOpen: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            showProgress: false,
            currentDialogId: null
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.showProgress;
            },
            set(val) {
                this.showProgress = val;
            },
        },
        doneCount() {
            return this.progressList.filter(item => item.completed).length;
        },
        headerTitle() {
            return this.topicType === 2 ? this.lang.homework.progress.answeringHeader : this.lang.homework.progress.correctionHeader;
        },
        currentTopic() {
            if (!this.examDetail || !this.examDetail.content) {
                return null;
            }

            let currentIndex = 0;
            for (const topicType of this.examDetail.content) {
                for (const topic of topicType.list) {
                    currentIndex++;
                    if (currentIndex === this.topicStep) {
                        return topic;
                    }
                }
            }
            return null;
        },
        currentTopicType() {
            if (!this.examDetail || !this.examDetail.content) {
                return '';
            }

            let currentIndex = 0;
            for (const topicType of this.examDetail.content) {
                for (const topic of topicType.list) {
                    currentIndex++;
                    if (currentIndex === this.topicStep) {
                        return topicType.type;
                    }
                }
            }
            return '';
        }
    },
    watch: {
        showProgress: {
            handler(val) {
                if (val) {
                    // 弹窗打开时，注册到DialogManager
                    this.currentDialogId = DialogManager.register(this, {
                        open: this.showDialog.bind(this),
                        close: this.closeDialog.bind(this),
                    });
                } else {
                    // 弹窗关闭时，从DialogManager注销
                    if (this.currentDialogId) {
                        DialogManager.unregister(this.currentDialogId);
                        this.currentDialogId = null;
                    }
                }
            },
            immediate: true
        }
    },
    mounted() {
    },
    methods: {
        showDialog() {
            this.dialogVisible = true;
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        checkIsShow() {
            return this.showProgress;
        },
        openProgress() {
            if (this.isImageViewerOpen) {
                return;
            }
            this.showProgress = true;
        },
        closeProgress() {
            this.showProgress = false;
        },
        onPopupOpen() {
        },
        onPopupClosed() {
        },
        jumpTo(index) {
            // 如果图片预览打开，不允许跳转
            if (this.isImageViewerOpen) {
                return;
            }
            this.$emit("jump", index + 1);
            this.closeProgress();
        },
        formatAnswer(answer, type) {
            switch (type) {
            case 'singleSelect':
                return answer;
            case 'multiSelect':
                return Array.isArray(answer) ? answer.join('、') : answer;
            default:
                return answer;
            }
        },
        handleSubmit() {
            this.$emit('submit');
            this.closeProgress();
        },
    },
    beforeDestroy() {
        // 组件销毁前清理DialogManager中的注册
        if (this.currentDialogId) {
            DialogManager.unregister(this.currentDialogId);
            this.currentDialogId = null;
        }
    }
};
</script>

<style lang="scss" scoped>
.progress-float-btn {
  position: fixed;
  right: 1rem;
  bottom: 5.5rem;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  // background-color: #00c59d;
  background-color: #7478e6;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  z-index: 100;
}

.progress-indicator {
  display: flex;
  flex-direction: column;
  width: 13.125rem;
  height: 100%;
}

.header {
  background-color: #00c59d;
  color: #fff;
  padding: 0.8rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .back-icon {
    font-size: 1.2rem;
  }

  .header-title {
    font-size: 1rem;
    font-weight: bold;
    text-align: right;
    flex: 1;
    margin: 0 3% 0 0;
  }
}

.content {
  flex: 1;
  padding: 0.8rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow-y: auto;
}

.question-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.2rem;
  justify-content: flex-start;
  padding: 0 0.5rem;
}

.question-item {
  width: 1.8rem;
  height: 1.8rem;
  line-height: 1.8rem;
  text-align: center;
  border-radius: 0.3rem;
  border: 1px solid #ccc;
  font-size: 0.8rem;
  margin: 0;
  box-sizing: border-box;
}

/* 状态颜色 */
.question-item.none {
  background-color: transparent;
  color: #000;
}
.question-item.done {
  background-color: #ccc;
  color: #fff;
}
.question-item.current {
  background-color: #00c59d;
  color: #fff;
}

.progress-text {
  font-size: 0.9rem;
  text-align: center;
}

.actions {
  display: flex;
  justify-content: space-around;
  margin: 1rem 0;

  .van-button {
    flex: 1;
    margin: 0 0.3rem;
    height: auto;
    padding: 0.4rem;
    border-radius: 0.8rem;
    font-size: 0.8rem;
  }

  .save-btn {
    background-color: #00c59d;
    color: #fff;
  }

  .submit-btn {
    background-color: #ff675c;
    color: #fff;
  }
}

.legend {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  font-size: 0.7rem;

  .legend-item {
    display: flex;
    align-items: center;
    margin: 0.3rem;

    .box {
      display: inline-block;
      width: 0.8rem;
      height: 0.8rem;
      margin-right: 0.3rem;
      border: 1px solid #ccc;
      border-radius: 0.2rem;
    }

    .box.none {
      background-color: transparent;
    }
    .box.done {
      background-color: #ccc;
    }
    .box.current {
      background-color: #00c59d;
    }
  }
}

.total-score {
  margin-top: 0.5rem;

  .score-value {
    color: #ff675c;
  }
}

/* 参考答案样式 */
.reference-answer-section {
  margin-top: 1rem;
  padding: 0.5rem;
  border-top: 1px solid #eee;
}

.reference-answer-display {
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: start;
  gap: 0.5rem;

  .ref-label {
    color: #666;
    white-space: nowrap;
    font-size: 0.8rem;
  }

  .ref-content {
    color: #FF9900;
    word-break: break-word;
    font-size: 0.8rem;
  }
}
</style>
