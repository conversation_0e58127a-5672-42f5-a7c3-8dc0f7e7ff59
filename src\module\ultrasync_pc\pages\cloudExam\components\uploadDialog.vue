<template>
  <common-dialog
    :show.sync="visible"
    :title="lang.homework.upload.upload_excel"
    :submit-show="false"
    :reject-show="false"
    @closed="handleClose"
    :footShow="false"
    :append-to-body="true"
  >
    <div class="upload-dialog-content">
      <div class="button-group">
        <el-button
          size="large"
          type="default"
          plain
          class="fixed-btn"
          @click="handleDownload"
        >
          <i class="el-icon-download" style="margin-right: 5px;"></i>
          {{lang.homework.upload.download_excel_template}}
        </el-button>
        <el-upload
          ref="upload"
          action="#"
          :auto-upload="false"
          :show-file-list="false"
          :on-change="handleFileChange"
          :limit="1"
          accept=".xlsx,.xls"
        >
          <el-button size="large" type="primary" :loading="uploading" class="fixed-btn">
            <i class="el-icon-upload" style="margin-right: 5px;"></i>
            {{ lang.homework.upload.upload_excel }}
          </el-button>
        </el-upload>
      </div>
      
      <div class="upload-tips">
        <ul>
          <li>{{lang.homework.upload_requirements.item1}}</li>
          <li>{{lang.homework.upload_requirements.item2}}</li>
          <li>{{lang.homework.upload_requirements.item3}}</li>
          <li>{{lang.homework.upload_requirements.item4}}</li>
          <li>{{lang.homework.upload_requirements.item5}}</li>
        </ul>
      </div>
    </div>
  </common-dialog>
</template>

<script>
import base from '../../../lib/base'
import CommonDialog from '../../../MRComponents/commonDialog'
import { convertExcelToMongoContent } from '../../../lib/excelToPaper.js'
import Tool from '@/common/tool.js'
export default {
    name: 'UploadDialog',
    components: {
        CommonDialog
    },
    mixins: [base],
    props: {
        show: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            uploading: false,
            visible: false
        }
    },
    watch: {
        show(val) {
            this.visible = val
        }
    },
    methods: {
        async handleFileChange(file) {
            if (!file) {
                return
            }

            // 限制文件大小不超过2MB
            const maxFileSize = 2 * 1024 * 1024; // 2MB
            if (file.raw.size > maxFileSize) {
                this.$message.error(this.lang.homework.upload.error.fileSize);
                this.$refs.upload.clearFiles();
                return;
            }

            // 验证文件格式
            const fileExtension = file.name.split('.').pop().toLowerCase()
            if (!['xlsx', 'xls'].includes(fileExtension)) {
                this.$message.error(this.lang.homework.upload.error.fileFormat)
                this.$refs.upload.clearFiles()
                return
            }
      
            this.uploading = true
            try {
                const jsonResult = await convertExcelToMongoContent(file.raw)
                this.$store.commit('homework/setUploadPaper', jsonResult)
                this.$emit('update:show', false)
                this.$router.push({
                    path: this.$route.fullPath + '/exam/5/preview'
                })
            } catch (err) {
                const [errorType, sheetName] = err.message.split(':');
                let errorMessage = '';
                
                switch(errorType) {
                case 'MAX_ROWS':
                    errorMessage = this.lang.homework.upload.error.maxRows.replace('{sheetName}', sheetName);
                    break;
                case 'MAX_COLUMNS':
                    errorMessage = this.lang.homework.upload.error.maxColumns.replace('{sheetName}', sheetName);
                    break;
                case 'FIELD_REQUIRED':
                    errorMessage = this.lang.homework.upload.error.fieldRequired.replace('{field}', sheetName);
                    break;
                case 'INVALID_SCORE':
                    errorMessage = this.lang.homework.upload.error.invalidScore.replace('{type}', this.lang.topic_type[sheetName]);
                    break;
                case 'NO_OPTIONS':
                    errorMessage = this.lang.homework.upload.error.noOptions.replace('{type}', this.lang.topic_type[sheetName]);
                    break;
                default:
                    errorMessage = this.lang.homework.upload.error.general; // + ': ' + err.message
                }
                
                this.$message.error(errorMessage);
            } finally {
                this.uploading = false;
                this.$refs.upload.clearFiles();
            }
        },
        handleClose() {
            this.$emit('update:show', false)
        },
        handleDownload() {
            const fileName = this.lang.currentLanguage === 'CN' 
                ? '作业模板.xlsx'
                : 'Exam_Template.xlsx'
            const url = `static/resource_pc/templates/${fileName}`
            Tool.downloadFileByBrowser(url, fileName)
        }
    }
}
</script>

<style lang="scss" scoped>
.upload-dialog-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;

  .button-group {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
  }

  .fixed-btn {
    width: flex;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .upload-tips {
    width: 100%;
    color: #666;

    ul {
      padding-left: 20px;
      
      li {
        line-height: 1.8;
      }
    }
  }
}
</style> 