<template>
	<transition name="slide" appear>
		<div class="subject_page fourth_level_page">
            <mrHeader>
                <template #title>
                    {{lang.edit_group_set}}
                </template>
            </mrHeader>
			<div class="container">
                <template v-if="type==0">
                    <button class="primary_bg choose needsclick" @click="choosePhoto">{{lang.choose_file}}</button>
                    <input type="file" accept="image/*" ref="fileRef" class="fileRef" @change="changeFile">
                    <vue-cropper v-show="showCropper"
                        ref="cropper"
                        :img="uploadedImageURL"
                        :autoCrop="true"
                        :autoCropWidth="200"
                        :autoCropHeight="200"
                        :fixedBox="true"
                    ></vue-cropper>
                    <button class="primary_bg modify_subject_btn" @click="submitProfile" v-show="showCropper" v-loading="uploading">{{lang.save_txt}}</button>
                </template>
				<template v-if="type==1">
					<p class="modify_tip">{{lang.group_set_name}}</p>
					<input type="text" class="commont_input" @click="scrollInput" v-model="subject" maxlength="50" ref="subject">
					<button class="primary_bg modify_subject_btn" @click="submit">{{lang.save_txt}}</button>
				</template>
				<template v-if="type==2">
                    <textarea  class="commont_input" @click="scrollInput" v-model="description" maxlength="300" ref="subject"></textarea>
                    <button class="primary_bg modify_subject_btn" @click="submit">{{lang.save_txt}}</button>
				</template>
			</div>
		</div>
	</transition>
</template>
<script>
import base from '../lib/base'
import { Toast } from 'vant';
import {VueCropper} from 'vue-cropper'
import groupsetTool from '../lib/groupsetTool'
import {checkImgExist} from '../lib/common_base'
export default {
    mixins: [base,groupsetTool],
    name: 'modify_groupset',
    components: {VueCropper},
    data(){
        return {
            type:this.$route.params.type,
            id:this.$route.params.id,
            subject:'',
            description:'',
            loading:false,
            uploadedImageURL:'',
            showCropper:false,
            uploading:false
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.subject=this.currentGroupset.subject;
            this.description=this.currentGroupset.description;
            this.file_input=this.$refs.fileRef;
        })
    },
    computed:{
        details(){
            return this.$store.state.groupset.details
        },
        currentGroupset(){
            return this.details[this.id]
        },
    },
    methods:{
        submit(){
            if (this.subject=='') {
                return
            }
            let parmas={
                groupSetID:this.currentGroupset.id,
                subject:this.subject,
                description:this.description
            }
            this.loading=true;
            window.main_screen.updateGroupset(parmas,(data)=>{
                this.loading=false
                console.log('data',data)
                if (data.error_code==0) {
                    this.$store.commit('groupset/updateGroupsetDetail',data.data)
                    this.back();
                }
            })
    	},
        choosePhoto(){
            this.$refs.fileRef.click();
        },
    	scrollInput(){
    		setTimeout(()=>{
    			this.$refs.subject.scrollIntoViewIfNeeded(true);
    		},300)
    	},
        changeFile(){
            this.showCropper=true;
            const URL = window.URL || window.webkitURL || window.mozURL
            const files = this.file_input.files
            if(files && files.length) {
                const file = files[0]
                if(/^image\/.+/.test(file.type)) {
                    this.uploadedImageURL = URL.createObjectURL(file)
                    checkImgExist(this.uploadedImageURL).then(img => {
                        // 图片完整，加载成功，不做任何操作
                    }).catch(err => {
                        // 图片损坏，加载失败，撤回链接释放内存
                        URL.revokeObjectURL(this.uploadedImageURL)
                        Toast(this.lang.image_corruption_text)
                    })
                }else{
                    Toast('Please choose an image file:'+file.type);
                }
            }
        },
        submitProfile(){
            const that=this;
            this.$refs.cropper.getCropData((data)=>{
                that.uploading=true;
                const controller = window.main_screen.controller;
                const gid = that.currentGroupset.id

                controller.emit("set_groupset_portrait",{
                    groupset_id:gid,
                    fileName: 'groupset_avatar_' + new Date().getTime() + '.jpg',
                    file:data
                },function(err,result){
                    if(!err){
                        that.uploading=false;
                    }else{
                        Toast('set_groupset_portrait error')
                    }
                });


            })
        },
    }
}

</script>
<style lang="scss">
	.subject_page{
		.container{
			margin:.8rem;
			.modify_tip{
				font-size:.8rem;
				color:#707070;
				margin:.1rem 0;
			}
			input{
				background-color:transparent;
				margin:0;
				color:#333;
				transform: translateZ(0px);
			}
			.modify_subject_btn{
				display: block;
			    width: 100%;
			    border: none;
			    font-size: 1rem;
			    line-height: 2rem;
			    margin: 1rem 0 .6rem;
			    border-radius: .2rem;
			}
            textarea{
                background-color:transparent;
                margin:0;
                color:#333;
                height:10rem;
                font-size:0.9rem;
                border:none;
                border-bottom: 2px solid #c8c7cc;
                resize: none;
            }
            .choose{
                display: inline-block;
                border: none;
                font-size: 0.8rem;
                line-height: 1.6rem;
                border-radius: .2rem;
                margin: 0.3rem 0.5rem;
            }
            .fileRef{
                display:none;
            }
            .vue-cropper{
                height:20rem;
            }
		}
	}
</style>
