<template>
    <div>
        <div class="main-container">
            <div class="weixin-tip" v-if="isWechatTipShow">
                <h3 class="cover_tip_text">{{ lang.wechat_unsupported_tip }}</h3>
            </div>
            <div class="container">
                <div class="logo"><img src="static/resource_activity/images/ios-180.png" /></div>
                <a :href="downloadAppAddress" class="download_app" :data-clipboard-text="base64Param">
                    <span>{{ lang.qr_install_app.download }} </span>
                    <span>{{ lang.app_name }} App </span>
                    <span v-if="serverType">({{ serverType }})</span>
                </a>
                <div class="open_app" :data-clipboard-text="base64Param" @click="openApp()">
                    <template v-if="currentLanguage === 'CN' || !currentLanguage">
                        {{ lang.qr_install_app.has_downloaded }}{{ lang.qr_install_app.start_directly }}
                    </template>
                    <template v-else>
                        <p>{{ lang.qr_install_app.has_downloaded }}</p>
                        <p>{{ lang.qr_install_app.start_directly }}</p>
                    </template>
                </div>
                <div class="params-container" v-if="isParamContainerShow">
                    <div class="tips-title">{{ lang.qr_install_app.first_start_with_var }}</div>
                    <div class="tips-small">{{ lang.qr_install_app.allow_clipboard }}</div>
                    <ul>
                        <li class="addgroup_content" v-if="isAddgroupShow">
                            <strong>{{ lang.qr_install_app.join_group }}: </strong>
                            <span class="addgroupSubject">{{ param.subject }}</span>
                        </li>
                        <li class="addfriend_content" v-if="isAddFriendsShow">
                            <strong>{{ lang.qr_install_app.add_friend }}</strong>
                            <span class="addfriend">{{ param.uname }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../lib/base";
import vConsole from "vconsole";
import { Base64 } from "js-base64";
import ClipboardJS from "clipboard";
import CallApp from "callapp-lib";
import service from "../service/service";
export default {
    mixins: [base],
    name: "qrInstallApp",
    data() {
        return {
            browser: {
                versions: {
                    trident: null,
                    presto: null,
                    webKit: null,
                    gecko: null,
                    mobile: null,
                    ios: null,
                    android: null,
                    iPhone: null,
                    iPad: null,
                    webApp: null,
                    weChat: null,
                    userAgent: null,
                },
                language: "",
            },
            serverType: "",
            schemeIOSUrl: "",
            schemeAndroidUrl: "",
            ultraSyncDownloadAddrAndroid: "",
            ultraSyncDownloadAddrIOS: "",
            ultraSyncAndroidUrl: "",
            ultraSyncIOSUrl: "",
            param: {
                act: "",
                add_sharer: 0,
            },
            base64Param: "",
            defaultRes: {
                ios_addr: "https://www.pgyer.com/tmsW",
                android_addr:
                    "https://rmtus-attachment-dev.oss-cn-shanghai.aliyuncs.com/Installer/Android/app-release-2.11.10089.apk",
                scheme_ios_url: "webultrasync",
                scheme_android_url: "webultrasync",
                build_version: "prod",
            },
            ajaxServer: ''
        };
    },
    computed: {
        deviceInfo() {
            return this.$store.state.device;
        },
        currentLanguage() {
            return localStorage.getItem("lang");
        },
        downloadAppAddress() {
            if (this.browser.versions.ios || this.browser.versions.iPhone || this.browser.versions.iPad) {
                return this.ultraSyncDownloadAddrIOS;
            } else {
                return this.ultraSyncDownloadAddrAndroid;
            }
            // } else {
            //     try{
            //         throw "Unknown browser version " + JSON.stringify(this.browser);
            //     } catch(e) {
            //         console.error(e)
            //         return 'javascript:alert(`This is not for PC/Desktop devices`)'
            //     }
            // }
        },
        isAddgroupShow() {
            return this.param.act === "add_group";
        },
        isAddFriendsShow() {
            return this.param.act === "add_friend" || Number(this.param.add_sharer) === 1;
        },
        isParamContainerShow() {
            return this.isAddgroupShow || this.isAddFriendsShow;
        },
        isWechatTipShow() {
            return !!this.browser.versions.weChat;
        },
    },
    created() {
        let u = navigator.userAgent;
        this.browser.versions = {
            //移动终端浏览器版本信息
            trident: u.indexOf("Trident") > -1, //IE内核
            presto: u.indexOf("Presto") > -1, //opera内核
            webKit: u.indexOf("AppleWebKit") > -1, //苹果、谷歌内核
            gecko: u.indexOf("Gecko") > -1 && u.indexOf("KHTML") == -1, //火狐内核
            mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/AppleWebKit/), //是否为移动终端
            ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
            android: u.indexOf("Android") > -1 || u.indexOf("Linux") > -1, //android终端或者uc浏览器
            iPhone: u.indexOf("iPhone") > -1 || u.indexOf("Mac") > -1, //是否为iPhone或者QQHD浏览器
            iPad: u.indexOf("iPad") > -1, //是否iPad
            webApp: u.indexOf("Safari") == -1, //是否web应该程序，没有头部与底部
            weChat: u.toLowerCase().match(/MicroMessenger/i) == "micromessenger",
            userAgent: u,
        };
        this.$nextTick(async () => {
            this.ajaxServer =
                this.systemConfig.server_type.protocol +
                this.systemConfig.server_type.host +
                this.systemConfig.server_type.port;
            let { data: res } = await service.getInstallQrCodeInfo(this.ajaxServer);
            if (!res) {
                res = this.defaultRes;
            }
            this.serverType = res.build_version || this.lang.qr_install_app.unknown_server_type;
            this.browser.language = navigator.language.toLowerCase();
            this.param = this.$route.query
            console.log('this.param',this.param)
            this.schemeIOSUrl = res.scheme_ios_url;
            this.schemeAndroidUrl = res.scheme_android_url;
            this.ultraSyncDownloadAddrAndroid = res.android_addr;
            this.ultraSyncDownloadAddrIOS = res.ios_addr;
            let ultraSyncIOSUrl = this.schemeIOSUrl + "://?";
            let ultraSyncAndroidUrl = this.schemeAndroidUrl + "://?";
            Object.keys(this.param).map((item, index) => {
                // ultraSyncIOSUrl+=`${item}=${param[item]}`
                // ultraSyncAndroidUrl+=`${item}=${param[item]}`
                ultraSyncIOSUrl += `${decodeURIComponent(item)}=${this.param[item]}`;
                ultraSyncAndroidUrl += `${decodeURIComponent(item)}=${this.param[item]}`;
                if (index !== Object.keys(this.param).length - 1) {
                    ultraSyncIOSUrl += "&";
                    ultraSyncAndroidUrl += "&";
                }
            });
            this.ultraSyncAndroidUrl = ultraSyncAndroidUrl;
            this.ultraSyncIOSUrl = ultraSyncIOSUrl;
            this.base64Param = Base64.encode(JSON.stringify(this.param));

            let clipboard = new ClipboardJS(".download_app");
            let clipboard2 = new ClipboardJS(".open_app");
            // 将地址栏信息写入剪切板
            clipboard.on("success", (e) => {
                console.log("复制的内容是" + e.text);
                // downLoadApp()
            });
            clipboard.on("error", (e) => {
                alert('复制到剪切板失败：'+e.action);
                // downLoadApp()
            });
            clipboard2.on("success", (e) => {
                console.log("复制的内容是" + e.text);
                // downLoadApp()
            });
            clipboard2.on("error", (e) => {
                alert('复制到剪切板失败：'+e.action);
                // downLoadApp()
            });
        });
    },
    methods: {
        openApp() {
            setTimeout(()=>{
                // 等待剪切板操作完成再执行
                let isAndroid = this.browser.versions.android;
                const option = {
                    scheme: {
                        protocol: isAndroid ? this.ultraSyncAndroidUrl : this.ultraSyncIOSUrl,
                    },
                    timeout: 10000,
                };
                const lib = new CallApp(option);
                lib.open({
                    path: "",
                    callback: () => {
                        // mui.confirm('未成功唤起APP，请尝试重新下载','提示',['确定',],function(res){
                        //     if(res.index===0){
                        //     }
                        // })
                    },
                });
            },200)
        },
    },
};
</script>
<style lang="scss">
* {
    margin: 0;
    padding: 0;
}

@media only screen and (min-width: 320px) {
    html,
    body {
        font-size: 20px !important;
    }
}

@media only screen and (min-width: 640px) {
    html,
    body {
        font-size: 30px !important;
    }
}

@media only screen and (min-width: 750px) {
    html,
    body {
        font-size: 40px !important;
    }
}

@media only screen and (min-width: 1242px) {
    html,
    body {
        font-size: 48px !important;
        max-width: 768px;
        box-sizing: border-box;
        position: relative;
    }
}

body {
    margin: 0px;
}

.weixin-tip h3 {
    max-width: 100%;
    height: auto;
    text-align: center;
    color: #ffff4f;
}

.weixin-tip {
    box-sizing: border-box;
    padding: 10% 2%;
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    filter: alpha(opacity=80);
    height: 100vh;
    width: 100vw;
    z-index: 1000000;
}

.weixin-tip p {
    text-align: center;
    margin-top: 80%;
    padding: 0 5%;
}

.container {
    text-align: center;
}

.container .logo {
    margin-top: 40px;
}

.container .download_app,
.container .open_app {
    margin: 40px auto;
    min-width: 240px;
    padding: 10px;
    background-color: #2cc185;
    border-radius: 5px;
    color: #fdf6ec;
    width: 80%;
    display: block;
}

.container .open_app {
    background-color: #54c4ef;
}

.container .disable {
    background-color: darkgray;
}

.params-container {
    color: #000;
    font-size: 0.7rem;
    text-align: left;
    padding: 0.5rem;
}

.tips-small {
    font-size: 0.5rem;
    color: red;
}

.params-container ul {
    padding: 0.7rem;
}

.params-container ul li {
    padding: 0 0.5rem;
    line-height: 1.2rem;
}
</style>
