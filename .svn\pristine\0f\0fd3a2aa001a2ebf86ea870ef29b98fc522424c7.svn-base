<template>
    <div class="case_gallery" v-if="dialogVisible">
        <div v-if="isCaseImage">
            <div class="image_gallery">
                <div class="wrap_header">
                    <span class="back_btn cancel_wrap" @click.stop="handleSubmitOld">
                        <i class="iconfont svg_icon_back icon-back"></i>
                    </span>
                </div>
                <span class="images_count" v-if="pathologyInfo.ultrasonic_diagnosis_list" >{{sliderKey+1}}/{{pathologyInfo.ultrasonic_diagnosis_list.length}}</span>
                <div class="mui-slider" id="case_slider">
                    <div class="mui-slider-group" ref="sliderGroup">
                        <div v-for="(item,index) of pathologyInfo.ultrasonic_diagnosis_list" class="mui-slider-item mui-zoom-wrapper" :key="index">
                            <div class="mui-zoom-scroller image_wrap">
                                <img :src="prefUrl+item.path" class="file mui-zoom" >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="case_result">
                <div class="color_frame_horizontal">
                    <div v-for="index of 5" :key="index" :class="'color_item_'+index" ></div>
                </div>
                <div class="case_dignose">
                    <p class="title">{{lang.ultrasonic_diagnosis}}:</p>
                    <p class="info">{{pathologyResult}}</p>
                </div>
                <div class="case_pathology">
                    <p class="title">{{lang.pthological_conclusion}}:</p>
                    <p class="info">{{pathologyInfo.postoperative_pathology}}:{{pathologyInfo.benign_malignant}}</p>
                </div>
            </div>

        </div>
        <div  v-else>
            <div class="image_gallery image_gallery_image">
                <canvas style="display:none" id="ai_canvas"></canvas>
                <div class="wrap_header">
                    <span class="back_btn cancel_wrap" @click.stop="handleSubmitOld">
                        <i class="iconfont svg_icon_back icon-back"></i>
                    </span>
                </div>
                <!-- <span class="images_count" v-if="pathologyInfo.ultrasonic_diagnosis_list" >{{sliderKey+1}}/{{pathologyInfo.ultrasonic_diagnosis_list.length}}</span> -->
                <div  class="not_case_image" ref="notCaseImage" id="notCaseImage">
                    <img :src="showUrl" class="not_case_image_wrap" ref="notCaseImageWrap" id="notCaseImageWrap">
                </div>
            </div>
                <div class="case_result case_result_image">
                    <div class="color_frame_horizontal">
                        <div v-for="index of 5" :key="index" :class="'color_item_'+index" >
                        </div>
                    </div>
                    <div class="case_dignose ">
                        <p class="title">{{lang.ai_searc_in_case_database}}:</p>

                        <p class="info">
                            {{ lang.searc_in_case_database_result.replace('{1}',rois.length)}}
                        </p>
                        <template v-if="rois.length>1">
                            <!-- <p class="title">选择病灶：</p> -->
                            <div style="display:flex;flex-direction: row;">
                                <div>
                                    <van-radio-group v-model="selectRoi" >
                                        <van-radio
                                            :icon-size="17"
                                            :label-position="'left'"
                                            v-for="(roi,index) in rois"
                                            :key="index"
                                            :name="index">
                                            <!-- :checked-color="colors[index]" -->
                                            <div class="color-signal" :style="{background:colors[index]}">
                                            </div>
                                            <span class="color-info">{{lang.thyroid.focus}}{{index+1}}</span>
                                        </van-radio>
                                    </van-radio-group>
                                </div>
                            </div>
                        </template>

                    </div>
                    <p class="edit_comment_btn" @click='handleSubmit' v-if="rois.length>1" >{{lang.search}}</p>
                </div>
            </div>
    </div>
</template>
<script>
import base from '../lib/base';
import Tool from '@/common/tool.js';
import DialogManager from "../lib/dialogManager";
import {  RadioGroup, Radio,} from 'vant';
export default {
    mixins: [base],
    name: 'caseGallery',
    components: { VanRadio:Radio,VanRadioGroup:RadioGroup,},
    props:{
        pathologyInfo:{
            type:Object,
            default:null,
        },
        imageObj:{
            type: Object,
            default: null
        },
        show: {
            type: Boolean,
            default: false,
        },
        isCaseImage:{
            type: Boolean,
            default: false,
        }
    },
    computed: {
        dialogVisible: {
            get() {
                if(this.show){
                    this.initCaseImageShow()
                }
                this.sliderKey = 0;
                return this.show;
            },
            set(val) {
                this.$emit("update:show", val);
            },
        },
        prefUrl(){
            return this.systemConfig.serverInfo.oss_attachment_server.playback_https_addr + '/' + 'AiSearch' + '/'
        },
        rois(){
            // let xis = [50,150,290,200]
            // xis = [500,850,550,690]
            // xis = [700,1150,750,890]
            // xis = [900,650,950,790]
            // return [
            //     [50,150,290,200],
            //     [500,850,550,690],
            //     [700,1150,750,890],
            // ]

            if(this.imageObj && this.imageObj.selectRoi){
                this.selectRoi = this.imageObj.selectRoi||0
                this.oldSelectRoi = this.imageObj.selectRoi||0
            }
            return this.imageObj.roi || []
        }
    },
    watch: {
        // 监听弹窗显示状态
        dialogVisible(val) {
            if (val && !this.caseGalleryDialogId) {
                // 打开弹窗时，注册到 DialogManager
                this.caseGalleryDialogId = DialogManager.register(this, {
                    open: () => {
                        // 弹窗已经通过 v-if 显示，这里不需要额外操作
                    },
                    close: () => {
                        this.dialogVisible = false;
                    },
                    canCloseOnPopstate: true,
                    canClose: true
                });
            } else if (!val && this.caseGalleryDialogId) {
                // 关闭弹窗时，从 DialogManager 注销
                DialogManager.unregister(this.caseGalleryDialogId);
                this.caseGalleryDialogId = null;
                // 触发原有的关闭事件
                this.$emit('close',{flag:this.selectRoi!=this.oldSelectRoi,roiIndex:this.selectRoi});
            }
        }
    },
    data(){
        return {
            sliderKey:0,
            showUrl: '',
            pathologyResult:'',
            sliderObj:{},
            colors:['#FF0000','#00FF00','#0000FF','#8000FF','#FFFFFF','#FFFF00'],
            selectRoi:0,
            oldSelectRoi:0,
            caseGalleryDialogId: null, // DialogManager 中弹窗的ID
        }
    },
    created(){
    },
    mounted(){

    },
    beforeDestroy() {
        // 清理 DialogManager 中的弹窗注册
        if (this.caseGalleryDialogId) {
            DialogManager.unregister(this.caseGalleryDialogId);
            this.caseGalleryDialogId = null;
        }
    },
    methods:{
        initCaseImageShow(){
            let that = this
            setTimeout(()=>{
                if(this.isCaseImage){
                    document.getElementById('case_slider').addEventListener('slide',this.slideHandler)
                    var gallery=window.mui('#case_slider')
                    this.sliderObj=gallery.slider()
                    let sliderObj=this.sliderObj
                    sliderObj.gotoItem(this.sliderKey,0);
                    // 缩小放大
                    window.mui('.mui-zoom-wrapper').zoom();

                    this.pathologyResult=Tool.getPathologyConclusion(this.pathologyInfo,this.sliderKey)
                } else{
                    // console.log('imageObj:',that.imageObj)
                    if(!that.imageObj.isHttpResoucel){
                        // if(that.imageObj.imageUrl.indexOf('blob:http://')>-1 || that.imageObj.imageUrl.indexOf('blob:https://')>-1){
                        // console.log('---**')
                        // console.error('---**',that.imageObj)
                        // let reader = new FileReader()
                        // reader.onload = function (e) {
                        //     let data = e.target.result
                        //     let image = new Image()
                        //     image.src= data
                        //     image.onload=function(){
                        //         that.showUrl=that.drawCanvasToImage(image)
                        //     }
                        //     // console.error('that.imageObj.file:',that.imageObj.file)
                        // };
                        // reader.readAsDataURL(that.imageObj.file);
                        // console.log('imageObj:',that.imageObj)
                        let image=new Image()
                        image.onerror=function(e){
                            console.log('preload blob Error',e)
                        }
                        image.onload=function(){
                            that.showUrl=that.drawCanvasToImage(image)
                        }
                        image.src=that.imageObj.file
                    }else{
                        let image=new Image()
                        image.onerror=function(e){
                            console.log('preload http resource Error',e)
                        }
                        image.onload=function(){
                            that.showUrl=that.drawCanvasToImage(image)
                        }
                        if(this.systemConfig.serverInfo.network_environment === 1){
                            that.imageObj.file = Tool.replaceInternalNetworkEnvImageHost(that.imageObj.file)
                        }
                        image.setAttribute("crossOrigin",'anonymous');
                        image.src=`${that.imageObj.file}?temp=${Date.now()}`;
                    }

                }

            },0)

        },
        drawCanvasToImage(realImage){
            let that = this
            //左右[x1,y1,x2,y2]
            var canvas=document.getElementById('ai_canvas')
            var context=canvas.getContext('2d');
            canvas.width=realImage.width;
            canvas.height=realImage.height;
            context.drawImage(realImage,0,0);
            context.strokeStyle="#f00"
            context.lineWidth=2

            if(this.rois){
                let i = 0
                for(let roi of this.rois){
                    context.beginPath();
                    context.moveTo(roi[0], roi[1]); //把画笔移动到指定的坐标
                    context.lineTo(roi[2], roi[1]);  //绘制一条从当前位置到指定坐标(200, 50)的直线.
                    context.lineTo(roi[2], roi[3]);
                    context.lineTo(roi[0], roi[3]);
                    //闭合路径。会拉一条从当前点到path起始点的直线。如果当前点与起始点重合，则什么都不做
                    context.closePath();
                    context.strokeStyle = this.colors[i];
                    context.lineWidth=8;
                    // context.fillStyle = this.colors[i];
                    // context.fill();
                    context.stroke(); //绘制路径。
                    ++i
                }
            }
            // context.beginPath()
            // context.arc(,8,0,2*Math.PI);
            // context.strokeStyle = "black"
            // context.fillStyle = "";
            // context.fill() //填充当前路径颜色
            // context.stroke() //绘制已/定义的路径颜色
            // context.fillStyle = "black"
            // context.font = "20px Times New Roman"
            // context.textBaseline = "middle"
            // context.fillText(1, (xis[2] + xis[0]) / 2,(xis[3] + xis[1]) / 2)
            let base64=canvas.toDataURL("image/jpeg");
            return base64;
        },
        handleSubmitOld(){
            this.selectRoi = this.oldSelectRoi
            this.dialogVisible = false
        },
        handleSubmit(){
            this.dialogVisible = false
        },
        slideHandler(event){
            this.sliderKey = event.detail.slideNumber
        }
    },
}
</script>
<style lang="scss">
.case_gallery{
    .image_gallery{
        position: absolute;
        top: 0;
        left: 0;
        height: 57%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-items: center;
        background-color:#000000;
        z-index: 999;
        .wrap_header{
            color:#fff;
            width: 100%;
            display: inline-flex;
            padding-left: 1rem;
            padding-top: 1.5rem;
            .back_btn{
                width: 1rem;
                height: 1.2rem;
                text-align: left;
                fill: #fff;
                i{
                    width: 100%;
                    height: 75%;
                }
            }

        }

        .image_wrap{
            img{
                max-height:100%;
                max-width:100%;
                touch-action: none;
            }
        }
        .not_case_image{
            text-align:center;
            overflow: scroll;
            display:flex;
            justify-content:center;
            .not_case_image_wrap{
                display:block;
                max-height:100%;
                max-width:100%;
            }
        }
        .images_count{
            position: relative;
            left: 85%;
            margin: 1rem 1rem 1.8rem 0;
            text-align: center;
            background-color: #6d7271;
            color: #c9cfce;
            border-radius: 0.35rem;
            width: 2rem;
            line-height: 1rem;
            font-size: 0.65rem;
            letter-spacing: 0.1rem;
            font-style: normal;
        }
    }
    .case_result{
        position: absolute;
        bottom: 0;
        left: 0;
        height: 43%;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: left;
        background-color: #212121;
        font-size: 0.7rem;
        line-height: 1.25rem;
        z-index: 2;
        .color_frame_horizontal{
            height: 0.2rem;
            width: 100%;
            display: flex;
            .color_item_1{
                width: 20%;
                background-color:rgb(255,103,92);
            }
            .color_item_2{
                width: 20%;
                background-color:#00c59d;
            }
            .color_item_3{
                width: 20%;
                background-color:rgb(255,177,68);
            }
            .color_item_4{
                width: 20%;
                background-color:rgb(86,200,255);
            }
            .color_item_5{
                width: 20%;
                background-color:rgb(116,128,234);
            }

        }
        .case_dignose,.case_pathology{
            overflow: scroll;
            padding: 1rem;
            .title{
                color: #00c59d;
                font-size: 0.9rem;
                margin: 0.5rem 0;
            }
            .info{
                color:#c9cfce;
            }
            height: calc(100%);

        }


        .edit_comment_btn{
            font-size: 0.7rem;
            padding: 0.2rem 0.3rem;
            background-color: #01bd97;
            line-height: 1rem;
            margin: 0.4rem 0.6rem;
            border-radius: 0.2rem;
            text-align: center;
            color:white;
        }
        .color-signal{
            display:inline-block;
            width:1em;
            height:1em;
            vertical-align:middle;

        }
        .color-info{
            color:#c9cfce;
            margin-right:1rem;
            margin-left:0.5rem;
        }
        .van-radio{
            margin-bottom:0.5rem;
        }
    }
    .case_result_image{
        height: 33% !important;
    }
    .image_gallery_image{
        height: 67%  !important;
    }
}
</style>
